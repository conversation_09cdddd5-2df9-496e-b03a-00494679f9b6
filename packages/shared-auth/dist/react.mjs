// src/auth-provider.tsx
import React2, { useState, useEffect, useCallback, useRef } from "react";

// src/auth-context.tsx
import { createContext, useContext } from "react";
var AuthContext = createContext(void 0);
var useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
};

// src/auth-client.ts
import axios from "axios";
import { jwtDecode } from "jwt-decode";
import Cookies from "js-cookie";

// src/types.ts
var AuthError = class extends Error {
  constructor(message, code, statusCode, details) {
    super(message);
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
    this.name = "AuthError";
  }
};

// src/auth-client.ts
var AuthClient = class {
  constructor(config) {
    this.refreshPromise = null;
    this.config = {
      authEndpoint: "/auth",
      tokenKey: "access_token",
      refreshTokenKey: "refresh_token",
      userKey: "user",
      enableAutoRefresh: true,
      refreshInterval: 10 * 60 * 1e3,
      // 10 minutes
      storage: "localStorage",
      ...config
    };
    this.api = axios.create({
      baseURL: config.apiBaseUrl,
      headers: {
        "Content-Type": "application/json"
      }
    });
    this.setupInterceptors();
  }
  setupInterceptors() {
    this.api.interceptors.request.use(
      (config) => {
        const token = this.getAccessToken();
        if (token && !config.headers["skipAuth"]) {
          config.headers["Authorization"] = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          try {
            await this.refreshTokens();
            const token = this.getAccessToken();
            if (token) {
              originalRequest.headers["Authorization"] = `Bearer ${token}`;
              return this.api(originalRequest);
            }
          } catch (refreshError) {
            this.config.onAuthError?.(new AuthError(
              "Session expired. Please login again.",
              "SESSION_EXPIRED",
              401
            ));
            this.clearAuth();
            throw refreshError;
          }
        }
        throw this.handleApiError(error);
      }
    );
  }
  handleApiError(error) {
    if (!error.response) {
      return new AuthError("Network error", "NETWORK_ERROR");
    }
    const { status, data } = error.response;
    const message = data?.message || "An error occurred";
    const code = data?.code || "UNKNOWN_ERROR";
    return new AuthError(message, code, status, data);
  }
  // Storage methods
  getStorage() {
    switch (this.config.storage) {
      case "sessionStorage":
        return sessionStorage;
      case "cookie":
        return {
          getItem: (key) => Cookies.get(key) || null,
          setItem: (key, value) => {
            Cookies.set(key, value, this.config.cookieOptions);
          },
          removeItem: (key) => Cookies.remove(key)
        };
      default:
        return localStorage;
    }
  }
  setTokens(tokens) {
    const storage = this.getStorage();
    storage.setItem(this.config.tokenKey, tokens.accessToken);
    storage.setItem(this.config.refreshTokenKey, tokens.refreshToken);
  }
  getAccessToken() {
    return this.getStorage().getItem(this.config.tokenKey);
  }
  getRefreshToken() {
    return this.getStorage().getItem(this.config.refreshTokenKey);
  }
  setUser(user) {
    const storage = this.getStorage();
    storage.setItem(this.config.userKey, JSON.stringify(user));
  }
  getUser() {
    const storage = this.getStorage();
    const userStr = storage.getItem(this.config.userKey);
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch {
        return null;
      }
    }
    return null;
  }
  clearAuth() {
    const storage = this.getStorage();
    storage.removeItem(this.config.tokenKey);
    storage.removeItem(this.config.refreshTokenKey);
    storage.removeItem(this.config.userKey);
  }
  // Auth methods
  async login(credentials) {
    const response = await this.api.post(`${this.config.authEndpoint}/login`, credentials, {
      headers: { skipAuth: true }
    });
    const { user, accessToken, refreshToken, expiresIn } = response.data;
    const tokens = {
      accessToken,
      refreshToken,
      expiresIn,
      tokenType: "Bearer"
    };
    this.setTokens(tokens);
    this.setUser(user);
    return { user, tokens };
  }
  async register(data) {
    const response = await this.api.post(`${this.config.authEndpoint}/register`, data, {
      headers: { skipAuth: true }
    });
    const { user, accessToken, refreshToken, expiresIn } = response.data;
    const tokens = {
      accessToken,
      refreshToken,
      expiresIn,
      tokenType: "Bearer"
    };
    this.setTokens(tokens);
    this.setUser(user);
    return { user, tokens };
  }
  async logout() {
    try {
      await this.api.post(`${this.config.authEndpoint}/logout`);
    } catch (error) {
      console.error("Logout API failed:", error);
    } finally {
      this.clearAuth();
    }
  }
  async refreshTokens() {
    if (this.refreshPromise) {
      return this.refreshPromise;
    }
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      throw new AuthError("No refresh token available", "NO_REFRESH_TOKEN");
    }
    this.refreshPromise = this.api.post(
      `${this.config.authEndpoint}/refresh`,
      { refreshToken },
      { headers: { skipAuth: true } }
    ).then((response) => {
      const { accessToken, refreshToken: newRefreshToken, expiresIn } = response.data;
      const tokens = {
        accessToken,
        refreshToken: newRefreshToken,
        expiresIn,
        tokenType: "Bearer"
      };
      this.setTokens(tokens);
      this.config.onTokenRefresh?.(tokens);
      return tokens;
    }).finally(() => {
      this.refreshPromise = null;
    });
    return this.refreshPromise;
  }
  async getCurrentUser() {
    const response = await this.api.get(`${this.config.authEndpoint}/me`);
    const user = response.data;
    this.setUser(user);
    return user;
  }
  async updateProfile(data) {
    const response = await this.api.patch(`${this.config.authEndpoint}/profile`, data);
    const user = response.data;
    this.setUser(user);
    return user;
  }
  async changePassword(currentPassword, newPassword) {
    await this.api.post(`${this.config.authEndpoint}/change-password`, {
      currentPassword,
      newPassword
    });
  }
  async requestPasswordReset(email) {
    await this.api.post(`${this.config.authEndpoint}/forgot-password`, { email }, {
      headers: { skipAuth: true }
    });
  }
  async resetPassword(token, newPassword) {
    await this.api.post(`${this.config.authEndpoint}/reset-password`, {
      token,
      newPassword
    }, {
      headers: { skipAuth: true }
    });
  }
  // Token validation
  isTokenExpired(token) {
    const tokenToCheck = token || this.getAccessToken();
    if (!tokenToCheck) return true;
    try {
      const decoded = jwtDecode(tokenToCheck);
      return decoded.exp * 1e3 < Date.now();
    } catch {
      return true;
    }
  }
  // Session management
  async validateSession() {
    try {
      const token = this.getAccessToken();
      if (!token || this.isTokenExpired(token)) {
        await this.refreshTokens();
      }
      await this.getCurrentUser();
      return true;
    } catch {
      return false;
    }
  }
  // Utility methods
  getStoredUser() {
    return this.getUser();
  }
  getStoredTokens() {
    const accessToken = this.getAccessToken();
    const refreshToken = this.getRefreshToken();
    if (!accessToken || !refreshToken) {
      return null;
    }
    return {
      accessToken,
      refreshToken,
      tokenType: "Bearer"
    };
  }
  clearSession() {
    this.clearAuth();
  }
};

// src/auth-provider.tsx
var AuthProvider = ({
  children,
  config,
  initialState,
  onAuthStateChange
}) => {
  const authClient = useRef(new AuthClient(config));
  const [state, setState] = useState({
    user: null,
    tokens: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
    ...initialState
  });
  useEffect(() => {
    onAuthStateChange?.(state);
  }, [state, onAuthStateChange]);
  useEffect(() => {
    const initAuth = async () => {
      try {
        const storedUser = authClient.current.getStoredUser();
        const storedTokens = authClient.current.getStoredTokens();
        if (storedUser && storedTokens) {
          const isValid = await authClient.current.validateSession();
          if (isValid) {
            setState({
              user: authClient.current.getStoredUser(),
              tokens: authClient.current.getStoredTokens(),
              isAuthenticated: true,
              isLoading: false,
              error: null
            });
          } else {
            authClient.current.clearSession();
            setState({
              user: null,
              tokens: null,
              isAuthenticated: false,
              isLoading: false,
              error: null
            });
          }
        } else {
          setState((prev) => ({ ...prev, isLoading: false }));
        }
      } catch (error) {
        setState({
          user: null,
          tokens: null,
          isAuthenticated: false,
          isLoading: false,
          error
        });
      }
    };
    initAuth();
  }, []);
  useEffect(() => {
    if (!config.enableAutoRefresh || !state.isAuthenticated) {
      return;
    }
    const interval = setInterval(async () => {
      try {
        const tokens = await authClient.current.refreshTokens();
        setState((prev) => ({ ...prev, tokens }));
      } catch (error) {
        console.error("Auto refresh failed:", error);
      }
    }, config.refreshInterval || 10 * 60 * 1e3);
    return () => clearInterval(interval);
  }, [state.isAuthenticated, config.enableAutoRefresh, config.refreshInterval]);
  const login = useCallback(async (credentials) => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));
    try {
      const { user, tokens } = await authClient.current.login(credentials);
      setState({
        user,
        tokens,
        isAuthenticated: true,
        isLoading: false,
        error: null
      });
      return user;
    } catch (error) {
      const authError = error;
      setState({
        user: null,
        tokens: null,
        isAuthenticated: false,
        isLoading: false,
        error: authError
      });
      throw authError;
    }
  }, []);
  const register = useCallback(async (data) => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));
    try {
      const { user, tokens } = await authClient.current.register(data);
      setState({
        user,
        tokens,
        isAuthenticated: true,
        isLoading: false,
        error: null
      });
      return user;
    } catch (error) {
      const authError = error;
      setState({
        user: null,
        tokens: null,
        isAuthenticated: false,
        isLoading: false,
        error: authError
      });
      throw authError;
    }
  }, []);
  const logout = useCallback(async () => {
    setState((prev) => ({ ...prev, isLoading: true }));
    try {
      await authClient.current.logout();
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      setState({
        user: null,
        tokens: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      });
    }
  }, []);
  const refreshAuth = useCallback(async () => {
    try {
      const tokens = await authClient.current.refreshTokens();
      const user = await authClient.current.getCurrentUser();
      setState({
        user,
        tokens,
        isAuthenticated: true,
        isLoading: false,
        error: null
      });
    } catch (error) {
      const authError = error;
      setState({
        user: null,
        tokens: null,
        isAuthenticated: false,
        isLoading: false,
        error: authError
      });
      throw authError;
    }
  }, []);
  const updateUser = useCallback(async (data) => {
    if (!state.user) {
      throw new AuthError("No authenticated user", "NO_USER");
    }
    try {
      const updatedUser = await authClient.current.updateProfile(data);
      setState((prev) => ({ ...prev, user: updatedUser }));
      return updatedUser;
    } catch (error) {
      throw error;
    }
  }, [state.user]);
  const hasRole = useCallback((role) => {
    return state.user?.roles.includes(role) || false;
  }, [state.user]);
  const hasPermission = useCallback((permission) => {
    return state.user?.permissions.includes(permission) || false;
  }, [state.user]);
  const hasAnyRole = useCallback((roles) => {
    return roles.some((role) => hasRole(role));
  }, [hasRole]);
  const hasAllRoles = useCallback((roles) => {
    return roles.every((role) => hasRole(role));
  }, [hasRole]);
  const hasAnyPermission = useCallback((permissions) => {
    return permissions.some((permission) => hasPermission(permission));
  }, [hasPermission]);
  const hasAllPermissions = useCallback((permissions) => {
    return permissions.every((permission) => hasPermission(permission));
  }, [hasPermission]);
  const contextValue = {
    user: state.user,
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    error: state.error,
    login,
    logout,
    register,
    refreshAuth,
    updateUser,
    hasRole,
    hasPermission,
    hasAnyRole,
    hasAllRoles,
    hasAnyPermission,
    hasAllPermissions
  };
  return /* @__PURE__ */ React2.createElement(AuthContext.Provider, { value: contextValue }, children);
};

// src/hooks.ts
import { useEffect as useEffect2 } from "react";
import { useNavigate, useLocation } from "@tanstack/react-router";
export {
  AuthProvider,
  useAuthContext as useAuth
};
//# sourceMappingURL=react.mjs.map