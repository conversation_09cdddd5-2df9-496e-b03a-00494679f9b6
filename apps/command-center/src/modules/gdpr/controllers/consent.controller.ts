import {
  Controller,
  Get,
  Post,
  Put,
  Query,
  Param,
  Body,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsBoolean, IsEnum } from 'class-validator';

import { ConsentService } from '../services/consent.service';

export class RecordConsentDto {
  @IsString()
  userId: string;

  @IsString()
  purpose: string;

  @IsEnum(['granted', 'denied', 'withdrawn'])
  status: 'granted' | 'denied' | 'withdrawn';

  @IsOptional()
  @IsString()
  legalBasis?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  metadata?: Record<string, any>;
}

export class UpdateConsentDto {
  @IsEnum(['granted', 'denied', 'withdrawn'])
  status: 'granted' | 'denied' | 'withdrawn';

  @IsOptional()
  @IsString()
  reason?: string;

  @IsOptional()
  metadata?: Record<string, any>;
}

export class GetConsentsDto {
  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsString()
  purpose?: string;

  @IsOptional()
  @IsEnum(['granted', 'denied', 'withdrawn'])
  status?: 'granted' | 'denied' | 'withdrawn';

  @IsOptional()
  limit?: number = 100;

  @IsOptional()
  offset?: number = 0;
}

@ApiTags('Consent Management')
@ApiBearerAuth()
@Controller('consent')
export class ConsentController {
  constructor(private readonly consentService: ConsentService) {}

  @Post()
  @ApiOperation({ summary: 'Record user consent' })
  @ApiResponse({ status: 201, description: 'Consent recorded successfully' })
  async recordConsent(@Body(ValidationPipe) consentDto: RecordConsentDto) {
    const consent = await this.consentService.recordConsent({
      userId: consentDto.userId,
      purpose: consentDto.purpose,
      status: consentDto.status,
      legalBasis: consentDto.legalBasis,
      description: consentDto.description,
      metadata: consentDto.metadata,
      timestamp: new Date(),
    });

    return {
      success: true,
      data: consent,
      message: 'Consent recorded successfully',
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get consent records' })
  @ApiResponse({ status: 200, description: 'Consent records retrieved successfully' })
  async getConsentRecords(@Query(ValidationPipe) query: GetConsentsDto) {
    const consents = await this.consentService.getConsentRecords({
      userId: query.userId,
      purpose: query.purpose,
      status: query.status,
      limit: query.limit,
      offset: query.offset,
    });

    return {
      success: true,
      data: consents,
      total: consents.length,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get consent record by ID' })
  @ApiResponse({ status: 200, description: 'Consent record retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Consent record not found' })
  async getConsentById(@Param('id') id: string) {
    const consent = await this.consentService.getConsentById(id);

    if (!consent) {
      return {
        success: false,
        message: 'Consent record not found',
      };
    }

    return {
      success: true,
      data: consent,
    };
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update consent record' })
  @ApiResponse({ status: 200, description: 'Consent updated successfully' })
  @ApiResponse({ status: 404, description: 'Consent record not found' })
  async updateConsent(
    @Param('id') id: string,
    @Body(ValidationPipe) updateDto: UpdateConsentDto
  ) {
    const consent = await this.consentService.updateConsent(id, {
      status: updateDto.status,
      reason: updateDto.reason,
      metadata: updateDto.metadata,
    });

    if (!consent) {
      return {
        success: false,
        message: 'Consent record not found',
      };
    }

    return {
      success: true,
      data: consent,
      message: 'Consent updated successfully',
    };
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get all consents for a user' })
  @ApiResponse({ status: 200, description: 'User consents retrieved successfully' })
  async getUserConsents(@Param('userId') userId: string) {
    const consents = await this.consentService.getUserConsents(userId);

    return {
      success: true,
      data: consents,
      total: consents.length,
    };
  }

  @Post('user/:userId/withdraw')
  @ApiOperation({ summary: 'Withdraw all user consents' })
  @ApiResponse({ status: 200, description: 'User consents withdrawn successfully' })
  async withdrawUserConsents(
    @Param('userId') userId: string,
    @Body() body: { reason?: string; purposes?: string[] }
  ) {
    const result = await this.consentService.withdrawUserConsents(
      userId,
      body.purposes,
      body.reason
    );

    return {
      success: true,
      data: result,
      message: `${result.withdrawnCount} consents withdrawn successfully`,
    };
  }

  @Get('user/:userId/status')
  @ApiOperation({ summary: 'Get user consent status summary' })
  @ApiResponse({ status: 200, description: 'User consent status retrieved successfully' })
  async getUserConsentStatus(@Param('userId') userId: string) {
    const status = await this.consentService.getUserConsentStatus(userId);

    return {
      success: true,
      data: status,
    };
  }

  @Get('purposes/list')
  @ApiOperation({ summary: 'Get available consent purposes' })
  @ApiResponse({ status: 200, description: 'Consent purposes retrieved successfully' })
  async getConsentPurposes() {
    const purposes = await this.consentService.getConsentPurposes();

    return {
      success: true,
      data: purposes,
    };
  }

  @Post('bulk-record')
  @ApiOperation({ summary: 'Record multiple consents' })
  @ApiResponse({ status: 201, description: 'Consents recorded successfully' })
  async bulkRecordConsents(@Body() body: { consents: RecordConsentDto[] }) {
    const results = await this.consentService.bulkRecordConsents(
      body.consents.map(consent => ({
        userId: consent.userId,
        purpose: consent.purpose,
        status: consent.status,
        legalBasis: consent.legalBasis,
        description: consent.description,
        metadata: consent.metadata,
        timestamp: new Date(),
      }))
    );

    return {
      success: true,
      data: results,
      message: `${results.successful} consents recorded successfully, ${results.failed} failed`,
    };
  }

  @Get('analytics/summary')
  @ApiOperation({ summary: 'Get consent analytics summary' })
  @ApiResponse({ status: 200, description: 'Consent analytics retrieved successfully' })
  async getConsentAnalytics(@Query('days') days?: number) {
    const analytics = await this.consentService.getConsentAnalytics(days || 30);

    return {
      success: true,
      data: analytics,
    };
  }

  @Get('compliance/report')
  @ApiOperation({ summary: 'Generate consent compliance report' })
  @ApiResponse({ status: 200, description: 'Consent compliance report generated successfully' })
  async generateConsentComplianceReport(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    const dateRange = startDate && endDate ? {
      start: new Date(startDate),
      end: new Date(endDate),
    } : undefined;

    const report = await this.consentService.generateConsentReport(dateRange);

    return {
      success: true,
      data: report,
    };
  }

  @Post('verify')
  @ApiOperation({ summary: 'Verify consent for specific purpose' })
  @ApiResponse({ status: 200, description: 'Consent verification completed' })
  async verifyConsent(
    @Body() body: { userId: string; purpose: string; requiredStatus?: string }
  ) {
    const verification = await this.consentService.verifyConsent(
      body.userId,
      body.purpose,
      body.requiredStatus as any
    );

    return {
      success: true,
      data: verification,
    };
  }

  @Get('expiring/list')
  @ApiOperation({ summary: 'Get expiring consents' })
  @ApiResponse({ status: 200, description: 'Expiring consents retrieved successfully' })
  async getExpiringConsents(@Query('days') days?: number) {
    const expiring = await this.consentService.getExpiringConsents(days || 30);

    return {
      success: true,
      data: expiring,
      total: expiring.length,
    };
  }

  @Post('renew/:id')
  @ApiOperation({ summary: 'Renew consent record' })
  @ApiResponse({ status: 200, description: 'Consent renewed successfully' })
  @ApiResponse({ status: 404, description: 'Consent record not found' })
  async renewConsent(@Param('id') id: string) {
    const renewed = await this.consentService.renewConsent(id);

    if (!renewed) {
      return {
        success: false,
        message: 'Consent record not found',
      };
    }

    return {
      success: true,
      data: renewed,
      message: 'Consent renewed successfully',
    };
  }

  @Get('audit-trail/:userId')
  @ApiOperation({ summary: 'Get consent audit trail for user' })
  @ApiResponse({ status: 200, description: 'Consent audit trail retrieved successfully' })
  async getConsentAuditTrail(@Param('userId') userId: string) {
    const auditTrail = await this.consentService.getConsentAuditTrail(userId);

    return {
      success: true,
      data: auditTrail,
      total: auditTrail.length,
    };
  }
}