import { useEffect } from 'react'
import { useNavigate, useLocation } from '@tanstack/react-router'
import { useAuthContext } from './auth-context'

export { useAuthContext as useAuth } from './auth-context'

// Hook to protect routes
export function useRequireAuth(options?: {
  redirectTo?: string
  roles?: string[]
  permissions?: string[]
}) {
  const { 
    isAuthenticated, 
    isLoading, 
    user,
    hasAllRoles,
    hasAllPermissions 
  } = useAuthContext()
  const navigate = useNavigate()
  const location = useLocation()

  useEffect(() => {
    if (isLoading) return

    const redirectTo = options?.redirectTo || '/login'
    
    if (!isAuthenticated) {
      navigate({ to: redirectTo })
      return
    }

    // Check role requirements
    if (options?.roles && !hasAllRoles(options.roles)) {
      navigate({ to: '/unauthorized' })
      return
    }

    // Check permission requirements
    if (options?.permissions && !hasAllPermissions(options.permissions)) {
      navigate({ to: '/unauthorized' })
      return
    }
  }, [
    isAuthenticated,
    isLoading,
    user,
    navigate,
    location,
    options,
    hasAllRoles,
    hasAllPermissions
  ])

  return {
    isAuthenticated,
    isLoading,
    user,
    isAuthorized: true
  }
}

// Hook to redirect if already authenticated
export function useRedirectIfAuthenticated(redirectTo = '/dashboard') {
  const { isAuthenticated, isLoading } = useAuthContext()
  const navigate = useNavigate()
  const location = useLocation()

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      const from = (location.state as any)?.from || redirectTo
      navigate({ to: from })
    }
  }, [isAuthenticated, isLoading, navigate, location, redirectTo])
}

// Hook to check permissions
export function usePermissions() {
  const {
    user,
    hasRole,
    hasPermission,
    hasAnyRole,
    hasAllRoles,
    hasAnyPermission,
    hasAllPermissions
  } = useAuthContext()

  return {
    user,
    roles: user?.roles || [],
    permissions: user?.permissions || [],
    hasRole,
    hasPermission,
    hasAnyRole,
    hasAllRoles,
    hasAnyPermission,
    hasAllPermissions,
    isAdmin: hasRole('admin'),
    isManager: hasAnyRole(['admin', 'manager']),
    isUser: hasRole('user')
  }
}

// Hook for conditional rendering based on auth
export function useAuthGuard(
  condition: 'authenticated' | 'unauthenticated' | 'loading'
): boolean {
  const { isAuthenticated, isLoading } = useAuthContext()

  switch (condition) {
    case 'authenticated':
      return isAuthenticated && !isLoading
    case 'unauthenticated':
      return !isAuthenticated && !isLoading
    case 'loading':
      return isLoading
    default:
      return false
  }
}

// Hook to handle auth errors
export function useAuthError(handler?: (error: Error) => void) {
  const { error } = useAuthContext()

  useEffect(() => {
    if (error && handler) {
      handler(error)
    }
  }, [error, handler])

  return error
}