/**
 * Mock Server - MSW setup for API testing
 */

import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { beforeAll, afterEach, afterAll } from 'vitest';
import { testDataFactory } from './test-utils';

// Mock API handlers
export const handlers = [
  // Users API
  http.get('/api/users', ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search');
    
    let users = Array.from({ length: 50 }, (_, i) => 
      testDataFactory.createUser({ 
        id: `${i + 1}`, 
        name: `User ${i + 1}`,
        email: `user${i + 1}@example.com`
      })
    );

    if (search) {
      users = users.filter(user => 
        user.name.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase())
      );
    }

    const start = (page - 1) * limit;
    const end = start + limit;
    const paginatedUsers = users.slice(start, end);

    return HttpResponse.json(testDataFactory.createApiResponse(paginatedUsers, {
      metadata: {
        page,
        limit,
        total: users.length,
        hasNext: end < users.length,
        hasPrevious: page > 1
      }
    }));
  }),

  http.get('/api/users/:id', ({ params }) => {
    const user = testDataFactory.createUser({ 
      id: params.id as string,
      name: `User ${params.id}`,
      email: `user${params.id}@example.com`
    });
    return HttpResponse.json(testDataFactory.createApiResponse(user));
  }),

  http.post('/api/users', async ({ request }) => {
    const userData = await request.json() as any;
    const user = testDataFactory.createUser({
      id: Math.random().toString(36).substr(2, 9),
      ...userData
    });
    return HttpResponse.json(testDataFactory.createApiResponse(user), { status: 201 });
  }),

  http.put('/api/users/:id', async ({ params, request }) => {
    const userData = await request.json() as any;
    const user = testDataFactory.createUser({
      id: params.id as string,
      ...userData
    });
    return HttpResponse.json(testDataFactory.createApiResponse(user));
  }),

  http.delete('/api/users/:id', () => {
    return new HttpResponse(null, { status: 204 });
  }),

  // Training API
  http.get('/api/trainings', ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const category = url.searchParams.get('categoryId');
    
    let trainings = Array.from({ length: 30 }, (_, i) => 
      testDataFactory.createTraining({ 
        id: `${i + 1}`, 
        title: `Training ${i + 1}`,
        category: { id: category || '1', name: 'Test Category' }
      })
    );

    if (category) {
      trainings = trainings.filter(training => training.category.id === category);
    }

    const start = (page - 1) * limit;
    const end = start + limit;
    const paginatedTrainings = trainings.slice(start, end);

    return HttpResponse.json(testDataFactory.createApiResponse(paginatedTrainings, {
      metadata: {
        page,
        limit,
        total: trainings.length,
        hasNext: end < trainings.length,
        hasPrevious: page > 1
      }
    }));
  }),

  http.get('/api/trainings/:id', ({ params }) => {
    const training = testDataFactory.createTraining({ 
      id: params.id as string,
      title: `Training ${params.id}`
    });
    return HttpResponse.json(testDataFactory.createApiResponse(training));
  }),

  http.post('/api/trainings', async ({ request }) => {
    const trainingData = await request.json() as any;
    const training = testDataFactory.createTraining({
      id: Math.random().toString(36).substr(2, 9),
      ...trainingData
    });
    return HttpResponse.json(testDataFactory.createApiResponse(training), { status: 201 });
  }),

  // Training enrollment
  http.post('/api/trainings/:id/enroll', async ({ request }) => {
    await request.json() as any;
    return new HttpResponse(null, { status: 204 });
  }),

  http.delete('/api/trainings/:trainingId/enroll/:userId', () => {
    return new HttpResponse(null, { status: 204 });
  }),

  // Error scenarios for testing
  http.get('/api/users/error', () => {
    return HttpResponse.json(
      testDataFactory.createApiError({ message: 'Server error' }),
      { status: 500 }
    );
  }),

  http.get('/api/users/unauthorized', () => {
    return HttpResponse.json(
      testDataFactory.createApiError({ 
        message: 'Unauthorized',
        code: 'AUTH_REQUIRED'
      }),
      { status: 401 }
    );
  }),

  http.get('/api/users/slow', async () => {
    // Simulate slow response
    await new Promise(resolve => setTimeout(resolve, 2000));
    return HttpResponse.json(testDataFactory.createApiResponse([]));
  }),
];

// Create mock server
export const server = setupServer(...handlers);

// Server utilities
export const mockServerUtils = {
  // Add temporary handler
  addHandler: (handler: any) => {
    server.use(handler);
  },

  // Reset handlers to original state
  resetHandlers: () => {
    server.resetHandlers(...handlers);
  },

  // Create error response
  createErrorResponse: (status: number, message: string) => {
    return HttpResponse.json(
      testDataFactory.createApiError({ message }),
      { status }
    );
  },

  // Create success response with delay
  createDelayedResponse: (data: any, delay: number = 100) => {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(HttpResponse.json(testDataFactory.createApiResponse(data)));
      }, delay);
    });
  },

  // Mock network error
  mockNetworkError: (url: string) => {
    server.use(
      http.get(url, () => {
        return HttpResponse.error();
      })
    );
  },

  // Mock timeout
  mockTimeout: (url: string, timeout: number = 5000) => {
    server.use(
      http.get(url, async () => {
        await new Promise(resolve => setTimeout(resolve, timeout));
        return HttpResponse.json(testDataFactory.createApiResponse([]));
      })
    );
  },
};

// Setup and teardown utilities
export const setupMockServer = () => {
  // Start server before all tests
  beforeAll(() => {
    server.listen({ onUnhandledRequest: 'error' });
  });

  // Reset handlers after each test
  afterEach(() => {
    server.resetHandlers();
  });

  // Close server after all tests
  afterAll(() => {
    server.close();
  });
};