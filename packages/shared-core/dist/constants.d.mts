/**
 * Application-wide constants for Luminar platform
 */
declare const API_CONFIG: {
    readonly TIMEOUT: 10000;
    readonly RETRY_ATTEMPTS: 3;
    readonly RETRY_DELAY: 1000;
    readonly DEFAULT_PAGE_SIZE: 20;
    readonly MAX_PAGE_SIZE: 100;
};
declare const AUTH_CONFIG: {
    readonly SESSION_TIMEOUT: number;
    readonly REFRESH_INTERVAL: number;
    readonly TOKEN_STORAGE_KEY: "luminar_auth_token";
    readonly REFRESH_TOKEN_STORAGE_KEY: "luminar_refresh_token";
    readonly USER_STORAGE_KEY: "luminar_user";
    readonly REMEMBER_ME_DURATION: number;
};
declare const FILE_CONFIG: {
    readonly MAX_FILE_SIZE: number;
    readonly ALLOWED_IMAGE_TYPES: readonly ["image/jpeg", "image/png", "image/gif", "image/webp"];
    readonly ALLOWED_DOCUMENT_TYPES: readonly ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "text/plain", "text/csv"];
    readonly CHUNK_SIZE: number;
};
declare const UI_CONFIG: {
    readonly DEBOUNCE_DELAY: 300;
    readonly THROTTLE_DELAY: 100;
    readonly ANIMATION_DURATION: 200;
    readonly TOAST_DURATION: 5000;
    readonly MODAL_Z_INDEX: 1000;
    readonly DROPDOWN_Z_INDEX: 999;
    readonly TOOLTIP_Z_INDEX: 998;
};
declare const THEME_CONFIG: {
    readonly DEFAULT_THEME: "light";
    readonly STORAGE_KEY: "luminar_theme";
    readonly BREAKPOINTS: {
        readonly sm: "640px";
        readonly md: "768px";
        readonly lg: "1024px";
        readonly xl: "1280px";
        readonly '2xl': "1536px";
    };
    readonly COLORS: {
        readonly primary: {
            readonly 50: "#eff6ff";
            readonly 100: "#dbeafe";
            readonly 200: "#bfdbfe";
            readonly 300: "#93c5fd";
            readonly 400: "#60a5fa";
            readonly 500: "#3b82f6";
            readonly 600: "#2563eb";
            readonly 700: "#1d4ed8";
            readonly 800: "#1e40af";
            readonly 900: "#1e3a8a";
        };
        readonly gray: {
            readonly 50: "#f9fafb";
            readonly 100: "#f3f4f6";
            readonly 200: "#e5e7eb";
            readonly 300: "#d1d5db";
            readonly 400: "#9ca3af";
            readonly 500: "#6b7280";
            readonly 600: "#4b5563";
            readonly 700: "#374151";
            readonly 800: "#1f2937";
            readonly 900: "#111827";
        };
    };
};
declare const VALIDATION_RULES: {
    readonly EMAIL_REGEX: RegExp;
    readonly PASSWORD_MIN_LENGTH: 8;
    readonly PASSWORD_REGEX: RegExp;
    readonly PHONE_REGEX: RegExp;
    readonly URL_REGEX: RegExp;
    readonly USERNAME_REGEX: RegExp;
};
declare const DATE_FORMATS: {
    readonly ISO: "yyyy-MM-dd";
    readonly US: "MM/dd/yyyy";
    readonly EU: "dd/MM/yyyy";
    readonly DISPLAY: "MMM d, yyyy";
    readonly DISPLAY_WITH_TIME: "MMM d, yyyy h:mm a";
    readonly TIME_12H: "h:mm a";
    readonly TIME_24H: "HH:mm";
    readonly DATETIME_ISO: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx";
};
declare const HTTP_STATUS: {
    readonly OK: 200;
    readonly CREATED: 201;
    readonly NO_CONTENT: 204;
    readonly BAD_REQUEST: 400;
    readonly UNAUTHORIZED: 401;
    readonly FORBIDDEN: 403;
    readonly NOT_FOUND: 404;
    readonly CONFLICT: 409;
    readonly UNPROCESSABLE_ENTITY: 422;
    readonly INTERNAL_SERVER_ERROR: 500;
    readonly BAD_GATEWAY: 502;
    readonly SERVICE_UNAVAILABLE: 503;
};
declare const ERROR_CODES: {
    readonly AUTH_INVALID_CREDENTIALS: "AUTH_INVALID_CREDENTIALS";
    readonly AUTH_TOKEN_EXPIRED: "AUTH_TOKEN_EXPIRED";
    readonly AUTH_TOKEN_INVALID: "AUTH_TOKEN_INVALID";
    readonly AUTH_INSUFFICIENT_PERMISSIONS: "AUTH_INSUFFICIENT_PERMISSIONS";
    readonly AUTH_ACCOUNT_LOCKED: "AUTH_ACCOUNT_LOCKED";
    readonly AUTH_ACCOUNT_NOT_VERIFIED: "AUTH_ACCOUNT_NOT_VERIFIED";
    readonly VALIDATION_REQUIRED_FIELD: "VALIDATION_REQUIRED_FIELD";
    readonly VALIDATION_INVALID_FORMAT: "VALIDATION_INVALID_FORMAT";
    readonly VALIDATION_MIN_LENGTH: "VALIDATION_MIN_LENGTH";
    readonly VALIDATION_MAX_LENGTH: "VALIDATION_MAX_LENGTH";
    readonly VALIDATION_INVALID_EMAIL: "VALIDATION_INVALID_EMAIL";
    readonly VALIDATION_PASSWORD_WEAK: "VALIDATION_PASSWORD_WEAK";
    readonly NETWORK_CONNECTION_ERROR: "NETWORK_CONNECTION_ERROR";
    readonly NETWORK_TIMEOUT: "NETWORK_TIMEOUT";
    readonly NETWORK_SERVER_ERROR: "NETWORK_SERVER_ERROR";
    readonly FILE_TOO_LARGE: "FILE_TOO_LARGE";
    readonly FILE_INVALID_TYPE: "FILE_INVALID_TYPE";
    readonly FILE_UPLOAD_FAILED: "FILE_UPLOAD_FAILED";
    readonly UNKNOWN_ERROR: "UNKNOWN_ERROR";
    readonly OPERATION_FAILED: "OPERATION_FAILED";
    readonly RESOURCE_NOT_FOUND: "RESOURCE_NOT_FOUND";
    readonly RESOURCE_ALREADY_EXISTS: "RESOURCE_ALREADY_EXISTS";
};
declare const USER_ROLES: {
    readonly SUPER_ADMIN: "super_admin";
    readonly ADMIN: "admin";
    readonly MANAGER: "manager";
    readonly USER: "user";
    readonly GUEST: "guest";
};
declare const PERMISSIONS: {
    readonly USERS_READ: "users:read";
    readonly USERS_WRITE: "users:write";
    readonly USERS_DELETE: "users:delete";
    readonly CONTENT_READ: "content:read";
    readonly CONTENT_WRITE: "content:write";
    readonly CONTENT_DELETE: "content:delete";
    readonly CONTENT_PUBLISH: "content:publish";
    readonly ANALYTICS_READ: "analytics:read";
    readonly ANALYTICS_EXPORT: "analytics:export";
    readonly SYSTEM_CONFIG: "system:config";
    readonly SYSTEM_LOGS: "system:logs";
    readonly SYSTEM_BACKUP: "system:backup";
};
declare const ROUTES: {
    readonly HOME: "/";
    readonly LOGIN: "/login";
    readonly REGISTER: "/register";
    readonly FORGOT_PASSWORD: "/forgot-password";
    readonly RESET_PASSWORD: "/reset-password";
    readonly DASHBOARD: "/dashboard";
    readonly PROFILE: "/profile";
    readonly SETTINGS: "/settings";
    readonly USERS: "/users";
    readonly ANALYTICS: "/analytics";
    readonly HELP: "/help";
    readonly PRIVACY: "/privacy";
    readonly TERMS: "/terms";
};
declare const STORAGE_KEYS: {
    readonly THEME: "luminar_theme";
    readonly LANGUAGE: "luminar_language";
    readonly SIDEBAR_COLLAPSED: "luminar_sidebar_collapsed";
    readonly TABLE_PREFERENCES: "luminar_table_preferences";
    readonly RECENT_SEARCHES: "luminar_recent_searches";
    readonly DRAFT_DATA: "luminar_draft_data";
};
declare const EVENTS: {
    readonly AUTH_LOGIN: "auth:login";
    readonly AUTH_LOGOUT: "auth:logout";
    readonly AUTH_SESSION_EXPIRED: "auth:session-expired";
    readonly THEME_CHANGED: "theme:changed";
    readonly NOTIFICATION_RECEIVED: "notification:received";
    readonly FILE_UPLOAD_PROGRESS: "file:upload-progress";
    readonly FILE_UPLOAD_COMPLETE: "file:upload-complete";
    readonly FILE_UPLOAD_ERROR: "file:upload-error";
};
declare const FEATURE_FLAGS: {
    readonly ENABLE_DARK_MODE: "enable_dark_mode";
    readonly ENABLE_NOTIFICATIONS: "enable_notifications";
    readonly ENABLE_FILE_UPLOAD: "enable_file_upload";
    readonly ENABLE_ANALYTICS: "enable_analytics";
    readonly ENABLE_REAL_TIME: "enable_real_time";
    readonly ENABLE_OFFLINE_MODE: "enable_offline_mode";
};
declare const APP_METADATA: {
    readonly NAME: "Luminar";
    readonly DESCRIPTION: "Learning & Development Platform";
    readonly VERSION: "1.0.0";
    readonly AUTHOR: "Luminar Team";
    readonly SUPPORT_EMAIL: "<EMAIL>";
    readonly DOCUMENTATION_URL: "https://docs.luminar.ai";
    readonly GITHUB_URL: "https://github.com/luminar/platform";
};

export { API_CONFIG, APP_METADATA, AUTH_CONFIG, DATE_FORMATS, ERROR_CODES, EVENTS, FEATURE_FLAGS, FILE_CONFIG, HTTP_STATUS, PERMISSIONS, ROUTES, STORAGE_KEYS, THEME_CONFIG, UI_CONFIG, USER_ROLES, VALIDATION_RULES };
