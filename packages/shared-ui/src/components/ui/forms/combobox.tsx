import React from "react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "~/lib/utils"
import { forwardRef, HTMLAttributes, useState, useEffect, useRef, createContext, useContext, ReactNode } from "react"
import { Check, ChevronsUpDown, Search, X } from "lucide-react"
import {
  createGlassmorphism,
  type GlassConfig,
  type GlassProfile,
  type GlassElement
} from "~/lib/glassmorphism"
import { LuminarButton } from "../actions/button-advanced"
import { LuminarInput } from "../forms/input"
import { microInteractions } from "~/lib/micro-interactions"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '~/types/component-props';
import { defaultComponentProps } from '~/types/component-props';

// Types
export interface ComboboxOption {
  value: string
  label: string
  disabled?: boolean
  keywords?: string[]
}

// Context for combobox state
interface ComboboxContextType {
  open: boolean
  setOpen: (open: boolean) => void
  value: string
  setValue: (value: string) => void
  search: string
  setSearch: (search: string) => void
  options: ComboboxOption[]
}

const ComboboxContext = createContext<ComboboxContextType | undefined>(undefined)

const useCombobox = () => {
  const context = useContext(ComboboxContext)
  if (!context) {
    throw new Error("Combobox components must be used within Combobox")
  }
  return context
}

// Root component
export interface LuminarComboboxProps {
  value?: string
  onValueChange?: (value: string) => void
  children: ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

const LuminarCombobox = ({ 
  value = "", 
  onValueChange, 
  children, 
  open, 
  onOpenChange 
}: LuminarComboboxProps) => {
  const [internalOpen, setInternalOpen] = useState(false)
  const [internalValue, setInternalValue] = useState(value)
  const [search, setSearch] = useState("")
  const [options, setOptions] = useState<ComboboxOption[]>([])

  const isControlled = open !== undefined && onOpenChange !== undefined

  const handleOpenChange = (newOpen: boolean) => {
    if (isControlled) {
      onOpenChange(newOpen)
    } else {
      setInternalOpen(newOpen)
    }
  }

  const handleValueChange = (newValue: string) => {
    setInternalValue(newValue)
    onValueChange?.(newValue)
  }

  const contextValue: ComboboxContextType = {
    open: isControlled ? open : internalOpen,
    setOpen: handleOpenChange,
    value: internalValue,
    setValue: handleValueChange,
    search,
    setSearch,
    options
  }

  // Update options when children change
  useEffect(() => {
    const extractOptions = (children: ReactNode): ComboboxOption[] => {
      const options: ComboboxOption[] = []
      
      const processChild = (child: ReactNode) => {
        if (React.isValidElement(child)) {
          if (child.type === LuminarComboboxItem) {
            const props = child.props as any
            options.push({
              value: props.value,
              label: props.children || props.value,
              disabled: props.disabled,
              keywords: props.keywords
            })
          } else if ((child.props as any)?.children) {
            React.Children.forEach((child.props as any).children, processChild)
          }
        }
      }
      React.Children.forEach(children, processChild)
      return options
    }

    setOptions(extractOptions(children))
  }, [children])

  return (
    <ComboboxContext.Provider value={contextValue}>
      {children}
    </ComboboxContext.Provider>
  )
}

// Trigger component
export interface LuminarComboboxTriggerProps extends Omit<HTMLAttributes<HTMLButtonElement>, 'onDrag' | 'onDragStart' | 'onDragEnd' | 'onDragEnter' | 'onDragLeave' | 'onDragOver' | 'onDrop' | 'onAnimationStart' | 'onAnimationEnd' | 'onAnimationIteration'> {
  variant?: GlassVariant
  size?: ComponentSize
  glassIntensity?: GlassIntensity
  colorTheme?: ColorTheme
  placeholder?: string
  asChild?: boolean
}

const LuminarComboboxTrigger = forwardRef<HTMLButtonElement, LuminarComboboxTriggerProps>(
  ({ 
    className,
    variant = "input",
    size = defaultComponentProps.size,
    glassIntensity = "medium",
    colorTheme = "neutral",
    placeholder = "Select option...",
    asChild = false,
    ...props 
  }, ref) => {
    const { open, setOpen, value, options } = useCombobox()
    
    // Generate glass classes
    const glassClasses = colorTheme !== "neutral" 
      ? getGlassClasses(variant, { intensity: glassIntensity })
      : getGlassClasses(variant, {
          intensity: glassIntensity,
          depth: "surface",
          animated: true,
          interactive: true
        })

    const sizeClasses = componentSizes[size]
    const selectedOption = options.find(opt => opt.value === value)

    if (asChild) {
      return (
        <div onClick={() => setOpen(!open)}>
          {props.children}
        </div>
      )
    }

    return (
      <LuminarButton
        ref={ref}
        variant="outline"
        size={size}
        role="combobox"
        aria-expanded={open}
        className={cn(
          "w-full justify-between",
          glassClasses,
          !selectedOption && "text-muted-foreground",
          className
        )}
        onClick={() => setOpen(!open)}
        {...props}
      >
        {selectedOption ? selectedOption.label : placeholder}
        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
      </LuminarButton>
    )
  }
)

// Content component
export interface LuminarComboboxContentProps extends Omit<HTMLAttributes<HTMLDivElement>, 'onDrag' | 'onDragStart' | 'onDragEnd' | 'onDragEnter' | 'onDragLeave' | 'onDragOver' | 'onDrop' | 'onAnimationStart' | 'onAnimationEnd' | 'onAnimationIteration'> {
  variant?: GlassVariant
  size?: ComponentSize
  glassIntensity?: GlassIntensity
  colorTheme?: ColorTheme
  animation?: keyof typeof animationPresets
  disableAnimation?: boolean
  align?: "start" | "center" | "end"
  side?: "top" | "bottom" | "left" | "right"
}

const LuminarComboboxContent = forwardRef<HTMLDivElement, LuminarComboboxContentProps>(
  ({ 
    className,
    variant = "modal",
    size = defaultComponentProps.size,
    glassIntensity = "medium",
    colorTheme = "neutral",
    animation = "slideDown",
    disableAnimation = false,
    align = "start",
    side = "bottom",
    children,
    ...props 
  }, ref) => {
    const { open, setOpen } = useCombobox()
    const contentRef = useRef<HTMLDivElement>(null)

    // Generate glass classes
    const glassClasses = colorTheme !== "neutral" 
      ? getGlassClasses(variant, { intensity: glassIntensity })
      : getGlassClasses(variant, {
          intensity: glassIntensity,
          depth: "floating",
          animated: !disableAnimation,
          interactive: true
        })

    const animationProps = disableAnimation ? {} : animationPresets[animation]

    // Handle click outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (contentRef.current && !contentRef.current.contains(event.target as Node)) {
          setOpen(false)
        }
      }

      if (open) {
        document.addEventListener('mousedown', handleClickOutside)
      }

      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }, [open, setOpen])

    return (
      <AnimatePresence>
        {open && (
          <motion.div
            ref={contentRef}
            className={cn(
              "absolute z-50 w-full min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",
              glassClasses,
              className
            )}
            {...(animationProps as any)}
            transition={transitions.spring}
            {...(props as any)}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    )
  }
)

// Input component for search
export interface LuminarComboboxInputProps extends Omit<HTMLAttributes<HTMLInputElement>, 'onDrag' | 'onDragStart' | 'onDragEnd' | 'onDragEnter' | 'onDragLeave' | 'onDragOver' | 'onDrop' | 'onAnimationStart' | 'onAnimationEnd' | 'onAnimationIteration'> {
  placeholder?: string
  variant?: GlassVariant
  size?: ComponentSize
  glassIntensity?: GlassIntensity
  colorTheme?: ColorTheme
}

const LuminarComboboxInput = forwardRef<HTMLInputElement, LuminarComboboxInputProps>(
  ({ 
    className,
    placeholder = "Search...",
    variant = "input",
    size = defaultComponentProps.size,
    glassIntensity = "light",
    colorTheme = "neutral",
    ...props 
  }, ref) => {
    const { search, setSearch } = useCombobox()

    return (
      <div className="flex items-center border-b px-3 border-white/10">
        <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
        <input
          ref={ref}
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          placeholder={placeholder}
          className={cn(
            "flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",
            className
          )}
          {...props}
        />
      </div>
    )
  }
)

// Empty component
export interface LuminarComboboxEmptyProps extends Omit<HTMLAttributes<HTMLDivElement>, 'onDrag' | 'onDragStart' | 'onDragEnd' | 'onDragEnter' | 'onDragLeave' | 'onDragOver' | 'onDrop' | 'onAnimationStart' | 'onAnimationEnd' | 'onAnimationIteration'> {}

const LuminarComboboxEmpty = forwardRef<HTMLDivElement, LuminarComboboxEmptyProps>(
  ({ className, ...props }, ref) => {
    const { search, options } = useCombobox()
    
    // Filter options based on search
    const filteredOptions = options.filter(option => {
      if (!search) return true
      
      const searchLower = search.toLowerCase()
      const matchesLabel = option.label.toLowerCase().includes(searchLower)
      const matchesValue = option.value.toLowerCase().includes(searchLower)
      const matchesKeywords = option.keywords?.some(keyword => 
        keyword.toLowerCase().includes(searchLower)
      )
      
      return matchesLabel || matchesValue || matchesKeywords
    })

    if (filteredOptions.length > 0) return null

    return (
      <div
        ref={ref}
        className={cn(
          "py-6 text-center text-sm text-muted-foreground",
          className
        )}
        {...props}
      />
    )
  }
)

// Group component
export interface LuminarComboboxGroupProps extends Omit<HTMLAttributes<HTMLDivElement>, 'onDrag' | 'onDragStart' | 'onDragEnd' | 'onDragEnter' | 'onDragLeave' | 'onDragOver' | 'onDrop' | 'onAnimationStart' | 'onAnimationEnd' | 'onAnimationIteration'> {
  heading?: string
}

const LuminarComboboxGroup = forwardRef<HTMLDivElement, LuminarComboboxGroupProps>(
  ({ className, heading, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("overflow-hidden p-1", className)}
      {...props}
    >
      {heading && (
        <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground">
          {heading}
        </div>
      )}
      {children}
    </div>
  )
)

// Item component
export interface LuminarComboboxItemProps extends Omit<HTMLAttributes<HTMLDivElement>, 'onSelect' | 'onDrag' | 'onDragStart' | 'onDragEnd' | 'onDragEnter' | 'onDragLeave' | 'onDragOver' | 'onDrop'> {
  value: string
  disabled?: boolean
  keywords?: string[]
  onSelect?: (value: string) => void
}

const LuminarComboboxItem = forwardRef<HTMLDivElement, LuminarComboboxItemProps>(
  ({ 
    className, 
    value, 
    disabled = false, 
    keywords = [], 
    onSelect,
    children,
    ...props 
  }, ref) => {
    const { value: selectedValue, setValue, setOpen, search } = useCombobox()
    
    // Filter based on search
    const searchLower = search.toLowerCase()
    const matchesLabel = children?.toString().toLowerCase().includes(searchLower)
    const matchesValue = value.toLowerCase().includes(searchLower)
    const matchesKeywords = keywords.some(keyword => 
      keyword.toLowerCase().includes(searchLower)
    )
    
    const isVisible = !search || matchesLabel || matchesValue || matchesKeywords
    const isSelected = selectedValue === value

    if (!isVisible) return null

    const handleSelect = () => {
      if (disabled) return
      
      setValue(value)
      setOpen(false)
      onSelect?.(value)
    }

    return (
      <motion.div
        ref={ref}
        role="option"
        aria-selected={isSelected}
        className={cn(
          "relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors",
          "hover:bg-white/10 focus:bg-white/10 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
          isSelected && "bg-white/10",
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        onClick={handleSelect}
        {...(!disabled && microInteractions.buttonPress)}
        {...(props as any)}
      >
        <Check
          className={cn(
            "mr-2 h-4 w-4",
            isSelected ? "opacity-100" : "opacity-0"
          )}
        />
        {children}
      </motion.div>
    )
  }
)

// Separator component
export interface LuminarComboboxSeparatorProps extends Omit<HTMLAttributes<HTMLDivElement>, 'onDrag' | 'onDragStart' | 'onDragEnd' | 'onDragEnter' | 'onDragLeave' | 'onDragOver' | 'onDrop' | 'onAnimationStart' | 'onAnimationEnd' | 'onAnimationIteration'> {}

const LuminarComboboxSeparator = forwardRef<HTMLDivElement, LuminarComboboxSeparatorProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("my-1 h-px bg-white/10", className)}
      {...props}
    />
  )
)

// Set display names
LuminarCombobox.displayName = "LuminarCombobox"
LuminarComboboxTrigger.displayName = "LuminarComboboxTrigger"
LuminarComboboxContent.displayName = "LuminarComboboxContent"
LuminarComboboxInput.displayName = "LuminarComboboxInput"
LuminarComboboxEmpty.displayName = "LuminarComboboxEmpty"
LuminarComboboxGroup.displayName = "LuminarComboboxGroup"
LuminarComboboxItem.displayName = "LuminarComboboxItem"
LuminarComboboxSeparator.displayName = "LuminarComboboxSeparator"

export {
  LuminarCombobox,
  LuminarComboboxTrigger,
  LuminarComboboxContent,
  LuminarComboboxInput,
  LuminarComboboxEmpty,
  LuminarComboboxGroup,
  LuminarComboboxItem,
  LuminarComboboxSeparator,
}

// Simplified API for quick usage (shadcn/ui style)
export const Combobox = LuminarCombobox
export const ComboboxTrigger = LuminarComboboxTrigger
export const ComboboxContent = LuminarComboboxContent
export const ComboboxInput = LuminarComboboxInput
export const ComboboxEmpty = LuminarComboboxEmpty
export const ComboboxGroup = LuminarComboboxGroup
export const ComboboxItem = LuminarComboboxItem
export const ComboboxSeparator = LuminarComboboxSeparator

// Hook for easier usage
export const useComboboxValue = () => {
  const { value, setValue } = useCombobox()
  return [value, setValue] as const
}