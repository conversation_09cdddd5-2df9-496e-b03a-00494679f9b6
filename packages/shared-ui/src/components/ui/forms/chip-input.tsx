import React from "react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "~/lib/utils"
import { forwardRef, HTMLAttributes, useState, useRef, useCallback, KeyboardEvent } from "react"
import { X } from "lucide-react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '~/types/component-props';
import { defaultComponentProps } from '~/types/component-props';

export interface Chip {
  id: string
  label: string
  value?: any
  color?: string
  icon?: React.ElementType
  removable?: boolean
}

export interface LuminarChipInputProps extends Omit<HTMLAttributes<HTMLDivElement>, 'onChange' | 'onDrag' | 'onDragStart' | 'onDragEnd' | 'onDragEnter' | 'onDragLeave' | 'onDragOver' | 'onDrop' | 'onAnimationStart' | 'onAnimationEnd' | 'onAnimationIteration'> {
  value?: Chip[]
  onChange?: (chips: Chip[]) => void
  placeholder?: string
  maxChips?: number
  allowDuplicates?: boolean
  validateChip?: (value: string) => boolean | string
  suggestions?: string[]
  delimiter?: string | RegExp
  glass?: boolean
  animated?: boolean
  disabled?: boolean
  readOnly?: boolean
  variant?: ComponentVariant | 'outlined' | 'filled'
  size?: ComponentSize | 'md' | 'lg'
  colorScheme?: 'auto' | 'single' | string[]
  onChipClick?: (chip: Chip, index: number) => void
  renderChip?: (chip: Chip, index: number) => React.ReactNode
}

const sizeClasses: Record<ComponentSize, string> = {
  xs: 'text-xs py-0.5 px-1.5',
  sm: 'text-xs py-1 px-2',
  md: 'text-sm py-1.5 px-3',
  lg: 'text-base py-2 px-4',
  xl: 'text-lg py-2.5 px-5'
}

const defaultColors = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
  '#EC4899', '#14B8A6', '#F97316', '#6366F1', '#84CC16'
]

const LuminarChipInput = forwardRef<HTMLDivElement, LuminarChipInputProps>(
  ({ 
    value = [],
    onChange,
    placeholder = "Type and press Enter",
    maxChips,
    allowDuplicates = false,
    validateChip,
    suggestions = [],
    delimiter = /[,\n]/,
    glass = true,
    animated = true,
    disabled = false,
    readOnly = false,
    variant = defaultComponentProps.variant,
    size = defaultComponentProps.size,
    colorScheme = 'auto',
    onChipClick,
    renderChip,
    className,
    ...props 
  }, ref) => {
    const [inputValue, setInputValue] = useState("")
    const [showSuggestions, setShowSuggestions] = useState(false)
    const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1)
    const [error, setError] = useState<string | null>(null)
    
    const inputRef = useRef<HTMLInputElement>(null)
    const containerRef = useRef<HTMLDivElement>(null)

    const getChipColor = useCallback((index: number): string => {
      if (colorScheme === 'auto') {
        return defaultColors[index % defaultColors.length]
      } else if (typeof colorScheme === 'string') {
        return colorScheme
      } else if (Array.isArray(colorScheme)) {
        return colorScheme[index % colorScheme.length]
      }
      return '#3B82F6'
    }, [colorScheme])

    const filteredSuggestions = suggestions.filter(suggestion =>
      suggestion.toLowerCase().includes(inputValue.toLowerCase()) &&
      !value.some(chip => chip.label === suggestion)
    )

    const createChip = useCallback((label: string): Chip | null => {
      const trimmedLabel = label.trim()
      
      if (!trimmedLabel) return null

      // Check for duplicates
      if (!allowDuplicates && value.some(chip => chip.label === trimmedLabel)) {
        setError("Duplicate chip")
        return null
      }

      // Validate chip
      if (validateChip) {
        const validationResult = validateChip(trimmedLabel)
        if (validationResult !== true) {
          setError(typeof validationResult === 'string' ? validationResult : "Invalid chip")
          return null
        }
      }

      // Check max chips
      if (maxChips && value.length >= maxChips) {
        setError(`Maximum ${maxChips} chips allowed`)
        return null
      }

      return {
        id: `chip-${Date.now()}-${Math.random()}`,
        label: trimmedLabel,
        removable: true
      }
    }, [value, allowDuplicates, validateChip, maxChips])

    const addChip = useCallback((label: string) => {
      const newChip = createChip(label)
      if (newChip) {
        onChange?.([...value, newChip])
        setInputValue("")
        setError(null)
        setShowSuggestions(false)
      }
    }, [value, onChange, createChip])

    const removeChip = useCallback((index: number) => {
      const chip = value[index]
      if (!chip.removable && chip.removable !== undefined) return
      
      const newChips = value.filter((_, i) => i !== index)
      onChange?.(newChips)
    }, [value, onChange])

    const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value
      setInputValue(newValue)
      setError(null)
      
      // Check for delimiter
      const delimiterRegex = typeof delimiter === 'string' 
        ? new RegExp(delimiter.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))
        : delimiter

      if (delimiterRegex.test(newValue)) {
        const parts = newValue.split(delimiterRegex).filter(Boolean)
        parts.forEach(part => addChip(part))
      } else {
        setShowSuggestions(suggestions.length > 0 && newValue.length > 0)
      }
    }, [delimiter, suggestions, addChip])

    const handleKeyDown = useCallback((e: KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter') {
        e.preventDefault()
        
        if (selectedSuggestionIndex >= 0 && selectedSuggestionIndex < filteredSuggestions.length) {
          addChip(filteredSuggestions[selectedSuggestionIndex])
          setSelectedSuggestionIndex(-1)
        } else if (inputValue) {
          addChip(inputValue)
        }
      } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
        removeChip(value.length - 1)
      } else if (e.key === 'ArrowDown') {
        e.preventDefault()
        setSelectedSuggestionIndex(prev => 
          prev < filteredSuggestions.length - 1 ? prev + 1 : 0
        )
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : filteredSuggestions.length - 1
        )
      } else if (e.key === 'Escape') {
        setShowSuggestions(false)
        setSelectedSuggestionIndex(-1)
      }
    }, [inputValue, value, filteredSuggestions, selectedSuggestionIndex, addChip, removeChip])

    const handlePaste = useCallback((e: React.ClipboardEvent) => {
      e.preventDefault()
      const pastedText = e.clipboardData.getData('text')
      
      const delimiterRegex = typeof delimiter === 'string' 
        ? new RegExp(delimiter.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
        : delimiter

      const parts = pastedText.split(delimiterRegex).filter(Boolean)
      parts.forEach(part => addChip(part))
    }, [delimiter, addChip])

    const containerClasses = cn(
      "relative flex flex-wrap items-center gap-2 p-2 rounded-lg transition-all min-h-[42px]",
      variant === 'default' && glass && "backdrop-blur-sm bg-white/5 border border-white/20",
      variant === 'outlined' && "border-2 border-gray-300 dark:border-gray-700",
      variant === 'filled' && "bg-gray-100 dark:bg-gray-800",
      disabled && "opacity-50 cursor-not-allowed",
      "focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2",
      className
    )

    return (
      <div ref={ref} {...props}>
        <div
          ref={containerRef}
          className={containerClasses}
          onClick={() => !disabled && !readOnly && inputRef.current?.focus()}
        >
          {/* Chips */}
          <AnimatePresence>
            {value.map((chip, index) => (
              <motion.div
                key={chip.id}
                initial={animated ? { scale: 0, opacity: 0 } : {}}
                animate={animated ? { scale: 1, opacity: 1 } : {}}
                exit={animated ? { scale: 0, opacity: 0 } : {}}
                transition={{ duration: 0.2 }}
              >
                {renderChip ? (
                  renderChip(chip, index)
                ) : (
                  <div
                    className={cn(
                      "inline-flex items-center gap-1 rounded-full font-medium transition-all",
                      sizeClasses[size],
                      chip.removable !== false && "pr-1",
                      onChipClick && "cursor-pointer hover:opacity-80"
                    )}
                    style={{ 
                      backgroundColor: chip.color || getChipColor(index),
                      color: 'white'
                    }}
                    onClick={() => onChipClick?.(chip, index)}
                  >
                    {chip.icon && React.createElement(chip.icon, { className: "w-4 h-4" })}
                    <span>{chip.label}</span>
                    {chip.removable !== false && !disabled && !readOnly && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          removeChip(index)
                        }}
                        className="ml-1 rounded-full p-0.5 hover:bg-white/20 transition-colors"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    )}
                  </div>
                )}
              </motion.div>
            ))}
          </AnimatePresence>

          {/* Input */}
          {!disabled && !readOnly && (!maxChips || value.length < maxChips) && (
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onPaste={handlePaste}
              onFocus={() => setShowSuggestions(suggestions.length > 0 && inputValue.length > 0)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              placeholder={value.length === 0 ? placeholder : ""}
              className={cn(
                "flex-1 min-w-[120px] bg-transparent outline-none",
                sizeClasses[size]
              )}
            />
          )}
        </div>

        {/* Error message */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -5 }}
              className="text-red-500 text-sm mt-1"
            >
              {error}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Suggestions */}
        <AnimatePresence>
          {showSuggestions && filteredSuggestions.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className={cn(
                "absolute z-50 mt-2 w-full rounded-lg shadow-lg overflow-hidden",
                glass && "backdrop-blur-md bg-white/5 border border-white/20",
                !glass && "bg-gray-900 border border-gray-800"
              )}
            >
              {filteredSuggestions.map((suggestion, index) => (
                <div
                  key={suggestion}
                  className={cn(
                    "px-3 py-2 cursor-pointer transition-colors",
                    index === selectedSuggestionIndex && "bg-white/10",
                    "hover:bg-white/5"
                  )}
                  onClick={() => {
                    addChip(suggestion)
                    setSelectedSuggestionIndex(-1)
                  }}
                >
                  {suggestion}
                </div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    )
  }
)
LuminarChipInput.displayName = "LuminarChipInput"

// Export a simple Chip component as well
export const LuminarChip = forwardRef<
  HTMLDivElement,
  Omit<HTMLAttributes<HTMLDivElement>, 'onDrag' | 'onDragStart' | 'onDragEnd' | 'onDragEnter' | 'onDragLeave' | 'onDragOver' | 'onDrop' | 'onAnimationStart' | 'onAnimationEnd' | 'onAnimationIteration'> & {
    label: string
    onRemove?: () => void
    icon?: React.ElementType
    color?: string
    size?: ComponentSize
    variant?: ComponentVariant | 'outlined'
  }
>(({ label, onRemove, icon: Icon, color = '#3B82F6', size = defaultComponentProps.size || 'md', variant = 'filled', className, ...props }, ref) => {
  return (
    <motion.div
      ref={ref}
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      exit={{ scale: 0 }}
      className={cn(
        "inline-flex items-center gap-1 rounded-full font-medium transition-all",
        sizeClasses[size],
        variant === 'filled' && "text-white",
        variant === 'outlined' && "border-2",
        onRemove && "pr-1",
        className
      )}
      style={{
        backgroundColor: variant === 'filled' ? color : 'transparent',
        borderColor: variant === 'outlined' ? color : undefined,
        color: variant === 'outlined' ? color : undefined
      }}
      {...props}
    >
      {Icon && React.createElement(Icon, { className: "w-4 h-4" })}
      <span>{label}</span>
      {onRemove && (
        <button
          onClick={onRemove}
          className="ml-1 rounded-full p-0.5 hover:bg-white/20 transition-colors"
        >
          <X className="w-3 h-3" />
        </button>
      )}
    </motion.div>
  )
})
LuminarChip.displayName = "LuminarChip"

export { LuminarChipInput }