import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('compliance_reports')
export class ComplianceReport {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  reportType: string;

  @Column({
    type: 'enum',
    enum: ['daily', 'weekly', 'monthly', 'quarterly'],
    default: 'monthly',
  })
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';

  @Column({
    type: 'enum',
    enum: ['pdf', 'excel', 'json'],
    default: 'pdf',
  })
  format: 'pdf' | 'excel' | 'json';

  @Column({
    type: 'enum',
    enum: ['generating', 'completed', 'failed'],
    default: 'generating',
  })
  @Index()
  status: 'generating' | 'completed' | 'failed';

  @Column({ type: 'jsonb', nullable: true })
  data?: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  config?: Record<string, any>;

  @Column({ type: 'text', nullable: true })
  errorMessage?: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  filePath?: string;

  @Column({ type: 'int', nullable: true })
  fileSizeBytes?: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Index()
  generatedBy?: string;

  @CreateDateColumn()
  @Index()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  completedAt?: Date;

  @Column({ type: 'int', default: 0 })
  downloadCount: number;

  @Column({ type: 'timestamp', nullable: true })
  lastDownloadedAt?: Date;

  @Column({ type: 'boolean', default: false })
  isArchived: boolean;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt?: Date;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;
}