import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Cron } from '@nestjs/schedule';

import { SecurityEvent, SecurityEventType, SecurityThreatLevel, SecurityEventStatus } from '../entities/security-event.entity';
import { AuditLog, AuditEventType, AuditSeverity } from '../entities/audit-log.entity';
import { AuditLogService } from './audit-log.service';

export interface SecurityAuditReport {
  auditDate: Date;
  timeRange: {
    start: Date;
    end: Date;
  };
  summary: {
    totalEvents: number;
    criticalEvents: number;
    highThreatEvents: number;
    resolvedEvents: number;
    activeThreats: number;
    averageResponseTime: number;
    topThreatTypes: Array<{ type: SecurityEventType; count: number }>;
    topAttackSources: Array<{ source: string; count: number }>;
  };
  trends: {
    eventTrend: Array<{ date: string; count: number }>;
    threatLevelTrend: Array<{ date: string; critical: number; high: number; medium: number; low: number }>;
  };
  recommendations: string[];
  details: {
    activeThreats: SecurityEvent[];
    recentIncidents: SecurityEvent[];
    failedLogins: number;
    suspiciousActivities: number;
    blockedRequests: number;
  };
}

export interface ThreatIntelligence {
  ipAddress: string;
  threatLevel: SecurityThreatLevel;
  eventCount: number;
  firstSeen: Date;
  lastSeen: Date;
  eventTypes: SecurityEventType[];
  isBlocked: boolean;
  country?: string;
  organization?: string;
  reputation?: number;
}

@Injectable()
export class SecurityAuditService {
  private readonly logger = new Logger(SecurityAuditService.name);
  private readonly threatIntelligenceCache = new Map<string, ThreatIntelligence>();

  constructor(
    @InjectRepository(SecurityEvent)
    private readonly securityEventRepository: Repository<SecurityEvent>,
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    private readonly auditLogService: AuditLogService,
    private readonly configService: ConfigService,
  ) {}

  async generateSecurityAuditReport(startDate: Date, endDate: Date): Promise<SecurityAuditReport> {
    this.logger.log(`Generating security audit report for ${startDate} to ${endDate}`);

    const events = await this.securityEventRepository.find({
      where: {
        timestamp: Between(startDate, endDate),
      },
      order: {
        timestamp: 'DESC',
      },
    });

    const report: SecurityAuditReport = {
      auditDate: new Date(),
      timeRange: { start: startDate, end: endDate },
      summary: await this.generateSummary(events),
      trends: await this.generateTrends(events, startDate, endDate),
      recommendations: await this.generateRecommendations(events),
      details: await this.generateDetails(events),
    };

    // Log the report generation
    await this.auditLogService.logSecurity(
      AuditEventType.SECURITY_SCAN,
      'Security audit report generated',
      { reportPeriod: { start: startDate, end: endDate }, eventCount: events.length },
      AuditSeverity.LOW
    );

    return report;
  }

  private async generateSummary(events: SecurityEvent[]): Promise<SecurityAuditReport['summary']> {
    const criticalEvents = events.filter(e => e.threatLevel === SecurityThreatLevel.CRITICAL);
    const highThreatEvents = events.filter(e => e.threatLevel === SecurityThreatLevel.HIGH);
    const resolvedEvents = events.filter(e => e.isResolved());
    const activeThreats = events.filter(e => e.isActive());

    // Calculate average response time
    const resolvedEventsWithTime = resolvedEvents.filter(e => e.resolvedAt);
    const averageResponseTime = resolvedEventsWithTime.length > 0
      ? resolvedEventsWithTime.reduce((sum, event) => {
          return sum + (event.resolvedAt!.getTime() - event.timestamp.getTime());
        }, 0) / resolvedEventsWithTime.length
      : 0;

    // Top threat types
    const threatTypeCounts: Record<SecurityEventType, number> = {} as any;
    events.forEach(event => {
      threatTypeCounts[event.eventType] = (threatTypeCounts[event.eventType] || 0) + 1;
    });

    const topThreatTypes = Object.entries(threatTypeCounts)
      .map(([type, count]) => ({ type: type as SecurityEventType, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Top attack sources
    const sourceCounts: Record<string, number> = {};
    events.forEach(event => {
      if (event.ipAddress) {
        sourceCounts[event.ipAddress] = (sourceCounts[event.ipAddress] || 0) + 1;
      }
    });

    const topAttackSources = Object.entries(sourceCounts)
      .map(([source, count]) => ({ source, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalEvents: events.length,
      criticalEvents: criticalEvents.length,
      highThreatEvents: highThreatEvents.length,
      resolvedEvents: resolvedEvents.length,
      activeThreats: activeThreats.length,
      averageResponseTime: Math.round(averageResponseTime / 1000 / 60), // Convert to minutes
      topThreatTypes,
      topAttackSources,
    };
  }

  private async generateTrends(events: SecurityEvent[], startDate: Date, endDate: Date): Promise<SecurityAuditReport['trends']> {
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const eventTrend: Array<{ date: string; count: number }> = [];
    const threatLevelTrend: Array<{ date: string; critical: number; high: number; medium: number; low: number }> = [];

    for (let i = 0; i < daysDiff; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];

      const dayEvents = events.filter(event => {
        const eventDate = new Date(event.timestamp);
        return eventDate.toISOString().split('T')[0] === dateStr;
      });

      eventTrend.push({
        date: dateStr,
        count: dayEvents.length,
      });

      threatLevelTrend.push({
        date: dateStr,
        critical: dayEvents.filter(e => e.threatLevel === SecurityThreatLevel.CRITICAL).length,
        high: dayEvents.filter(e => e.threatLevel === SecurityThreatLevel.HIGH).length,
        medium: dayEvents.filter(e => e.threatLevel === SecurityThreatLevel.MEDIUM).length,
        low: dayEvents.filter(e => e.threatLevel === SecurityThreatLevel.LOW).length,
      });
    }

    return { eventTrend, threatLevelTrend };
  }

  private async generateRecommendations(events: SecurityEvent[]): Promise<string[]> {
    const recommendations: string[] = [];
    const activeThreats = events.filter(e => e.isActive());
    const criticalEvents = events.filter(e => e.threatLevel === SecurityThreatLevel.CRITICAL);
    const recurringEvents = events.filter(e => e.isRecurring());

    if (activeThreats.length > 0) {
      recommendations.push(`${activeThreats.length} active security threats require immediate attention`);
    }

    if (criticalEvents.length > 0) {
      recommendations.push(`${criticalEvents.length} critical security events need investigation`);
    }

    if (recurringEvents.length > 0) {
      recommendations.push(`${recurringEvents.length} recurring security events suggest persistent threats`);
    }

    // Check for brute force patterns
    const bruteForceEvents = events.filter(e => e.eventType === SecurityEventType.BRUTE_FORCE_ATTACK);
    if (bruteForceEvents.length > 10) {
      recommendations.push('High number of brute force attacks detected - consider implementing rate limiting');
    }

    // Check for injection attempts
    const injectionEvents = events.filter(e => 
      e.eventType === SecurityEventType.SQL_INJECTION_ATTEMPT ||
      e.eventType === SecurityEventType.XSS_ATTEMPT
    );
    if (injectionEvents.length > 5) {
      recommendations.push('Multiple injection attempts detected - review input validation and sanitization');
    }

    // Check for privilege escalation
    const privilegeEscalationEvents = events.filter(e => e.eventType === SecurityEventType.PRIVILEGE_ESCALATION);
    if (privilegeEscalationEvents.length > 0) {
      recommendations.push('Privilege escalation attempts detected - review access controls and permissions');
    }

    // Check response times
    const unresolvedEvents = events.filter(e => !e.isResolved());
    const overdueEvents = unresolvedEvents.filter(e => e.escalationRequired());
    if (overdueEvents.length > 0) {
      recommendations.push(`${overdueEvents.length} security events are overdue for resolution`);
    }

    return recommendations;
  }

  private async generateDetails(events: SecurityEvent[]): Promise<SecurityAuditReport['details']> {
    const activeThreats = events.filter(e => e.isActive()).slice(0, 10);
    const recentIncidents = events.filter(e => e.isResolved()).slice(0, 10);

    // Get additional metrics from audit logs
    const failedLogins = await this.auditLogRepository.count({
      where: {
        eventType: AuditEventType.LOGIN_FAILED,
        timestamp: Between(
          new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          new Date()
        ),
      },
    });

    const suspiciousActivities = await this.auditLogRepository.count({
      where: {
        eventType: AuditEventType.SUSPICIOUS_ACTIVITY,
        timestamp: Between(
          new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          new Date()
        ),
      },
    });

    const blockedRequests = events.filter(e => e.isBlocked).length;

    return {
      activeThreats,
      recentIncidents,
      failedLogins,
      suspiciousActivities,
      blockedRequests,
    };
  }

  async analyzeSecurityEvent(event: SecurityEvent): Promise<void> {
    this.logger.log(`Analyzing security event: ${event.eventType} from ${event.ipAddress}`);

    // Update threat intelligence
    if (event.ipAddress) {
      await this.updateThreatIntelligence(event);
    }

    // Check for patterns
    await this.checkForSecurityPatterns(event);

    // Auto-respond if configured
    if (event.shouldAutoRemediate()) {
      await this.autoRemediateSecurityEvent(event);
    }

    // Escalate if required
    if (event.escalationRequired()) {
      await this.escalateSecurityEvent(event);
    }
  }

  private async updateThreatIntelligence(event: SecurityEvent): Promise<void> {
    if (!event.ipAddress) return;

    let intelligence = this.threatIntelligenceCache.get(event.ipAddress);
    
    if (!intelligence) {
      intelligence = {
        ipAddress: event.ipAddress,
        threatLevel: event.threatLevel,
        eventCount: 0,
        firstSeen: event.timestamp,
        lastSeen: event.timestamp,
        eventTypes: [],
        isBlocked: false,
      };
    }

    intelligence.eventCount++;
    intelligence.lastSeen = event.timestamp;
    
    if (!intelligence.eventTypes.includes(event.eventType)) {
      intelligence.eventTypes.push(event.eventType);
    }

    // Update threat level to highest observed
    if (this.getThreatLevelWeight(event.threatLevel) > this.getThreatLevelWeight(intelligence.threatLevel)) {
      intelligence.threatLevel = event.threatLevel;
    }

    // Auto-block if threshold exceeded
    if (intelligence.eventCount >= 10 && intelligence.threatLevel === SecurityThreatLevel.HIGH) {
      intelligence.isBlocked = true;
      await this.blockThreatSource(event.ipAddress);
    }

    this.threatIntelligenceCache.set(event.ipAddress, intelligence);
  }

  private getThreatLevelWeight(level: SecurityThreatLevel): number {
    switch (level) {
      case SecurityThreatLevel.LOW: return 1;
      case SecurityThreatLevel.MEDIUM: return 2;
      case SecurityThreatLevel.HIGH: return 3;
      case SecurityThreatLevel.CRITICAL: return 4;
      default: return 0;
    }
  }

  private async checkForSecurityPatterns(event: SecurityEvent): Promise<void> {
    const recentEvents = await this.securityEventRepository.find({
      where: {
        eventType: event.eventType,
        ipAddress: event.ipAddress,
        timestamp: Between(
          new Date(Date.now() - 60 * 60 * 1000), // Last hour
          new Date()
        ),
      },
      order: { timestamp: 'DESC' },
    });

    // Check for brute force pattern
    if (recentEvents.length >= 5) {
      await this.auditLogService.logSecurity(
        AuditEventType.SUSPICIOUS_ACTIVITY,
        `Potential brute force attack detected from ${event.ipAddress}`,
        { eventCount: recentEvents.length, eventType: event.eventType },
        AuditSeverity.HIGH
      );
    }

    // Check for distributed attack pattern
    const sameTypeEvents = await this.securityEventRepository.find({
      where: {
        eventType: event.eventType,
        timestamp: Between(
          new Date(Date.now() - 30 * 60 * 1000), // Last 30 minutes
          new Date()
        ),
      },
    });

    const uniqueIPs = new Set(sameTypeEvents.map(e => e.ipAddress).filter(ip => ip));
    if (uniqueIPs.size >= 10) {
      await this.auditLogService.logSecurity(
        AuditEventType.SUSPICIOUS_ACTIVITY,
        `Potential distributed attack detected: ${event.eventType}`,
        { uniqueIPs: uniqueIPs.size, eventCount: sameTypeEvents.length },
        AuditSeverity.CRITICAL
      );
    }
  }

  private async autoRemediateSecurityEvent(event: SecurityEvent): Promise<void> {
    this.logger.log(`Auto-remediating security event: ${event.id}`);

    try {
      switch (event.eventType) {
        case SecurityEventType.BRUTE_FORCE_ATTACK:
          if (event.ipAddress) {
            await this.blockThreatSource(event.ipAddress);
          }
          break;
        case SecurityEventType.RATE_LIMIT_EXCEEDED:
          // Rate limiting is typically handled by middleware
          break;
        case SecurityEventType.SUSPICIOUS_FILE_UPLOAD:
          await this.quarantineFile(event.details?.fileName);
          break;
        default:
          this.logger.warn(`No auto-remediation available for event type: ${event.eventType}`);
      }

      // Update event with remediation details
      await this.securityEventRepository.update(event.id, {
        status: SecurityEventStatus.MITIGATED,
        mitigationActions: `Auto-remediated by security audit service`,
        responseDetails: { autoRemediated: true, timestamp: new Date() },
      });

      await this.auditLogService.logSecurity(
        AuditEventType.SECURITY_VIOLATION,
        `Auto-remediated security event: ${event.eventType}`,
        { eventId: event.id, eventType: event.eventType },
        AuditSeverity.MEDIUM
      );
    } catch (error) {
      this.logger.error(`Failed to auto-remediate security event ${event.id}:`, error);
    }
  }

  private async escalateSecurityEvent(event: SecurityEvent): Promise<void> {
    this.logger.warn(`Escalating security event: ${event.id}`);

    await this.auditLogService.logSecurity(
      AuditEventType.SECURITY_VIOLATION,
      `Security event escalated: ${event.eventType}`,
      { 
        eventId: event.id, 
        eventType: event.eventType,
        threatLevel: event.threatLevel,
        ageInMinutes: event.getAgeInMinutes()
      },
      AuditSeverity.HIGH
    );

    // Update event status
    await this.securityEventRepository.update(event.id, {
      status: SecurityEventStatus.INVESTIGATING,
      requiresInvestigation: true,
    });

    // Here you would typically send notifications to security team
    // await this.notificationService.sendSecurityAlert(event);
  }

  private async blockThreatSource(ipAddress: string): Promise<void> {
    this.logger.warn(`Blocking threat source: ${ipAddress}`);

    // This would typically integrate with your firewall or security system
    // For now, we'll just log the action
    await this.auditLogService.logSecurity(
      AuditEventType.SECURITY_VIOLATION,
      `Blocked threat source: ${ipAddress}`,
      { ipAddress, action: 'blocked' },
      AuditSeverity.HIGH
    );
  }

  private async quarantineFile(fileName?: string): Promise<void> {
    if (!fileName) return;

    this.logger.warn(`Quarantining suspicious file: ${fileName}`);

    await this.auditLogService.logSecurity(
      AuditEventType.SECURITY_VIOLATION,
      `Quarantined suspicious file: ${fileName}`,
      { fileName, action: 'quarantined' },
      AuditSeverity.HIGH
    );
  }

  async getThreatIntelligence(ipAddress: string): Promise<ThreatIntelligence | null> {
    return this.threatIntelligenceCache.get(ipAddress) || null;
  }

  async getAllThreatIntelligence(): Promise<ThreatIntelligence[]> {
    return Array.from(this.threatIntelligenceCache.values());
  }

  @Cron('0 0 * * *') // Daily at midnight
  async performDailySecurityAudit(): Promise<void> {
    this.logger.log('Performing daily security audit');

    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    try {
      const report = await this.generateSecurityAuditReport(yesterday, today);
      
      // Store the report or send to security team
      await this.auditLogService.logSecurity(
        AuditEventType.SECURITY_SCAN,
        'Daily security audit completed',
        { 
          reportSummary: report.summary,
          activeThreats: report.summary.activeThreats,
          criticalEvents: report.summary.criticalEvents
        },
        AuditSeverity.LOW
      );

      // Alert on critical findings
      if (report.summary.criticalEvents > 0 || report.summary.activeThreats > 0) {
        await this.auditLogService.logSecurity(
          AuditEventType.SECURITY_VIOLATION,
          'Daily security audit found critical issues',
          { 
            criticalEvents: report.summary.criticalEvents,
            activeThreats: report.summary.activeThreats,
            recommendations: report.recommendations
          },
          AuditSeverity.CRITICAL
        );
      }
    } catch (error) {
      this.logger.error('Daily security audit failed:', error);
      await this.auditLogService.logSecurity(
        AuditEventType.SYSTEM_ERROR,
        'Daily security audit failed',
        { error: error.message },
        AuditSeverity.HIGH
      );
    }
  }

  @Cron('0 */6 * * *') // Every 6 hours
  async cleanupThreatIntelligenceCache(): Promise<void> {
    this.logger.log('Cleaning up threat intelligence cache');

    const cutoffTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago

    for (const [ipAddress, intelligence] of this.threatIntelligenceCache.entries()) {
      if (intelligence.lastSeen < cutoffTime) {
        this.threatIntelligenceCache.delete(ipAddress);
      }
    }

    this.logger.log(`Threat intelligence cache cleaned up. Current size: ${this.threatIntelligenceCache.size}`);
  }
}