import React from 'react';
import { A as AuthConfig } from './types--xQR_mKr.js';

interface ProtectedRouteProps {
    children: React.ReactNode;
    roles?: string[];
    permissions?: string[];
    redirectTo?: string;
    fallback?: React.ReactNode;
}
declare const ProtectedRoute: React.FC<ProtectedRouteProps>;

interface PermissionGateProps {
    children: React.ReactNode;
    roles?: string[];
    permissions?: string[];
    requireAll?: boolean;
    fallback?: React.ReactNode;
}
declare const PermissionGate: React.FC<PermissionGateProps>;

interface UnifiedLoginPageProps {
    appName: string;
    appLogo?: string;
    appDescription?: string;
    showRegisterLink?: boolean;
    registerUrl?: string;
    forgotPasswordUrl?: string;
    customStyles?: {
        primaryColor?: string;
        backgroundColor?: string;
        cardBackground?: string;
    };
    onLoginSuccess?: () => void;
}
declare const UnifiedLoginPage: React.FC<UnifiedLoginPageProps>;

interface AppLink {
    id: string;
    name: string;
    url: string;
    icon?: React.ReactNode;
    description?: string;
    roles?: string[];
    permissions?: string[];
}
interface CrossAppNavigationProps {
    currentApp: string;
    apps: AppLink[];
    position?: 'top' | 'left' | 'right';
    theme?: 'light' | 'dark';
    onAppSwitch?: (app: AppLink) => void;
}
declare const CrossAppNavigation: React.FC<CrossAppNavigationProps>;

interface OAuthButtonsProps {
    providers?: ('google' | 'github' | 'microsoft')[];
    authConfig: AuthConfig;
    className?: string;
    buttonClassName?: string;
    showDivider?: boolean;
    dividerText?: string;
}
declare const OAuthButtons: React.FC<OAuthButtonsProps>;

export { CrossAppNavigation, OAuthButtons, PermissionGate, ProtectedRoute, UnifiedLoginPage };
