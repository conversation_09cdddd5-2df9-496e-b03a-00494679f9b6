{"extends": "./tsconfig.base.json", "compilerOptions": {"composite": true, "incremental": true}, "include": [], "references": [{"path": "./packages/shared-ui"}, {"path": "./packages/shared-core"}, {"path": "./packages/shared-auth"}, {"path": "./packages/shared-config"}, {"path": "./packages/feature-flags"}, {"path": "./packages/runtime-config"}, {"path": "./packages/debug-utils"}, {"path": "./packages/module-federation"}, {"path": "./packages/testing"}, {"path": "./apps/shell"}]}