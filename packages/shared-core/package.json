{"name": "@luminar/shared-core", "version": "1.0.0", "description": "Core utilities, types, and constants for Luminar applications", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.js"}, "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.mjs", "require": "./dist/types.js"}, "./constants": {"types": "./dist/constants.d.ts", "import": "./dist/constants.mjs", "require": "./dist/constants.js"}}, "files": ["dist", "README.md"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"axios": "^1.10.0", "clsx": "^2.1.1", "tailwind-merge": "^3.2.0", "date-fns": "^3.6.0", "zod": "^3.25.71"}, "devDependencies": {"@types/node": "^22.5.4", "eslint": "^9.15.0", "tsup": "^8.0.0", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "peerDependencies": {"react": ">=18.0.0"}, "publishConfig": {"access": "restricted"}}