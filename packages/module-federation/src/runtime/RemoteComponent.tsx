import React, { Suspense, lazy, useEffect, useState } from 'react';
import type { RemoteComponentProps } from '../types';
import { loadRemoteModule } from '../utils';
import { ErrorBoundary } from './ErrorBoundary';

/**
 * Component for dynamically loading remote modules
 */
export function RemoteComponent<T = any>({
  remote,
  module = './App',
  fallback = <div>Loading remote component...</div>,
  onError,
  props,
}: RemoteComponentProps<T>) {
  const [Component, setComponent] = useState<React.ComponentType<T> | null>(null);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let mounted = true;

    const loadComponent = async () => {
      try {
        const loadedModule = await loadRemoteModule<{ default: React.ComponentType<T> }>(
          remote,
          module
        );
        
        if (mounted) {
          setComponent(() => loadedModule.default || loadedModule);
          setError(null);
        }
      } catch (err) {
        const error = err as Error;
        console.error(`Failed to load remote component ${remote}${module}:`, error);
        if (mounted) {
          setError(error);
          onError?.(error);
        }
      }
    };

    loadComponent();

    return () => {
      mounted = false;
    };
  }, [remote, module, onError]);

  if (error) {
    return (
      <div className="p-4 border border-red-300 rounded-md bg-red-50">
        <h3 className="text-lg font-semibold text-red-800">Failed to load component</h3>
        <p className="mt-2 text-red-600">{error.message}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Reload Page
        </button>
      </div>
    );
  }

  if (!Component) {
    return <>{fallback}</>;
  }

  return (
    <ErrorBoundary
      fallback={({ error, retry }) => (
        <div className="p-4 border border-red-300 rounded-md bg-red-50">
          <h3 className="text-lg font-semibold text-red-800">Component Error</h3>
          <p className="mt-2 text-red-600">{error.message}</p>
          <button
            onClick={retry}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      )}
    >
      <Suspense fallback={fallback}>
        <Component {...(props || {} as any)} />
      </Suspense>
    </ErrorBoundary>
  );
}

/**
 * Lazy load a remote component
 */
export function createRemoteComponent<T = any>(
  remote: string,
  module: string = './App'
): React.ComponentType<T> {
  return lazy(async () => {
    try {
      const loadedModule = await loadRemoteModule<{ default: React.ComponentType<T> }>(
        remote,
        module
      );
      return { default: loadedModule.default || loadedModule };
    } catch (error) {
      console.error(`Failed to create remote component ${remote}${module}:`, error);
      throw error;
    }
  });
}