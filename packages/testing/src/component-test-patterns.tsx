/**
 * Component Test Patterns - Reusable testing patterns for components
 */

import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from './test-utils';
import type { ReactElement } from 'react';

// Base component test pattern
export interface ComponentTestPattern<P = any> {
  component: (props: P) => ReactElement;
  defaultProps: P;
  testCases: ComponentTestCase<P>[];
}

export interface ComponentTestCase<P = any> {
  name: string;
  props?: Partial<P>;
  setup?: () => void | Promise<void>;
  test: (props: P) => void | Promise<void>;
  cleanup?: () => void | Promise<void>;
}

// Generic component tester
export function createComponentTester<P extends object>(
  pattern: ComponentTestPattern<P>
) {
  return {
    // Test all cases
    testAll: () => {
      pattern.testCases.forEach(testCase => {
        it(testCase.name, async () => {
          await testCase.setup?.();
          
          const props = { ...pattern.defaultProps, ...testCase.props };
          renderWithProviders(pattern.component(props));
          
          await testCase.test(props);
          
          await testCase.cleanup?.();
        });
      });
    },

    // Test specific case
    testCase: (caseName: string) => {
      const testCase = pattern.testCases.find(tc => tc.name === caseName);
      if (!testCase) {
        throw new Error(`Test case "${caseName}" not found`);
      }

      it(testCase.name, async () => {
        await testCase.setup?.();
        
        const props = { ...pattern.defaultProps, ...testCase.props };
        renderWithProviders(pattern.component(props));
        
        await testCase.test(props);
        
        await testCase.cleanup?.();
      });
    },

    // Add custom test case
    addTestCase: (testCase: ComponentTestCase<P>) => {
      pattern.testCases.push(testCase);
    }
  };
}

// Common test patterns
export const commonTestPatterns = {
  // Button component pattern
  button: {
    rendering: (getButton: () => HTMLElement) => ({
      name: 'renders correctly',
      test: () => {
        const button = getButton();
        expect(button).toBeInTheDocument();
        expect(button).toBeVisible();
      }
    }),

    clickHandler: (getButton: () => HTMLElement, onClick: () => void) => ({
      name: 'handles click events',
      test: async () => {
        const button = getButton();
        const user = userEvent.setup();
        
        await user.click(button);
        expect(onClick).toHaveBeenCalledTimes(1);
      }
    }),

    disabled: (getButton: () => HTMLElement) => ({
      name: 'handles disabled state',
      props: { disabled: true },
      test: () => {
        const button = getButton();
        expect(button).toBeDisabled();
      }
    }),

    loading: (getButton: () => HTMLElement) => ({
      name: 'shows loading state',
      props: { loading: true },
      test: () => {
        const button = getButton();
        expect(button).toHaveAttribute('aria-busy', 'true');
        expect(screen.getByRole('status')).toBeInTheDocument();
      }
    }),

    accessibility: (getButton: () => HTMLElement) => ({
      name: 'meets accessibility requirements',
      test: () => {
        const button = getButton();
        expect(button).toHaveAttribute('type');
        expect(button).not.toHaveAttribute('aria-label', '');
      }
    })
  },

  // Form input pattern
  input: {
    rendering: (getInput: () => HTMLElement) => ({
      name: 'renders correctly',
      test: () => {
        const input = getInput();
        expect(input).toBeInTheDocument();
        expect(input).toBeVisible();
      }
    }),

    valueChange: (getInput: () => HTMLElement, onChange: () => void) => ({
      name: 'handles value changes',
      test: async () => {
        const input = getInput();
        const user = userEvent.setup();
        
        await user.type(input, 'test value');
        expect(onChange).toHaveBeenCalled();
        expect(input).toHaveValue('test value');
      }
    }),

    validation: (getInput: () => HTMLElement) => ({
      name: 'shows validation errors',
      props: { error: 'This field is required' },
      test: () => {
        const input = getInput();
        expect(input).toHaveAttribute('aria-invalid', 'true');
        expect(screen.getByText('This field is required')).toBeInTheDocument();
      }
    }),

    required: (getInput: () => HTMLElement) => ({
      name: 'handles required state',
      props: { required: true },
      test: () => {
        const input = getInput();
        expect(input).toHaveAttribute('required');
        expect(input).toHaveAttribute('aria-required', 'true');
      }
    })
  },

  // Modal/Dialog pattern
  modal: {
    rendering: (isOpen: boolean) => ({
      name: 'renders when open',
      props: { isOpen },
      test: () => {
        if (isOpen) {
          expect(screen.getByRole('dialog')).toBeInTheDocument();
        } else {
          expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
        }
      }
    }),

    closeOnEscape: (onClose: () => void) => ({
      name: 'closes on escape key',
      props: { isOpen: true },
      test: async () => {
        const user = userEvent.setup();
        await user.keyboard('{Escape}');
        expect(onClose).toHaveBeenCalled();
      }
    }),

    closeOnOverlay: (onClose: () => void) => ({
      name: 'closes on overlay click',
      props: { isOpen: true },
      test: async () => {
        const user = userEvent.setup();
        const overlay = screen.getByTestId('modal-overlay');
        await user.click(overlay);
        expect(onClose).toHaveBeenCalled();
      }
    }),

    focusTrap: () => ({
      name: 'traps focus within modal',
      props: { isOpen: true },
      test: async () => {
        const modal = screen.getByRole('dialog');
        const focusableElements = modal.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        if (focusableElements.length > 0) {
          expect(focusableElements[0]).toHaveFocus();
        }
      }
    })
  },

  // List/Table pattern
  list: {
    rendering: (items: any[]) => ({
      name: 'renders list items',
      props: { items },
      test: () => {
        items.forEach((_item, index) => {
          expect(screen.getByTestId(`list-item-${index}`)).toBeInTheDocument();
        });
      }
    }),

    emptyState: () => ({
      name: 'shows empty state',
      props: { items: [] },
      test: () => {
        expect(screen.getByText(/no items/i)).toBeInTheDocument();
      }
    }),

    loading: () => ({
      name: 'shows loading state',
      props: { loading: true },
      test: () => {
        expect(screen.getByRole('status')).toBeInTheDocument();
        expect(screen.getByText(/loading/i)).toBeInTheDocument();
      }
    }),

    selection: (onSelect: () => void) => ({
      name: 'handles item selection',
      test: async () => {
        const user = userEvent.setup();
        const firstItem = screen.getByTestId('list-item-0');
        
        await user.click(firstItem);
        expect(onSelect).toHaveBeenCalled();
      }
    })
  }
};

// Accessibility test patterns
export const a11yTestPatterns = {
  // Keyboard navigation
  keyboardNavigation: (getElement: () => HTMLElement) => ({
    name: 'supports keyboard navigation',
    test: async () => {
      const element = getElement();
      const user = userEvent.setup();
      
      element.focus();
      expect(element).toHaveFocus();
      
      await user.keyboard('{Tab}');
      // Test that focus moves appropriately
    }
  }),

  // Screen reader support
  screenReader: (getElement: () => HTMLElement, expectedLabel: string) => ({
    name: 'provides screen reader support',
    test: () => {
      const element = getElement();
      expect(element).toHaveAccessibleName(expectedLabel);
    }
  }),

  // ARIA attributes
  ariaAttributes: (getElement: () => HTMLElement, requiredAttributes: string[]) => ({
    name: 'has required ARIA attributes',
    test: () => {
      const element = getElement();
      requiredAttributes.forEach(attr => {
        expect(element).toHaveAttribute(attr);
      });
    }
  }),

  // Color contrast (simplified check)
  colorContrast: (getElement: () => HTMLElement) => ({
    name: 'meets color contrast requirements',
    test: () => {
      const element = getElement();
      const styles = window.getComputedStyle(element);
      
      // This is a simplified check - in reality you'd use a proper contrast calculation
      expect(styles.color).not.toBe(styles.backgroundColor);
    }
  })
};

// Performance test patterns
export const performanceTestPatterns = {
  // Render performance
  renderPerformance: (component: ReactElement, threshold: number = 100) => ({
    name: 'renders within performance threshold',
    test: async () => {
      const start = performance.now();
      renderWithProviders(component);
      const end = performance.now();
      
      expect(end - start).toBeLessThan(threshold);
    }
  }),

  // Memory usage
  memoryUsage: (component: ReactElement) => ({
    name: 'does not leak memory',
    test: async () => {
      const { unmount } = renderWithProviders(component);
      
      // Force garbage collection if available
      if ((global as any).gc) {
        (global as any).gc();
      }
      
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      unmount();
      
      if ((global as any).gc) {
        (global as any).gc();
      }
      
      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
      const memoryDiff = finalMemory - initialMemory;
      
      // Allow for some memory variance
      expect(Math.abs(memoryDiff)).toBeLessThan(1000000); // 1MB threshold
    }
  }),

  // Re-render optimization
  rerenderOptimization: (component: ReactElement, propChanges: any[]) => ({
    name: 'optimizes re-renders',
    test: async () => {
      let renderCount = 0;
      
      const TestComponent = (_props: any) => {
        renderCount++;
        return component;
      };
      
      const { rerender } = renderWithProviders(<TestComponent />);
      
      propChanges.forEach(props => {
        rerender(<TestComponent {...props} />);
      });
      
      // Should not re-render unnecessarily
      expect(renderCount).toBeLessThanOrEqual(propChanges.length + 1);
    }
  })
};

// Integration test patterns
export const integrationTestPatterns = {
  // API integration
  apiIntegration: (apiCall: () => Promise<any>, expectedResult: any) => ({
    name: 'integrates with API correctly',
    test: async () => {
      const result = await apiCall();
      expect(result).toEqual(expectedResult);
    }
  }),

  // Error handling
  errorHandling: (apiCall: () => Promise<any>, expectedError: any) => ({
    name: 'handles API errors correctly',
    test: async () => {
      await expect(apiCall()).rejects.toEqual(expectedError);
    }
  }),

  // Loading states
  loadingStates: (component: ReactElement) => ({
    name: 'shows loading states during API calls',
    test: async () => {
      renderWithProviders(component);
      
      // Should show loading initially
      expect(screen.getByRole('status')).toBeInTheDocument();
      
      // Should hide loading after API call
      await waitFor(() => {
        expect(screen.queryByRole('status')).not.toBeInTheDocument();
      });
    }
  })
};

export default {
  createComponentTester,
  commonTestPatterns,
  a11yTestPatterns,
  performanceTestPatterns,
  integrationTestPatterns
};