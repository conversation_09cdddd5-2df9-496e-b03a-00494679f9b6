import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tsConfigPaths from 'vite-tsconfig-paths'
import { visualizer } from 'rollup-plugin-visualizer'

export default defineConfig({
  server: {
    port: 3000,
    hmr: {
      overlay: true,
    },
  },
  build: {
    lib: {
      entry: './src/index.ts',
      name: 'LuminarUI',
      fileName: (format) => `luminar-ui.${format}.js`,
      formats: ['es', 'cjs']
    },
    target: 'esnext',
    minify: 'esbuild',
    sourcemap: true,
    rollupOptions: {
      external: [
        'react',
        'react-dom',
        'react/jsx-runtime',
        '@tanstack/react-router',
        '@tanstack/react-query',
        '@tanstack/react-query-devtools',
        '@mui/material',
        '@mui/icons-material',
        'framer-motion',
        'lucide-react',
        'three',
        '@react-three/fiber',
        '@luminar/shared-core',
        '@luminar/shared-auth'
      ],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
          'react/jsx-runtime': 'react/jsx-runtime'
        }
      },
    },
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@tanstack/react-router',
      '@tanstack/react-start',
      'framer-motion',
      'lucide-react',
      'three',
      '@react-three/fiber',
    ],
  },
  plugins: [
    tsConfigPaths({
      projects: ['./tsconfig.json'],
    }),
    react(),
    visualizer({
      filename: 'dist/stats.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    }),
  ],
  define: {
    __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
  },
})
