{"name": "@luminar/feature-flags", "version": "1.0.0", "private": true, "description": "Feature flag system with A/B testing for Luminar platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "vitest", "typecheck": "tsc --noEmit"}, "dependencies": {"@luminar/runtime-config": "workspace:*", "axios": "^1.10.0", "js-cookie": "^3.0.5", "uuid": "^9.0.1", "zustand": "^5.0.6"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^22.5.4", "@types/uuid": "^9.0.8", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "peerDependencies": {"react": "^19.0.0"}}