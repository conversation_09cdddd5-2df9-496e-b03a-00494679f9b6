import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Query,
  Param,
  Body,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsEnum, IsBoolean } from 'class-validator';

import { GdprService } from '../services/gdpr.service';
import { DataRetentionService } from '../services/data-retention.service';
import { DataAnonymizationService } from '../services/data-anonymization.service';

export class CreateRetentionPolicyDto {
  @IsString()
  entityType: string;

  @IsString()
  retentionPeriodDays: number;

  @IsEnum(['hard_delete', 'soft_delete', 'anonymize'])
  deletionMethod: 'hard_delete' | 'soft_delete' | 'anonymize';

  @IsArray()
  @IsString({ each: true })
  triggers: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  exceptions?: string[];
}

export class AnonymizeDataDto {
  @IsString()
  entityType: string;

  @IsString()
  entityId: string;

  @IsArray()
  fields: Array<{
    fieldName: string;
    method: 'redact' | 'hash' | 'generalize' | 'suppress' | 'pseudonymize';
    options?: Record<string, any>;
  }>;

  @IsOptional()
  @IsBoolean()
  preserveStructure?: boolean;

  @IsOptional()
  @IsBoolean()
  retainMetadata?: boolean;
}

@ApiTags('GDPR Management')
@ApiBearerAuth()
@Controller('gdpr')
export class GdprController {
  constructor(
    private readonly gdprService: GdprService,
    private readonly dataRetentionService: DataRetentionService,
    private readonly dataAnonymizationService: DataAnonymizationService,
  ) {}

  @Get('compliance-status')
  @ApiOperation({ summary: 'Get GDPR compliance status' })
  @ApiResponse({ status: 200, description: 'Compliance status retrieved successfully' })
  async getComplianceStatus() {
    const status = await this.gdprService.getComplianceStatus();

    return {
      success: true,
      data: status,
    };
  }

  @Get('data-inventory')
  @ApiOperation({ summary: 'Get personal data inventory' })
  @ApiResponse({ status: 200, description: 'Data inventory retrieved successfully' })
  async getDataInventory(@Query('userId') userId?: string) {
    const inventory = await this.gdprService.getPersonalDataInventory(userId);

    return {
      success: true,
      data: inventory,
    };
  }

  @Get('retention-policies')
  @ApiOperation({ summary: 'Get data retention policies' })
  @ApiResponse({ status: 200, description: 'Retention policies retrieved successfully' })
  async getRetentionPolicies(@Query('entityType') entityType?: string) {
    const policies = await this.dataRetentionService.getRetentionPolicies(entityType);

    return {
      success: true,
      data: policies,
      total: policies.length,
    };
  }

  @Post('retention-policies')
  @ApiOperation({ summary: 'Create data retention policy' })
  @ApiResponse({ status: 201, description: 'Retention policy created successfully' })
  async createRetentionPolicy(@Body(ValidationPipe) policyDto: CreateRetentionPolicyDto) {
    const policy = await this.dataRetentionService.createRetentionPolicy(policyDto);

    return {
      success: true,
      data: policy,
      message: 'Retention policy created successfully',
    };
  }

  @Put('retention-policies/:id')
  @ApiOperation({ summary: 'Update data retention policy' })
  @ApiResponse({ status: 200, description: 'Retention policy updated successfully' })
  @ApiResponse({ status: 404, description: 'Retention policy not found' })
  async updateRetentionPolicy(
    @Param('id') id: string,
    @Body(ValidationPipe) updates: Partial<CreateRetentionPolicyDto>
  ) {
    const policy = await this.dataRetentionService.updateRetentionPolicy(id, updates);

    if (!policy) {
      return {
        success: false,
        message: 'Retention policy not found',
      };
    }

    return {
      success: true,
      data: policy,
      message: 'Retention policy updated successfully',
    };
  }

  @Delete('retention-policies/:id')
  @ApiOperation({ summary: 'Delete data retention policy' })
  @ApiResponse({ status: 200, description: 'Retention policy deleted successfully' })
  @ApiResponse({ status: 404, description: 'Retention policy not found' })
  async deleteRetentionPolicy(@Param('id') id: string) {
    const deleted = await this.dataRetentionService.deleteRetentionPolicy(id);

    if (!deleted) {
      return {
        success: false,
        message: 'Retention policy not found',
      };
    }

    return {
      success: true,
      message: 'Retention policy deleted successfully',
    };
  }

  @Post('retention/check')
  @ApiOperation({ summary: 'Check retention compliance for entity' })
  @ApiResponse({ status: 200, description: 'Retention compliance checked successfully' })
  async checkRetentionCompliance(
    @Body() body: { entityType: string; entityId: string }
  ) {
    const compliance = await this.dataRetentionService.checkRetentionCompliance(
      body.entityType,
      body.entityId
    );

    return {
      success: true,
      data: compliance,
    };
  }

  @Post('retention/execute')
  @ApiOperation({ summary: 'Execute data retention for entity' })
  @ApiResponse({ status: 200, description: 'Data retention executed successfully' })
  async executeDataRetention(
    @Body() body: { entityType: string; entityId: string }
  ) {
    const result = await this.dataRetentionService.executeDataRetention(
      body.entityType,
      body.entityId
    );

    return {
      success: result.success,
      data: result,
      message: result.success ? 'Data retention executed successfully' : 'Data retention failed',
    };
  }

  @Get('retention/statistics')
  @ApiOperation({ summary: 'Get data retention statistics' })
  @ApiResponse({ status: 200, description: 'Retention statistics retrieved successfully' })
  async getRetentionStatistics() {
    const statistics = await this.dataRetentionService.getRetentionStatistics();

    return {
      success: true,
      data: statistics,
    };
  }

  @Post('anonymize')
  @ApiOperation({ summary: 'Anonymize data' })
  @ApiResponse({ status: 200, description: 'Data anonymized successfully' })
  async anonymizeData(@Body(ValidationPipe) anonymizeDto: AnonymizeDataDto) {
    // For this endpoint, we need the actual data - in a real implementation,
    // you would fetch it from the database based on entityType and entityId
    const mockData = { id: anonymizeDto.entityId, name: 'Sample Data' };

    const result = await this.dataAnonymizationService.anonymizeData(
      anonymizeDto.entityType,
      anonymizeDto.entityId,
      mockData,
      anonymizeDto
    );

    return {
      success: result.success,
      data: result,
      message: result.success ? 'Data anonymized successfully' : 'Data anonymization failed',
    };
  }

  @Get('anonymization/templates')
  @ApiOperation({ summary: 'Get anonymization templates' })
  @ApiResponse({ status: 200, description: 'Anonymization templates retrieved successfully' })
  async getAnonymizationTemplates() {
    const templates = this.dataAnonymizationService.getAnonymizationTemplates();

    return {
      success: true,
      data: templates,
    };
  }

  @Post('anonymization/validate')
  @ApiOperation({ summary: 'Validate anonymization configuration' })
  @ApiResponse({ status: 200, description: 'Anonymization configuration validated' })
  async validateAnonymizationConfig(@Body() config: any) {
    const validation = this.dataAnonymizationService.validateAnonymizationConfig(config);

    return {
      success: true,
      data: validation,
    };
  }

  @Get('anonymization/report')
  @ApiOperation({ summary: 'Generate anonymization report' })
  @ApiResponse({ status: 200, description: 'Anonymization report generated successfully' })
  async generateAnonymizationReport(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    const dateRange = startDate && endDate ? {
      start: new Date(startDate),
      end: new Date(endDate),
    } : undefined;

    const report = await this.dataAnonymizationService.generateAnonymizationReport(dateRange);

    return {
      success: true,
      data: report,
    };
  }

  @Get('compliance-dashboard')
  @ApiOperation({ summary: 'Get GDPR compliance dashboard data' })
  @ApiResponse({ status: 200, description: 'Compliance dashboard data retrieved successfully' })
  async getComplianceDashboard() {
    const [
      complianceStatus,
      retentionStats,
      anonymizationReport,
    ] = await Promise.all([
      this.gdprService.getComplianceStatus(),
      this.dataRetentionService.getRetentionStatistics(),
      this.dataAnonymizationService.generateAnonymizationReport(),
    ]);

    const dashboardData = {
      overview: {
        complianceScore: complianceStatus.overallScore,
        totalPolicies: retentionStats.totalPolicies,
        activePolicies: retentionStats.activePolicies,
        recentAnonymizations: anonymizationReport.statistics.totalEntitiesProcessed,
      },
      compliance: complianceStatus,
      retention: retentionStats,
      anonymization: anonymizationReport.statistics,
      alerts: [
        ...complianceStatus.issues,
        ...(retentionStats.entitiesPendingDeletion > 100 ? ['High number of entities pending deletion'] : []),
      ],
    };

    return {
      success: true,
      data: dashboardData,
    };
  }

  @Get('rights-requests/summary')
  @ApiOperation({ summary: 'Get data subject rights requests summary' })
  @ApiResponse({ status: 200, description: 'Rights requests summary retrieved successfully' })
  async getRightsRequestsSummary(@Query('days') days?: number) {
    const summary = await this.gdprService.getDataRightsSummary(days || 30);

    return {
      success: true,
      data: summary,
    };
  }

  @Get('processing-activities')
  @ApiOperation({ summary: 'Get data processing activities' })
  @ApiResponse({ status: 200, description: 'Processing activities retrieved successfully' })
  async getProcessingActivities(@Query('limit') limit?: number) {
    const activities = await this.gdprService.getProcessingActivities(limit || 50);

    return {
      success: true,
      data: activities,
      total: activities.length,
    };
  }

  @Get('legal-basis')
  @ApiOperation({ summary: 'Get legal basis for data processing' })
  @ApiResponse({ status: 200, description: 'Legal basis information retrieved successfully' })
  async getLegalBasis(@Query('entityType') entityType?: string) {
    const legalBasis = await this.gdprService.getLegalBasisInfo(entityType);

    return {
      success: true,
      data: legalBasis,
    };
  }
}