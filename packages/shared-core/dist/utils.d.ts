import { ClassValue } from 'clsx';

/**
 * Combines class names using clsx and tailwind-merge
 * This replaces all duplicate cn() functions across apps
 */
declare function cn(...inputs: ClassValue[]): string;
/**
 * Format bytes to human readable string
 */
declare function formatBytes(bytes: number, decimals?: number): string;
/**
 * Format number with K/M suffixes
 */
declare function formatNumber(num: number): string;
/**
 * Format currency
 */
declare function formatCurrency(amount: number, currency?: string): string;
/**
 * Debounce function
 */
declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
/**
 * Throttle function
 */
declare function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void;
/**
 * Generate unique ID
 */
declare function generateId(prefix?: string): string;
/**
 * Sleep utility
 */
declare function sleep(ms: number): Promise<void>;
/**
 * Capitalize first letter
 */
declare function capitalize(str: string): string;
/**
 * Truncate string with ellipsis
 */
declare function truncate(str: string, length: number): string;
/**
 * Check if value is empty (null, undefined, empty string, empty array, empty object)
 */
declare function isEmpty(value: any): boolean;
/**
 * Deep clone object
 */
declare function deepClone<T>(obj: T): T;
/**
 * Get nested object property safely
 */
declare function get(obj: any, path: string, defaultValue?: any): any;
/**
 * Set nested object property
 */
declare function set(obj: any, path: string, value: any): void;
/**
 * Omit properties from object
 */
declare function omit<T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Omit<T, K>;
/**
 * Pick properties from object
 */
declare function pick<T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Pick<T, K>;

export { capitalize, cn, debounce, deepClone, formatBytes, formatCurrency, formatNumber, generateId, get, isEmpty, omit, pick, set, sleep, throttle, truncate };
