import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor, within } from '@testing-library/react'
import { render } from '../../test/test-utils'
import { ReportBuilder } from './ReportBuilder'

describe('ReportBuilder', () => {
  const mockSubmissions = [
    {
      id: 'sub-1',
      type: 'achievement',
      title: 'Completed Project X',
      description: 'Successfully delivered Project X ahead of schedule',
      submittedBy: '<PERSON>',
      submittedAt: '2024-01-15T10:00:00Z',
      category: 'project',
      impact: 'high',
      metrics: {
        timesSaved: 40,
        costSavings: 5000,
      },
    },
    {
      id: 'sub-2',
      type: 'cost_initiative',
      title: 'Cloud Optimization',
      description: 'Reduced cloud costs by optimizing resource usage',
      submittedBy: '<PERSON>',
      submittedAt: '2024-01-14T14:00:00Z',
      category: 'cost_savings',
      impact: 'medium',
      metrics: {
        costSavings: 15000,
      },
    },
    {
      id: 'sub-3',
      type: 'recognition',
      title: 'Team Excellence Award',
      description: 'Received team excellence award for Q4 performance',
      submittedBy: '<PERSON>',
      submittedAt: '2024-01-13T09:00:00Z',
      category: 'award',
      impact: 'low',
      recognitionFor: ['teamwork', 'innovation'],
    },
  ]

  const defaultProps = {
    submissions: mockSubmissions,
    dateRange: {
      start: new Date('2024-01-08'),
      end: new Date('2024-01-14'),
    },
    onGenerate: vi.fn(),
    onExport: vi.fn(),
    isGenerating: false,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders report builder with title', () => {
    render(<ReportBuilder {...defaultProps} />)

    expect(screen.getByText('Weekly Wins Report')).toBeInTheDocument()
    expect(screen.getByText(/january 8 - january 14, 2024/i)).toBeInTheDocument()
  })

  it('displays submission summary statistics', () => {
    render(<ReportBuilder {...defaultProps} />)

    const summarySection = screen.getByTestId('report-summary')
    expect(within(summarySection).getByText('Total Submissions')).toBeInTheDocument()
    expect(within(summarySection).getByText('3')).toBeInTheDocument()
  })

  it('categorizes submissions by type', () => {
    render(<ReportBuilder {...defaultProps} />)

    expect(screen.getByText('Achievements (1)')).toBeInTheDocument()
    expect(screen.getByText('Cost Initiatives (1)')).toBeInTheDocument()
    expect(screen.getByText('Recognitions (1)')).toBeInTheDocument()
  })

  it('allows selecting/deselecting submissions', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const firstCheckbox = screen.getAllByRole('checkbox')[0]
    expect(firstCheckbox).toBeChecked()

    await user.click(firstCheckbox)
    expect(firstCheckbox).not.toBeChecked()

    await user.click(firstCheckbox)
    expect(firstCheckbox).toBeChecked()
  })

  it('provides select all functionality', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const selectAllCheckbox = screen.getByLabelText(/select all/i)
    
    // Deselect all
    await user.click(selectAllCheckbox)
    const checkboxes = screen.getAllByRole('checkbox')
    checkboxes.slice(1).forEach(checkbox => {
      expect(checkbox).not.toBeChecked()
    })

    // Select all
    await user.click(selectAllCheckbox)
    checkboxes.slice(1).forEach(checkbox => {
      expect(checkbox).toBeChecked()
    })
  })

  it('filters submissions by type', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const typeFilter = screen.getByLabelText(/filter by type/i)
    await user.selectOptions(typeFilter, 'achievement')

    expect(screen.getByText('Completed Project X')).toBeInTheDocument()
    expect(screen.queryByText('Cloud Optimization')).not.toBeInTheDocument()
    expect(screen.queryByText('Team Excellence Award')).not.toBeInTheDocument()
  })

  it('filters submissions by impact', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const impactFilter = screen.getByLabelText(/filter by impact/i)
    await user.selectOptions(impactFilter, 'high')

    expect(screen.getByText('Completed Project X')).toBeInTheDocument()
    expect(screen.queryByText('Cloud Optimization')).not.toBeInTheDocument()
  })

  it('sorts submissions by date', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const sortSelect = screen.getByLabelText(/sort by/i)
    await user.selectOptions(sortSelect, 'date_desc')

    const submissionTitles = screen.getAllByTestId('submission-title')
    expect(submissionTitles[0]).toHaveTextContent('Completed Project X')
    expect(submissionTitles[1]).toHaveTextContent('Cloud Optimization')
    expect(submissionTitles[2]).toHaveTextContent('Team Excellence Award')
  })

  it('sorts submissions by impact', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const sortSelect = screen.getByLabelText(/sort by/i)
    await user.selectOptions(sortSelect, 'impact')

    const submissionTitles = screen.getAllByTestId('submission-title')
    expect(submissionTitles[0]).toHaveTextContent('Completed Project X') // high
    expect(submissionTitles[1]).toHaveTextContent('Cloud Optimization') // medium
  })

  it('shows report preview', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const previewButton = screen.getByRole('button', { name: /preview report/i })
    await user.click(previewButton)

    expect(screen.getByTestId('report-preview')).toBeInTheDocument()
    expect(screen.getByText(/executive summary/i)).toBeInTheDocument()
  })

  it('calculates total cost savings', () => {
    render(<ReportBuilder {...defaultProps} />)

    const metricsSection = screen.getByTestId('report-metrics')
    expect(within(metricsSection).getByText('Total Cost Savings')).toBeInTheDocument()
    expect(within(metricsSection).getByText('$20,000')).toBeInTheDocument()
  })

  it('calculates total time saved', () => {
    render(<ReportBuilder {...defaultProps} />)

    const metricsSection = screen.getByTestId('report-metrics')
    expect(within(metricsSection).getByText('Total Hours Saved')).toBeInTheDocument()
    expect(within(metricsSection).getByText('40')).toBeInTheDocument()
  })

  it('generates report with selected submissions', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    // Deselect one submission
    const checkboxes = screen.getAllByRole('checkbox')
    await user.click(checkboxes[3]) // Deselect the third submission

    const generateButton = screen.getByRole('button', { name: /generate report/i })
    await user.click(generateButton)

    expect(defaultProps.onGenerate).toHaveBeenCalledWith({
      submissions: [mockSubmissions[0], mockSubmissions[1]],
      dateRange: defaultProps.dateRange,
      format: 'pdf',
      includeCharts: true,
      includeMetrics: true,
    })
  })

  it('disables generate button when no submissions selected', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const selectAllCheckbox = screen.getByLabelText(/select all/i)
    await user.click(selectAllCheckbox) // Deselect all

    const generateButton = screen.getByRole('button', { name: /generate report/i })
    expect(generateButton).toBeDisabled()
  })

  it('shows loading state while generating', () => {
    render(<ReportBuilder {...defaultProps} isGenerating={true} />)

    expect(screen.getByText(/generating report/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /generating/i })).toBeDisabled()
  })

  it('allows format selection', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const formatSelect = screen.getByLabelText(/report format/i)
    await user.selectOptions(formatSelect, 'excel')

    const generateButton = screen.getByRole('button', { name: /generate report/i })
    await user.click(generateButton)

    expect(defaultProps.onGenerate).toHaveBeenCalledWith(
      expect.objectContaining({
        format: 'excel',
      })
    )
  })

  it('toggles chart inclusion', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const chartsCheckbox = screen.getByLabelText(/include charts/i)
    await user.click(chartsCheckbox)

    const generateButton = screen.getByRole('button', { name: /generate report/i })
    await user.click(generateButton)

    expect(defaultProps.onGenerate).toHaveBeenCalledWith(
      expect.objectContaining({
        includeCharts: false,
      })
    )
  })

  it('toggles metrics inclusion', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const metricsCheckbox = screen.getByLabelText(/include detailed metrics/i)
    await user.click(metricsCheckbox)

    const generateButton = screen.getByRole('button', { name: /generate report/i })
    await user.click(generateButton)

    expect(defaultProps.onGenerate).toHaveBeenCalledWith(
      expect.objectContaining({
        includeMetrics: false,
      })
    )
  })

  it('exports report in different formats', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const exportButton = screen.getByRole('button', { name: /export/i })
    await user.click(exportButton)

    const pdfOption = screen.getByRole('menuitem', { name: /export as pdf/i })
    await user.click(pdfOption)

    expect(defaultProps.onExport).toHaveBeenCalledWith('pdf')
  })

  it('adds custom notes to report', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const notesTextarea = screen.getByLabelText(/executive notes/i)
    await user.type(notesTextarea, 'Great progress this week!')

    const generateButton = screen.getByRole('button', { name: /generate report/i })
    await user.click(generateButton)

    expect(defaultProps.onGenerate).toHaveBeenCalledWith(
      expect.objectContaining({
        executiveNotes: 'Great progress this week!',
      })
    )
  })

  it('shows submission details on hover', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const firstSubmission = screen.getByText('Completed Project X')
    await user.hover(firstSubmission)

    expect(screen.getByRole('tooltip')).toHaveTextContent('Successfully delivered Project X ahead of schedule')
  })

  it('groups submissions by submitter', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const groupBySelect = screen.getByLabelText(/group by/i)
    await user.selectOptions(groupBySelect, 'submitter')

    expect(screen.getByText('John Doe (1)')).toBeInTheDocument()
    expect(screen.getByText('Jane Smith (1)')).toBeInTheDocument()
    expect(screen.getByText('Mike Johnson (1)')).toBeInTheDocument()
  })

  it('handles empty submissions list', () => {
    render(<ReportBuilder {...defaultProps} submissions={[]} />)

    expect(screen.getByText(/no submissions found for this period/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /generate report/i })).toBeDisabled()
  })

  it('shows date range in report preview', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const previewButton = screen.getByRole('button', { name: /preview report/i })
    await user.click(previewButton)

    const preview = screen.getByTestId('report-preview')
    expect(within(preview).getByText(/week of january 8-14, 2024/i)).toBeInTheDocument()
  })

  it('includes submission contributors in report', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const previewButton = screen.getByRole('button', { name: /preview report/i })
    await user.click(previewButton)

    const preview = screen.getByTestId('report-preview')
    expect(within(preview).getByText('Contributors')).toBeInTheDocument()
    expect(within(preview).getByText('John Doe')).toBeInTheDocument()
    expect(within(preview).getByText('Jane Smith')).toBeInTheDocument()
  })

  it('saves report preferences', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const savePrefsCheckbox = screen.getByLabelText(/save preferences/i)
    await user.click(savePrefsCheckbox)

    const generateButton = screen.getByRole('button', { name: /generate report/i })
    await user.click(generateButton)

    // Preferences should be saved to localStorage
    expect(localStorage.getItem('reportPreferences')).toBeTruthy()
  })

  it('loads saved report preferences', () => {
    localStorage.setItem('reportPreferences', JSON.stringify({
      format: 'excel',
      includeCharts: false,
      includeMetrics: true,
    }))

    render(<ReportBuilder {...defaultProps} />)

    expect(screen.getByLabelText(/report format/i)).toHaveValue('excel')
    expect(screen.getByLabelText(/include charts/i)).not.toBeChecked()
    expect(screen.getByLabelText(/include detailed metrics/i)).toBeChecked()
  })

  it('allows template selection', async () => {
    const { user } = render(<ReportBuilder {...defaultProps} />)

    const templateSelect = screen.getByLabelText(/report template/i)
    await user.selectOptions(templateSelect, 'executive_summary')

    const generateButton = screen.getByRole('button', { name: /generate report/i })
    await user.click(generateButton)

    expect(defaultProps.onGenerate).toHaveBeenCalledWith(
      expect.objectContaining({
        template: 'executive_summary',
      })
    )
  })
})

