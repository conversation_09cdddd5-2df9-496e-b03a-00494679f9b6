import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

import { ConsentService } from './consent.service';
import { DataProtectionService } from './data-protection.service';
import { DataRetentionService } from './data-retention.service';
import { DataPortabilityService } from './data-portability.service';
import { DataAnonymizationService } from './data-anonymization.service';

import { ConsentRecord } from '../entities/consent-record.entity';
import { DataRequest, DataRequestType } from '../entities/data-request.entity';
import { DataProcessingLog } from '../entities/data-processing-log.entity';
import { PersonalDataInventory } from '../entities/personal-data-inventory.entity';

export interface GdprComplianceReport {
  consentCompliance: {
    totalConsents: number;
    validConsents: number;
    expiredConsents: number;
    withdrawnConsents: number;
    complianceRate: number;
  };
  dataRequests: {
    totalRequests: number;
    pendingRequests: number;
    overdueRequests: number;
    completedRequests: number;
    averageResponseTime: number;
  };
  dataRetention: {
    totalDataElements: number;
    elementsWithRetentionPolicies: number;
    expiredDataElements: number;
    retentionComplianceRate: number;
  };
  dataProcessing: {
    totalProcessingActivities: number;
    compliantActivities: number;
    highRiskActivities: number;
    processingComplianceRate: number;
  };
  overallComplianceScore: number;
  recommendations: string[];
}

@Injectable()
export class GdprService {
  private readonly logger = new Logger(GdprService.name);

  constructor(
    @InjectRepository(ConsentRecord)
    private readonly consentRepository: Repository<ConsentRecord>,
    @InjectRepository(DataRequest)
    private readonly dataRequestRepository: Repository<DataRequest>,
    @InjectRepository(DataProcessingLog)
    private readonly processingLogRepository: Repository<DataProcessingLog>,
    @InjectRepository(PersonalDataInventory)
    private readonly dataInventoryRepository: Repository<PersonalDataInventory>,
    @InjectQueue('gdpr-processing')
    private readonly gdprQueue: Queue,
    private readonly consentService: ConsentService,
    private readonly dataProtectionService: DataProtectionService,
    private readonly dataRetentionService: DataRetentionService,
    private readonly dataPortabilityService: DataPortabilityService,
    private readonly dataAnonymizationService: DataAnonymizationService,
  ) {}

  async generateComplianceReport(): Promise<GdprComplianceReport> {
    this.logger.log('Generating GDPR compliance report');

    const [
      consentCompliance,
      dataRequests,
      dataRetention,
      dataProcessing
    ] = await Promise.all([
      this.assessConsentCompliance(),
      this.assessDataRequestCompliance(),
      this.assessDataRetentionCompliance(),
      this.assessDataProcessingCompliance(),
    ]);

    const overallComplianceScore = this.calculateOverallComplianceScore({
      consentCompliance,
      dataRequests,
      dataRetention,
      dataProcessing,
    });

    const recommendations = this.generateRecommendations({
      consentCompliance,
      dataRequests,
      dataRetention,
      dataProcessing,
    });

    return {
      consentCompliance,
      dataRequests,
      dataRetention,
      dataProcessing,
      overallComplianceScore,
      recommendations,
    };
  }

  private async assessConsentCompliance() {
    const totalConsents = await this.consentRepository.count();
    const validConsents = await this.consentRepository.count({
      where: { status: 'given' },
    });
    const expiredConsents = await this.consentRepository
      .createQueryBuilder('consent')
      .where('consent.expirationDate < :now', { now: new Date() })
      .getCount();
    const withdrawnConsents = await this.consentRepository.count({
      where: { status: 'withdrawn' },
    });

    const complianceRate = totalConsents > 0 ? (validConsents / totalConsents) * 100 : 100;

    return {
      totalConsents,
      validConsents,
      expiredConsents,
      withdrawnConsents,
      complianceRate,
    };
  }

  private async assessDataRequestCompliance() {
    const totalRequests = await this.dataRequestRepository.count();
    const pendingRequests = await this.dataRequestRepository.count({
      where: { status: 'pending' },
    });
    const overdueRequests = await this.dataRequestRepository
      .createQueryBuilder('request')
      .where('request.dueDate < :now', { now: new Date() })
      .andWhere('request.status != :completed', { completed: 'completed' })
      .getCount();
    const completedRequests = await this.dataRequestRepository.count({
      where: { status: 'completed' },
    });

    // Calculate average response time
    const completedRequestsWithTimes = await this.dataRequestRepository
      .createQueryBuilder('request')
      .where('request.status = :completed', { completed: 'completed' })
      .andWhere('request.completedAt IS NOT NULL')
      .getMany();

    const averageResponseTime = completedRequestsWithTimes.length > 0
      ? completedRequestsWithTimes.reduce((sum, request) => {
          const responseTime = request.completedAt.getTime() - request.createdAt.getTime();
          return sum + responseTime;
        }, 0) / completedRequestsWithTimes.length / (1000 * 3600 * 24) // Convert to days
      : 0;

    return {
      totalRequests,
      pendingRequests,
      overdueRequests,
      completedRequests,
      averageResponseTime,
    };
  }

  private async assessDataRetentionCompliance() {
    const totalDataElements = await this.dataInventoryRepository.count({
      where: { isActive: true },
    });
    const elementsWithRetentionPolicies = await this.dataInventoryRepository.count({
      where: { 
        isActive: true,
        retentionPolicyId: 'NOT NULL' as any,
      },
    });
    const expiredDataElements = await this.dataInventoryRepository
      .createQueryBuilder('inventory')
      .where('inventory.isActive = :active', { active: true })
      .andWhere('inventory.retentionPeriodDays IS NOT NULL')
      .getCount();

    const retentionComplianceRate = totalDataElements > 0
      ? (elementsWithRetentionPolicies / totalDataElements) * 100
      : 100;

    return {
      totalDataElements,
      elementsWithRetentionPolicies,
      expiredDataElements,
      retentionComplianceRate,
    };
  }

  private async assessDataProcessingCompliance() {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const totalProcessingActivities = await this.processingLogRepository.count({
      where: {
        timestamp: 'MORE_THAN_OR_EQUAL' as any,
        // timestamp: thirtyDaysAgo, // TypeORM syntax issue
      },
    });

    const compliantActivities = await this.processingLogRepository
      .createQueryBuilder('log')
      .where('log.timestamp >= :thirtyDaysAgo', { thirtyDaysAgo })
      .andWhere('log.hasUserConsent = :hasConsent', { hasConsent: true })
      .andWhere('log.result = :success', { success: 'success' })
      .getCount();

    const highRiskActivities = await this.processingLogRepository
      .createQueryBuilder('log')
      .where('log.timestamp >= :thirtyDaysAgo', { thirtyDaysAgo })
      .andWhere('(log.involvesSpecialCategories = :special OR log.activity = :profiling OR log.activity = :automated)',
        { special: true, profiling: 'profiling', automated: 'automated_decision' })
      .getCount();

    const processingComplianceRate = totalProcessingActivities > 0
      ? (compliantActivities / totalProcessingActivities) * 100
      : 100;

    return {
      totalProcessingActivities,
      compliantActivities,
      highRiskActivities,
      processingComplianceRate,
    };
  }

  private calculateOverallComplianceScore(metrics: any): number {
    const weights = {
      consent: 0.3,
      dataRequests: 0.25,
      dataRetention: 0.25,
      dataProcessing: 0.2,
    };

    const consentScore = metrics.consentCompliance.complianceRate;
    const dataRequestScore = metrics.dataRequests.overdueRequests === 0 ? 100 : 
      Math.max(0, 100 - (metrics.dataRequests.overdueRequests * 10));
    const retentionScore = metrics.dataRetention.retentionComplianceRate;
    const processingScore = metrics.dataProcessing.processingComplianceRate;

    return Math.round(
      (consentScore * weights.consent) +
      (dataRequestScore * weights.dataRequests) +
      (retentionScore * weights.dataRetention) +
      (processingScore * weights.dataProcessing)
    );
  }

  private generateRecommendations(metrics: any): string[] {
    const recommendations: string[] = [];

    // Consent recommendations
    if (metrics.consentCompliance.complianceRate < 90) {
      recommendations.push('Review and update consent mechanisms to improve compliance rate');
    }
    if (metrics.consentCompliance.expiredConsents > 0) {
      recommendations.push('Implement automated consent renewal process for expired consents');
    }

    // Data request recommendations
    if (metrics.dataRequests.overdueRequests > 0) {
      recommendations.push('Address overdue data requests immediately to ensure 30-day compliance');
    }
    if (metrics.dataRequests.averageResponseTime > 25) {
      recommendations.push('Optimize data request processing to reduce average response time');
    }

    // Data retention recommendations
    if (metrics.dataRetention.retentionComplianceRate < 95) {
      recommendations.push('Implement retention policies for all personal data elements');
    }
    if (metrics.dataRetention.expiredDataElements > 0) {
      recommendations.push('Execute data retention policies for expired data elements');
    }

    // Data processing recommendations
    if (metrics.dataProcessing.processingComplianceRate < 95) {
      recommendations.push('Review data processing activities for GDPR compliance');
    }
    if (metrics.dataProcessing.highRiskActivities > 0) {
      recommendations.push('Conduct privacy impact assessments for high-risk processing activities');
    }

    return recommendations;
  }

  async handleDataSubjectRequest(userId: string, requestType: DataRequestType, description: string): Promise<DataRequest> {
    this.logger.log(`Processing data subject request: ${requestType} for user ${userId}`);

    const request = await this.dataRequestRepository.save({
      userId,
      type: requestType,
      description,
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      isVerified: false,
    });

    // Add to processing queue
    await this.gdprQueue.add('process-data-request', {
      requestId: request.id,
      requestType,
      userId,
    });

    return request;
  }

  async processDataRequest(requestId: string): Promise<void> {
    const request = await this.dataRequestRepository.findOne({
      where: { id: requestId },
      relations: ['user'],
    });

    if (!request) {
      throw new Error(`Data request ${requestId} not found`);
    }

    try {
      switch (request.type) {
        case DataRequestType.ACCESS:
          await this.handleDataAccessRequest(request);
          break;
        case DataRequestType.RECTIFICATION:
          await this.handleDataRectificationRequest(request);
          break;
        case DataRequestType.ERASURE:
          await this.handleDataErasureRequest(request);
          break;
        case DataRequestType.PORTABILITY:
          await this.handleDataPortabilityRequest(request);
          break;
        case DataRequestType.RESTRICT_PROCESSING:
          await this.handleRestrictProcessingRequest(request);
          break;
        case DataRequestType.OBJECTION:
          await this.handleObjectionRequest(request);
          break;
        default:
          throw new Error(`Unknown request type: ${request.type}`);
      }

      await this.dataRequestRepository.update(request.id, {
        status: 'completed',
        completedAt: new Date(),
      });

      this.logger.log(`Successfully processed data request ${requestId}`);
    } catch (error) {
      this.logger.error(`Failed to process data request ${requestId}:`, error);
      await this.dataRequestRepository.update(request.id, {
        status: 'rejected',
        rejectionReason: error.message,
      });
    }
  }

  private async handleDataAccessRequest(request: DataRequest): Promise<void> {
    const userData = await this.dataPortabilityService.exportUserData(request.userId, {
      format: 'json',
      dataTypes: ['profile', 'activities', 'preferences'],
      includeMetadata: false,
    });
    await this.dataRequestRepository.update(request.id, {
      responseData: userData,
    });
  }

  private async handleDataRectificationRequest(request: DataRequest): Promise<void> {
    // Implementation depends on specific rectification requirements
    // This would involve updating user data based on request details
    this.logger.log(`Processing rectification request for user ${request.userId}`);
  }

  private async handleDataErasureRequest(request: DataRequest): Promise<void> {
    await this.dataAnonymizationService.anonymizeUserData(request.userId);
  }

  private async handleDataPortabilityRequest(request: DataRequest): Promise<void> {
    const portableData = await this.dataPortabilityService.exportUserData(request.userId, {
      format: 'json',
      dataTypes: ['profile', 'activities', 'preferences'],
      includeMetadata: true,
    });
    await this.dataRequestRepository.update(request.id, {
      responseData: portableData,
    });
  }

  private async handleRestrictProcessingRequest(request: DataRequest): Promise<void> {
    // Implement processing restriction logic
    this.logger.log(`Processing restriction request for user ${request.userId}`);
  }

  private async handleObjectionRequest(request: DataRequest): Promise<void> {
    // Implement objection handling logic
    this.logger.log(`Processing objection request for user ${request.userId}`);
  }

  async scheduleDataRetentionAudit(): Promise<void> {
    await this.gdprQueue.add('retention-audit', {}, {
      repeat: { cron: '0 2 * * *' }, // Daily at 2 AM
    });
  }

  async scheduleConsentAudit(): Promise<void> {
    await this.gdprQueue.add('consent-audit', {}, {
      repeat: { cron: '0 1 * * *' }, // Daily at 1 AM
    });
  }

  async logDataProcessingActivity(
    userId: string,
    activity: string,
    dataSubject: string,
    purpose: string,
    legalBasis: string,
    dataCategories: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.processingLogRepository.save({
      userId,
      activity: activity as any,
      dataSubject,
      processingPurpose: purpose as any,
      legalBasis,
      dataCategories,
      systemComponent: 'luminar-platform',
      metadata,
      hasUserConsent: await this.consentService.hasValidConsent(userId, 'functional'),
      timestamp: new Date(),
    });
  }
}