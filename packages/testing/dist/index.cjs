'use strict';

var React = require('react');
var react_star = require('@testing-library/react');
var reactQuery = require('@tanstack/react-query');
var reactRouter = require('@tanstack/react-router');
var userEvent2 = require('@testing-library/user-event');
var vitest = require('vitest');
var jsxRuntime = require('react/jsx-runtime');
var node = require('msw/node');
var msw = require('msw');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return Object.freeze(n);
}

var React__default = /*#__PURE__*/_interopDefault(React);
var react_star__namespace = /*#__PURE__*/_interopNamespace(react_star);
var userEvent2__namespace = /*#__PURE__*/_interopNamespace(userEvent2);

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget);

// src/index.ts
var src_exports = {};
__export(src_exports, {
  TestProviders: () => TestProviders,
  a11yTestPatterns: () => a11yTestPatterns,
  a11yTestUtils: () => a11yTestUtils,
  afterAll: () => vitest.afterAll,
  afterEach: () => vitest.afterEach,
  beforeAll: () => vitest.beforeAll,
  beforeEach: () => vitest.beforeEach,
  commonTestPatterns: () => commonTestPatterns,
  componentTestUtils: () => componentTestUtils,
  createComponentTester: () => createComponentTester,
  createMockApiClient: () => createMockApiClient,
  createMockAuthProvider: () => createMockAuthProvider,
  createTestQueryClient: () => createTestQueryClient,
  customMatchers: () => customMatchers,
  describe: () => vitest.describe,
  expect: () => vitest.expect,
  handlers: () => handlers,
  integrationTestPatterns: () => integrationTestPatterns,
  it: () => vitest.it,
  mockHandlers: () => mockHandlers,
  mockServerUtils: () => mockServerUtils,
  performanceTestPatterns: () => performanceTestPatterns,
  performanceTestUtils: () => performanceTestUtils,
  renderWithProviders: () => renderWithProviders,
  server: () => server,
  setupMockServer: () => setupMockServer,
  testDataFactory: () => testDataFactory,
  userEvent: () => userEvent2__namespace.default,
  vi: () => vitest.vi
});

// src/test-utils.tsx
var test_utils_exports = {};
__export(test_utils_exports, {
  TestProviders: () => TestProviders,
  a11yTestUtils: () => a11yTestUtils,
  componentTestUtils: () => componentTestUtils,
  createMockApiClient: () => createMockApiClient,
  createMockAuthProvider: () => createMockAuthProvider,
  createTestQueryClient: () => createTestQueryClient,
  customMatchers: () => customMatchers,
  mockHandlers: () => mockHandlers,
  performanceTestUtils: () => performanceTestUtils,
  renderWithProviders: () => renderWithProviders,
  testDataFactory: () => testDataFactory,
  userEvent: () => userEvent2__namespace.default,
  vi: () => vitest.vi
});
__reExport(test_utils_exports, react_star__namespace);
__reExport(test_utils_exports, userEvent2__namespace);
function createTestQueryClient() {
  return new reactQuery.QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0
      },
      mutations: {
        retry: false
      }
    }
  });
}
function TestProviders({
  children,
  queryClient = createTestQueryClient()
}) {
  const rootRoute = reactRouter.createRootRoute({
    component: () => /* @__PURE__ */ jsxRuntime.jsx("div", { children })
  });
  const indexRoute = reactRouter.createRoute({
    getParentRoute: () => rootRoute,
    path: "/",
    component: () => /* @__PURE__ */ jsxRuntime.jsx(jsxRuntime.Fragment, { children })
  });
  const routeTree = rootRoute.addChildren([indexRoute]);
  const router = reactRouter.createRouter({
    routeTree,
    defaultPreload: "intent"
  });
  return /* @__PURE__ */ jsxRuntime.jsx(reactQuery.QueryClientProvider, { client: queryClient, children: /* @__PURE__ */ jsxRuntime.jsx(reactRouter.RouterProvider, { router }) });
}
function renderWithProviders(ui, options = {}) {
  const {
    queryClient,
    initialEntries,
    routeConfig,
    withProviders = true,
    ...renderOptions
  } = options;
  const user = userEvent2__namespace.default.setup();
  const Wrapper = withProviders ? ({ children }) => /* @__PURE__ */ jsxRuntime.jsx(
    TestProviders,
    {
      queryClient: queryClient || createTestQueryClient(),
      initialEntries: initialEntries || [],
      routeConfig,
      children
    }
  ) : void 0;
  const result = react_star.render(ui, {
    wrapper: Wrapper,
    ...renderOptions
  });
  return {
    ...result,
    user
  };
}
function createMockApiClient() {
  return {
    get: vitest.vi.fn(),
    post: vitest.vi.fn(),
    put: vitest.vi.fn(),
    patch: vitest.vi.fn(),
    delete: vitest.vi.fn(),
    addRequestInterceptor: vitest.vi.fn(),
    addResponseInterceptor: vitest.vi.fn(),
    removeRequestInterceptor: vitest.vi.fn(),
    removeResponseInterceptor: vitest.vi.fn(),
    setBaseURL: vitest.vi.fn(),
    setDefaultHeaders: vitest.vi.fn(),
    getConfig: vitest.vi.fn()
  };
}
function createMockAuthProvider(initialState = { isAuthenticated: false }) {
  const AuthContext = React__default.default.createContext(initialState);
  const MockAuthProvider = ({ children, state = initialState }) => /* @__PURE__ */ jsxRuntime.jsx(AuthContext.Provider, { value: state, children });
  const useAuth = () => React__default.default.useContext(AuthContext);
  return {
    MockAuthProvider,
    useAuth,
    AuthContext
  };
}
var testDataFactory = {
  // User factory
  createUser: (overrides = {}) => ({
    id: "1",
    name: "Test User",
    email: "<EMAIL>",
    roles: ["user"],
    permissions: ["read"],
    isActive: true,
    createdAt: /* @__PURE__ */ new Date(),
    updatedAt: /* @__PURE__ */ new Date(),
    ...overrides
  }),
  // Training factory
  createTraining: (overrides = {}) => ({
    id: "1",
    title: "Test Training",
    description: "Test training description",
    category: { id: "1", name: "Test Category" },
    duration: 60,
    difficulty: "beginner",
    instructor: testDataFactory.createUser({ id: "2", name: "Instructor" }),
    participants: [],
    status: "published",
    isPublic: true,
    createdAt: /* @__PURE__ */ new Date(),
    updatedAt: /* @__PURE__ */ new Date(),
    ...overrides
  }),
  // API response factory
  createApiResponse: (data, overrides = {}) => ({
    data,
    status: 200,
    statusText: "OK",
    message: "Success",
    ...overrides
  }),
  // Error factory
  createApiError: (overrides = {}) => ({
    message: "Test error",
    code: "TEST_ERROR",
    details: {
      type: "CLIENT_ERROR",
      status: 400,
      timestamp: /* @__PURE__ */ new Date()
    },
    ...overrides
  })
};
var mockHandlers = {
  // Success response handler
  success: (data) => ({
    status: 200,
    json: testDataFactory.createApiResponse(data)
  }),
  // Error response handler
  error: (status = 400, message = "Test error") => ({
    status,
    json: testDataFactory.createApiError({ message })
  }),
  // Loading delay handler
  delay: (ms = 100) => new Promise((resolve) => setTimeout(resolve, ms))
};
var componentTestUtils = {
  // Wait for component to be loaded (for lazy components)
  waitForComponent: async (getByTestId, testId) => {
    return new Promise((resolve) => {
      const checkForComponent = () => {
        try {
          const element = getByTestId(testId);
          resolve(element);
        } catch {
          setTimeout(checkForComponent, 10);
        }
      };
      checkForComponent();
    });
  },
  // Simulate network delay
  simulateNetworkDelay: (ms = 100) => new Promise((resolve) => setTimeout(resolve, ms)),
  // Mock intersection observer for lazy loading tests
  mockIntersectionObserver: () => {
    const mockIntersectionObserver = vitest.vi.fn();
    mockIntersectionObserver.mockReturnValue({
      observe: vitest.vi.fn(),
      unobserve: vitest.vi.fn(),
      disconnect: vitest.vi.fn()
    });
    Object.defineProperty(window, "IntersectionObserver", {
      writable: true,
      configurable: true,
      value: mockIntersectionObserver
    });
    return mockIntersectionObserver;
  },
  // Mock ResizeObserver
  mockResizeObserver: () => {
    const mockResizeObserver = vitest.vi.fn();
    mockResizeObserver.mockReturnValue({
      observe: vitest.vi.fn(),
      unobserve: vitest.vi.fn(),
      disconnect: vitest.vi.fn()
    });
    Object.defineProperty(window, "ResizeObserver", {
      writable: true,
      configurable: true,
      value: mockResizeObserver
    });
    return mockResizeObserver;
  }
};
var a11yTestUtils = {
  // Check for required ARIA attributes
  checkAriaAttributes: (element, requiredAttributes) => {
    const missingAttributes = requiredAttributes.filter(
      (attr) => !element.hasAttribute(attr)
    );
    if (missingAttributes.length > 0) {
      throw new Error(`Missing ARIA attributes: ${missingAttributes.join(", ")}`);
    }
  },
  // Check for keyboard navigation
  checkKeyboardNavigation: async (user, element) => {
    element.focus();
    expect(element).toHaveFocus();
    await user.keyboard("{Tab}");
  },
  // Check color contrast (simplified)
  checkColorContrast: (element) => {
    const styles = window.getComputedStyle(element);
    const backgroundColor = styles.backgroundColor;
    const color = styles.color;
    if (backgroundColor === color) {
      throw new Error("Insufficient color contrast");
    }
  }
};
var performanceTestUtils = {
  // Measure render time
  measureRenderTime: async (renderFn) => {
    const start = performance.now();
    await renderFn();
    const end = performance.now();
    return end - start;
  },
  // Check for memory leaks (simplified)
  checkMemoryLeaks: (cleanup) => {
    const initialMemory = performance.memory?.usedJSHeapSize || 0;
    return () => {
      cleanup();
      if (global.gc) {
        global.gc();
      }
      const finalMemory = performance.memory?.usedJSHeapSize || 0;
      const memoryDiff = finalMemory - initialMemory;
      if (memoryDiff > 1e6) {
        console.warn(`Potential memory leak detected: ${memoryDiff} bytes`);
      }
    };
  }
};
var customMatchers = {
  // Check if element is accessible
  toBeAccessible: (element) => {
    try {
      const hasAriaLabel = element.hasAttribute("aria-label") || element.hasAttribute("aria-labelledby");
      const hasRole = element.hasAttribute("role") || ["button", "link", "input"].includes(element.tagName.toLowerCase());
      if (!hasAriaLabel && !hasRole) {
        return {
          message: () => "Element is not accessible - missing aria-label or role",
          pass: false
        };
      }
      return {
        message: () => "Element is accessible",
        pass: true
      };
    } catch (error) {
      return {
        message: () => `Accessibility check failed: ${error}`,
        pass: false
      };
    }
  }
};

// src/index.ts
__reExport(src_exports, test_utils_exports);
var handlers = [
  // Users API
  msw.http.get("/api/users", ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const search = url.searchParams.get("search");
    let users = Array.from(
      { length: 50 },
      (_, i) => testDataFactory.createUser({
        id: `${i + 1}`,
        name: `User ${i + 1}`,
        email: `user${i + 1}@example.com`
      })
    );
    if (search) {
      users = users.filter(
        (user) => user.name.toLowerCase().includes(search.toLowerCase()) || user.email.toLowerCase().includes(search.toLowerCase())
      );
    }
    const start = (page - 1) * limit;
    const end = start + limit;
    const paginatedUsers = users.slice(start, end);
    return msw.HttpResponse.json(testDataFactory.createApiResponse(paginatedUsers, {
      metadata: {
        page,
        limit,
        total: users.length,
        hasNext: end < users.length,
        hasPrevious: page > 1
      }
    }));
  }),
  msw.http.get("/api/users/:id", ({ params }) => {
    const user = testDataFactory.createUser({
      id: params.id,
      name: `User ${params.id}`,
      email: `user${params.id}@example.com`
    });
    return msw.HttpResponse.json(testDataFactory.createApiResponse(user));
  }),
  msw.http.post("/api/users", async ({ request }) => {
    const userData = await request.json();
    const user = testDataFactory.createUser({
      id: Math.random().toString(36).substr(2, 9),
      ...userData
    });
    return msw.HttpResponse.json(testDataFactory.createApiResponse(user), { status: 201 });
  }),
  msw.http.put("/api/users/:id", async ({ params, request }) => {
    const userData = await request.json();
    const user = testDataFactory.createUser({
      id: params.id,
      ...userData
    });
    return msw.HttpResponse.json(testDataFactory.createApiResponse(user));
  }),
  msw.http.delete("/api/users/:id", () => {
    return new msw.HttpResponse(null, { status: 204 });
  }),
  // Training API
  msw.http.get("/api/trainings", ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const category = url.searchParams.get("categoryId");
    let trainings = Array.from(
      { length: 30 },
      (_, i) => testDataFactory.createTraining({
        id: `${i + 1}`,
        title: `Training ${i + 1}`,
        category: { id: category || "1", name: "Test Category" }
      })
    );
    if (category) {
      trainings = trainings.filter((training) => training.category.id === category);
    }
    const start = (page - 1) * limit;
    const end = start + limit;
    const paginatedTrainings = trainings.slice(start, end);
    return msw.HttpResponse.json(testDataFactory.createApiResponse(paginatedTrainings, {
      metadata: {
        page,
        limit,
        total: trainings.length,
        hasNext: end < trainings.length,
        hasPrevious: page > 1
      }
    }));
  }),
  msw.http.get("/api/trainings/:id", ({ params }) => {
    const training = testDataFactory.createTraining({
      id: params.id,
      title: `Training ${params.id}`
    });
    return msw.HttpResponse.json(testDataFactory.createApiResponse(training));
  }),
  msw.http.post("/api/trainings", async ({ request }) => {
    const trainingData = await request.json();
    const training = testDataFactory.createTraining({
      id: Math.random().toString(36).substr(2, 9),
      ...trainingData
    });
    return msw.HttpResponse.json(testDataFactory.createApiResponse(training), { status: 201 });
  }),
  // Training enrollment
  msw.http.post("/api/trainings/:id/enroll", async ({ request }) => {
    await request.json();
    return new msw.HttpResponse(null, { status: 204 });
  }),
  msw.http.delete("/api/trainings/:trainingId/enroll/:userId", () => {
    return new msw.HttpResponse(null, { status: 204 });
  }),
  // Error scenarios for testing
  msw.http.get("/api/users/error", () => {
    return msw.HttpResponse.json(
      testDataFactory.createApiError({ message: "Server error" }),
      { status: 500 }
    );
  }),
  msw.http.get("/api/users/unauthorized", () => {
    return msw.HttpResponse.json(
      testDataFactory.createApiError({
        message: "Unauthorized",
        code: "AUTH_REQUIRED"
      }),
      { status: 401 }
    );
  }),
  msw.http.get("/api/users/slow", async () => {
    await new Promise((resolve) => setTimeout(resolve, 2e3));
    return msw.HttpResponse.json(testDataFactory.createApiResponse([]));
  })
];
var server = node.setupServer(...handlers);
var mockServerUtils = {
  // Add temporary handler
  addHandler: (handler) => {
    server.use(handler);
  },
  // Reset handlers to original state
  resetHandlers: () => {
    server.resetHandlers(...handlers);
  },
  // Create error response
  createErrorResponse: (status, message) => {
    return msw.HttpResponse.json(
      testDataFactory.createApiError({ message }),
      { status }
    );
  },
  // Create success response with delay
  createDelayedResponse: (data, delay = 100) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(msw.HttpResponse.json(testDataFactory.createApiResponse(data)));
      }, delay);
    });
  },
  // Mock network error
  mockNetworkError: (url) => {
    server.use(
      msw.http.get(url, () => {
        return msw.HttpResponse.error();
      })
    );
  },
  // Mock timeout
  mockTimeout: (url, timeout = 5e3) => {
    server.use(
      msw.http.get(url, async () => {
        await new Promise((resolve) => setTimeout(resolve, timeout));
        return msw.HttpResponse.json(testDataFactory.createApiResponse([]));
      })
    );
  }
};
var setupMockServer = () => {
  vitest.beforeAll(() => {
    server.listen({ onUnhandledRequest: "error" });
  });
  vitest.afterEach(() => {
    server.resetHandlers();
  });
  vitest.afterAll(() => {
    server.close();
  });
};
function createComponentTester(pattern) {
  return {
    // Test all cases
    testAll: () => {
      pattern.testCases.forEach((testCase) => {
        it(testCase.name, async () => {
          await testCase.setup?.();
          const props = { ...pattern.defaultProps, ...testCase.props };
          renderWithProviders(pattern.component(props));
          await testCase.test(props);
          await testCase.cleanup?.();
        });
      });
    },
    // Test specific case
    testCase: (caseName) => {
      const testCase = pattern.testCases.find((tc) => tc.name === caseName);
      if (!testCase) {
        throw new Error(`Test case "${caseName}" not found`);
      }
      it(testCase.name, async () => {
        await testCase.setup?.();
        const props = { ...pattern.defaultProps, ...testCase.props };
        renderWithProviders(pattern.component(props));
        await testCase.test(props);
        await testCase.cleanup?.();
      });
    },
    // Add custom test case
    addTestCase: (testCase) => {
      pattern.testCases.push(testCase);
    }
  };
}
var commonTestPatterns = {
  // Button component pattern
  button: {
    rendering: (getButton) => ({
      name: "renders correctly",
      test: () => {
        const button = getButton();
        expect(button).toBeInTheDocument();
        expect(button).toBeVisible();
      }
    }),
    clickHandler: (getButton, onClick) => ({
      name: "handles click events",
      test: async () => {
        const button = getButton();
        const user = userEvent2__namespace.default.setup();
        await user.click(button);
        expect(onClick).toHaveBeenCalledTimes(1);
      }
    }),
    disabled: (getButton) => ({
      name: "handles disabled state",
      props: { disabled: true },
      test: () => {
        const button = getButton();
        expect(button).toBeDisabled();
      }
    }),
    loading: (getButton) => ({
      name: "shows loading state",
      props: { loading: true },
      test: () => {
        const button = getButton();
        expect(button).toHaveAttribute("aria-busy", "true");
        expect(react_star.screen.getByRole("status")).toBeInTheDocument();
      }
    }),
    accessibility: (getButton) => ({
      name: "meets accessibility requirements",
      test: () => {
        const button = getButton();
        expect(button).toHaveAttribute("type");
        expect(button).not.toHaveAttribute("aria-label", "");
      }
    })
  },
  // Form input pattern
  input: {
    rendering: (getInput) => ({
      name: "renders correctly",
      test: () => {
        const input = getInput();
        expect(input).toBeInTheDocument();
        expect(input).toBeVisible();
      }
    }),
    valueChange: (getInput, onChange) => ({
      name: "handles value changes",
      test: async () => {
        const input = getInput();
        const user = userEvent2__namespace.default.setup();
        await user.type(input, "test value");
        expect(onChange).toHaveBeenCalled();
        expect(input).toHaveValue("test value");
      }
    }),
    validation: (getInput) => ({
      name: "shows validation errors",
      props: { error: "This field is required" },
      test: () => {
        const input = getInput();
        expect(input).toHaveAttribute("aria-invalid", "true");
        expect(react_star.screen.getByText("This field is required")).toBeInTheDocument();
      }
    }),
    required: (getInput) => ({
      name: "handles required state",
      props: { required: true },
      test: () => {
        const input = getInput();
        expect(input).toHaveAttribute("required");
        expect(input).toHaveAttribute("aria-required", "true");
      }
    })
  },
  // Modal/Dialog pattern
  modal: {
    rendering: (isOpen) => ({
      name: "renders when open",
      props: { isOpen },
      test: () => {
        if (isOpen) {
          expect(react_star.screen.getByRole("dialog")).toBeInTheDocument();
        } else {
          expect(react_star.screen.queryByRole("dialog")).not.toBeInTheDocument();
        }
      }
    }),
    closeOnEscape: (onClose) => ({
      name: "closes on escape key",
      props: { isOpen: true },
      test: async () => {
        const user = userEvent2__namespace.default.setup();
        await user.keyboard("{Escape}");
        expect(onClose).toHaveBeenCalled();
      }
    }),
    closeOnOverlay: (onClose) => ({
      name: "closes on overlay click",
      props: { isOpen: true },
      test: async () => {
        const user = userEvent2__namespace.default.setup();
        const overlay = react_star.screen.getByTestId("modal-overlay");
        await user.click(overlay);
        expect(onClose).toHaveBeenCalled();
      }
    }),
    focusTrap: () => ({
      name: "traps focus within modal",
      props: { isOpen: true },
      test: async () => {
        const modal = react_star.screen.getByRole("dialog");
        const focusableElements = modal.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        if (focusableElements.length > 0) {
          expect(focusableElements[0]).toHaveFocus();
        }
      }
    })
  },
  // List/Table pattern
  list: {
    rendering: (items) => ({
      name: "renders list items",
      props: { items },
      test: () => {
        items.forEach((_item, index) => {
          expect(react_star.screen.getByTestId(`list-item-${index}`)).toBeInTheDocument();
        });
      }
    }),
    emptyState: () => ({
      name: "shows empty state",
      props: { items: [] },
      test: () => {
        expect(react_star.screen.getByText(/no items/i)).toBeInTheDocument();
      }
    }),
    loading: () => ({
      name: "shows loading state",
      props: { loading: true },
      test: () => {
        expect(react_star.screen.getByRole("status")).toBeInTheDocument();
        expect(react_star.screen.getByText(/loading/i)).toBeInTheDocument();
      }
    }),
    selection: (onSelect) => ({
      name: "handles item selection",
      test: async () => {
        const user = userEvent2__namespace.default.setup();
        const firstItem = react_star.screen.getByTestId("list-item-0");
        await user.click(firstItem);
        expect(onSelect).toHaveBeenCalled();
      }
    })
  }
};
var a11yTestPatterns = {
  // Keyboard navigation
  keyboardNavigation: (getElement) => ({
    name: "supports keyboard navigation",
    test: async () => {
      const element = getElement();
      const user = userEvent2__namespace.default.setup();
      element.focus();
      expect(element).toHaveFocus();
      await user.keyboard("{Tab}");
    }
  }),
  // Screen reader support
  screenReader: (getElement, expectedLabel) => ({
    name: "provides screen reader support",
    test: () => {
      const element = getElement();
      expect(element).toHaveAccessibleName(expectedLabel);
    }
  }),
  // ARIA attributes
  ariaAttributes: (getElement, requiredAttributes) => ({
    name: "has required ARIA attributes",
    test: () => {
      const element = getElement();
      requiredAttributes.forEach((attr) => {
        expect(element).toHaveAttribute(attr);
      });
    }
  }),
  // Color contrast (simplified check)
  colorContrast: (getElement) => ({
    name: "meets color contrast requirements",
    test: () => {
      const element = getElement();
      const styles = window.getComputedStyle(element);
      expect(styles.color).not.toBe(styles.backgroundColor);
    }
  })
};
var performanceTestPatterns = {
  // Render performance
  renderPerformance: (component, threshold = 100) => ({
    name: "renders within performance threshold",
    test: async () => {
      const start = performance.now();
      renderWithProviders(component);
      const end = performance.now();
      expect(end - start).toBeLessThan(threshold);
    }
  }),
  // Memory usage
  memoryUsage: (component) => ({
    name: "does not leak memory",
    test: async () => {
      const { unmount } = renderWithProviders(component);
      if (global.gc) {
        global.gc();
      }
      const initialMemory = performance.memory?.usedJSHeapSize || 0;
      unmount();
      if (global.gc) {
        global.gc();
      }
      const finalMemory = performance.memory?.usedJSHeapSize || 0;
      const memoryDiff = finalMemory - initialMemory;
      expect(Math.abs(memoryDiff)).toBeLessThan(1e6);
    }
  }),
  // Re-render optimization
  rerenderOptimization: (component, propChanges) => ({
    name: "optimizes re-renders",
    test: async () => {
      let renderCount = 0;
      const TestComponent = (_props) => {
        renderCount++;
        return component;
      };
      const { rerender } = renderWithProviders(/* @__PURE__ */ jsxRuntime.jsx(TestComponent, {}));
      propChanges.forEach((props) => {
        rerender(/* @__PURE__ */ jsxRuntime.jsx(TestComponent, { ...props }));
      });
      expect(renderCount).toBeLessThanOrEqual(propChanges.length + 1);
    }
  })
};
var integrationTestPatterns = {
  // API integration
  apiIntegration: (apiCall, expectedResult) => ({
    name: "integrates with API correctly",
    test: async () => {
      const result = await apiCall();
      expect(result).toEqual(expectedResult);
    }
  }),
  // Error handling
  errorHandling: (apiCall, expectedError) => ({
    name: "handles API errors correctly",
    test: async () => {
      await expect(apiCall()).rejects.toEqual(expectedError);
    }
  }),
  // Loading states
  loadingStates: (component) => ({
    name: "shows loading states during API calls",
    test: async () => {
      renderWithProviders(component);
      expect(react_star.screen.getByRole("status")).toBeInTheDocument();
      await react_star.waitFor(() => {
        expect(react_star.screen.queryByRole("status")).not.toBeInTheDocument();
      });
    }
  })
};

Object.defineProperty(exports, "userEvent", {
  enumerable: true,
  get: function () { return userEvent2__namespace.default; }
});
Object.defineProperty(exports, "afterAll", {
  enumerable: true,
  get: function () { return vitest.afterAll; }
});
Object.defineProperty(exports, "afterEach", {
  enumerable: true,
  get: function () { return vitest.afterEach; }
});
Object.defineProperty(exports, "beforeAll", {
  enumerable: true,
  get: function () { return vitest.beforeAll; }
});
Object.defineProperty(exports, "beforeEach", {
  enumerable: true,
  get: function () { return vitest.beforeEach; }
});
Object.defineProperty(exports, "describe", {
  enumerable: true,
  get: function () { return vitest.describe; }
});
Object.defineProperty(exports, "expect", {
  enumerable: true,
  get: function () { return vitest.expect; }
});
Object.defineProperty(exports, "it", {
  enumerable: true,
  get: function () { return vitest.it; }
});
Object.defineProperty(exports, "vi", {
  enumerable: true,
  get: function () { return vitest.vi; }
});
exports.TestProviders = TestProviders;
exports.a11yTestPatterns = a11yTestPatterns;
exports.a11yTestUtils = a11yTestUtils;
exports.commonTestPatterns = commonTestPatterns;
exports.componentTestUtils = componentTestUtils;
exports.createComponentTester = createComponentTester;
exports.createMockApiClient = createMockApiClient;
exports.createMockAuthProvider = createMockAuthProvider;
exports.createTestQueryClient = createTestQueryClient;
exports.customMatchers = customMatchers;
exports.handlers = handlers;
exports.integrationTestPatterns = integrationTestPatterns;
exports.mockHandlers = mockHandlers;
exports.mockServerUtils = mockServerUtils;
exports.performanceTestPatterns = performanceTestPatterns;
exports.performanceTestUtils = performanceTestUtils;
exports.renderWithProviders = renderWithProviders;
exports.server = server;
exports.setupMockServer = setupMockServer;
exports.testDataFactory = testDataFactory;
Object.keys(react_star).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return react_star[k]; }
  });
});
Object.keys(userEvent2).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return userEvent2[k]; }
  });
});
//# sourceMappingURL=index.cjs.map
//# sourceMappingURL=index.cjs.map