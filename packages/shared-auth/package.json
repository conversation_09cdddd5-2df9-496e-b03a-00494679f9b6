{"name": "@luminar/shared-auth", "version": "2.0.0", "description": "Enhanced authentication library with React providers for Luminar L&D applications", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./react": {"types": "./dist/react.d.ts", "import": "./dist/react.mjs", "require": "./dist/react.js"}, "./components": {"types": "./dist/components.d.ts", "import": "./dist/components.mjs", "require": "./dist/components.js"}}, "files": ["dist", "README.md"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@luminar/shared-core": "workspace:*", "axios": "^1.10.0", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0"}, "devDependencies": {"@types/jest": "^29.5.10", "@types/js-cookie": "^3.0.6", "@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "eslint": "^9.15.0", "tsup": "^8.0.0", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0", "@tanstack/react-router": ">=1.0.0"}, "publishConfig": {"access": "restricted"}}