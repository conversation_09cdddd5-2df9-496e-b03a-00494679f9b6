export { capitalize, cn, debounce, deepClone, formatBytes, formatCurrency, formatNumber, generateId, get, isEmpty, omit, pick, set, sleep, throttle, truncate } from './utils.js';
export { API_CONFIG, APP_METADATA, AUTH_CONFIG, DATE_FORMATS, ERROR_CODES, EVENTS, FEATURE_FLAGS, FILE_CONFIG, HTTP_STATUS, PERMISSIONS, ROUTES, STORAGE_KEYS, THEME_CONFIG, UI_CONFIG, USER_ROLES, VALIDATION_RULES } from './constants.js';
export { ChangePasswordFormData, FileUploadFormData, ForgotPasswordFormData, LoginFormData, RegisterFormData, ResetPasswordFormData, UserPreferencesFormData, UserProfileFormData, apiErrorSchema, apiResponseSchema, baseQuerySchema, changePasswordSchema, emailSchema, fileUploadSchema, forgotPasswordSchema, idSchema, loginSchema, nameSchema, paginatedResponseSchema, paginationSchema, passwordSchema, phoneSchema, registerSchema, resetPasswordSchema, searchSchema, slugSchema, sortSchema, urlSchema, userPreferencesSchema, userProfileSchema } from './schemas.js';
export { AuthError, ConflictError, FileError, LuminarError, NetworkError, NotFoundError, PermissionError, ValidationError, createAuthError, createErrorFromStatus, createFileError, createNetworkError, createValidationError, getErrorMessage, isAuthError, isConflictError, isFileError, isLuminarError, isNetworkError, isNotFoundError, isPermissionError, isValidationError, logError, toLuminarError } from './errors.js';
export { AnalyticsData, AppConfig, AuditLogEntry, AuthState, BaseEntity, BaseUser, ChartDataPoint, EnvironmentVariables, FeatureFlag, FileUpload, FormField, KeyValuePair, LoginCredentials, Notification, NotificationAction, PaginatedResponse, Priority, RegisterData, Status, TableColumn, ThemeConfig, TimeSeriesDataPoint } from './types.js';
import { AxiosError } from 'axios';
import 'clsx';
import 'zod';

/**
 * Core API types and interfaces for the Luminar API Client
 */
interface RequestConfig {
    headers?: Record<string, string>;
    timeout?: number;
    retries?: number;
    retryDelay?: number;
    signal?: AbortSignal;
}
interface ApiResponse<T = any> {
    data: T;
    status: number;
    statusText: string;
    message?: string;
    errors?: ApiError[];
    metadata?: ApiMetadata;
}
interface ApiError {
    field?: string;
    message: string;
    code: string;
    details?: Record<string, any>;
}
interface ApiMetadata {
    page?: number;
    limit?: number;
    total?: number;
    hasNext?: boolean;
    hasPrevious?: boolean;
    timestamp?: string;
}
interface RequestInterceptor {
    onRequest?: (config: RequestConfig & {
        url: string;
        method: string;
        data?: any;
    }) => RequestConfig & {
        url: string;
        method: string;
        data?: any;
    } | Promise<RequestConfig & {
        url: string;
        method: string;
        data?: any;
    }>;
    onRequestError?: (error: Error) => Error | Promise<Error>;
}
interface ResponseInterceptor {
    onResponse?: <T>(response: ApiResponse<T>) => ApiResponse<T> | Promise<ApiResponse<T>>;
    onResponseError?: (error: ApiError | Error) => ApiError | Error | Promise<ApiError | Error>;
}
interface LuminarAPIClientConfig {
    baseURL: string;
    timeout?: number;
    defaultHeaders?: Record<string, string>;
    retries?: number;
    retryDelay?: number;
    enableLogging?: boolean;
}
interface LuminarAPIClient {
    get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>;
    post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;
    put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;
    patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;
    delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>;
    addRequestInterceptor(interceptor: RequestInterceptor): number;
    addResponseInterceptor(interceptor: ResponseInterceptor): number;
    removeRequestInterceptor(id: number): void;
    removeResponseInterceptor(id: number): void;
    setBaseURL(url: string): void;
    setDefaultHeaders(headers: Record<string, string>): void;
    getConfig(): LuminarAPIClientConfig;
}
declare enum ErrorType {
    NETWORK_ERROR = "NETWORK_ERROR",
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR",
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR",
    VALIDATION_ERROR = "VALIDATION_ERROR",
    SERVER_ERROR = "SERVER_ERROR",
    CLIENT_ERROR = "CLIENT_ERROR",
    TIMEOUT_ERROR = "TIMEOUT_ERROR",
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
}
interface ErrorContext {
    url?: string;
    method?: string;
    component?: string;
    action?: string;
    user?: any;
    timestamp: Date;
    requestId?: string;
}
interface RetryConfig {
    maxAttempts: number;
    backoffStrategy: 'linear' | 'exponential';
    baseDelay: number;
    maxDelay: number;
    retryCondition?: (error: Error) => boolean;
}

/**
 * Authentication Interceptor - Handles token attachment and refresh
 */

interface AuthTokenManager {
    getAccessToken(): string | null;
    getRefreshToken(): string | null;
    setTokens(accessToken: string, refreshToken?: string): void;
    clearTokens(): void;
    isTokenExpired(token: string): boolean;
    refreshAccessToken(): Promise<{
        accessToken: string;
        refreshToken?: string;
    }>;
}
interface AuthInterceptorConfig {
    tokenManager: AuthTokenManager;
    authHeaderName?: string;
    authHeaderPrefix?: string;
    excludeUrls?: string[];
    onAuthError?: (error: any) => void;
    onTokenRefresh?: (tokens: {
        accessToken: string;
        refreshToken?: string;
    }) => void;
}
declare class AuthenticationInterceptor {
    private config;
    private refreshPromise;
    private requestQueue;
    constructor(config: AuthInterceptorConfig);
    getRequestInterceptor(): RequestInterceptor;
    getResponseInterceptor(): ResponseInterceptor;
    private getValidToken;
    private refreshToken;
    private performTokenRefresh;
    private processRequestQueue;
    private rejectRequestQueue;
    private shouldExcludeUrl;
    private isAuthError;
}
declare class LocalStorageTokenManager implements AuthTokenManager {
    private accessTokenKey;
    private refreshTokenKey;
    getAccessToken(): string | null;
    getRefreshToken(): string | null;
    setTokens(accessToken: string, refreshToken?: string): void;
    clearTokens(): void;
    isTokenExpired(token: string): boolean;
    refreshAccessToken(): Promise<{
        accessToken: string;
        refreshToken?: string;
    }>;
}
declare function createAuthInterceptor(config?: Partial<AuthInterceptorConfig>): AuthenticationInterceptor;

/**
 * Luminar API Client - Unified HTTP client with interceptors, retry logic, and error handling
 */

declare class LuminarAPIClientImpl implements LuminarAPIClient {
    private axiosInstance;
    private config;
    private requestInterceptors;
    private responseInterceptors;
    private interceptorIdCounter;
    private errorHandler;
    constructor(config: LuminarAPIClientConfig);
    private setupInterceptors;
    private transformResponse;
    private executeWithRetry;
    get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>;
    post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;
    put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;
    patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;
    delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>;
    addRequestInterceptor(interceptor: RequestInterceptor): number;
    addResponseInterceptor(interceptor: ResponseInterceptor): number;
    removeRequestInterceptor(id: number): void;
    removeResponseInterceptor(id: number): void;
    setBaseURL(url: string): void;
    setDefaultHeaders(headers: Record<string, string>): void;
    getConfig(): LuminarAPIClientConfig;
    private mergeConfig;
}
declare function createLuminarAPIClient(config: LuminarAPIClientConfig): LuminarAPIClient;
declare function getDefaultAPIClient(): LuminarAPIClient;
declare function initializeDefaultAPIClient(config: LuminarAPIClientConfig): void;
declare function setDefaultAPIClient(client: LuminarAPIClient): void;

/**
 * API Error Handler - Comprehensive error handling and categorization
 */

declare class ApiErrorHandler {
    handleAxiosError(error: AxiosError): ApiError;
    private handleResponseError;
    private handleNetworkError;
    private handleUnknownError;
    categorizeError(error: Error): ErrorType;
    formatUserMessage(error: ApiError): string;
    shouldRetry(error: ApiError): boolean;
    logError(error: ApiError, context?: Partial<ErrorContext>): void;
    private generateRequestId;
}
declare function isApiError(error: any): error is ApiError;
declare function createApiError(message: string, code: string, type?: ErrorType, field?: string, details?: any): ApiError;

/**
 * API Module - Unified API client and utilities
 */

declare function createConfiguredAPIClient(config: {
    baseURL: string;
    tokenManager?: AuthTokenManager;
    enableAuth?: boolean;
    enableLogging?: boolean;
    timeout?: number;
    retries?: number;
}): LuminarAPIClient;

/**
 * Domain Service Types and Interfaces
 */

interface BaseQueryParams {
    page?: number;
    limit?: number;
    sort?: string;
    order?: 'asc' | 'desc';
    search?: string;
}
interface User {
    id: string;
    email: string;
    name: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    roles: Role[];
    permissions: Permission[];
    isActive: boolean;
    lastLoginAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}
interface Role {
    id: string;
    name: string;
    description?: string;
    permissions: Permission[];
    isSystem: boolean;
    createdAt: Date;
    updatedAt: Date;
}
interface Permission {
    id: string;
    name: string;
    resource: string;
    action: string;
    description?: string;
    createdAt: Date;
    updatedAt: Date;
}
interface CreateUserDto {
    email: string;
    name: string;
    firstName?: string;
    lastName?: string;
    password: string;
    roleIds?: string[];
    isActive?: boolean;
}
interface UpdateUserDto {
    name?: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    roleIds?: string[];
    isActive?: boolean;
}
interface UserQueryParams extends BaseQueryParams {
    role?: string;
    isActive?: boolean;
    createdAfter?: string;
    createdBefore?: string;
}
interface Training {
    id: string;
    title: string;
    description: string;
    content?: string;
    category: TrainingCategory;
    tags: string[];
    duration: number;
    difficulty: TrainingDifficulty;
    instructor: User;
    participants: User[];
    maxParticipants?: number;
    status: TrainingStatus;
    startDate?: Date;
    endDate?: Date;
    isPublic: boolean;
    createdAt: Date;
    updatedAt: Date;
}
interface TrainingCategory {
    id: string;
    name: string;
    description?: string;
    color?: string;
    icon?: string;
    parentId?: string;
    children?: TrainingCategory[];
    createdAt: Date;
    updatedAt: Date;
}
declare enum TrainingDifficulty {
    BEGINNER = "beginner",
    INTERMEDIATE = "intermediate",
    ADVANCED = "advanced",
    EXPERT = "expert"
}
declare enum TrainingStatus {
    DRAFT = "draft",
    PUBLISHED = "published",
    SCHEDULED = "scheduled",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled",
    ARCHIVED = "archived"
}
interface CreateTrainingDto {
    title: string;
    description: string;
    content?: string;
    categoryId: string;
    tags?: string[];
    duration: number;
    difficulty: TrainingDifficulty;
    instructorId: string;
    maxParticipants?: number;
    startDate?: Date;
    endDate?: Date;
    isPublic?: boolean;
}
interface UpdateTrainingDto {
    title?: string;
    description?: string;
    content?: string;
    categoryId?: string;
    tags?: string[];
    duration?: number;
    difficulty?: TrainingDifficulty;
    instructorId?: string;
    maxParticipants?: number;
    startDate?: Date;
    endDate?: Date;
    isPublic?: boolean;
    status?: TrainingStatus;
}
interface TrainingQueryParams extends BaseQueryParams {
    categoryId?: string;
    instructorId?: string;
    difficulty?: TrainingDifficulty;
    status?: TrainingStatus;
    isPublic?: boolean;
    startAfter?: string;
    startBefore?: string;
    tags?: string[];
}
interface Vendor {
    id: string;
    name: string;
    description?: string;
    website?: string;
    email?: string;
    phone?: string;
    address?: Address;
    contactPerson?: ContactPerson;
    services: VendorService[];
    rating?: number;
    isActive: boolean;
    isVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
}
interface Address {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
}
interface ContactPerson {
    name: string;
    email: string;
    phone?: string;
    position?: string;
}
interface VendorService {
    id: string;
    name: string;
    description?: string;
    category: string;
    price?: number;
    currency?: string;
    isActive: boolean;
}
interface CreateVendorDto {
    name: string;
    description?: string;
    website?: string;
    email?: string;
    phone?: string;
    address?: Address;
    contactPerson?: ContactPerson;
    services?: Omit<VendorService, 'id'>[];
    isActive?: boolean;
}
interface UpdateVendorDto {
    name?: string;
    description?: string;
    website?: string;
    email?: string;
    phone?: string;
    address?: Address;
    contactPerson?: ContactPerson;
    services?: VendorService[];
    rating?: number;
    isActive?: boolean;
    isVerified?: boolean;
}
interface VendorQueryParams extends BaseQueryParams {
    category?: string;
    isActive?: boolean;
    isVerified?: boolean;
    minRating?: number;
    city?: string;
    country?: string;
}
interface Email {
    id: string;
    from: string;
    to: string[];
    cc?: string[];
    bcc?: string[];
    subject: string;
    body: string;
    isHtml: boolean;
    attachments?: EmailAttachment[];
    status: EmailStatus;
    sentAt?: Date;
    deliveredAt?: Date;
    openedAt?: Date;
    clickedAt?: Date;
    errorMessage?: string;
    templateId?: string;
    templateData?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
}
interface EmailAttachment {
    id: string;
    filename: string;
    contentType: string;
    size: number;
    url?: string;
    data?: string;
}
declare enum EmailStatus {
    DRAFT = "draft",
    QUEUED = "queued",
    SENDING = "sending",
    SENT = "sent",
    DELIVERED = "delivered",
    OPENED = "opened",
    CLICKED = "clicked",
    BOUNCED = "bounced",
    FAILED = "failed"
}
interface SendEmailDto {
    to: string[];
    cc?: string[];
    bcc?: string[];
    subject: string;
    body: string;
    isHtml?: boolean;
    attachments?: Omit<EmailAttachment, 'id'>[];
    templateId?: string;
    templateData?: Record<string, any>;
    scheduledAt?: Date;
}
interface EmailQueryParams extends BaseQueryParams {
    status?: EmailStatus;
    from?: string;
    to?: string;
    sentAfter?: string;
    sentBefore?: string;
    templateId?: string;
}
interface Win {
    id: string;
    title: string;
    description: string;
    category: WinCategory;
    impact: WinImpact;
    author: User;
    participants?: User[];
    tags: string[];
    metrics?: WinMetric[];
    attachments?: WinAttachment[];
    isPublic: boolean;
    isFeatured: boolean;
    likes: number;
    views: number;
    achievedAt: Date;
    createdAt: Date;
    updatedAt: Date;
}
declare enum WinCategory {
    PERSONAL = "personal",
    TEAM = "team",
    PROJECT = "project",
    PROCESS = "process",
    INNOVATION = "innovation",
    CUSTOMER = "customer",
    FINANCIAL = "financial",
    OTHER = "other"
}
declare enum WinImpact {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    CRITICAL = "critical"
}
interface WinMetric {
    id: string;
    name: string;
    value: number;
    unit: string;
    description?: string;
}
interface WinAttachment {
    id: string;
    filename: string;
    contentType: string;
    size: number;
    url: string;
}
interface CreateWinDto {
    title: string;
    description: string;
    category: WinCategory;
    impact: WinImpact;
    participantIds?: string[];
    tags?: string[];
    metrics?: Omit<WinMetric, 'id'>[];
    attachments?: Omit<WinAttachment, 'id'>[];
    isPublic?: boolean;
    achievedAt?: Date;
}
interface UpdateWinDto {
    title?: string;
    description?: string;
    category?: WinCategory;
    impact?: WinImpact;
    participantIds?: string[];
    tags?: string[];
    metrics?: WinMetric[];
    attachments?: WinAttachment[];
    isPublic?: boolean;
    isFeatured?: boolean;
    achievedAt?: Date;
}
interface WinQueryParams extends BaseQueryParams {
    category?: WinCategory;
    impact?: WinImpact;
    authorId?: string;
    participantId?: string;
    isPublic?: boolean;
    isFeatured?: boolean;
    achievedAfter?: string;
    achievedBefore?: string;
    tags?: string[];
}
interface UsersService {
    getUsers(params?: UserQueryParams): Promise<ApiResponse<User[]>>;
    getUser(id: string): Promise<ApiResponse<User>>;
    createUser(user: CreateUserDto): Promise<ApiResponse<User>>;
    updateUser(id: string, user: UpdateUserDto): Promise<ApiResponse<User>>;
    deleteUser(id: string): Promise<ApiResponse<void>>;
    getUserRoles(id: string): Promise<ApiResponse<Role[]>>;
    updateUserRoles(id: string, roleIds: string[]): Promise<ApiResponse<User>>;
    getUserPermissions(id: string): Promise<ApiResponse<Permission[]>>;
}
interface TrainingService {
    getTrainings(params?: TrainingQueryParams): Promise<ApiResponse<Training[]>>;
    getTraining(id: string): Promise<ApiResponse<Training>>;
    createTraining(training: CreateTrainingDto): Promise<ApiResponse<Training>>;
    updateTraining(id: string, training: UpdateTrainingDto): Promise<ApiResponse<Training>>;
    deleteTraining(id: string): Promise<ApiResponse<void>>;
    enrollInTraining(trainingId: string, userId: string): Promise<ApiResponse<void>>;
    unenrollFromTraining(trainingId: string, userId: string): Promise<ApiResponse<void>>;
    getTrainingParticipants(id: string): Promise<ApiResponse<User[]>>;
    getTrainingCategories(): Promise<ApiResponse<TrainingCategory[]>>;
}
interface VendorsService {
    getVendors(params?: VendorQueryParams): Promise<ApiResponse<Vendor[]>>;
    getVendor(id: string): Promise<ApiResponse<Vendor>>;
    createVendor(vendor: CreateVendorDto): Promise<ApiResponse<Vendor>>;
    updateVendor(id: string, vendor: UpdateVendorDto): Promise<ApiResponse<Vendor>>;
    deleteVendor(id: string): Promise<ApiResponse<void>>;
    rateVendor(id: string, rating: number): Promise<ApiResponse<Vendor>>;
    verifyVendor(id: string): Promise<ApiResponse<Vendor>>;
}
interface EmailService {
    getEmails(params?: EmailQueryParams): Promise<ApiResponse<Email[]>>;
    getEmail(id: string): Promise<ApiResponse<Email>>;
    sendEmail(email: SendEmailDto): Promise<ApiResponse<Email>>;
    scheduleEmail(email: SendEmailDto): Promise<ApiResponse<Email>>;
    cancelScheduledEmail(id: string): Promise<ApiResponse<void>>;
    getEmailTemplates(): Promise<ApiResponse<any[]>>;
    getEmailStats(id: string): Promise<ApiResponse<any>>;
}
interface WinsService {
    getWins(params?: WinQueryParams): Promise<ApiResponse<Win[]>>;
    getWin(id: string): Promise<ApiResponse<Win>>;
    createWin(win: CreateWinDto): Promise<ApiResponse<Win>>;
    updateWin(id: string, win: UpdateWinDto): Promise<ApiResponse<Win>>;
    deleteWin(id: string): Promise<ApiResponse<void>>;
    likeWin(id: string): Promise<ApiResponse<Win>>;
    unlikeWin(id: string): Promise<ApiResponse<Win>>;
    featureWin(id: string): Promise<ApiResponse<Win>>;
    unfeatureWin(id: string): Promise<ApiResponse<Win>>;
}

/**
 * Users Service - User management operations
 */

declare class UsersServiceImpl implements UsersService {
    private apiClient;
    private basePath;
    constructor(apiClient: LuminarAPIClient);
    getUsers(params?: UserQueryParams): Promise<ApiResponse<User[]>>;
    getUser(id: string): Promise<ApiResponse<User>>;
    createUser(user: CreateUserDto): Promise<ApiResponse<User>>;
    updateUser(id: string, user: UpdateUserDto): Promise<ApiResponse<User>>;
    deleteUser(id: string): Promise<ApiResponse<void>>;
    getUserRoles(id: string): Promise<ApiResponse<Role[]>>;
    updateUserRoles(id: string, roleIds: string[]): Promise<ApiResponse<User>>;
    getUserPermissions(id: string): Promise<ApiResponse<Permission[]>>;
    searchUsers(query: string, limit?: number): Promise<ApiResponse<User[]>>;
    getUsersByRole(roleId: string): Promise<ApiResponse<User[]>>;
    getActiveUsers(): Promise<ApiResponse<User[]>>;
    deactivateUser(id: string): Promise<ApiResponse<User>>;
    activateUser(id: string): Promise<ApiResponse<User>>;
    private validateCreateUserDto;
    private isValidEmail;
}
declare function createUsersService(apiClient: LuminarAPIClient): UsersService;

/**
 * Training Service - Training management operations
 */

declare class TrainingServiceImpl implements TrainingService {
    private apiClient;
    private basePath;
    constructor(apiClient: LuminarAPIClient);
    getTrainings(params?: TrainingQueryParams): Promise<ApiResponse<Training[]>>;
    getTraining(id: string): Promise<ApiResponse<Training>>;
    createTraining(training: CreateTrainingDto): Promise<ApiResponse<Training>>;
    updateTraining(id: string, training: UpdateTrainingDto): Promise<ApiResponse<Training>>;
    deleteTraining(id: string): Promise<ApiResponse<void>>;
    enrollInTraining(trainingId: string, userId: string): Promise<ApiResponse<void>>;
    unenrollFromTraining(trainingId: string, userId: string): Promise<ApiResponse<void>>;
    getTrainingParticipants(id: string): Promise<ApiResponse<User[]>>;
    getTrainingCategories(): Promise<ApiResponse<TrainingCategory[]>>;
    getPublicTrainings(): Promise<ApiResponse<Training[]>>;
    getTrainingsByInstructor(instructorId: string): Promise<ApiResponse<Training[]>>;
    getTrainingsByCategory(categoryId: string): Promise<ApiResponse<Training[]>>;
    getTrainingsByDifficulty(difficulty: TrainingDifficulty): Promise<ApiResponse<Training[]>>;
    searchTrainings(query: string, limit?: number): Promise<ApiResponse<Training[]>>;
    getUpcomingTrainings(): Promise<ApiResponse<Training[]>>;
    getCompletedTrainings(): Promise<ApiResponse<Training[]>>;
    publishTraining(id: string): Promise<ApiResponse<Training>>;
    scheduleTraining(id: string, startDate: Date, endDate?: Date): Promise<ApiResponse<Training>>;
    startTraining(id: string): Promise<ApiResponse<Training>>;
    completeTraining(id: string): Promise<ApiResponse<Training>>;
    cancelTraining(id: string): Promise<ApiResponse<Training>>;
    archiveTraining(id: string): Promise<ApiResponse<Training>>;
    private validateCreateTrainingDto;
}
declare function createTrainingService(apiClient: LuminarAPIClient): TrainingService;

/**
 * Vendors Service - Vendor management operations
 */

declare class VendorsServiceImpl implements VendorsService {
    private apiClient;
    private basePath;
    constructor(apiClient: LuminarAPIClient);
    getVendors(params?: VendorQueryParams): Promise<ApiResponse<Vendor[]>>;
    getVendor(id: string): Promise<ApiResponse<Vendor>>;
    createVendor(vendor: CreateVendorDto): Promise<ApiResponse<Vendor>>;
    updateVendor(id: string, vendor: UpdateVendorDto): Promise<ApiResponse<Vendor>>;
    deleteVendor(id: string): Promise<ApiResponse<void>>;
    rateVendor(id: string, rating: number): Promise<ApiResponse<Vendor>>;
    verifyVendor(id: string): Promise<ApiResponse<Vendor>>;
    searchVendors(query: string, limit?: number): Promise<ApiResponse<Vendor[]>>;
    getActiveVendors(): Promise<ApiResponse<Vendor[]>>;
    getVerifiedVendors(): Promise<ApiResponse<Vendor[]>>;
    getVendorsByCategory(category: string): Promise<ApiResponse<Vendor[]>>;
    getTopRatedVendors(minRating?: number): Promise<ApiResponse<Vendor[]>>;
    getVendorsByLocation(city?: string, country?: string): Promise<ApiResponse<Vendor[]>>;
    deactivateVendor(id: string): Promise<ApiResponse<Vendor>>;
    activateVendor(id: string): Promise<ApiResponse<Vendor>>;
    private validateCreateVendorDto;
    private isValidEmail;
    private isValidUrl;
}
declare function createVendorsService(apiClient: LuminarAPIClient): VendorsService;

/**
 * Email Service - Email management and sending operations
 */

declare class EmailServiceImpl implements EmailService {
    private apiClient;
    private basePath;
    constructor(apiClient: LuminarAPIClient);
    getEmails(params?: EmailQueryParams): Promise<ApiResponse<Email[]>>;
    getEmail(id: string): Promise<ApiResponse<Email>>;
    sendEmail(email: SendEmailDto): Promise<ApiResponse<Email>>;
    scheduleEmail(email: SendEmailDto): Promise<ApiResponse<Email>>;
    cancelScheduledEmail(id: string): Promise<ApiResponse<void>>;
    getEmailTemplates(): Promise<ApiResponse<any[]>>;
    getEmailStats(id: string): Promise<ApiResponse<any>>;
    getSentEmails(): Promise<ApiResponse<Email[]>>;
    getFailedEmails(): Promise<ApiResponse<Email[]>>;
    getScheduledEmails(): Promise<ApiResponse<Email[]>>;
    getEmailsByTemplate(templateId: string): Promise<ApiResponse<Email[]>>;
    searchEmails(query: string, limit?: number): Promise<ApiResponse<Email[]>>;
    getEmailsByRecipient(recipient: string): Promise<ApiResponse<Email[]>>;
    getEmailsBySender(sender: string): Promise<ApiResponse<Email[]>>;
    resendEmail(id: string): Promise<ApiResponse<Email>>;
    sendBulkEmail(emails: SendEmailDto[]): Promise<ApiResponse<Email[]>>;
    sendTemplateEmail(templateId: string, to: string[], templateData: Record<string, any>, options?: Partial<SendEmailDto>): Promise<ApiResponse<Email>>;
    private validateSendEmailDto;
    private isValidEmail;
}
declare function createEmailService(apiClient: LuminarAPIClient): EmailService;

/**
 * Wins Service - Wins tracking and management operations
 */

declare class WinsServiceImpl implements WinsService {
    private apiClient;
    private basePath;
    constructor(apiClient: LuminarAPIClient);
    getWins(params?: WinQueryParams): Promise<ApiResponse<Win[]>>;
    getWin(id: string): Promise<ApiResponse<Win>>;
    createWin(win: CreateWinDto): Promise<ApiResponse<Win>>;
    updateWin(id: string, win: UpdateWinDto): Promise<ApiResponse<Win>>;
    deleteWin(id: string): Promise<ApiResponse<void>>;
    likeWin(id: string): Promise<ApiResponse<Win>>;
    unlikeWin(id: string): Promise<ApiResponse<Win>>;
    featureWin(id: string): Promise<ApiResponse<Win>>;
    unfeatureWin(id: string): Promise<ApiResponse<Win>>;
    getPublicWins(): Promise<ApiResponse<Win[]>>;
    getFeaturedWins(): Promise<ApiResponse<Win[]>>;
    getWinsByCategory(category: WinCategory): Promise<ApiResponse<Win[]>>;
    getWinsByImpact(impact: WinImpact): Promise<ApiResponse<Win[]>>;
    getWinsByAuthor(authorId: string): Promise<ApiResponse<Win[]>>;
    getWinsByParticipant(participantId: string): Promise<ApiResponse<Win[]>>;
    searchWins(query: string, limit?: number): Promise<ApiResponse<Win[]>>;
    getRecentWins(days?: number): Promise<ApiResponse<Win[]>>;
    getTopWins(limit?: number): Promise<ApiResponse<Win[]>>;
    getWinsByTags(tags: string[]): Promise<ApiResponse<Win[]>>;
    getHighImpactWins(): Promise<ApiResponse<Win[]>>;
    getCriticalImpactWins(): Promise<ApiResponse<Win[]>>;
    getTeamWins(): Promise<ApiResponse<Win[]>>;
    getPersonalWins(): Promise<ApiResponse<Win[]>>;
    getProjectWins(): Promise<ApiResponse<Win[]>>;
    getInnovationWins(): Promise<ApiResponse<Win[]>>;
    incrementWinViews(id: string): Promise<ApiResponse<Win>>;
    private validateCreateWinDto;
}
declare function createWinsService(apiClient: LuminarAPIClient): WinsService;

declare function createAllServices(apiClient: LuminarAPIClient): {
    users: UsersService;
    training: TrainingService;
    vendors: VendorsService;
    email: EmailService;
    wins: WinsService;
};
interface ServiceConfig {
    apiClient: LuminarAPIClient;
    enableLogging?: boolean;
    enableCaching?: boolean;
}
declare function createConfiguredServices(config: ServiceConfig): {
    users: UsersService;
    training: TrainingService;
    vendors: VendorsService;
    email: EmailService;
    wins: WinsService;
};

export { type Address, type ApiError, ApiErrorHandler, type ApiMetadata, type ApiResponse, type AuthInterceptorConfig, type AuthTokenManager, AuthenticationInterceptor, type BaseQueryParams, type ContactPerson, type CreateTrainingDto, type CreateUserDto, type CreateVendorDto, type CreateWinDto, type Email, type EmailAttachment, type EmailQueryParams, type EmailService, EmailServiceImpl, EmailStatus, type ErrorContext, ErrorType, LocalStorageTokenManager, type LuminarAPIClient, type LuminarAPIClientConfig, LuminarAPIClientImpl, type Permission, type RequestConfig, type RequestInterceptor, type ResponseInterceptor, type RetryConfig, type Role, type SendEmailDto, type ServiceConfig, type Training, type TrainingCategory, TrainingDifficulty, type TrainingQueryParams, type TrainingService, TrainingServiceImpl, TrainingStatus, type UpdateTrainingDto, type UpdateUserDto, type UpdateVendorDto, type UpdateWinDto, type User, type UserQueryParams, type UsersService, UsersServiceImpl, type Vendor, type VendorQueryParams, type VendorService, type VendorsService, VendorsServiceImpl, type Win, type WinAttachment, WinCategory, WinImpact, type WinMetric, type WinQueryParams, type WinsService, WinsServiceImpl, createAllServices, createApiError, createAuthInterceptor, createConfiguredAPIClient, createConfiguredServices, createEmailService, createLuminarAPIClient, createTrainingService, createUsersService, createVendorsService, createWinsService, getDefaultAPIClient, initializeDefaultAPIClient, isApiError, setDefaultAPIClient };
