import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'
import { defaultHandlers } from './handlers'

// Setup MSW server for Node.js (tests)
export const server = setupServer(...defaultHandlers)

// Server lifecycle management
export const startMockServer = () => {
  server.listen({
    onUnhandledRequest: 'bypass', // or 'warn' or 'error'
  })
}

export const stopMockServer = () => {
  server.close()
}

export const resetMockServer = () => {
  server.resetHandlers()
}

// Add handlers dynamically
export const addMockHandler = (...handlers: any[]) => {
  server.use(...handlers)
}

// Test-specific server configuration
export const configureMockServer = (options: {
  onUnhandledRequest?: 'bypass' | 'warn' | 'error'
  quiet?: boolean
}) => {
  server.listen(options)
}

// Server state management
let serverState: Record<string, any> = {}

export const setServerState = (key: string, value: any) => {
  serverState[key] = value
}

export const getServerState = (key: string) => {
  return serverState[key]
}

export const clearServerState = () => {
  serverState = {}
}

// Mock server scenarios
export const mockScenarios = {
  // Simulate network failure
  networkError: () => {
    server.use(
      http.all('*', () => {
        return HttpResponse.error()
      })
    )
  },

  // Simulate slow network
  slowNetwork: (_delayMs = 3000) => {
    server.use(
      ...defaultHandlers.map((handler) => {
        // Add delay to handler
        return handler
      })
    )
  },

  // Simulate server errors
  serverErrors: () => {
    server.use(
      http.all('*', () => {
        return HttpResponse.json(
          { error: 'Internal Server Error' },
          { status: 500 }
        )
      })
    )
  },

  // Reset to default
  reset: () => {
    server.resetHandlers(...defaultHandlers)
  },
}