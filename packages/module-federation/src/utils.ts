/**
 * Utility functions for Module Federation
 */

/**
 * Dynamically import a remote module with retry logic
 */
export async function loadRemoteModule<T = any>(
  remoteName: string,
  modulePath: string = './Module',
  retries: number = 3,
  retryDelay: number = 1000
): Promise<T> {
  let lastError: Error | null = null;

  for (let i = 0; i < retries; i++) {
    try {
      // @ts-ignore - Module federation runtime
      const container = await window[remoteName];
      if (!container) {
        throw new Error(`Remote "${remoteName}" not found`);
      }

      // Initialize the container
      // @ts-ignore - webpack runtime global
      const shareScope = window.__webpack_share_scopes__?.default || {};
      await container.init(shareScope);
      
      // Get the module factory
      const factory = await container.get(modulePath);
      if (!factory) {
        throw new Error(`Module "${modulePath}" not found in remote "${remoteName}"`);
      }

      // Execute the factory to get the module
      const Module = factory();
      return Module;
    } catch (error) {
      lastError = error as Error;
      console.error(`Failed to load remote module ${remoteName}${modulePath}:`, error);
      
      if (i < retries - 1) {
        await new Promise(resolve => setTimeout(resolve, retryDelay * (i + 1)));
      }
    }
  }

  throw lastError || new Error(`Failed to load remote module after ${retries} attempts`);
}

/**
 * Check if a remote is available
 */
export async function isRemoteAvailable(
  remoteName: string,
  timeout: number = 5000
): Promise<boolean> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    // @ts-ignore
    const remoteUrl = window.__FEDERATION__?.remotes?.[remoteName];
    if (!remoteUrl) {
      return false;
    }

    const response = await fetch(remoteUrl, {
      method: 'HEAD',
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.error(`Remote ${remoteName} health check failed:`, error);
    return false;
  }
}

/**
 * Preload a remote module
 */
export async function preloadRemote(
  remoteName: string,
  modulePath?: string
): Promise<void> {
  try {
    await loadRemoteModule(remoteName, modulePath, 1, 0);
  } catch (error) {
    console.warn(`Failed to preload remote ${remoteName}:`, error);
  }
}

/**
 * Create a script tag to load a remote
 */
export function loadRemoteScript(
  url: string,
  remoteName: string
): Promise<void> {
  return new Promise((resolve, reject) => {
    const existingScript = document.querySelector(`script[data-remote="${remoteName}"]`);
    if (existingScript) {
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.src = url;
    script.type = 'module';
    script.async = true;
    script.setAttribute('data-remote', remoteName);

    script.onload = () => resolve();
    script.onerror = () => reject(new Error(`Failed to load remote script: ${url}`));

    document.head.appendChild(script);
  });
}

/**
 * Version compatibility checker
 */
export function checkVersionCompatibility(
  required: string,
  provided: string
): boolean {
  // Simple semver check - can be enhanced
  const parseVersion = (v: string) => {
    const [major, minor, patch] = v.replace(/[^\d.]/g, '').split('.').map(Number);
    return { major, minor: minor || 0, patch: patch || 0 };
  };

  const req = parseVersion(required);
  const prov = parseVersion(provided);

  // Major version must match
  if (req.major !== prov.major) return false;
  
  // Minor version must be compatible
  if (prov.minor < req.minor) return false;
  
  // If minor versions match, patch must be compatible
  if (prov.minor === req.minor && prov.patch < req.patch) return false;
  
  return true;
}

/**
 * Get shared scope
 */
export function getSharedScope(): any {
  // @ts-ignore
  return window.__webpack_share_scopes__?.default || {};
}

/**
 * Initialize shared scope
 */
export function initializeSharedScope(scope: Record<string, any>): void {
  // @ts-ignore
  if (!window.__webpack_share_scopes__) {
    // @ts-ignore
    window.__webpack_share_scopes__ = {};
  }
  // @ts-ignore
  window.__webpack_share_scopes__.default = {
    // @ts-ignore
    ...window.__webpack_share_scopes__.default,
    ...scope,
  };
}