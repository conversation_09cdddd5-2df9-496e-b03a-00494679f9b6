{"version": 3, "sources": ["../src/index.ts", "../src/test-utils.tsx", "../src/mock-server.ts", "../src/component-test-patterns.tsx"], "names": ["afterAll", "after<PERSON>ach", "beforeAll", "beforeEach", "describe", "expect", "it", "default", "vi", "react_star", "user_event_star", "QueryClient", "createRootRoute", "jsx", "createRoute", "Fragment", "createRouter", "QueryClientProvider", "RouterProvider", "userEvent", "render", "React", "http", "HttpResponse", "setupServer", "screen", "waitFor"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAA,WAAA,GAAA,EAAA;AAAA,QAAA,CAAA,WAAA,EAAA;AAAA,EAAA,aAAA,EAAA,MAAA,aAAA;AAAA,EAAA,gBAAA,EAAA,MAAA,gBAAA;AAAA,EAAA,aAAA,EAAA,MAAA,aAAA;AAAA,EAAAA,QAAAA,EAAAA,MAAAA,eAAAA;AAAA,EAAA,SAAAC,EAAAA,MAAAA,gBAAAA;AAAA,EAAA,SAAAC,EAAAA,MAAAA,gBAAAA;AAAA,EAAA,UAAA,EAAA,MAAAC,iBAAA;AAAA,EAAA,kBAAA,EAAA,MAAA,kBAAA;AAAA,EAAA,kBAAA,EAAA,MAAA,kBAAA;AAAA,EAAA,qBAAA,EAAA,MAAA,qBAAA;AAAA,EAAA,mBAAA,EAAA,MAAA,mBAAA;AAAA,EAAA,sBAAA,EAAA,MAAA,sBAAA;AAAA,EAAA,qBAAA,EAAA,MAAA,qBAAA;AAAA,EAAA,cAAA,EAAA,MAAA,cAAA;AAAA,EAAA,QAAA,EAAA,MAAAC,eAAA;AAAA,EAAAC,MAAAA,EAAAA,MAAAA,aAAAA;AAAA,EAAA,QAAA,EAAA,MAAA,QAAA;AAAA,EAAA,uBAAA,EAAA,MAAA,uBAAA;AAAA,EAAAC,EAAAA,EAAAA,MAAAA,SAAAA;AAAA,EAAA,YAAA,EAAA,MAAA,YAAA;AAAA,EAAA,eAAA,EAAA,MAAA,eAAA;AAAA,EAAA,uBAAA,EAAA,MAAA,uBAAA;AAAA,EAAA,oBAAA,EAAA,MAAA,oBAAA;AAAA,EAAA,mBAAA,EAAA,MAAA,mBAAA;AAAA,EAAA,MAAA,EAAA,MAAA,MAAA;AAAA,EAAA,eAAA,EAAA,MAAA,eAAA;AAAA,EAAA,eAAA,EAAA,MAAA,eAAA;AAAA,EAAAC,SAAAA,EAAAA,MAAAA,6BAAAA;AAAA,EAAA,EAAAC,EAAAA,MAAAA;AAAA,CAAA,CAAA;;;ACAA,IAAA,kBAAA,GAAA,EAAA;AAAA,QAAA,CAAA,kBAAA,EAAA;AAAA,EAAA,aAAA,EAAA,MAAA,aAAA;AAAA,EAAA,aAAA,EAAA,MAAA,aAAA;AAAA,EAAA,kBAAA,EAAA,MAAA,kBAAA;AAAA,EAAA,mBAAA,EAAA,MAAA,mBAAA;AAAA,EAAA,sBAAA,EAAA,MAAA,sBAAA;AAAA,EAAA,qBAAA,EAAA,MAAA,qBAAA;AAAA,EAAA,cAAA,EAAA,MAAA,cAAA;AAAA,EAAA,YAAA,EAAA,MAAA,YAAA;AAAA,EAAA,oBAAA,EAAA,MAAA,oBAAA;AAAA,EAAA,mBAAA,EAAA,MAAA,mBAAA;AAAA,EAAA,eAAA,EAAA,MAAA,eAAA;AAAA,EAAAD,SAAAA,EAAAA,MAAAA,6BAAAA;AAAA,EAAA,EAAAC,EAAAA,MAAAA;AAAA,CAAA,CAAA;AAmZA,UAAA,CAAA,kBAAA,EAAAC,qBAAA,CAAA;AACA,UAAA,CAAA,kBAAA,EAAAC,qBAAA,CAAA;AAhYO,SAAS,qBAAqC,GAAA;AACnD,EAAA,OAAO,IAAIC,sBAAY,CAAA;AAAA,IACrB,cAAgB,EAAA;AAAA,MACd,OAAS,EAAA;AAAA,QACP,KAAO,EAAA,KAAA;AAAA,QACP,MAAQ,EAAA;AAAA,OACV;AAAA,MACA,SAAW,EAAA;AAAA,QACT,KAAO,EAAA;AAAA;AACT;AACF,GACD,CAAA;AACH;AAGO,SAAS,aAAc,CAAA;AAAA,EAC5B,QAAA;AAAA,EACA,cAAc,qBAAsB;AACtC,CAAuB,EAAA;AAErB,EAAA,MAAM,YAAYC,2BAAgB,CAAA;AAAA,IAChC,SAAW,EAAA,sBAAOC,cAAA,CAAA,KAAA,EAAA,EAAK,QAAS,EAAA;AAAA,GACjC,CAAA;AAED,EAAA,MAAM,aAAaC,uBAAY,CAAA;AAAA,IAC7B,gBAAgB,MAAM,SAAA;AAAA,IACtB,IAAM,EAAA,GAAA;AAAA,IACN,SAAA,EAAW,sBAAMD,cAAA,CAAAE,mBAAA,EAAA,EAAG,QAAS,EAAA;AAAA,GAC9B,CAAA;AAED,EAAA,MAAM,SAAY,GAAA,SAAA,CAAU,WAAY,CAAA,CAAC,UAAU,CAAC,CAAA;AAEpD,EAAA,MAAM,SAASC,wBAAa,CAAA;AAAA,IAC1B,SAAA;AAAA,IACA,cAAgB,EAAA;AAAA,GACjB,CAAA;AAED,EAAA,sCACGC,8BAAoB,EAAA,EAAA,MAAA,EAAQ,aAC3B,QAAC,kBAAAJ,cAAA,CAAAK,0BAAA,EAAA,EAAe,QAAgB,CAClC,EAAA,CAAA;AAEJ;AAUO,SAAS,mBACd,CAAA,EAAA,EACA,OAA+B,GAAA,EAC8B,EAAA;AAC7D,EAAM,MAAA;AAAA,IACJ,WAAA;AAAA,IACA,cAAA;AAAA,IACA,WAAA;AAAA,IACA,aAAgB,GAAA,IAAA;AAAA,IAChB,GAAG;AAAA,GACD,GAAA,OAAA;AAEJ,EAAM,MAAA,IAAA,GAAOC,8BAAU,KAAM,EAAA;AAE7B,EAAA,MAAM,OAAU,GAAA,aAAA,GACZ,CAAC,EAAE,UACD,qBAAAN,cAAA;AAAA,IAAC,aAAA;AAAA,IAAA;AAAA,MACC,WAAA,EAAa,eAAe,qBAAsB,EAAA;AAAA,MAClD,cAAA,EAAgB,kBAAkB,EAAC;AAAA,MACnC,WAAA;AAAA,MAEC;AAAA;AAAA,GAGL,GAAA,MAAA;AAEJ,EAAM,MAAA,MAAA,GAASO,kBAAO,EAAI,EAAA;AAAA,IACxB,OAAS,EAAA,OAAA;AAAA,IACT,GAAG;AAAA,GACJ,CAAA;AAED,EAAO,OAAA;AAAA,IACL,GAAG,MAAA;AAAA,IACH;AAAA,GACF;AACF;AAkBO,SAAS,mBAAqC,GAAA;AACnD,EAAO,OAAA;AAAA,IACL,GAAA,EAAKZ,UAAG,EAAG,EAAA;AAAA,IACX,IAAA,EAAMA,UAAG,EAAG,EAAA;AAAA,IACZ,GAAA,EAAKA,UAAG,EAAG,EAAA;AAAA,IACX,KAAA,EAAOA,UAAG,EAAG,EAAA;AAAA,IACb,MAAA,EAAQA,UAAG,EAAG,EAAA;AAAA,IACd,qBAAA,EAAuBA,UAAG,EAAG,EAAA;AAAA,IAC7B,sBAAA,EAAwBA,UAAG,EAAG,EAAA;AAAA,IAC9B,wBAAA,EAA0BA,UAAG,EAAG,EAAA;AAAA,IAChC,yBAAA,EAA2BA,UAAG,EAAG,EAAA;AAAA,IACjC,UAAA,EAAYA,UAAG,EAAG,EAAA;AAAA,IAClB,iBAAA,EAAmBA,UAAG,EAAG,EAAA;AAAA,IACzB,SAAA,EAAWA,UAAG,EAAG;AAAA,GACnB;AACF;AAUO,SAAS,sBAAuB,CAAA,YAAA,GAA8B,EAAE,eAAA,EAAiB,OAAS,EAAA;AAC/F,EAAM,MAAA,WAAA,GAAca,sBAAM,CAAA,aAAA,CAAc,YAAY,CAAA;AAEpD,EAAA,MAAM,gBAAmB,GAAA,CAAC,EAAE,QAAA,EAAU,KAAQ,GAAA,YAAA,EAI5C,qBAAAR,cAAA,CAAC,WAAY,CAAA,QAAA,EAAZ,EAAqB,KAAA,EAAO,OAC1B,QACH,EAAA,CAAA;AAGF,EAAA,MAAM,OAAU,GAAA,MAAMQ,sBAAM,CAAA,UAAA,CAAW,WAAW,CAAA;AAElD,EAAO,OAAA;AAAA,IACL,gBAAA;AAAA,IACA,OAAA;AAAA,IACA;AAAA,GACF;AACF;AAGO,IAAM,eAAkB,GAAA;AAAA;AAAA,EAE7B,UAAY,EAAA,CAAC,SAA0B,GAAA,EAAQ,MAAA;AAAA,IAC7C,EAAI,EAAA,GAAA;AAAA,IACJ,IAAM,EAAA,WAAA;AAAA,IACN,KAAO,EAAA,kBAAA;AAAA,IACP,KAAA,EAAO,CAAC,MAAM,CAAA;AAAA,IACd,WAAA,EAAa,CAAC,MAAM,CAAA;AAAA,IACpB,QAAU,EAAA,IAAA;AAAA,IACV,SAAA,sBAAe,IAAK,EAAA;AAAA,IACpB,SAAA,sBAAe,IAAK,EAAA;AAAA,IACpB,GAAG;AAAA,GACL,CAAA;AAAA;AAAA,EAGA,cAAgB,EAAA,CAAC,SAA0B,GAAA,EAAQ,MAAA;AAAA,IACjD,EAAI,EAAA,GAAA;AAAA,IACJ,KAAO,EAAA,eAAA;AAAA,IACP,WAAa,EAAA,2BAAA;AAAA,IACb,QAAU,EAAA,EAAE,EAAI,EAAA,GAAA,EAAK,MAAM,eAAgB,EAAA;AAAA,IAC3C,QAAU,EAAA,EAAA;AAAA,IACV,UAAY,EAAA,UAAA;AAAA,IACZ,UAAA,EAAY,gBAAgB,UAAW,CAAA,EAAE,IAAI,GAAK,EAAA,IAAA,EAAM,cAAc,CAAA;AAAA,IACtE,cAAc,EAAC;AAAA,IACf,MAAQ,EAAA,WAAA;AAAA,IACR,QAAU,EAAA,IAAA;AAAA,IACV,SAAA,sBAAe,IAAK,EAAA;AAAA,IACpB,SAAA,sBAAe,IAAK,EAAA;AAAA,IACpB,GAAG;AAAA,GACL,CAAA;AAAA;AAAA,EAGA,iBAAmB,EAAA,CAAK,IAAS,EAAA,SAAA,GAA0B,EAAQ,MAAA;AAAA,IACjE,IAAA;AAAA,IACA,MAAQ,EAAA,GAAA;AAAA,IACR,UAAY,EAAA,IAAA;AAAA,IACZ,OAAS,EAAA,SAAA;AAAA,IACT,GAAG;AAAA,GACL,CAAA;AAAA;AAAA,EAGA,cAAgB,EAAA,CAAC,SAA0B,GAAA,EAAQ,MAAA;AAAA,IACjD,OAAS,EAAA,YAAA;AAAA,IACT,IAAM,EAAA,YAAA;AAAA,IACN,OAAS,EAAA;AAAA,MACP,IAAM,EAAA,cAAA;AAAA,MACN,MAAQ,EAAA,GAAA;AAAA,MACR,SAAA,sBAAe,IAAK;AAAA,KACtB;AAAA,IACA,GAAG;AAAA,GACL;AACF;AAGO,IAAM,YAAe,GAAA;AAAA;AAAA,EAE1B,OAAA,EAAS,CAAK,IAAa,MAAA;AAAA,IACzB,MAAQ,EAAA,GAAA;AAAA,IACR,IAAA,EAAM,eAAgB,CAAA,iBAAA,CAAkB,IAAI;AAAA,GAC9C,CAAA;AAAA;AAAA,EAGA,KAAO,EAAA,CAAC,MAAS,GAAA,GAAA,EAAK,UAAU,YAAkB,MAAA;AAAA,IAChD,MAAA;AAAA,IACA,IAAM,EAAA,eAAA,CAAgB,cAAe,CAAA,EAAE,SAAS;AAAA,GAClD,CAAA;AAAA;AAAA,EAGA,KAAA,EAAO,CAAC,EAAA,GAAK,GAAQ,KAAA,IAAI,QAAQ,CAAW,OAAA,KAAA,UAAA,CAAW,OAAS,EAAA,EAAE,CAAC;AACrE;AAGO,IAAM,kBAAqB,GAAA;AAAA;AAAA,EAEhC,gBAAA,EAAkB,OAAO,WAAA,EAA0C,MAAmB,KAAA;AACpF,IAAO,OAAA,IAAI,OAAqB,CAAA,CAAC,OAAY,KAAA;AAC3C,MAAA,MAAM,oBAAoB,MAAM;AAC9B,QAAI,IAAA;AACF,UAAM,MAAA,OAAA,GAAU,YAAY,MAAM,CAAA;AAClC,UAAA,OAAA,CAAQ,OAAO,CAAA;AAAA,SACT,CAAA,MAAA;AACN,UAAA,UAAA,CAAW,mBAAmB,EAAE,CAAA;AAAA;AAClC,OACF;AACA,MAAkB,iBAAA,EAAA;AAAA,KACnB,CAAA;AAAA,GACH;AAAA;AAAA,EAGA,oBAAA,EAAsB,CAAC,EAAA,GAAK,GAC1B,KAAA,IAAI,QAAQ,CAAW,OAAA,KAAA,UAAA,CAAW,OAAS,EAAA,EAAE,CAAC,CAAA;AAAA;AAAA,EAGhD,0BAA0B,MAAM;AAC9B,IAAM,MAAA,wBAAA,GAA2Bb,UAAG,EAAG,EAAA;AACvC,IAAA,wBAAA,CAAyB,eAAgB,CAAA;AAAA,MACvC,OAAA,EAASA,UAAG,EAAG,EAAA;AAAA,MACf,SAAA,EAAWA,UAAG,EAAG,EAAA;AAAA,MACjB,UAAA,EAAYA,UAAG,EAAG;AAAA,KACnB,CAAA;AAED,IAAO,MAAA,CAAA,cAAA,CAAe,QAAQ,sBAAwB,EAAA;AAAA,MACpD,QAAU,EAAA,IAAA;AAAA,MACV,YAAc,EAAA,IAAA;AAAA,MACd,KAAO,EAAA;AAAA,KACR,CAAA;AAED,IAAO,OAAA,wBAAA;AAAA,GACT;AAAA;AAAA,EAGA,oBAAoB,MAAM;AACxB,IAAM,MAAA,kBAAA,GAAqBA,UAAG,EAAG,EAAA;AACjC,IAAA,kBAAA,CAAmB,eAAgB,CAAA;AAAA,MACjC,OAAA,EAASA,UAAG,EAAG,EAAA;AAAA,MACf,SAAA,EAAWA,UAAG,EAAG,EAAA;AAAA,MACjB,UAAA,EAAYA,UAAG,EAAG;AAAA,KACnB,CAAA;AAED,IAAO,MAAA,CAAA,cAAA,CAAe,QAAQ,gBAAkB,EAAA;AAAA,MAC9C,QAAU,EAAA,IAAA;AAAA,MACV,YAAc,EAAA,IAAA;AAAA,MACd,KAAO,EAAA;AAAA,KACR,CAAA;AAED,IAAO,OAAA,kBAAA;AAAA;AAEX;AAGO,IAAM,aAAgB,GAAA;AAAA;AAAA,EAE3B,mBAAA,EAAqB,CAAC,OAAA,EAAsB,kBAAiC,KAAA;AAC3E,IAAA,MAAM,oBAAoB,kBAAmB,CAAA,MAAA;AAAA,MAC3C,CAAQ,IAAA,KAAA,CAAC,OAAQ,CAAA,YAAA,CAAa,IAAI;AAAA,KACpC;AAEA,IAAI,IAAA,iBAAA,CAAkB,SAAS,CAAG,EAAA;AAChC,MAAA,MAAM,IAAI,KAAM,CAAA,CAAA,yBAAA,EAA4B,kBAAkB,IAAK,CAAA,IAAI,CAAC,CAAE,CAAA,CAAA;AAAA;AAC5E,GACF;AAAA;AAAA,EAGA,uBAAA,EAAyB,OACvB,IAAA,EACA,OACG,KAAA;AACH,IAAA,OAAA,CAAQ,KAAM,EAAA;AACd,IAAO,MAAA,CAAA,OAAO,EAAE,WAAY,EAAA;AAE5B,IAAM,MAAA,IAAA,CAAK,SAAS,OAAO,CAAA;AAAA,GAE7B;AAAA;AAAA,EAGA,kBAAA,EAAoB,CAAC,OAAyB,KAAA;AAC5C,IAAM,MAAA,MAAA,GAAS,MAAO,CAAA,gBAAA,CAAiB,OAAO,CAAA;AAC9C,IAAA,MAAM,kBAAkB,MAAO,CAAA,eAAA;AAC/B,IAAA,MAAM,QAAQ,MAAO,CAAA,KAAA;AAGrB,IAAA,IAAI,oBAAoB,KAAO,EAAA;AAC7B,MAAM,MAAA,IAAI,MAAM,6BAA6B,CAAA;AAAA;AAC/C;AAEJ;AAGO,IAAM,oBAAuB,GAAA;AAAA;AAAA,EAElC,iBAAA,EAAmB,OAAO,QAAyC,KAAA;AACjE,IAAM,MAAA,KAAA,GAAQ,YAAY,GAAI,EAAA;AAC9B,IAAA,MAAM,QAAS,EAAA;AACf,IAAM,MAAA,GAAA,GAAM,YAAY,GAAI,EAAA;AAC5B,IAAA,OAAO,GAAM,GAAA,KAAA;AAAA,GACf;AAAA;AAAA,EAGA,gBAAA,EAAkB,CAAC,OAAwB,KAAA;AACzC,IAAM,MAAA,aAAA,GAAiB,WAAoB,CAAA,MAAA,EAAQ,cAAkB,IAAA,CAAA;AAErE,IAAA,OAAO,MAAM;AACX,MAAQ,OAAA,EAAA;AAGR,MAAA,IAAK,OAAe,EAAI,EAAA;AACtB,QAAC,OAAe,EAAG,EAAA;AAAA;AAGrB,MAAM,MAAA,WAAA,GAAe,WAAoB,CAAA,MAAA,EAAQ,cAAkB,IAAA,CAAA;AACnE,MAAA,MAAM,aAAa,WAAc,GAAA,aAAA;AAEjC,MAAA,IAAI,aAAa,GAAS,EAAA;AACxB,QAAQ,OAAA,CAAA,IAAA,CAAK,CAAmC,gCAAA,EAAA,UAAU,CAAQ,MAAA,CAAA,CAAA;AAAA;AACpE,KACF;AAAA;AAEJ;AAGO,IAAM,cAAiB,GAAA;AAAA;AAAA,EAE5B,cAAA,EAAgB,CAAC,OAAyB,KAAA;AACxC,IAAI,IAAA;AAEF,MAAA,MAAM,eAAe,OAAQ,CAAA,YAAA,CAAa,YAAY,CAClC,IAAA,OAAA,CAAQ,aAAa,iBAAiB,CAAA;AAC1D,MAAA,MAAM,OAAU,GAAA,OAAA,CAAQ,YAAa,CAAA,MAAM,KAC5B,CAAC,QAAA,EAAU,MAAQ,EAAA,OAAO,CAAE,CAAA,QAAA,CAAS,OAAQ,CAAA,OAAA,CAAQ,aAAa,CAAA;AAEjF,MAAI,IAAA,CAAC,YAAgB,IAAA,CAAC,OAAS,EAAA;AAC7B,QAAO,OAAA;AAAA,UACL,SAAS,MAAM,wDAAA;AAAA,UACf,IAAM,EAAA;AAAA,SACR;AAAA;AAGF,MAAO,OAAA;AAAA,QACL,SAAS,MAAM,uBAAA;AAAA,QACf,IAAM,EAAA;AAAA,OACR;AAAA,aACO,KAAO,EAAA;AACd,MAAO,OAAA;AAAA,QACL,OAAA,EAAS,MAAM,CAAA,4BAAA,EAA+B,KAAK,CAAA,CAAA;AAAA,QACnD,IAAM,EAAA;AAAA,OACR;AAAA;AACF;AAEJ;;;AD3YA,UAAc,CAAA,WAAA,EAAA,kBAAA,CAAA;AEKP,IAAM,QAAW,GAAA;AAAA;AAAA,EAEtBc,SAAK,GAAI,CAAA,YAAA,EAAc,CAAC,EAAE,SAAc,KAAA;AACtC,IAAA,MAAM,GAAM,GAAA,IAAI,GAAI,CAAA,OAAA,CAAQ,GAAG,CAAA;AAC/B,IAAA,MAAM,OAAO,QAAS,CAAA,GAAA,CAAI,aAAa,GAAI,CAAA,MAAM,KAAK,GAAG,CAAA;AACzD,IAAA,MAAM,QAAQ,QAAS,CAAA,GAAA,CAAI,aAAa,GAAI,CAAA,OAAO,KAAK,IAAI,CAAA;AAC5D,IAAA,MAAM,MAAS,GAAA,GAAA,CAAI,YAAa,CAAA,GAAA,CAAI,QAAQ,CAAA;AAE5C,IAAA,IAAI,QAAQ,KAAM,CAAA,IAAA;AAAA,MAAK,EAAE,QAAQ,EAAG,EAAA;AAAA,MAAG,CAAC,CAAA,EAAG,CACzC,KAAA,eAAA,CAAgB,UAAW,CAAA;AAAA,QACzB,EAAA,EAAI,CAAG,EAAA,CAAA,GAAI,CAAC,CAAA,CAAA;AAAA,QACZ,IAAA,EAAM,CAAQ,KAAA,EAAA,CAAA,GAAI,CAAC,CAAA,CAAA;AAAA,QACnB,KAAA,EAAO,CAAO,IAAA,EAAA,CAAA,GAAI,CAAC,CAAA,YAAA;AAAA,OACpB;AAAA,KACH;AAEA,IAAA,IAAI,MAAQ,EAAA;AACV,MAAA,KAAA,GAAQ,KAAM,CAAA,MAAA;AAAA,QAAO,UACnB,IAAK,CAAA,IAAA,CAAK,WAAY,EAAA,CAAE,SAAS,MAAO,CAAA,WAAA,EAAa,CAAA,IACrD,KAAK,KAAM,CAAA,WAAA,GAAc,QAAS,CAAA,MAAA,CAAO,aAAa;AAAA,OACxD;AAAA;AAGF,IAAM,MAAA,KAAA,GAAA,CAAS,OAAO,CAAK,IAAA,KAAA;AAC3B,IAAA,MAAM,MAAM,KAAQ,GAAA,KAAA;AACpB,IAAA,MAAM,cAAiB,GAAA,KAAA,CAAM,KAAM,CAAA,KAAA,EAAO,GAAG,CAAA;AAE7C,IAAA,OAAOC,gBAAa,CAAA,IAAA,CAAK,eAAgB,CAAA,iBAAA,CAAkB,cAAgB,EAAA;AAAA,MACzE,QAAU,EAAA;AAAA,QACR,IAAA;AAAA,QACA,KAAA;AAAA,QACA,OAAO,KAAM,CAAA,MAAA;AAAA,QACb,OAAA,EAAS,MAAM,KAAM,CAAA,MAAA;AAAA,QACrB,aAAa,IAAO,GAAA;AAAA;AACtB,KACD,CAAC,CAAA;AAAA,GACH,CAAA;AAAA,EAEDD,SAAK,GAAI,CAAA,gBAAA,EAAkB,CAAC,EAAE,QAAa,KAAA;AACzC,IAAM,MAAA,IAAA,GAAO,gBAAgB,UAAW,CAAA;AAAA,MACtC,IAAI,MAAO,CAAA,EAAA;AAAA,MACX,IAAA,EAAM,CAAQ,KAAA,EAAA,MAAA,CAAO,EAAE,CAAA,CAAA;AAAA,MACvB,KAAA,EAAO,CAAO,IAAA,EAAA,MAAA,CAAO,EAAE,CAAA,YAAA;AAAA,KACxB,CAAA;AACD,IAAA,OAAOC,gBAAa,CAAA,IAAA,CAAK,eAAgB,CAAA,iBAAA,CAAkB,IAAI,CAAC,CAAA;AAAA,GACjE,CAAA;AAAA,EAEDD,SAAK,IAAK,CAAA,YAAA,EAAc,OAAO,EAAE,SAAc,KAAA;AAC7C,IAAM,MAAA,QAAA,GAAW,MAAM,OAAA,CAAQ,IAAK,EAAA;AACpC,IAAM,MAAA,IAAA,GAAO,gBAAgB,UAAW,CAAA;AAAA,MACtC,EAAA,EAAI,KAAK,MAAO,EAAA,CAAE,SAAS,EAAE,CAAA,CAAE,MAAO,CAAA,CAAA,EAAG,CAAC,CAAA;AAAA,MAC1C,GAAG;AAAA,KACJ,CAAA;AACD,IAAO,OAAAC,gBAAA,CAAa,KAAK,eAAgB,CAAA,iBAAA,CAAkB,IAAI,CAAG,EAAA,EAAE,MAAQ,EAAA,GAAA,EAAK,CAAA;AAAA,GAClF,CAAA;AAAA,EAEDD,SAAK,GAAI,CAAA,gBAAA,EAAkB,OAAO,EAAE,MAAA,EAAQ,SAAc,KAAA;AACxD,IAAM,MAAA,QAAA,GAAW,MAAM,OAAA,CAAQ,IAAK,EAAA;AACpC,IAAM,MAAA,IAAA,GAAO,gBAAgB,UAAW,CAAA;AAAA,MACtC,IAAI,MAAO,CAAA,EAAA;AAAA,MACX,GAAG;AAAA,KACJ,CAAA;AACD,IAAA,OAAOC,gBAAa,CAAA,IAAA,CAAK,eAAgB,CAAA,iBAAA,CAAkB,IAAI,CAAC,CAAA;AAAA,GACjE,CAAA;AAAA,EAEDD,QAAA,CAAK,MAAO,CAAA,gBAAA,EAAkB,MAAM;AAClC,IAAA,OAAO,IAAIC,gBAAa,CAAA,IAAA,EAAM,EAAE,MAAA,EAAQ,KAAK,CAAA;AAAA,GAC9C,CAAA;AAAA;AAAA,EAGDD,SAAK,GAAI,CAAA,gBAAA,EAAkB,CAAC,EAAE,SAAc,KAAA;AAC1C,IAAA,MAAM,GAAM,GAAA,IAAI,GAAI,CAAA,OAAA,CAAQ,GAAG,CAAA;AAC/B,IAAA,MAAM,OAAO,QAAS,CAAA,GAAA,CAAI,aAAa,GAAI,CAAA,MAAM,KAAK,GAAG,CAAA;AACzD,IAAA,MAAM,QAAQ,QAAS,CAAA,GAAA,CAAI,aAAa,GAAI,CAAA,OAAO,KAAK,IAAI,CAAA;AAC5D,IAAA,MAAM,QAAW,GAAA,GAAA,CAAI,YAAa,CAAA,GAAA,CAAI,YAAY,CAAA;AAElD,IAAA,IAAI,YAAY,KAAM,CAAA,IAAA;AAAA,MAAK,EAAE,QAAQ,EAAG,EAAA;AAAA,MAAG,CAAC,CAAA,EAAG,CAC7C,KAAA,eAAA,CAAgB,cAAe,CAAA;AAAA,QAC7B,EAAA,EAAI,CAAG,EAAA,CAAA,GAAI,CAAC,CAAA,CAAA;AAAA,QACZ,KAAA,EAAO,CAAY,SAAA,EAAA,CAAA,GAAI,CAAC,CAAA,CAAA;AAAA,QACxB,UAAU,EAAE,EAAA,EAAI,QAAY,IAAA,GAAA,EAAK,MAAM,eAAgB;AAAA,OACxD;AAAA,KACH;AAEA,IAAA,IAAI,QAAU,EAAA;AACZ,MAAA,SAAA,GAAY,UAAU,MAAO,CAAA,CAAA,QAAA,KAAY,QAAS,CAAA,QAAA,CAAS,OAAO,QAAQ,CAAA;AAAA;AAG5E,IAAM,MAAA,KAAA,GAAA,CAAS,OAAO,CAAK,IAAA,KAAA;AAC3B,IAAA,MAAM,MAAM,KAAQ,GAAA,KAAA;AACpB,IAAA,MAAM,kBAAqB,GAAA,SAAA,CAAU,KAAM,CAAA,KAAA,EAAO,GAAG,CAAA;AAErD,IAAA,OAAOC,gBAAa,CAAA,IAAA,CAAK,eAAgB,CAAA,iBAAA,CAAkB,kBAAoB,EAAA;AAAA,MAC7E,QAAU,EAAA;AAAA,QACR,IAAA;AAAA,QACA,KAAA;AAAA,QACA,OAAO,SAAU,CAAA,MAAA;AAAA,QACjB,OAAA,EAAS,MAAM,SAAU,CAAA,MAAA;AAAA,QACzB,aAAa,IAAO,GAAA;AAAA;AACtB,KACD,CAAC,CAAA;AAAA,GACH,CAAA;AAAA,EAEDD,SAAK,GAAI,CAAA,oBAAA,EAAsB,CAAC,EAAE,QAAa,KAAA;AAC7C,IAAM,MAAA,QAAA,GAAW,gBAAgB,cAAe,CAAA;AAAA,MAC9C,IAAI,MAAO,CAAA,EAAA;AAAA,MACX,KAAA,EAAO,CAAY,SAAA,EAAA,MAAA,CAAO,EAAE,CAAA;AAAA,KAC7B,CAAA;AACD,IAAA,OAAOC,gBAAa,CAAA,IAAA,CAAK,eAAgB,CAAA,iBAAA,CAAkB,QAAQ,CAAC,CAAA;AAAA,GACrE,CAAA;AAAA,EAEDD,SAAK,IAAK,CAAA,gBAAA,EAAkB,OAAO,EAAE,SAAc,KAAA;AACjD,IAAM,MAAA,YAAA,GAAe,MAAM,OAAA,CAAQ,IAAK,EAAA;AACxC,IAAM,MAAA,QAAA,GAAW,gBAAgB,cAAe,CAAA;AAAA,MAC9C,EAAA,EAAI,KAAK,MAAO,EAAA,CAAE,SAAS,EAAE,CAAA,CAAE,MAAO,CAAA,CAAA,EAAG,CAAC,CAAA;AAAA,MAC1C,GAAG;AAAA,KACJ,CAAA;AACD,IAAO,OAAAC,gBAAA,CAAa,KAAK,eAAgB,CAAA,iBAAA,CAAkB,QAAQ,CAAG,EAAA,EAAE,MAAQ,EAAA,GAAA,EAAK,CAAA;AAAA,GACtF,CAAA;AAAA;AAAA,EAGDD,SAAK,IAAK,CAAA,2BAAA,EAA6B,OAAO,EAAE,SAAc,KAAA;AAC5D,IAAA,MAAM,QAAQ,IAAK,EAAA;AACnB,IAAA,OAAO,IAAIC,gBAAa,CAAA,IAAA,EAAM,EAAE,MAAA,EAAQ,KAAK,CAAA;AAAA,GAC9C,CAAA;AAAA,EAEDD,QAAA,CAAK,MAAO,CAAA,2CAAA,EAA6C,MAAM;AAC7D,IAAA,OAAO,IAAIC,gBAAa,CAAA,IAAA,EAAM,EAAE,MAAA,EAAQ,KAAK,CAAA;AAAA,GAC9C,CAAA;AAAA;AAAA,EAGDD,QAAA,CAAK,GAAI,CAAA,kBAAA,EAAoB,MAAM;AACjC,IAAA,OAAOC,gBAAa,CAAA,IAAA;AAAA,MAClB,eAAgB,CAAA,cAAA,CAAe,EAAE,OAAA,EAAS,gBAAgB,CAAA;AAAA,MAC1D,EAAE,QAAQ,GAAI;AAAA,KAChB;AAAA,GACD,CAAA;AAAA,EAEDD,QAAA,CAAK,GAAI,CAAA,yBAAA,EAA2B,MAAM;AACxC,IAAA,OAAOC,gBAAa,CAAA,IAAA;AAAA,MAClB,gBAAgB,cAAe,CAAA;AAAA,QAC7B,OAAS,EAAA,cAAA;AAAA,QACT,IAAM,EAAA;AAAA,OACP,CAAA;AAAA,MACD,EAAE,QAAQ,GAAI;AAAA,KAChB;AAAA,GACD,CAAA;AAAA,EAEDD,QAAA,CAAK,GAAI,CAAA,iBAAA,EAAmB,YAAY;AAEtC,IAAA,MAAM,IAAI,OAAQ,CAAA,CAAA,OAAA,KAAW,UAAW,CAAA,OAAA,EAAS,GAAI,CAAC,CAAA;AACtD,IAAA,OAAOC,iBAAa,IAAK,CAAA,eAAA,CAAgB,iBAAkB,CAAA,EAAE,CAAC,CAAA;AAAA,GAC/D;AACH;AAGa,IAAA,MAAA,GAASC,gBAAY,CAAA,GAAG,QAAQ;AAGtC,IAAM,eAAkB,GAAA;AAAA;AAAA,EAE7B,UAAA,EAAY,CAAC,OAAiB,KAAA;AAC5B,IAAA,MAAA,CAAO,IAAI,OAAO,CAAA;AAAA,GACpB;AAAA;AAAA,EAGA,eAAe,MAAM;AACnB,IAAO,MAAA,CAAA,aAAA,CAAc,GAAG,QAAQ,CAAA;AAAA,GAClC;AAAA;AAAA,EAGA,mBAAA,EAAqB,CAAC,MAAA,EAAgB,OAAoB,KAAA;AACxD,IAAA,OAAOD,gBAAa,CAAA,IAAA;AAAA,MAClB,eAAgB,CAAA,cAAA,CAAe,EAAE,OAAA,EAAS,CAAA;AAAA,MAC1C,EAAE,MAAO;AAAA,KACX;AAAA,GACF;AAAA;AAAA,EAGA,qBAAuB,EAAA,CAAC,IAAW,EAAA,KAAA,GAAgB,GAAQ,KAAA;AACzD,IAAO,OAAA,IAAI,QAAQ,CAAW,OAAA,KAAA;AAC5B,MAAA,UAAA,CAAW,MAAM;AACf,QAAA,OAAA,CAAQA,iBAAa,IAAK,CAAA,eAAA,CAAgB,iBAAkB,CAAA,IAAI,CAAC,CAAC,CAAA;AAAA,SACjE,KAAK,CAAA;AAAA,KACT,CAAA;AAAA,GACH;AAAA;AAAA,EAGA,gBAAA,EAAkB,CAAC,GAAgB,KAAA;AACjC,IAAO,MAAA,CAAA,GAAA;AAAA,MACLD,QAAA,CAAK,GAAI,CAAA,GAAA,EAAK,MAAM;AAClB,QAAA,OAAOC,iBAAa,KAAM,EAAA;AAAA,OAC3B;AAAA,KACH;AAAA,GACF;AAAA;AAAA,EAGA,WAAa,EAAA,CAAC,GAAa,EAAA,OAAA,GAAkB,GAAS,KAAA;AACpD,IAAO,MAAA,CAAA,GAAA;AAAA,MACLD,QAAA,CAAK,GAAI,CAAA,GAAA,EAAK,YAAY;AACxB,QAAA,MAAM,IAAI,OAAQ,CAAA,CAAA,OAAA,KAAW,UAAW,CAAA,OAAA,EAAS,OAAO,CAAC,CAAA;AACzD,QAAA,OAAOC,iBAAa,IAAK,CAAA,eAAA,CAAgB,iBAAkB,CAAA,EAAE,CAAC,CAAA;AAAA,OAC/D;AAAA,KACH;AAAA;AAEJ;AAGO,IAAM,kBAAkB,MAAM;AAEnC,EAAArB,gBAAA,CAAU,MAAM;AACd,IAAA,MAAA,CAAO,MAAO,CAAA,EAAE,kBAAoB,EAAA,OAAA,EAAS,CAAA;AAAA,GAC9C,CAAA;AAGD,EAAAD,gBAAA,CAAU,MAAM;AACd,IAAA,MAAA,CAAO,aAAc,EAAA;AAAA,GACtB,CAAA;AAGD,EAAAD,eAAA,CAAS,MAAM;AACb,IAAA,MAAA,CAAO,KAAM,EAAA;AAAA,GACd,CAAA;AACH;AChNO,SAAS,sBACd,OACA,EAAA;AACA,EAAO,OAAA;AAAA;AAAA,IAEL,SAAS,MAAM;AACb,MAAQ,OAAA,CAAA,SAAA,CAAU,QAAQ,CAAY,QAAA,KAAA;AACpC,QAAG,EAAA,CAAA,QAAA,CAAS,MAAM,YAAY;AAC5B,UAAA,MAAM,SAAS,KAAQ,IAAA;AAEvB,UAAA,MAAM,QAAQ,EAAE,GAAG,QAAQ,YAAc,EAAA,GAAG,SAAS,KAAM,EAAA;AAC3D,UAAoB,mBAAA,CAAA,OAAA,CAAQ,SAAU,CAAA,KAAK,CAAC,CAAA;AAE5C,UAAM,MAAA,QAAA,CAAS,KAAK,KAAK,CAAA;AAEzB,UAAA,MAAM,SAAS,OAAU,IAAA;AAAA,SAC1B,CAAA;AAAA,OACF,CAAA;AAAA,KACH;AAAA;AAAA,IAGA,QAAA,EAAU,CAAC,QAAqB,KAAA;AAC9B,MAAA,MAAM,WAAW,OAAQ,CAAA,SAAA,CAAU,KAAK,CAAM,EAAA,KAAA,EAAA,CAAG,SAAS,QAAQ,CAAA;AAClE,MAAA,IAAI,CAAC,QAAU,EAAA;AACb,QAAA,MAAM,IAAI,KAAA,CAAM,CAAc,WAAA,EAAA,QAAQ,CAAa,WAAA,CAAA,CAAA;AAAA;AAGrD,MAAG,EAAA,CAAA,QAAA,CAAS,MAAM,YAAY;AAC5B,QAAA,MAAM,SAAS,KAAQ,IAAA;AAEvB,QAAA,MAAM,QAAQ,EAAE,GAAG,QAAQ,YAAc,EAAA,GAAG,SAAS,KAAM,EAAA;AAC3D,QAAoB,mBAAA,CAAA,OAAA,CAAQ,SAAU,CAAA,KAAK,CAAC,CAAA;AAE5C,QAAM,MAAA,QAAA,CAAS,KAAK,KAAK,CAAA;AAEzB,QAAA,MAAM,SAAS,OAAU,IAAA;AAAA,OAC1B,CAAA;AAAA,KACH;AAAA;AAAA,IAGA,WAAA,EAAa,CAAC,QAAmC,KAAA;AAC/C,MAAQ,OAAA,CAAA,SAAA,CAAU,KAAK,QAAQ,CAAA;AAAA;AACjC,GACF;AACF;AAGO,IAAM,kBAAqB,GAAA;AAAA;AAAA,EAEhC,MAAQ,EAAA;AAAA,IACN,SAAA,EAAW,CAAC,SAAkC,MAAA;AAAA,MAC5C,IAAM,EAAA,mBAAA;AAAA,MACN,MAAM,MAAM;AACV,QAAA,MAAM,SAAS,SAAU,EAAA;AACzB,QAAO,MAAA,CAAA,MAAM,EAAE,iBAAkB,EAAA;AACjC,QAAO,MAAA,CAAA,MAAM,EAAE,WAAY,EAAA;AAAA;AAC7B,KACF,CAAA;AAAA,IAEA,YAAA,EAAc,CAAC,SAAA,EAA8B,OAAyB,MAAA;AAAA,MACpE,IAAM,EAAA,sBAAA;AAAA,MACN,MAAM,YAAY;AAChB,QAAA,MAAM,SAAS,SAAU,EAAA;AACzB,QAAM,MAAA,IAAA,GAAOmB,8BAAU,KAAM,EAAA;AAE7B,QAAM,MAAA,IAAA,CAAK,MAAM,MAAM,CAAA;AACvB,QAAO,MAAA,CAAA,OAAO,CAAE,CAAA,qBAAA,CAAsB,CAAC,CAAA;AAAA;AACzC,KACF,CAAA;AAAA,IAEA,QAAA,EAAU,CAAC,SAAkC,MAAA;AAAA,MAC3C,IAAM,EAAA,wBAAA;AAAA,MACN,KAAA,EAAO,EAAE,QAAA,EAAU,IAAK,EAAA;AAAA,MACxB,MAAM,MAAM;AACV,QAAA,MAAM,SAAS,SAAU,EAAA;AACzB,QAAO,MAAA,CAAA,MAAM,EAAE,YAAa,EAAA;AAAA;AAC9B,KACF,CAAA;AAAA,IAEA,OAAA,EAAS,CAAC,SAAkC,MAAA;AAAA,MAC1C,IAAM,EAAA,qBAAA;AAAA,MACN,KAAA,EAAO,EAAE,OAAA,EAAS,IAAK,EAAA;AAAA,MACvB,MAAM,MAAM;AACV,QAAA,MAAM,SAAS,SAAU,EAAA;AACzB,QAAA,MAAA,CAAO,MAAM,CAAA,CAAE,eAAgB,CAAA,WAAA,EAAa,MAAM,CAAA;AAClD,QAAA,MAAA,CAAOM,iBAAO,CAAA,SAAA,CAAU,QAAQ,CAAC,EAAE,iBAAkB,EAAA;AAAA;AACvD,KACF,CAAA;AAAA,IAEA,aAAA,EAAe,CAAC,SAAkC,MAAA;AAAA,MAChD,IAAM,EAAA,kCAAA;AAAA,MACN,MAAM,MAAM;AACV,QAAA,MAAM,SAAS,SAAU,EAAA;AACzB,QAAO,MAAA,CAAA,MAAM,CAAE,CAAA,eAAA,CAAgB,MAAM,CAAA;AACrC,QAAA,MAAA,CAAO,MAAM,CAAA,CAAE,GAAI,CAAA,eAAA,CAAgB,cAAc,EAAE,CAAA;AAAA;AACrD,KACF;AAAA,GACF;AAAA;AAAA,EAGA,KAAO,EAAA;AAAA,IACL,SAAA,EAAW,CAAC,QAAiC,MAAA;AAAA,MAC3C,IAAM,EAAA,mBAAA;AAAA,MACN,MAAM,MAAM;AACV,QAAA,MAAM,QAAQ,QAAS,EAAA;AACvB,QAAO,MAAA,CAAA,KAAK,EAAE,iBAAkB,EAAA;AAChC,QAAO,MAAA,CAAA,KAAK,EAAE,WAAY,EAAA;AAAA;AAC5B,KACF,CAAA;AAAA,IAEA,WAAA,EAAa,CAAC,QAAA,EAA6B,QAA0B,MAAA;AAAA,MACnE,IAAM,EAAA,uBAAA;AAAA,MACN,MAAM,YAAY;AAChB,QAAA,MAAM,QAAQ,QAAS,EAAA;AACvB,QAAM,MAAA,IAAA,GAAON,8BAAU,KAAM,EAAA;AAE7B,QAAM,MAAA,IAAA,CAAK,IAAK,CAAA,KAAA,EAAO,YAAY,CAAA;AACnC,QAAO,MAAA,CAAA,QAAQ,EAAE,gBAAiB,EAAA;AAClC,QAAO,MAAA,CAAA,KAAK,CAAE,CAAA,WAAA,CAAY,YAAY,CAAA;AAAA;AACxC,KACF,CAAA;AAAA,IAEA,UAAA,EAAY,CAAC,QAAiC,MAAA;AAAA,MAC5C,IAAM,EAAA,yBAAA;AAAA,MACN,KAAA,EAAO,EAAE,KAAA,EAAO,wBAAyB,EAAA;AAAA,MACzC,MAAM,MAAM;AACV,QAAA,MAAM,QAAQ,QAAS,EAAA;AACvB,QAAA,MAAA,CAAO,KAAK,CAAA,CAAE,eAAgB,CAAA,cAAA,EAAgB,MAAM,CAAA;AACpD,QAAA,MAAA,CAAOM,iBAAO,CAAA,SAAA,CAAU,wBAAwB,CAAC,EAAE,iBAAkB,EAAA;AAAA;AACvE,KACF,CAAA;AAAA,IAEA,QAAA,EAAU,CAAC,QAAiC,MAAA;AAAA,MAC1C,IAAM,EAAA,wBAAA;AAAA,MACN,KAAA,EAAO,EAAE,QAAA,EAAU,IAAK,EAAA;AAAA,MACxB,MAAM,MAAM;AACV,QAAA,MAAM,QAAQ,QAAS,EAAA;AACvB,QAAO,MAAA,CAAA,KAAK,CAAE,CAAA,eAAA,CAAgB,UAAU,CAAA;AACxC,QAAA,MAAA,CAAO,KAAK,CAAA,CAAE,eAAgB,CAAA,eAAA,EAAiB,MAAM,CAAA;AAAA;AACvD,KACF;AAAA,GACF;AAAA;AAAA,EAGA,KAAO,EAAA;AAAA,IACL,SAAA,EAAW,CAAC,MAAqB,MAAA;AAAA,MAC/B,IAAM,EAAA,mBAAA;AAAA,MACN,KAAA,EAAO,EAAE,MAAO,EAAA;AAAA,MAChB,MAAM,MAAM;AACV,QAAA,IAAI,MAAQ,EAAA;AACV,UAAA,MAAA,CAAOA,iBAAO,CAAA,SAAA,CAAU,QAAQ,CAAC,EAAE,iBAAkB,EAAA;AAAA,SAChD,MAAA;AACL,UAAA,MAAA,CAAOA,kBAAO,WAAY,CAAA,QAAQ,CAAC,CAAA,CAAE,IAAI,iBAAkB,EAAA;AAAA;AAC7D;AACF,KACF,CAAA;AAAA,IAEA,aAAA,EAAe,CAAC,OAAyB,MAAA;AAAA,MACvC,IAAM,EAAA,sBAAA;AAAA,MACN,KAAA,EAAO,EAAE,MAAA,EAAQ,IAAK,EAAA;AAAA,MACtB,MAAM,YAAY;AAChB,QAAM,MAAA,IAAA,GAAON,8BAAU,KAAM,EAAA;AAC7B,QAAM,MAAA,IAAA,CAAK,SAAS,UAAU,CAAA;AAC9B,QAAO,MAAA,CAAA,OAAO,EAAE,gBAAiB,EAAA;AAAA;AACnC,KACF,CAAA;AAAA,IAEA,cAAA,EAAgB,CAAC,OAAyB,MAAA;AAAA,MACxC,IAAM,EAAA,yBAAA;AAAA,MACN,KAAA,EAAO,EAAE,MAAA,EAAQ,IAAK,EAAA;AAAA,MACtB,MAAM,YAAY;AAChB,QAAM,MAAA,IAAA,GAAOA,8BAAU,KAAM,EAAA;AAC7B,QAAM,MAAA,OAAA,GAAUM,iBAAO,CAAA,WAAA,CAAY,eAAe,CAAA;AAClD,QAAM,MAAA,IAAA,CAAK,MAAM,OAAO,CAAA;AACxB,QAAO,MAAA,CAAA,OAAO,EAAE,gBAAiB,EAAA;AAAA;AACnC,KACF,CAAA;AAAA,IAEA,WAAW,OAAO;AAAA,MAChB,IAAM,EAAA,0BAAA;AAAA,MACN,KAAA,EAAO,EAAE,MAAA,EAAQ,IAAK,EAAA;AAAA,MACtB,MAAM,YAAY;AAChB,QAAM,MAAA,KAAA,GAAQA,iBAAO,CAAA,SAAA,CAAU,QAAQ,CAAA;AACvC,QAAA,MAAM,oBAAoB,KAAM,CAAA,gBAAA;AAAA,UAC9B;AAAA,SACF;AAEA,QAAI,IAAA,iBAAA,CAAkB,SAAS,CAAG,EAAA;AAChC,UAAA,MAAA,CAAO,iBAAkB,CAAA,CAAC,CAAC,CAAA,CAAE,WAAY,EAAA;AAAA;AAC3C;AACF,KACF;AAAA,GACF;AAAA;AAAA,EAGA,IAAM,EAAA;AAAA,IACJ,SAAA,EAAW,CAAC,KAAkB,MAAA;AAAA,MAC5B,IAAM,EAAA,oBAAA;AAAA,MACN,KAAA,EAAO,EAAE,KAAM,EAAA;AAAA,MACf,MAAM,MAAM;AACV,QAAM,KAAA,CAAA,OAAA,CAAQ,CAAC,KAAA,EAAO,KAAU,KAAA;AAC9B,UAAA,MAAA,CAAOA,kBAAO,WAAY,CAAA,CAAA,UAAA,EAAa,KAAK,CAAE,CAAA,CAAC,EAAE,iBAAkB,EAAA;AAAA,SACpE,CAAA;AAAA;AACH,KACF,CAAA;AAAA,IAEA,YAAY,OAAO;AAAA,MACjB,IAAM,EAAA,mBAAA;AAAA,MACN,KAAO,EAAA,EAAE,KAAO,EAAA,EAAG,EAAA;AAAA,MACnB,MAAM,MAAM;AACV,QAAA,MAAA,CAAOA,iBAAO,CAAA,SAAA,CAAU,WAAW,CAAC,EAAE,iBAAkB,EAAA;AAAA;AAC1D,KACF,CAAA;AAAA,IAEA,SAAS,OAAO;AAAA,MACd,IAAM,EAAA,qBAAA;AAAA,MACN,KAAA,EAAO,EAAE,OAAA,EAAS,IAAK,EAAA;AAAA,MACvB,MAAM,MAAM;AACV,QAAA,MAAA,CAAOA,iBAAO,CAAA,SAAA,CAAU,QAAQ,CAAC,EAAE,iBAAkB,EAAA;AACrD,QAAA,MAAA,CAAOA,iBAAO,CAAA,SAAA,CAAU,UAAU,CAAC,EAAE,iBAAkB,EAAA;AAAA;AACzD,KACF,CAAA;AAAA,IAEA,SAAA,EAAW,CAAC,QAA0B,MAAA;AAAA,MACpC,IAAM,EAAA,wBAAA;AAAA,MACN,MAAM,YAAY;AAChB,QAAM,MAAA,IAAA,GAAON,8BAAU,KAAM,EAAA;AAC7B,QAAM,MAAA,SAAA,GAAYM,iBAAO,CAAA,WAAA,CAAY,aAAa,CAAA;AAElD,QAAM,MAAA,IAAA,CAAK,MAAM,SAAS,CAAA;AAC1B,QAAO,MAAA,CAAA,QAAQ,EAAE,gBAAiB,EAAA;AAAA;AACpC,KACF;AAAA;AAEJ;AAGO,IAAM,gBAAmB,GAAA;AAAA;AAAA,EAE9B,kBAAA,EAAoB,CAAC,UAAmC,MAAA;AAAA,IACtD,IAAM,EAAA,8BAAA;AAAA,IACN,MAAM,YAAY;AAChB,MAAA,MAAM,UAAU,UAAW,EAAA;AAC3B,MAAM,MAAA,IAAA,GAAON,8BAAU,KAAM,EAAA;AAE7B,MAAA,OAAA,CAAQ,KAAM,EAAA;AACd,MAAO,MAAA,CAAA,OAAO,EAAE,WAAY,EAAA;AAE5B,MAAM,MAAA,IAAA,CAAK,SAAS,OAAO,CAAA;AAAA;AAE7B,GACF,CAAA;AAAA;AAAA,EAGA,YAAA,EAAc,CAAC,UAAA,EAA+B,aAA2B,MAAA;AAAA,IACvE,IAAM,EAAA,gCAAA;AAAA,IACN,MAAM,MAAM;AACV,MAAA,MAAM,UAAU,UAAW,EAAA;AAC3B,MAAO,MAAA,CAAA,OAAO,CAAE,CAAA,oBAAA,CAAqB,aAAa,CAAA;AAAA;AACpD,GACF,CAAA;AAAA;AAAA,EAGA,cAAA,EAAgB,CAAC,UAAA,EAA+B,kBAAkC,MAAA;AAAA,IAChF,IAAM,EAAA,8BAAA;AAAA,IACN,MAAM,MAAM;AACV,MAAA,MAAM,UAAU,UAAW,EAAA;AAC3B,MAAA,kBAAA,CAAmB,QAAQ,CAAQ,IAAA,KAAA;AACjC,QAAO,MAAA,CAAA,OAAO,CAAE,CAAA,eAAA,CAAgB,IAAI,CAAA;AAAA,OACrC,CAAA;AAAA;AACH,GACF,CAAA;AAAA;AAAA,EAGA,aAAA,EAAe,CAAC,UAAmC,MAAA;AAAA,IACjD,IAAM,EAAA,mCAAA;AAAA,IACN,MAAM,MAAM;AACV,MAAA,MAAM,UAAU,UAAW,EAAA;AAC3B,MAAM,MAAA,MAAA,GAAS,MAAO,CAAA,gBAAA,CAAiB,OAAO,CAAA;AAG9C,MAAA,MAAA,CAAO,OAAO,KAAK,CAAA,CAAE,GAAI,CAAA,IAAA,CAAK,OAAO,eAAe,CAAA;AAAA;AACtD,GACF;AACF;AAGO,IAAM,uBAA0B,GAAA;AAAA;AAAA,EAErC,iBAAmB,EAAA,CAAC,SAAyB,EAAA,SAAA,GAAoB,GAAS,MAAA;AAAA,IACxE,IAAM,EAAA,sCAAA;AAAA,IACN,MAAM,YAAY;AAChB,MAAM,MAAA,KAAA,GAAQ,YAAY,GAAI,EAAA;AAC9B,MAAA,mBAAA,CAAoB,SAAS,CAAA;AAC7B,MAAM,MAAA,GAAA,GAAM,YAAY,GAAI,EAAA;AAE5B,MAAA,MAAA,CAAO,GAAM,GAAA,KAAK,CAAE,CAAA,YAAA,CAAa,SAAS,CAAA;AAAA;AAC5C,GACF,CAAA;AAAA;AAAA,EAGA,WAAA,EAAa,CAAC,SAA6B,MAAA;AAAA,IACzC,IAAM,EAAA,sBAAA;AAAA,IACN,MAAM,YAAY;AAChB,MAAA,MAAM,EAAE,OAAA,EAAY,GAAA,mBAAA,CAAoB,SAAS,CAAA;AAGjD,MAAA,IAAK,OAAe,EAAI,EAAA;AACtB,QAAC,OAAe,EAAG,EAAA;AAAA;AAGrB,MAAM,MAAA,aAAA,GAAiB,WAAoB,CAAA,MAAA,EAAQ,cAAkB,IAAA,CAAA;AAErE,MAAQ,OAAA,EAAA;AAER,MAAA,IAAK,OAAe,EAAI,EAAA;AACtB,QAAC,OAAe,EAAG,EAAA;AAAA;AAGrB,MAAM,MAAA,WAAA,GAAe,WAAoB,CAAA,MAAA,EAAQ,cAAkB,IAAA,CAAA;AACnE,MAAA,MAAM,aAAa,WAAc,GAAA,aAAA;AAGjC,MAAA,MAAA,CAAO,KAAK,GAAI,CAAA,UAAU,CAAC,CAAA,CAAE,aAAa,GAAO,CAAA;AAAA;AACnD,GACF,CAAA;AAAA;AAAA,EAGA,oBAAA,EAAsB,CAAC,SAAA,EAAyB,WAAwB,MAAA;AAAA,IACtE,IAAM,EAAA,sBAAA;AAAA,IACN,MAAM,YAAY;AAChB,MAAA,IAAI,WAAc,GAAA,CAAA;AAElB,MAAM,MAAA,aAAA,GAAgB,CAAC,MAAgB,KAAA;AACrC,QAAA,WAAA,EAAA;AACA,QAAO,OAAA,SAAA;AAAA,OACT;AAEA,MAAA,MAAM,EAAE,QAAS,EAAA,GAAI,oCAAoBN,cAAAA,CAAC,iBAAc,CAAE,CAAA;AAE1D,MAAA,WAAA,CAAY,QAAQ,CAAS,KAAA,KAAA;AAC3B,QAAA,QAAA,iBAASA,cAAAA,CAAC,aAAe,EAAA,EAAA,GAAG,OAAO,CAAE,CAAA;AAAA,OACtC,CAAA;AAGD,MAAA,MAAA,CAAO,WAAW,CAAA,CAAE,mBAAoB,CAAA,WAAA,CAAY,SAAS,CAAC,CAAA;AAAA;AAChE,GACF;AACF;AAGO,IAAM,uBAA0B,GAAA;AAAA;AAAA,EAErC,cAAA,EAAgB,CAAC,OAAA,EAA6B,cAAyB,MAAA;AAAA,IACrE,IAAM,EAAA,+BAAA;AAAA,IACN,MAAM,YAAY;AAChB,MAAM,MAAA,MAAA,GAAS,MAAM,OAAQ,EAAA;AAC7B,MAAO,MAAA,CAAA,MAAM,CAAE,CAAA,OAAA,CAAQ,cAAc,CAAA;AAAA;AACvC,GACF,CAAA;AAAA;AAAA,EAGA,aAAA,EAAe,CAAC,OAAA,EAA6B,aAAwB,MAAA;AAAA,IACnE,IAAM,EAAA,8BAAA;AAAA,IACN,MAAM,YAAY;AAChB,MAAA,MAAM,OAAO,OAAQ,EAAC,CAAE,CAAA,OAAA,CAAQ,QAAQ,aAAa,CAAA;AAAA;AACvD,GACF,CAAA;AAAA;AAAA,EAGA,aAAA,EAAe,CAAC,SAA6B,MAAA;AAAA,IAC3C,IAAM,EAAA,uCAAA;AAAA,IACN,MAAM,YAAY;AAChB,MAAA,mBAAA,CAAoB,SAAS,CAAA;AAG7B,MAAA,MAAA,CAAOY,iBAAO,CAAA,SAAA,CAAU,QAAQ,CAAC,EAAE,iBAAkB,EAAA;AAGrD,MAAA,MAAMC,mBAAQ,MAAM;AAClB,QAAA,MAAA,CAAOD,kBAAO,WAAY,CAAA,QAAQ,CAAC,CAAA,CAAE,IAAI,iBAAkB,EAAA;AAAA,OAC5D,CAAA;AAAA;AACH,GACF;AACF", "file": "index.cjs", "sourcesContent": ["/**\n * Testing Package - Shared testing utilities and patterns\n */\n\n// Core testing utilities\nexport * from './test-utils';\n\n// Mock server setup\nexport * from './mock-server';\n\n// Component testing patterns\nexport * from './component-test-patterns';\n\n// Re-export commonly used testing libraries\nexport * from '@testing-library/react';\nexport * from '@testing-library/user-event';\nexport { vi, describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';", "/**\n * Shared Testing Utilities - Common testing helpers and utilities\n */\n\nimport React, { ReactElement, ReactNode } from 'react';\nimport { render, RenderOptions, RenderResult } from '@testing-library/react';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { createRouter, createRootRoute, createRoute, RouterProvider } from '@tanstack/react-router';\nimport userEvent from '@testing-library/user-event';\nimport { vi } from 'vitest';\n\n// Test providers interface\ninterface TestProvidersProps {\n  children: ReactNode;\n  queryClient?: QueryClient;\n  initialEntries?: string[];\n  routeConfig?: any;\n}\n\n// Create test query client\nexport function createTestQueryClient(): QueryClient {\n  return new QueryClient({\n    defaultOptions: {\n      queries: {\n        retry: false,\n        gcTime: 0,\n      },\n      mutations: {\n        retry: false,\n      },\n    },\n  });\n}\n\n// Test providers wrapper\nexport function TestProviders({ \n  children, \n  queryClient = createTestQueryClient()\n}: TestProvidersProps) {\n  // Create memory router for testing\n  const rootRoute = createRootRoute({\n    component: () => <div>{children}</div>,\n  });\n\n  const indexRoute = createRoute({\n    getParentRoute: () => rootRoute,\n    path: '/',\n    component: () => <>{children}</>,\n  });\n\n  const routeTree = rootRoute.addChildren([indexRoute]);\n\n  const router = createRouter({\n    routeTree,\n    defaultPreload: 'intent',\n  });\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <RouterProvider router={router} />\n    </QueryClientProvider>\n  );\n}\n\n// Custom render function with providers\ninterface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {\n  queryClient?: QueryClient;\n  initialEntries?: string[];\n  routeConfig?: any;\n  withProviders?: boolean;\n}\n\nexport function renderWithProviders(\n  ui: ReactElement,\n  options: CustomRenderOptions = {}\n): RenderResult & { user: ReturnType<typeof userEvent.setup> } {\n  const {\n    queryClient,\n    initialEntries,\n    routeConfig,\n    withProviders = true,\n    ...renderOptions\n  } = options;\n\n  const user = userEvent.setup();\n\n  const Wrapper = withProviders\n    ? ({ children }: { children: ReactNode }) => (\n        <TestProviders\n          queryClient={queryClient || createTestQueryClient()}\n          initialEntries={initialEntries || []}\n          routeConfig={routeConfig}\n        >\n          {children}\n        </TestProviders>\n      )\n    : undefined;\n\n  const result = render(ui, {\n    wrapper: Wrapper,\n    ...renderOptions,\n  });\n\n  return {\n    ...result,\n    user,\n  };\n}\n\n// Mock API client for testing\nexport interface MockApiClient {\n  get: ReturnType<typeof vi.fn>;\n  post: ReturnType<typeof vi.fn>;\n  put: ReturnType<typeof vi.fn>;\n  patch: ReturnType<typeof vi.fn>;\n  delete: ReturnType<typeof vi.fn>;\n  addRequestInterceptor: ReturnType<typeof vi.fn>;\n  addResponseInterceptor: ReturnType<typeof vi.fn>;\n  removeRequestInterceptor: ReturnType<typeof vi.fn>;\n  removeResponseInterceptor: ReturnType<typeof vi.fn>;\n  setBaseURL: ReturnType<typeof vi.fn>;\n  setDefaultHeaders: ReturnType<typeof vi.fn>;\n  getConfig: ReturnType<typeof vi.fn>;\n}\n\nexport function createMockApiClient(): MockApiClient {\n  return {\n    get: vi.fn(),\n    post: vi.fn(),\n    put: vi.fn(),\n    patch: vi.fn(),\n    delete: vi.fn(),\n    addRequestInterceptor: vi.fn(),\n    addResponseInterceptor: vi.fn(),\n    removeRequestInterceptor: vi.fn(),\n    removeResponseInterceptor: vi.fn(),\n    setBaseURL: vi.fn(),\n    setDefaultHeaders: vi.fn(),\n    getConfig: vi.fn(),\n  };\n}\n\n// Mock auth provider\nexport interface MockAuthState {\n  isAuthenticated: boolean;\n  user?: any;\n  permissions?: string[];\n  roles?: string[];\n}\n\nexport function createMockAuthProvider(initialState: MockAuthState = { isAuthenticated: false }) {\n  const AuthContext = React.createContext(initialState);\n  \n  const MockAuthProvider = ({ children, state = initialState }: { \n    children: ReactNode; \n    state?: MockAuthState;\n  }) => (\n    <AuthContext.Provider value={state}>\n      {children}\n    </AuthContext.Provider>\n  );\n\n  const useAuth = () => React.useContext(AuthContext);\n\n  return {\n    MockAuthProvider,\n    useAuth,\n    AuthContext,\n  };\n}\n\n// Test data factories\nexport const testDataFactory = {\n  // User factory\n  createUser: (overrides: Partial<any> = {}) => ({\n    id: '1',\n    name: 'Test User',\n    email: '<EMAIL>',\n    roles: ['user'],\n    permissions: ['read'],\n    isActive: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    ...overrides,\n  }),\n\n  // Training factory\n  createTraining: (overrides: Partial<any> = {}) => ({\n    id: '1',\n    title: 'Test Training',\n    description: 'Test training description',\n    category: { id: '1', name: 'Test Category' },\n    duration: 60,\n    difficulty: 'beginner',\n    instructor: testDataFactory.createUser({ id: '2', name: 'Instructor' }),\n    participants: [],\n    status: 'published',\n    isPublic: true,\n    createdAt: new Date(),\n    updatedAt: new Date(),\n    ...overrides,\n  }),\n\n  // API response factory\n  createApiResponse: <T,>(data: T, overrides: Partial<any> = {}) => ({\n    data,\n    status: 200,\n    statusText: 'OK',\n    message: 'Success',\n    ...overrides,\n  }),\n\n  // Error factory\n  createApiError: (overrides: Partial<any> = {}) => ({\n    message: 'Test error',\n    code: 'TEST_ERROR',\n    details: {\n      type: 'CLIENT_ERROR',\n      status: 400,\n      timestamp: new Date(),\n    },\n    ...overrides,\n  }),\n};\n\n// Mock handlers for MSW\nexport const mockHandlers = {\n  // Success response handler\n  success: <T,>(data: T) => ({\n    status: 200,\n    json: testDataFactory.createApiResponse(data),\n  }),\n\n  // Error response handler\n  error: (status = 400, message = 'Test error') => ({\n    status,\n    json: testDataFactory.createApiError({ message }),\n  }),\n\n  // Loading delay handler\n  delay: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),\n};\n\n// Component testing utilities\nexport const componentTestUtils = {\n  // Wait for component to be loaded (for lazy components)\n  waitForComponent: async (getByTestId: (id: string) => HTMLElement, testId: string) => {\n    return new Promise<HTMLElement>((resolve) => {\n      const checkForComponent = () => {\n        try {\n          const element = getByTestId(testId);\n          resolve(element);\n        } catch {\n          setTimeout(checkForComponent, 10);\n        }\n      };\n      checkForComponent();\n    });\n  },\n\n  // Simulate network delay\n  simulateNetworkDelay: (ms = 100) => \n    new Promise(resolve => setTimeout(resolve, ms)),\n\n  // Mock intersection observer for lazy loading tests\n  mockIntersectionObserver: () => {\n    const mockIntersectionObserver = vi.fn();\n    mockIntersectionObserver.mockReturnValue({\n      observe: vi.fn(),\n      unobserve: vi.fn(),\n      disconnect: vi.fn(),\n    });\n    \n    Object.defineProperty(window, 'IntersectionObserver', {\n      writable: true,\n      configurable: true,\n      value: mockIntersectionObserver,\n    });\n\n    return mockIntersectionObserver;\n  },\n\n  // Mock ResizeObserver\n  mockResizeObserver: () => {\n    const mockResizeObserver = vi.fn();\n    mockResizeObserver.mockReturnValue({\n      observe: vi.fn(),\n      unobserve: vi.fn(),\n      disconnect: vi.fn(),\n    });\n    \n    Object.defineProperty(window, 'ResizeObserver', {\n      writable: true,\n      configurable: true,\n      value: mockResizeObserver,\n    });\n\n    return mockResizeObserver;\n  },\n};\n\n// Accessibility testing utilities\nexport const a11yTestUtils = {\n  // Check for required ARIA attributes\n  checkAriaAttributes: (element: HTMLElement, requiredAttributes: string[]) => {\n    const missingAttributes = requiredAttributes.filter(\n      attr => !element.hasAttribute(attr)\n    );\n    \n    if (missingAttributes.length > 0) {\n      throw new Error(`Missing ARIA attributes: ${missingAttributes.join(', ')}`);\n    }\n  },\n\n  // Check for keyboard navigation\n  checkKeyboardNavigation: async (\n    user: ReturnType<typeof userEvent.setup>,\n    element: HTMLElement\n  ) => {\n    element.focus();\n    expect(element).toHaveFocus();\n    \n    await user.keyboard('{Tab}');\n    // Additional keyboard navigation checks would go here\n  },\n\n  // Check color contrast (simplified)\n  checkColorContrast: (element: HTMLElement) => {\n    const styles = window.getComputedStyle(element);\n    const backgroundColor = styles.backgroundColor;\n    const color = styles.color;\n    \n    // This is a simplified check - in reality you'd use a proper contrast calculation\n    if (backgroundColor === color) {\n      throw new Error('Insufficient color contrast');\n    }\n  },\n};\n\n// Performance testing utilities\nexport const performanceTestUtils = {\n  // Measure render time\n  measureRenderTime: async (renderFn: () => Promise<void> | void) => {\n    const start = performance.now();\n    await renderFn();\n    const end = performance.now();\n    return end - start;\n  },\n\n  // Check for memory leaks (simplified)\n  checkMemoryLeaks: (cleanup: () => void) => {\n    const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;\n    \n    return () => {\n      cleanup();\n      \n      // Force garbage collection if available\n      if ((global as any).gc) {\n        (global as any).gc();\n      }\n      \n      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;\n      const memoryDiff = finalMemory - initialMemory;\n      \n      if (memoryDiff > 1000000) { // 1MB threshold\n        console.warn(`Potential memory leak detected: ${memoryDiff} bytes`);\n      }\n    };\n  },\n};\n\n// Custom matchers for testing\nexport const customMatchers = {\n  // Check if element is accessible\n  toBeAccessible: (element: HTMLElement) => {\n    try {\n      // Basic accessibility checks\n      const hasAriaLabel = element.hasAttribute('aria-label') || \n                          element.hasAttribute('aria-labelledby');\n      const hasRole = element.hasAttribute('role') || \n                     ['button', 'link', 'input'].includes(element.tagName.toLowerCase());\n      \n      if (!hasAriaLabel && !hasRole) {\n        return {\n          message: () => 'Element is not accessible - missing aria-label or role',\n          pass: false,\n        };\n      }\n      \n      return {\n        message: () => 'Element is accessible',\n        pass: true,\n      };\n    } catch (error) {\n      return {\n        message: () => `Accessibility check failed: ${error}`,\n        pass: false,\n      };\n    }\n  },\n};\n\n// Export everything\nexport * from '@testing-library/react';\nexport * from '@testing-library/user-event';\nexport { vi } from 'vitest';\nexport { default as userEvent } from '@testing-library/user-event';", "/**\n * Mock Server - MSW setup for API testing\n */\n\nimport { setupServer } from 'msw/node';\nimport { http, HttpResponse } from 'msw';\nimport { beforeAll, afterEach, afterAll } from 'vitest';\nimport { testDataFactory } from './test-utils';\n\n// Mock API handlers\nexport const handlers = [\n  // Users API\n  http.get('/api/users', ({ request }) => {\n    const url = new URL(request.url);\n    const page = parseInt(url.searchParams.get('page') || '1');\n    const limit = parseInt(url.searchParams.get('limit') || '10');\n    const search = url.searchParams.get('search');\n    \n    let users = Array.from({ length: 50 }, (_, i) => \n      testDataFactory.createUser({ \n        id: `${i + 1}`, \n        name: `User ${i + 1}`,\n        email: `user${i + 1}@example.com`\n      })\n    );\n\n    if (search) {\n      users = users.filter(user => \n        user.name.toLowerCase().includes(search.toLowerCase()) ||\n        user.email.toLowerCase().includes(search.toLowerCase())\n      );\n    }\n\n    const start = (page - 1) * limit;\n    const end = start + limit;\n    const paginatedUsers = users.slice(start, end);\n\n    return HttpResponse.json(testDataFactory.createApiResponse(paginatedUsers, {\n      metadata: {\n        page,\n        limit,\n        total: users.length,\n        hasNext: end < users.length,\n        hasPrevious: page > 1\n      }\n    }));\n  }),\n\n  http.get('/api/users/:id', ({ params }) => {\n    const user = testDataFactory.createUser({ \n      id: params.id as string,\n      name: `User ${params.id}`,\n      email: `user${params.id}@example.com`\n    });\n    return HttpResponse.json(testDataFactory.createApiResponse(user));\n  }),\n\n  http.post('/api/users', async ({ request }) => {\n    const userData = await request.json() as any;\n    const user = testDataFactory.createUser({\n      id: Math.random().toString(36).substr(2, 9),\n      ...userData\n    });\n    return HttpResponse.json(testDataFactory.createApiResponse(user), { status: 201 });\n  }),\n\n  http.put('/api/users/:id', async ({ params, request }) => {\n    const userData = await request.json() as any;\n    const user = testDataFactory.createUser({\n      id: params.id as string,\n      ...userData\n    });\n    return HttpResponse.json(testDataFactory.createApiResponse(user));\n  }),\n\n  http.delete('/api/users/:id', () => {\n    return new HttpResponse(null, { status: 204 });\n  }),\n\n  // Training API\n  http.get('/api/trainings', ({ request }) => {\n    const url = new URL(request.url);\n    const page = parseInt(url.searchParams.get('page') || '1');\n    const limit = parseInt(url.searchParams.get('limit') || '10');\n    const category = url.searchParams.get('categoryId');\n    \n    let trainings = Array.from({ length: 30 }, (_, i) => \n      testDataFactory.createTraining({ \n        id: `${i + 1}`, \n        title: `Training ${i + 1}`,\n        category: { id: category || '1', name: 'Test Category' }\n      })\n    );\n\n    if (category) {\n      trainings = trainings.filter(training => training.category.id === category);\n    }\n\n    const start = (page - 1) * limit;\n    const end = start + limit;\n    const paginatedTrainings = trainings.slice(start, end);\n\n    return HttpResponse.json(testDataFactory.createApiResponse(paginatedTrainings, {\n      metadata: {\n        page,\n        limit,\n        total: trainings.length,\n        hasNext: end < trainings.length,\n        hasPrevious: page > 1\n      }\n    }));\n  }),\n\n  http.get('/api/trainings/:id', ({ params }) => {\n    const training = testDataFactory.createTraining({ \n      id: params.id as string,\n      title: `Training ${params.id}`\n    });\n    return HttpResponse.json(testDataFactory.createApiResponse(training));\n  }),\n\n  http.post('/api/trainings', async ({ request }) => {\n    const trainingData = await request.json() as any;\n    const training = testDataFactory.createTraining({\n      id: Math.random().toString(36).substr(2, 9),\n      ...trainingData\n    });\n    return HttpResponse.json(testDataFactory.createApiResponse(training), { status: 201 });\n  }),\n\n  // Training enrollment\n  http.post('/api/trainings/:id/enroll', async ({ request }) => {\n    await request.json() as any;\n    return new HttpResponse(null, { status: 204 });\n  }),\n\n  http.delete('/api/trainings/:trainingId/enroll/:userId', () => {\n    return new HttpResponse(null, { status: 204 });\n  }),\n\n  // Error scenarios for testing\n  http.get('/api/users/error', () => {\n    return HttpResponse.json(\n      testDataFactory.createApiError({ message: 'Server error' }),\n      { status: 500 }\n    );\n  }),\n\n  http.get('/api/users/unauthorized', () => {\n    return HttpResponse.json(\n      testDataFactory.createApiError({ \n        message: 'Unauthorized',\n        code: 'AUTH_REQUIRED'\n      }),\n      { status: 401 }\n    );\n  }),\n\n  http.get('/api/users/slow', async () => {\n    // Simulate slow response\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    return HttpResponse.json(testDataFactory.createApiResponse([]));\n  }),\n];\n\n// Create mock server\nexport const server = setupServer(...handlers);\n\n// Server utilities\nexport const mockServerUtils = {\n  // Add temporary handler\n  addHandler: (handler: any) => {\n    server.use(handler);\n  },\n\n  // Reset handlers to original state\n  resetHandlers: () => {\n    server.resetHandlers(...handlers);\n  },\n\n  // Create error response\n  createErrorResponse: (status: number, message: string) => {\n    return HttpResponse.json(\n      testDataFactory.createApiError({ message }),\n      { status }\n    );\n  },\n\n  // Create success response with delay\n  createDelayedResponse: (data: any, delay: number = 100) => {\n    return new Promise(resolve => {\n      setTimeout(() => {\n        resolve(HttpResponse.json(testDataFactory.createApiResponse(data)));\n      }, delay);\n    });\n  },\n\n  // Mock network error\n  mockNetworkError: (url: string) => {\n    server.use(\n      http.get(url, () => {\n        return HttpResponse.error();\n      })\n    );\n  },\n\n  // Mock timeout\n  mockTimeout: (url: string, timeout: number = 5000) => {\n    server.use(\n      http.get(url, async () => {\n        await new Promise(resolve => setTimeout(resolve, timeout));\n        return HttpResponse.json(testDataFactory.createApiResponse([]));\n      })\n    );\n  },\n};\n\n// Setup and teardown utilities\nexport const setupMockServer = () => {\n  // Start server before all tests\n  beforeAll(() => {\n    server.listen({ onUnhandledRequest: 'error' });\n  });\n\n  // Reset handlers after each test\n  afterEach(() => {\n    server.resetHandlers();\n  });\n\n  // Close server after all tests\n  afterAll(() => {\n    server.close();\n  });\n};", "/**\n * Component Test Patterns - Reusable testing patterns for components\n */\n\nimport { screen, waitFor } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nimport { renderWithProviders } from './test-utils';\nimport type { ReactElement } from 'react';\n\n// Base component test pattern\nexport interface ComponentTestPattern<P = any> {\n  component: (props: P) => ReactElement;\n  defaultProps: P;\n  testCases: ComponentTestCase<P>[];\n}\n\nexport interface ComponentTestCase<P = any> {\n  name: string;\n  props?: Partial<P>;\n  setup?: () => void | Promise<void>;\n  test: (props: P) => void | Promise<void>;\n  cleanup?: () => void | Promise<void>;\n}\n\n// Generic component tester\nexport function createComponentTester<P extends object>(\n  pattern: ComponentTestPattern<P>\n) {\n  return {\n    // Test all cases\n    testAll: () => {\n      pattern.testCases.forEach(testCase => {\n        it(testCase.name, async () => {\n          await testCase.setup?.();\n          \n          const props = { ...pattern.defaultProps, ...testCase.props };\n          renderWithProviders(pattern.component(props));\n          \n          await testCase.test(props);\n          \n          await testCase.cleanup?.();\n        });\n      });\n    },\n\n    // Test specific case\n    testCase: (caseName: string) => {\n      const testCase = pattern.testCases.find(tc => tc.name === caseName);\n      if (!testCase) {\n        throw new Error(`Test case \"${caseName}\" not found`);\n      }\n\n      it(testCase.name, async () => {\n        await testCase.setup?.();\n        \n        const props = { ...pattern.defaultProps, ...testCase.props };\n        renderWithProviders(pattern.component(props));\n        \n        await testCase.test(props);\n        \n        await testCase.cleanup?.();\n      });\n    },\n\n    // Add custom test case\n    addTestCase: (testCase: ComponentTestCase<P>) => {\n      pattern.testCases.push(testCase);\n    }\n  };\n}\n\n// Common test patterns\nexport const commonTestPatterns = {\n  // Button component pattern\n  button: {\n    rendering: (getButton: () => HTMLElement) => ({\n      name: 'renders correctly',\n      test: () => {\n        const button = getButton();\n        expect(button).toBeInTheDocument();\n        expect(button).toBeVisible();\n      }\n    }),\n\n    clickHandler: (getButton: () => HTMLElement, onClick: () => void) => ({\n      name: 'handles click events',\n      test: async () => {\n        const button = getButton();\n        const user = userEvent.setup();\n        \n        await user.click(button);\n        expect(onClick).toHaveBeenCalledTimes(1);\n      }\n    }),\n\n    disabled: (getButton: () => HTMLElement) => ({\n      name: 'handles disabled state',\n      props: { disabled: true },\n      test: () => {\n        const button = getButton();\n        expect(button).toBeDisabled();\n      }\n    }),\n\n    loading: (getButton: () => HTMLElement) => ({\n      name: 'shows loading state',\n      props: { loading: true },\n      test: () => {\n        const button = getButton();\n        expect(button).toHaveAttribute('aria-busy', 'true');\n        expect(screen.getByRole('status')).toBeInTheDocument();\n      }\n    }),\n\n    accessibility: (getButton: () => HTMLElement) => ({\n      name: 'meets accessibility requirements',\n      test: () => {\n        const button = getButton();\n        expect(button).toHaveAttribute('type');\n        expect(button).not.toHaveAttribute('aria-label', '');\n      }\n    })\n  },\n\n  // Form input pattern\n  input: {\n    rendering: (getInput: () => HTMLElement) => ({\n      name: 'renders correctly',\n      test: () => {\n        const input = getInput();\n        expect(input).toBeInTheDocument();\n        expect(input).toBeVisible();\n      }\n    }),\n\n    valueChange: (getInput: () => HTMLElement, onChange: () => void) => ({\n      name: 'handles value changes',\n      test: async () => {\n        const input = getInput();\n        const user = userEvent.setup();\n        \n        await user.type(input, 'test value');\n        expect(onChange).toHaveBeenCalled();\n        expect(input).toHaveValue('test value');\n      }\n    }),\n\n    validation: (getInput: () => HTMLElement) => ({\n      name: 'shows validation errors',\n      props: { error: 'This field is required' },\n      test: () => {\n        const input = getInput();\n        expect(input).toHaveAttribute('aria-invalid', 'true');\n        expect(screen.getByText('This field is required')).toBeInTheDocument();\n      }\n    }),\n\n    required: (getInput: () => HTMLElement) => ({\n      name: 'handles required state',\n      props: { required: true },\n      test: () => {\n        const input = getInput();\n        expect(input).toHaveAttribute('required');\n        expect(input).toHaveAttribute('aria-required', 'true');\n      }\n    })\n  },\n\n  // Modal/Dialog pattern\n  modal: {\n    rendering: (isOpen: boolean) => ({\n      name: 'renders when open',\n      props: { isOpen },\n      test: () => {\n        if (isOpen) {\n          expect(screen.getByRole('dialog')).toBeInTheDocument();\n        } else {\n          expect(screen.queryByRole('dialog')).not.toBeInTheDocument();\n        }\n      }\n    }),\n\n    closeOnEscape: (onClose: () => void) => ({\n      name: 'closes on escape key',\n      props: { isOpen: true },\n      test: async () => {\n        const user = userEvent.setup();\n        await user.keyboard('{Escape}');\n        expect(onClose).toHaveBeenCalled();\n      }\n    }),\n\n    closeOnOverlay: (onClose: () => void) => ({\n      name: 'closes on overlay click',\n      props: { isOpen: true },\n      test: async () => {\n        const user = userEvent.setup();\n        const overlay = screen.getByTestId('modal-overlay');\n        await user.click(overlay);\n        expect(onClose).toHaveBeenCalled();\n      }\n    }),\n\n    focusTrap: () => ({\n      name: 'traps focus within modal',\n      props: { isOpen: true },\n      test: async () => {\n        const modal = screen.getByRole('dialog');\n        const focusableElements = modal.querySelectorAll(\n          'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\n        );\n        \n        if (focusableElements.length > 0) {\n          expect(focusableElements[0]).toHaveFocus();\n        }\n      }\n    })\n  },\n\n  // List/Table pattern\n  list: {\n    rendering: (items: any[]) => ({\n      name: 'renders list items',\n      props: { items },\n      test: () => {\n        items.forEach((_item, index) => {\n          expect(screen.getByTestId(`list-item-${index}`)).toBeInTheDocument();\n        });\n      }\n    }),\n\n    emptyState: () => ({\n      name: 'shows empty state',\n      props: { items: [] },\n      test: () => {\n        expect(screen.getByText(/no items/i)).toBeInTheDocument();\n      }\n    }),\n\n    loading: () => ({\n      name: 'shows loading state',\n      props: { loading: true },\n      test: () => {\n        expect(screen.getByRole('status')).toBeInTheDocument();\n        expect(screen.getByText(/loading/i)).toBeInTheDocument();\n      }\n    }),\n\n    selection: (onSelect: () => void) => ({\n      name: 'handles item selection',\n      test: async () => {\n        const user = userEvent.setup();\n        const firstItem = screen.getByTestId('list-item-0');\n        \n        await user.click(firstItem);\n        expect(onSelect).toHaveBeenCalled();\n      }\n    })\n  }\n};\n\n// Accessibility test patterns\nexport const a11yTestPatterns = {\n  // Keyboard navigation\n  keyboardNavigation: (getElement: () => HTMLElement) => ({\n    name: 'supports keyboard navigation',\n    test: async () => {\n      const element = getElement();\n      const user = userEvent.setup();\n      \n      element.focus();\n      expect(element).toHaveFocus();\n      \n      await user.keyboard('{Tab}');\n      // Test that focus moves appropriately\n    }\n  }),\n\n  // Screen reader support\n  screenReader: (getElement: () => HTMLElement, expectedLabel: string) => ({\n    name: 'provides screen reader support',\n    test: () => {\n      const element = getElement();\n      expect(element).toHaveAccessibleName(expectedLabel);\n    }\n  }),\n\n  // ARIA attributes\n  ariaAttributes: (getElement: () => HTMLElement, requiredAttributes: string[]) => ({\n    name: 'has required ARIA attributes',\n    test: () => {\n      const element = getElement();\n      requiredAttributes.forEach(attr => {\n        expect(element).toHaveAttribute(attr);\n      });\n    }\n  }),\n\n  // Color contrast (simplified check)\n  colorContrast: (getElement: () => HTMLElement) => ({\n    name: 'meets color contrast requirements',\n    test: () => {\n      const element = getElement();\n      const styles = window.getComputedStyle(element);\n      \n      // This is a simplified check - in reality you'd use a proper contrast calculation\n      expect(styles.color).not.toBe(styles.backgroundColor);\n    }\n  })\n};\n\n// Performance test patterns\nexport const performanceTestPatterns = {\n  // Render performance\n  renderPerformance: (component: ReactElement, threshold: number = 100) => ({\n    name: 'renders within performance threshold',\n    test: async () => {\n      const start = performance.now();\n      renderWithProviders(component);\n      const end = performance.now();\n      \n      expect(end - start).toBeLessThan(threshold);\n    }\n  }),\n\n  // Memory usage\n  memoryUsage: (component: ReactElement) => ({\n    name: 'does not leak memory',\n    test: async () => {\n      const { unmount } = renderWithProviders(component);\n      \n      // Force garbage collection if available\n      if ((global as any).gc) {\n        (global as any).gc();\n      }\n      \n      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;\n      \n      unmount();\n      \n      if ((global as any).gc) {\n        (global as any).gc();\n      }\n      \n      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;\n      const memoryDiff = finalMemory - initialMemory;\n      \n      // Allow for some memory variance\n      expect(Math.abs(memoryDiff)).toBeLessThan(1000000); // 1MB threshold\n    }\n  }),\n\n  // Re-render optimization\n  rerenderOptimization: (component: ReactElement, propChanges: any[]) => ({\n    name: 'optimizes re-renders',\n    test: async () => {\n      let renderCount = 0;\n      \n      const TestComponent = (_props: any) => {\n        renderCount++;\n        return component;\n      };\n      \n      const { rerender } = renderWithProviders(<TestComponent />);\n      \n      propChanges.forEach(props => {\n        rerender(<TestComponent {...props} />);\n      });\n      \n      // Should not re-render unnecessarily\n      expect(renderCount).toBeLessThanOrEqual(propChanges.length + 1);\n    }\n  })\n};\n\n// Integration test patterns\nexport const integrationTestPatterns = {\n  // API integration\n  apiIntegration: (apiCall: () => Promise<any>, expectedResult: any) => ({\n    name: 'integrates with API correctly',\n    test: async () => {\n      const result = await apiCall();\n      expect(result).toEqual(expectedResult);\n    }\n  }),\n\n  // Error handling\n  errorHandling: (apiCall: () => Promise<any>, expectedError: any) => ({\n    name: 'handles API errors correctly',\n    test: async () => {\n      await expect(apiCall()).rejects.toEqual(expectedError);\n    }\n  }),\n\n  // Loading states\n  loadingStates: (component: ReactElement) => ({\n    name: 'shows loading states during API calls',\n    test: async () => {\n      renderWithProviders(component);\n      \n      // Should show loading initially\n      expect(screen.getByRole('status')).toBeInTheDocument();\n      \n      // Should hide loading after API call\n      await waitFor(() => {\n        expect(screen.queryByRole('status')).not.toBeInTheDocument();\n      });\n    }\n  })\n};\n\nexport default {\n  createComponentTester,\n  commonTestPatterns,\n  a11yTestPatterns,\n  performanceTestPatterns,\n  integrationTestPatterns\n};"]}