import { useState, useEffect } from 'react';
import type { FeatureFlagConfig, ABTestConfig } from './types';

export function FeatureFlagAdmin() {
  const [flags, setFlags] = useState<FeatureFlagConfig[]>([]);
  const [tests, setTests] = useState<ABTestConfig[]>([]);

  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'flags' | 'tests'>('flags');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      // Load feature flags
      const flagsResponse = await fetch('/api/admin/feature-flags');
      const flagsData = await flagsResponse.json();
      setFlags(flagsData);

      // Load A/B tests
      const testsResponse = await fetch('/api/admin/ab-tests');
      const testsData = await testsResponse.json();
      setTests(testsData);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleFlag = async (flag: FeatureFlagConfig) => {
    try {
      const response = await fetch(`/api/admin/feature-flags/${flag.key}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled: !flag.enabled }),
      });
      
      if (response.ok) {
        await loadData();
      }
    } catch (error) {
      console.error('Failed to toggle flag:', error);
    }
  };

  const updateRollout = async (flag: FeatureFlagConfig, percentage: number) => {
    try {
      const response = await fetch(`/api/admin/feature-flags/${flag.key}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ rolloutPercentage: percentage }),
      });
      
      if (response.ok) {
        await loadData();
      }
    } catch (error) {
      console.error('Failed to update rollout:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Feature Flag Management</h1>
        <p className="mt-2 text-gray-600">
          Manage feature flags and A/B tests across the Luminar platform
        </p>
      </div>

      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('flags')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'flags'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Feature Flags ({flags.length})
          </button>
          <button
            onClick={() => setActiveTab('tests')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'tests'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            A/B Tests ({tests.length})
          </button>
        </nav>
      </div>

      {activeTab === 'flags' && (
        <div className="grid grid-cols-1 gap-6">
          {flags.map((flag) => (
            <FeatureFlagCard
              key={flag.key}
              flag={flag}
              onToggle={() => toggleFlag(flag)}
              onUpdateRollout={(percentage) => updateRollout(flag, percentage)}
              onSelect={() => {}}
            />
          ))}
        </div>
      )}

      {activeTab === 'tests' && (
        <div className="grid grid-cols-1 gap-6">
          {tests.map((test) => (
            <ABTestCard key={test.id} test={test} />
          ))}
        </div>
      )}
    </div>
  );
}

function FeatureFlagCard({
  flag,
  onToggle,
  onUpdateRollout,
  onSelect,
}: {
  flag: FeatureFlagConfig;
  onToggle: () => void;
  onUpdateRollout: (percentage: number) => void;
  onSelect: () => void;
}) {
  const [rollout, setRollout] = useState(flag.rolloutPercentage);

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{flag.name}</h3>
          <p className="text-sm text-gray-500">{flag.key}</p>
        </div>
        <button
          onClick={onToggle}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            flag.enabled ? 'bg-blue-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              flag.enabled ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>

      {flag.description && (
        <p className="text-gray-600 mb-4">{flag.description}</p>
      )}

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Rollout Percentage: {rollout}%
          </label>
          <input
            type="range"
            min="0"
            max="100"
            value={rollout}
            onChange={(e) => setRollout(Number(e.target.value))}
            onMouseUp={() => onUpdateRollout(rollout)}
            className="w-full"
          />
        </div>

        {flag.variants && flag.variants.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Variants</h4>
            <div className="space-y-2">
              {flag.variants.map((variant) => (
                <div key={variant.key} className="flex items-center justify-between text-sm">
                  <span>{variant.name}</span>
                  <span className="text-gray-500">{variant.weight}%</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {flag.targetingRules && flag.targetingRules.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Targeting Rules ({flag.targetingRules.length})
            </h4>
          </div>
        )}

        <button
          onClick={onSelect}
          className="text-sm text-blue-600 hover:text-blue-700"
        >
          View Details →
        </button>
      </div>
    </div>
  );
}

function ABTestCard({ test }: { test: ABTestConfig }) {
  const getStatusColor = (status: ABTestConfig['status']) => {
    switch (status) {
      case 'running':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{test.name}</h3>
          <p className="text-sm text-gray-500">Feature: {test.featureFlag}</p>
        </div>
        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(test.status)}`}>
          {test.status}
        </span>
      </div>

      {test.description && (
        <p className="text-gray-600 mb-4">{test.description}</p>
      )}

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <p className="text-sm text-gray-500">Traffic Allocation</p>
          <p className="text-lg font-semibold">{test.trafficAllocation}%</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Duration</p>
          <p className="text-lg font-semibold">
            {test.startDate && new Date(test.startDate).toLocaleDateString()}
          </p>
        </div>
      </div>

      <div className="space-y-2">
        {test.variants.map((variant) => (
          <div key={variant.key} className="flex items-center justify-between">
            <span className="text-sm font-medium">{variant.name}</span>
            <div className="text-sm text-gray-500">
              {variant.allocation}% • 
              {variant.conversions || 0} / {variant.impressions || 0}
            </div>
          </div>
        ))}
      </div>

      {test.winnerVariant && (
        <div className="mt-4 p-3 bg-green-50 rounded-md">
          <p className="text-sm font-medium text-green-800">
            Winner: {test.winnerVariant} 
            {test.statisticalSignificance && (
              <span className="ml-2">({test.statisticalSignificance}% confidence)</span>
            )}
          </p>
        </div>
      )}
    </div>
  );
}