import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { ActivityLog } from '../entities/activity-log.entity';

export interface ActivityTrackingConfig {
  trackUserActions: boolean;
  trackSystemEvents: boolean;
  retentionDays: number;
  enableRealTimeMonitoring: boolean;
}

export interface UserActivity {
  userId: string;
  action: string;
  resource: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

@Injectable()
export class ActivityTrackerService {
  private readonly logger = new Logger(ActivityTrackerService.name);

  constructor(
    @InjectRepository(ActivityLog)
    private readonly activityLogRepository: Repository<ActivityLog>,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Track user activity
   */
  async trackUserActivity(activity: UserActivity): Promise<ActivityLog> {
    this.logger.log(`Tracking user activity: ${activity.action} on ${activity.resource}`);

    const activityLog = this.activityLogRepository.create({
      userId: activity.userId,
      action: activity.action,
      resource: activity.resource,
      metadata: activity.metadata || {},
      ipAddress: activity.ipAddress,
      userAgent: activity.userAgent,
      timestamp: new Date(),
      type: 'user_action',
    });

    return this.activityLogRepository.save(activityLog);
  }

  /**
   * Track system event
   */
  async trackSystemEvent(event: string, metadata?: Record<string, any>): Promise<ActivityLog> {
    this.logger.log(`Tracking system event: ${event}`);

    const activityLog = this.activityLogRepository.create({
      action: event,
      resource: 'system',
      metadata: metadata || {},
      timestamp: new Date(),
      type: 'system_event',
    });

    return this.activityLogRepository.save(activityLog);
  }

  /**
   * Get user activities
   */
  async getUserActivities(userId: string, limit = 100): Promise<ActivityLog[]> {
    return this.activityLogRepository.find({
      where: { userId },
      take: limit,
      order: { timestamp: 'DESC' },
    });
  }

  /**
   * Get activities by resource
   */
  async getResourceActivities(resource: string, limit = 100): Promise<ActivityLog[]> {
    return this.activityLogRepository.find({
      where: { resource },
      take: limit,
      order: { timestamp: 'DESC' },
    });
  }

  /**
   * Get activities in date range
   */
  async getActivitiesInRange(startDate: Date, endDate: Date): Promise<ActivityLog[]> {
    return this.activityLogRepository
      .createQueryBuilder('activity')
      .where('activity.timestamp >= :startDate', { startDate })
      .andWhere('activity.timestamp <= :endDate', { endDate })
      .orderBy('activity.timestamp', 'DESC')
      .getMany();
  }

  /**
   * Generate activity summary
   */
  async generateActivitySummary(userId?: string): Promise<{
    totalActivities: number;
    userActions: number;
    systemEvents: number;
    topActions: Array<{ action: string; count: number }>;
    topResources: Array<{ resource: string; count: number }>;
  }> {
    const queryBuilder = this.activityLogRepository.createQueryBuilder('activity');

    if (userId) {
      queryBuilder.where('activity.userId = :userId', { userId });
    }

    const totalActivities = await queryBuilder.getCount();

    const userActions = await queryBuilder
      .andWhere('activity.type = :type', { type: 'user_action' })
      .getCount();

    const systemEvents = await queryBuilder
      .andWhere('activity.type = :type', { type: 'system_event' })
      .getCount();

    // Get top actions
    const topActionsQuery = this.activityLogRepository
      .createQueryBuilder('activity')
      .select('activity.action', 'action')
      .addSelect('COUNT(*)', 'count')
      .groupBy('activity.action')
      .orderBy('count', 'DESC')
      .limit(10);

    if (userId) {
      topActionsQuery.where('activity.userId = :userId', { userId });
    }

    const topActions = await topActionsQuery.getRawMany();

    // Get top resources
    const topResourcesQuery = this.activityLogRepository
      .createQueryBuilder('activity')
      .select('activity.resource', 'resource')
      .addSelect('COUNT(*)', 'count')
      .groupBy('activity.resource')
      .orderBy('count', 'DESC')
      .limit(10);

    if (userId) {
      topResourcesQuery.where('activity.userId = :userId', { userId });
    }

    const topResources = await topResourcesQuery.getRawMany();

    return {
      totalActivities,
      userActions,
      systemEvents,
      topActions: topActions.map(item => ({
        action: item.action,
        count: parseInt(item.count),
      })),
      topResources: topResources.map(item => ({
        resource: item.resource,
        count: parseInt(item.count),
      })),
    };
  }

  /**
   * Cleanup old activities
   */
  async cleanupOldActivities(): Promise<void> {
    const retentionDays = this.configService.get<number>('audit.activityRetentionDays', 365);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const result = await this.activityLogRepository
      .createQueryBuilder()
      .delete()
      .where('timestamp < :cutoffDate', { cutoffDate })
      .execute();

    this.logger.log(`Cleaned up ${result.affected} old activity logs`);
  }
}