interface User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    roles: string[];
    permissions: string[];
    department?: string;
    organizationId?: string;
    avatar?: string;
    metadata?: Record<string, any>;
}
interface AuthTokens {
    accessToken: string;
    refreshToken: string;
    expiresIn?: number;
    tokenType?: string;
}
interface LoginCredentials {
    email: string;
    password: string;
    rememberMe?: boolean;
}
interface RegisterData {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    organizationId?: string;
    department?: string;
}
interface AuthConfig {
    apiBaseUrl: string;
    authEndpoint?: string;
    tokenKey?: string;
    refreshTokenKey?: string;
    userKey?: string;
    enableAutoRefresh?: boolean;
    refreshInterval?: number;
    onAuthError?: (error: AuthError) => void;
    onTokenRefresh?: (tokens: AuthTokens) => void;
    storage?: 'localStorage' | 'sessionStorage' | 'cookie';
    cookieOptions?: {
        domain?: string;
        secure?: boolean;
        sameSite?: 'strict' | 'lax' | 'none';
        httpOnly?: boolean;
    };
}
interface AuthState {
    user: User | null;
    tokens: AuthTokens | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    error: AuthError | null;
}
declare class AuthError extends Error {
    code: string;
    statusCode?: number | undefined;
    details?: any | undefined;
    constructor(message: string, code: string, statusCode?: number | undefined, details?: any | undefined);
}
interface AuthContextValue {
    user: User | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    error: AuthError | null;
    login: (credentials: LoginCredentials) => Promise<User>;
    logout: () => Promise<void>;
    register: (data: RegisterData) => Promise<User>;
    refreshAuth: () => Promise<void>;
    updateUser: (data: Partial<User>) => Promise<User>;
    hasRole: (role: string) => boolean;
    hasPermission: (permission: string) => boolean;
    hasAnyRole: (roles: string[]) => boolean;
    hasAllRoles: (roles: string[]) => boolean;
    hasAnyPermission: (permissions: string[]) => boolean;
    hasAllPermissions: (permissions: string[]) => boolean;
}
interface AuthProviderProps {
    children: React.ReactNode;
    config: AuthConfig;
    initialState?: Partial<AuthState>;
    onAuthStateChange?: (state: AuthState) => void;
}

export { type AuthConfig as A, type LoginCredentials as L, type RegisterData as R, type User as U, type AuthTokens as a, AuthError as b, type AuthState as c, type AuthContextValue as d, type AuthProviderProps as e };
