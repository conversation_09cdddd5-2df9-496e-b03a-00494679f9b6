import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { IntegrationModule } from '../../integration.module';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';

describe('Integration Security E2E Tests', () => {
  let app: INestApplication;
  let validToken: string;
  let userAToken: string;
  let userBToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({ isGlobal: true }),
        JwtModule.register({
          secret: process.env.JWT_SECRET || 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
        IntegrationModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get tokens for different users
    const authA = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ username: 'userA', password: 'test' });
    userAToken = authA.body.access_token;

    const authB = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ username: 'userB', password: 'test' });
    userBToken = authB.body.access_token;

    validToken = userAToken;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Authentication and Authorization', () => {
    it('should reject requests without authentication', async () => {
      await request(app.getHttpServer())
        .get('/api/integration/preferences/user-123')
        .expect(401);

      await request(app.getHttpServer())
        .post('/api/integration/activity')
        .send({ test: true })
        .expect(401);
    });

    it('should reject requests with invalid tokens', async () => {
      await request(app.getHttpServer())
        .get('/api/integration/preferences/user-123')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });

    it('should prevent cross-user data access', async () => {
      // User A creates data
      const activityA = await request(app.getHttpServer())
        .post('/api/integration/activity')
        .set('Authorization', `Bearer ${userAToken}`)
        .send({
          userId: 'userA',
          source: 'e-connect',
          type: 'test',
          data: { secret: 'userA-data' },
        })
        .expect(201);

      // User B tries to access User A's data
      await request(app.getHttpServer())
        .get(`/api/integration/activities/userA`)
        .set('Authorization', `Bearer ${userBToken}`)
        .expect(403);

      // User B tries to modify User A's preferences
      await request(app.getHttpServer())
        .put('/api/integration/preferences/userA')
        .set('Authorization', `Bearer ${userBToken}`)
        .send({ real_time_sync: false })
        .expect(403);
    });

    it('should validate role-based access control', async () => {
      // Regular user cannot access admin endpoints
      await request(app.getHttpServer())
        .get('/api/integration/admin/stats')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(403);

      await request(app.getHttpServer())
        .post('/api/integration/admin/reset')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(403);
    });
  });

  describe('Input Validation and Sanitization', () => {
    it('should reject malformed activity data', async () => {
      const invalidActivities = [
        { userId: null, source: 'test', type: 'test' },
        { userId: 'test', source: null, type: 'test' },
        { userId: 'test', source: 'test', type: null },
        { userId: 'test', source: 'invalid-source', type: 'test' },
        {
          userId: 'test',
          source: 'e-connect',
          type: 'x'.repeat(256), // Too long
        },
      ];

      for (const activity of invalidActivities) {
        await request(app.getHttpServer())
          .post('/api/integration/activity')
          .set('Authorization', `Bearer ${validToken}`)
          .send(activity)
          .expect(400);
      }
    });

    it('should sanitize and escape user input', async () => {
      const xssAttempt = {
        userId: 'test-user',
        source: 'e-connect',
        type: 'email.sent',
        data: {
          subject: '<script>alert("XSS")</script>',
          body: '"><img src=x onerror=alert("XSS")>',
          metadata: {
            injection: "'; DROP TABLE activities; --",
          },
        },
      };

      const response = await request(app.getHttpServer())
        .post('/api/integration/activity')
        .set('Authorization', `Bearer ${validToken}`)
        .send(xssAttempt)
        .expect(201);

      // Verify data was sanitized
      const activity = await request(app.getHttpServer())
        .get(`/api/integration/activity/${response.body.id}`)
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      expect(activity.body.data.subject).not.toContain('<script>');
      expect(activity.body.data.body).not.toContain('onerror=');
      expect(activity.body.data.metadata.injection).toBe(
        "'; DROP TABLE activities; --",
      ); // Should be stored safely as string
    });

    it('should validate webhook signatures', async () => {
      // Valid webhook with signature
      const payload = { test: 'data' };
      const signature = 'valid-hmac-signature'; // Would be computed in real scenario

      await request(app.getHttpServer())
        .post('/api/integration/webhook/e-connect')
        .set('X-Webhook-Signature', signature)
        .send(payload)
        .expect(200);

      // Invalid signature
      await request(app.getHttpServer())
        .post('/api/integration/webhook/e-connect')
        .set('X-Webhook-Signature', 'invalid-signature')
        .send(payload)
        .expect(401);

      // Missing signature
      await request(app.getHttpServer())
        .post('/api/integration/webhook/e-connect')
        .send(payload)
        .expect(401);
    });
  });

  describe('Rate Limiting and DDoS Protection', () => {
    it('should enforce rate limits per user', async () => {
      // Send requests up to the limit
      for (let i = 0; i < 100; i++) {
        await request(app.getHttpServer())
          .post('/api/integration/activity')
          .set('Authorization', `Bearer ${validToken}`)
          .send({
            userId: 'test-user',
            source: 'e-connect',
            type: 'test',
            data: { index: i },
          })
          .expect(201);
      }

      // Next request should be rate limited
      await request(app.getHttpServer())
        .post('/api/integration/activity')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          userId: 'test-user',
          source: 'e-connect',
          type: 'test',
          data: { index: 101 },
        })
        .expect(429);
    });

    it('should implement exponential backoff for repeated failures', async () => {
      const startTime = Date.now();

      // Trigger multiple failures
      for (let i = 0; i < 5; i++) {
        await request(app.getHttpServer())
          .post('/api/integration/activity')
          .set('Authorization', `Bearer ${validToken}`)
          .send({ invalid: 'data' })
          .expect(400);
      }

      // Check if backoff is applied
      const response = await request(app.getHttpServer())
        .post('/api/integration/activity')
        .set('Authorization', `Bearer ${validToken}`)
        .send({ invalid: 'data' });

      const duration = Date.now() - startTime;
      expect(response.status).toBe(429);
      expect(response.body.retryAfter).toBeGreaterThan(0);
    });
  });

  describe('Data Privacy and GDPR Compliance', () => {
    it('should allow users to export their data', async () => {
      // Create some activities
      for (let i = 0; i < 5; i++) {
        await request(app.getHttpServer())
          .post('/api/integration/activity')
          .set('Authorization', `Bearer ${userAToken}`)
          .send({
            userId: 'userA',
            source: 'e-connect',
            type: 'test',
            data: { index: i },
          })
          .expect(201);
      }

      // Request data export
      const exportResponse = await request(app.getHttpServer())
        .post('/api/integration/gdpr/export')
        .set('Authorization', `Bearer ${userAToken}`)
        .expect(200);

      expect(exportResponse.body).toHaveProperty('exportId');
      expect(exportResponse.body.status).toBe('processing');

      // Check export status
      const statusResponse = await request(app.getHttpServer())
        .get(`/api/integration/gdpr/export/${exportResponse.body.exportId}`)
        .set('Authorization', `Bearer ${userAToken}`)
        .expect(200);

      expect(statusResponse.body.status).toMatch(/processing|completed/);
    });

    it('should support right to be forgotten', async () => {
      const testUserId = 'user-to-delete';

      // Create user data
      await request(app.getHttpServer())
        .post('/api/integration/activity')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          userId: testUserId,
          source: 'e-connect',
          type: 'test',
          data: { sensitive: 'information' },
        })
        .expect(201);

      // Request deletion
      const deleteResponse = await request(app.getHttpServer())
        .delete(`/api/integration/gdpr/user/${testUserId}`)
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      expect(deleteResponse.body.status).toBe('scheduled');

      // Verify data is marked for deletion
      const activitiesResponse = await request(app.getHttpServer())
        .get(`/api/integration/activities/${testUserId}`)
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      expect(activitiesResponse.body.activities).toHaveLength(0);
    });

    it('should anonymize data upon request', async () => {
      const testUserId = 'user-to-anonymize';

      // Create identifiable data
      const activity = await request(app.getHttpServer())
        .post('/api/integration/activity')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          userId: testUserId,
          source: 'e-connect',
          type: 'email.sent',
          data: {
            to: '<EMAIL>',
            from: '<EMAIL>',
            subject: 'Meeting with John Doe',
          },
        })
        .expect(201);

      // Request anonymization
      await request(app.getHttpServer())
        .post(`/api/integration/gdpr/anonymize/${testUserId}`)
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      // Verify data is anonymized
      const anonymizedActivity = await request(app.getHttpServer())
        .get(`/api/integration/activity/${activity.body.id}`)
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      expect(anonymizedActivity.body.userId).not.toBe(testUserId);
      expect(anonymizedActivity.body.data.to).not.toContain('john.doe');
      expect(anonymizedActivity.body.data.from).not.toContain('jane.smith');
    });
  });

  describe('Audit Logging and Compliance', () => {
    it('should log all data access attempts', async () => {
      const testUserId = 'audit-test-user';

      // Perform various operations
      await request(app.getHttpServer())
        .get(`/api/integration/preferences/${testUserId}`)
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      await request(app.getHttpServer())
        .put(`/api/integration/preferences/${testUserId}`)
        .set('Authorization', `Bearer ${validToken}`)
        .send({ real_time_sync: false })
        .expect(200);

      // Retrieve audit logs
      const auditResponse = await request(app.getHttpServer())
        .get('/api/integration/audit/logs')
        .set('Authorization', `Bearer ${validToken}`)
        .query({ userId: testUserId })
        .expect(200);

      expect(auditResponse.body.logs).toHaveLength(2);
      expect(auditResponse.body.logs[0]).toHaveProperty('action');
      expect(auditResponse.body.logs[0]).toHaveProperty('timestamp');
      expect(auditResponse.body.logs[0]).toHaveProperty('userId');
    });

    it('should track consent management', async () => {
      const consentData = {
        userId: 'consent-test-user',
        consents: {
          dataSharing: true,
          analytics: false,
          marketing: false,
          thirdPartyIntegration: true,
        },
      };

      // Record consent
      await request(app.getHttpServer())
        .post('/api/integration/gdpr/consent')
        .set('Authorization', `Bearer ${validToken}`)
        .send(consentData)
        .expect(201);

      // Verify consent is enforced
      const preferencesResponse = await request(app.getHttpServer())
        .get(`/api/integration/preferences/${consentData.userId}`)
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);

      expect(preferencesResponse.body.analytics_enabled).toBe(false);
      expect(preferencesResponse.body.third_party_sharing).toBe(true);
    });
  });
});
