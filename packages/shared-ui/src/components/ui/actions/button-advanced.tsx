import { motion, MotionProps } from "framer-motion"
import { cn } from "~/lib/utils"
import { forwardRef, ButtonHTMLAttributes, useState, useEffect, useRef } from "react"
import { LucideIcon } from "lucide-react"
import { 
  animationPresets,
  transitions,
  type ComponentSize,
  componentSizes
} from "~/design-system"
import { createGlassStyles, type GlassConfig } from "~/lib/glass-utils"
import { microInteractions } from "~/lib/micro-interactions"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant,
  defaultComponentProps
} from '~/types/component-props';

export interface LuminarButtonProps extends Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'onDrag' | 'onDragStart' | 'onDragEnd' | 'onAnimationStart' | 'onAnimationEnd'> {
  variant?: ComponentVariant | "destructive" | "outline" | "secondary" | "ghost" | "link" | "glass"
  size?: ComponentSize
  glassConfig?: GlassConfig
  icon?: LucideIcon
  iconPosition?: "left" | "right"
  loading?: boolean
  ripple?: boolean
  glow?: boolean
  animation?: keyof typeof animationPresets
  disableAnimation?: boolean
  magnetic?: boolean
  haptic?: boolean
  enablePerformanceMonitoring?: boolean
}

const LuminarButton = forwardRef<HTMLButtonElement, LuminarButtonProps>(
  ({ 
    className,
    variant = defaultComponentProps.variant,
    size = defaultComponentProps.size,
    glassConfig = { element: 'button' },
    icon: Icon,
    iconPosition = "left",
    loading = false,
    ripple = true,
    glow = false,
    animation = "scale",
    disableAnimation = false,
    magnetic = false,
    haptic = false,
    enablePerformanceMonitoring = false,
    children,
    disabled,
    onClick,
    ...props 
  }, ref) => {
    const [isPressed, setIsPressed] = useState(false)
    const [rippleEffect, setRippleEffect] = useState<{ x: number; y: number; id: number } | null>(null)
    const timeoutRefs = useRef<NodeJS.Timeout[]>([])
    
    // Clean up effects on unmount to prevent AbortError
    useEffect(() => {
      return () => {
        timeoutRefs.current.forEach(id => clearTimeout(id))
        timeoutRefs.current = []
        setRippleEffect(null)
        setIsPressed(false)
      }
    }, [])

    const glassClasses = variant === "glass" 
      ? createGlassStyles({ ...glassConfig, glow })
      : ""

    const sizeClasses = componentSizes[size]
    const animationProps = disableAnimation ? {} : animationPresets[animation]

    const variantStyles = {
      default: "bg-primary text-primary-foreground hover:bg-primary/90",
      destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
      outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
      secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
      ghost: "hover:bg-accent hover:text-accent-foreground",
      link: "text-primary underline-offset-4 hover:underline",
      glass: glassClasses
    }

    const interactionProps = !disableAnimation ? {
      ...microInteractions.buttonPress,
      ...(magnetic ? microInteractions.magnetic : {}),
      onTapStart: () => setIsPressed(true),
      onTap: () => setIsPressed(false),
      onTapCancel: () => setIsPressed(false)
    } : {}

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (disabled || loading) return

      if (ripple && !disableAnimation) {
        const rect = e.currentTarget.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top
        const rippleId = Date.now()
        setRippleEffect({ x, y, id: rippleId })
        
        // Use proper cleanup for ripple effect
        const timeoutId = setTimeout(() => {
          setRippleEffect(prev => prev?.id === rippleId ? null : prev)
        }, 600)
        
        // Store timeout reference for cleanup
        timeoutRefs.current.push(timeoutId)
      }

      if (haptic && 'vibrate' in navigator) {
        try {
          navigator.vibrate(10)
        } catch (error) {
          // Silently fail if vibration is not supported
        }
      }

      onClick?.(e)
    }

    const buttonContent = (
      <motion.button
        ref={ref}
        className={cn(
          "relative inline-flex items-center justify-center overflow-hidden",
          "whitespace-nowrap font-medium ring-offset-background",
          "transition-colors focus-visible:outline-none focus-visible:ring-2",
          "focus-visible:ring-ring focus-visible:ring-offset-2",
          "disabled:pointer-events-none disabled:opacity-50",
          "text-crisp", // Enhanced text clarity
          variant === 'glass' && "glass-text dark:glass-text-dark glass-optimized glass-accessible glass-wcag-compliant", // Glass-specific text enhancement, optimizations, and accessibility
          sizeClasses.padding,
          sizeClasses.text,
          sizeClasses.height,
          sizeClasses.radius,
          sizeClasses.gap,
          variantStyles[variant],
          glow && variant !== 'glass' && "shadow-lg shadow-current/25",
          loading && "cursor-wait",
          "will-change-transform backface-hidden", // Performance optimization
          className
        )}
        disabled={disabled || loading}
        onClick={handleClick}
        {...animationProps}
        {...interactionProps}
        transition={{
          ...transitions.default,
          // Prevent AbortError by ensuring animations complete properly
          duration: 0.2,
          type: "tween"
        } as any}
        // Add layout to prevent layout shift issues
        layout={false}
        // Accessibility improvements for glass variant
        aria-label={variant === 'glass' && !props['aria-label'] ? `Glass button: ${children}` : props['aria-label']}
        role="button"
        {...props}
      >
        {loading && (
          <motion.div
            className="absolute inset-0 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2, type: "tween" }}
          >
            <motion.div
              className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
              animate={{ rotate: 360 }}
              transition={{ 
                duration: 1, 
                repeat: Infinity, 
                ease: "linear",
                type: "tween"
              }}
            />
          </motion.div>
        )}

        <motion.div
          className={cn(
            "flex items-center gap-2",
            loading && "opacity-0",
            // Ensure text container has deterministic background for accessibility
            variant === 'glass' && "glass-text-container"
          )}
          data-glass-text={variant === 'glass' ? "true" : undefined}
          style={variant === 'glass' ? {
            backgroundColor: 'var(--glass-effective-bg)',
            color: 'var(--glass-effective-text)',
            position: 'relative',
            zIndex: 1
          } : undefined}
          animate={{ opacity: loading ? 0 : 1 }}
          transition={{ duration: 0.2, type: "tween" }}
        >
          {Icon && iconPosition === "left" && (
            <Icon className={cn(sizeClasses.icon, "flex-shrink-0")} />
          )}
          
          {children}
          
          {Icon && iconPosition === "right" && (
            <Icon className={cn(sizeClasses.icon, "flex-shrink-0")} />
          )}
        </motion.div>

        {rippleEffect && (
          <motion.div
            className="absolute rounded-full bg-white/30 pointer-events-none"
            style={{
              left: rippleEffect.x - 10,
              top: rippleEffect.y - 10,
              width: 20,
              height: 20,
            }}
            initial={{ scale: 0, opacity: 1 }}
            animate={{ scale: 4, opacity: 0 }}
            transition={{ duration: 0.6, ease: "easeOut", type: "tween" }}
            key={rippleEffect.id}
          />
        )}

        {glow && !disableAnimation && variant !== 'glass' && (
          <motion.div
            className="absolute inset-0 rounded-md bg-gradient-to-r from-transparent via-white/10 to-transparent"
            animate={{
              x: ["-100%", "100%"],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "linear",
              type: "tween"
            }}
          />
        )}

        {isPressed && (
          <div className="absolute inset-0 bg-black/10 rounded-md" />
        )}
      </motion.button>
    )

    return buttonContent
  }
)
LuminarButton.displayName = "LuminarButton"

export { LuminarButton }
