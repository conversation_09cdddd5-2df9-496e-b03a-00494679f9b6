/**
 * Base API Response structure
 */
interface ApiResponse<T = any> {
    data: T;
    message?: string;
    success: boolean;
    timestamp: string;
    requestId?: string;
}
/**
 * Paginated API Response
 */
interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}
/**
 * API Error Response
 */
interface ApiError {
    code: string;
    message: string;
    details?: Record<string, any>;
    timestamp: string;
    path?: string;
    requestId?: string;
}
/**
 * Pagination parameters
 */
interface PaginationParams {
    page?: number;
    limit?: number;
    offset?: number;
}
/**
 * Sorting parameters
 */
interface SortParams {
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
/**
 * Search parameters
 */
interface SearchParams {
    query?: string;
    filters?: Record<string, any>;
}
/**
 * Base query parameters combining pagination, sorting, and search
 */
interface BaseQueryParams extends PaginationParams, SortParams, SearchParams {
}
/**
 * User base interface
 */
interface BaseUser {
    id: string;
    email: string;
    name: string;
    avatar?: string;
    roles: string[];
    permissions: string[];
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
}
/**
 * Authentication state
 */
interface AuthState {
    user: BaseUser | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    error: string | null;
    tokens?: {
        accessToken: string;
        refreshToken: string;
        expiresAt: string;
    };
}
/**
 * Login credentials
 */
interface LoginCredentials {
    email: string;
    password: string;
    rememberMe?: boolean;
}
/**
 * Registration data
 */
interface RegisterData {
    email: string;
    password: string;
    name: string;
    confirmPassword: string;
    acceptTerms: boolean;
}
/**
 * File upload types
 */
interface FileUpload {
    id: string;
    name: string;
    size: number;
    type: string;
    url: string;
    uploadedAt: string;
    uploadedBy: string;
}
/**
 * Notification types
 */
interface Notification {
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    isRead: boolean;
    createdAt: string;
    expiresAt?: string;
    actions?: NotificationAction[];
}
interface NotificationAction {
    label: string;
    action: string;
    variant?: 'primary' | 'secondary' | 'danger';
}
/**
 * Theme configuration
 */
interface ThemeConfig {
    mode: 'light' | 'dark' | 'system';
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
    borderRadius: 'none' | 'sm' | 'md' | 'lg' | 'xl';
    fontFamily: string;
}
/**
 * App configuration
 */
interface AppConfig {
    name: string;
    version: string;
    environment: 'development' | 'staging' | 'production';
    apiUrl: string;
    wsUrl?: string;
    features: Record<string, boolean>;
    theme: ThemeConfig;
}
/**
 * Form field types
 */
interface FormField {
    name: string;
    label: string;
    type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'file' | 'date';
    placeholder?: string;
    required?: boolean;
    disabled?: boolean;
    options?: Array<{
        label: string;
        value: any;
    }>;
    validation?: {
        min?: number;
        max?: number;
        pattern?: string;
        custom?: (value: any) => string | null;
    };
}
/**
 * Table column definition
 */
interface TableColumn<T = any> {
    key: keyof T;
    title: string;
    sortable?: boolean;
    filterable?: boolean;
    width?: number | string;
    align?: 'left' | 'center' | 'right';
    render?: (value: any, record: T, index: number) => React.ReactNode;
}
/**
 * Chart data types
 */
interface ChartDataPoint {
    label: string;
    value: number;
    color?: string;
}
interface TimeSeriesDataPoint {
    timestamp: string;
    value: number;
    label?: string;
}
/**
 * Analytics data
 */
interface AnalyticsData {
    metrics: Record<string, number>;
    trends: Record<string, TimeSeriesDataPoint[]>;
    comparisons: Record<string, {
        current: number;
        previous: number;
        change: number;
    }>;
}
/**
 * Feature flag
 */
interface FeatureFlag {
    key: string;
    enabled: boolean;
    description?: string;
    conditions?: Record<string, any>;
}
/**
 * Audit log entry
 */
interface AuditLogEntry {
    id: string;
    userId: string;
    action: string;
    resource: string;
    resourceId?: string;
    details: Record<string, any>;
    timestamp: string;
    ipAddress?: string;
    userAgent?: string;
}
/**
 * Generic entity with common fields
 */
interface BaseEntity {
    id: string;
    createdAt: string;
    updatedAt: string;
    createdBy?: string;
    updatedBy?: string;
    version?: number;
}
/**
 * Status types
 */
type Status = 'active' | 'inactive' | 'pending' | 'archived' | 'deleted';
/**
 * Priority levels
 */
type Priority = 'low' | 'medium' | 'high' | 'urgent';
/**
 * Generic key-value pair
 */
interface KeyValuePair<T = string> {
    key: string;
    value: T;
}
/**
 * Environment variables type
 */
interface EnvironmentVariables {
    NODE_ENV: 'development' | 'staging' | 'production';
    API_URL: string;
    WS_URL?: string;
    DATABASE_URL?: string;
    REDIS_URL?: string;
    JWT_SECRET?: string;
    [key: string]: string | undefined;
}

export type { AnalyticsData, ApiError, ApiResponse, AppConfig, AuditLogEntry, AuthState, BaseEntity, BaseQueryParams, BaseUser, ChartDataPoint, EnvironmentVariables, FeatureFlag, FileUpload, FormField, KeyValuePair, LoginCredentials, Notification, NotificationAction, PaginatedResponse, PaginationParams, Priority, RegisterData, SearchParams, SortParams, Status, TableColumn, ThemeConfig, TimeSeriesDataPoint };
