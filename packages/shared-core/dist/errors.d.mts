/**
 * Base error class for Luminar applications
 */
declare class LuminarError extends Error {
    readonly code: string;
    readonly statusCode?: number;
    readonly details?: Record<string, any>;
    readonly timestamp: string;
    constructor(message: string, code?: string, statusCode?: number, details?: Record<string, any>);
    toJSON(): {
        name: string;
        message: string;
        code: string;
        statusCode: number | undefined;
        details: Record<string, any> | undefined;
        timestamp: string;
        stack: string | undefined;
    };
}
/**
 * Authentication related errors
 */
declare class AuthError extends LuminarError {
    constructor(message: string, code?: string, details?: Record<string, any>);
}
/**
 * Validation related errors
 */
declare class ValidationError extends LuminarError {
    constructor(message: string, code?: string, details?: Record<string, any>);
}
/**
 * Network related errors
 */
declare class NetworkError extends LuminarError {
    constructor(message: string, code?: string, details?: Record<string, any>);
}
/**
 * File upload related errors
 */
declare class FileError extends LuminarError {
    constructor(message: string, code?: string, details?: Record<string, any>);
}
/**
 * Permission related errors
 */
declare class PermissionError extends LuminarError {
    constructor(message: string, code?: string, details?: Record<string, any>);
}
/**
 * Resource not found errors
 */
declare class NotFoundError extends LuminarError {
    constructor(message: string, code?: string, details?: Record<string, any>);
}
/**
 * Conflict errors (resource already exists)
 */
declare class ConflictError extends LuminarError {
    constructor(message: string, code?: string, details?: Record<string, any>);
}
/**
 * Error factory functions for common scenarios
 */
declare const createAuthError: {
    invalidCredentials: (details?: Record<string, any>) => AuthError;
    tokenExpired: (details?: Record<string, any>) => AuthError;
    tokenInvalid: (details?: Record<string, any>) => AuthError;
    insufficientPermissions: (details?: Record<string, any>) => PermissionError;
    accountLocked: (details?: Record<string, any>) => AuthError;
    accountNotVerified: (details?: Record<string, any>) => AuthError;
};
declare const createValidationError: {
    requiredField: (fieldName: string, details?: Record<string, any>) => ValidationError;
    invalidFormat: (fieldName: string, details?: Record<string, any>) => ValidationError;
    minLength: (fieldName: string, minLength: number, details?: Record<string, any>) => ValidationError;
    maxLength: (fieldName: string, maxLength: number, details?: Record<string, any>) => ValidationError;
    invalidEmail: (details?: Record<string, any>) => ValidationError;
    weakPassword: (details?: Record<string, any>) => ValidationError;
};
declare const createNetworkError: {
    connectionError: (details?: Record<string, any>) => NetworkError;
    timeout: (details?: Record<string, any>) => NetworkError;
    serverError: (details?: Record<string, any>) => NetworkError;
};
declare const createFileError: {
    tooLarge: (maxSize: number, details?: Record<string, any>) => FileError;
    invalidType: (allowedTypes: string[], details?: Record<string, any>) => FileError;
    uploadFailed: (details?: Record<string, any>) => FileError;
};
/**
 * Error handler utility functions
 */
declare function isLuminarError(error: any): error is LuminarError;
declare function isAuthError(error: any): error is AuthError;
declare function isValidationError(error: any): error is ValidationError;
declare function isNetworkError(error: any): error is NetworkError;
declare function isFileError(error: any): error is FileError;
declare function isPermissionError(error: any): error is PermissionError;
declare function isNotFoundError(error: any): error is NotFoundError;
declare function isConflictError(error: any): error is ConflictError;
/**
 * Convert unknown error to LuminarError
 */
declare function toLuminarError(error: unknown): LuminarError;
/**
 * Error logging utility
 */
declare function logError(error: LuminarError, context?: Record<string, any>): void;
/**
 * Error boundary helper for React components
 */
declare function getErrorMessage(error: unknown): string;
/**
 * HTTP status code to error mapping
 */
declare function createErrorFromStatus(status: number, message?: string, details?: Record<string, any>): LuminarError;

export { AuthError, ConflictError, FileError, LuminarError, NetworkError, NotFoundError, PermissionError, ValidationError, createAuthError, createErrorFromStatus, createFileError, createNetworkError, createValidationError, getErrorMessage, isAuthError, isConflictError, isFileError, isLuminarError, isNetworkError, isNotFoundError, isPermissionError, isValidationError, logError, toLuminarError };
