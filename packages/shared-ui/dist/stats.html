
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title>Rollup Visualizer</title>
  <style>
:root {
  --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l,
    "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
    "Noto Color Emoji";
  --background-color: #2b2d42;
  --text-color: #edf2f4;
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

html {
  background-color: var(--background-color);
  color: var(--text-color);
  font-family: var(--font-family);
}

body {
  padding: 0;
  margin: 0;
}

html,
body {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

body {
  display: flex;
  flex-direction: column;
}

svg {
  vertical-align: middle;
  width: 100%;
  height: 100%;
  max-height: 100vh;
}

main {
  flex-grow: 1;
  height: 100vh;
  padding: 20px;
}

.tooltip {
  position: absolute;
  z-index: 1070;
  border: 2px solid;
  border-radius: 5px;
  padding: 5px;
  font-size: 0.875rem;
  background-color: var(--background-color);
  color: var(--text-color);
}

.tooltip-hidden {
  visibility: hidden;
  opacity: 0;
}

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: row;
  font-size: 0.7rem;
  align-items: center;
  margin: 0 50px;
  height: 20px;
}

.size-selectors {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.size-selector {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}
.size-selector input {
  margin: 0 0.3rem 0 0;
}

.filters {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.module-filters {
  display: flex;
  flex-grow: 1;
}

.module-filter {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex: 1;
}
.module-filter input {
  flex: 1;
  height: 1rem;
  padding: 0.01rem;
  font-size: 0.7rem;
  margin-left: 0.3rem;
}
.module-filter + .module-filter {
  margin-left: 0.5rem;
}

.node {
  cursor: pointer;
}
  </style>
</head>
<body>
  <main></main>
  <script>
  /*<!--*/
var drawChart = (function (exports) {
  'use strict';

  var n,l$1,u$2,i$1,r$1,o$1,e$1,f$2,c$1,s$1,a$1,h$1,p$1={},v$1=[],y$1=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,w$1=Array.isArray;function d$1(n,l){for(var u in l)n[u]=l[u];return n}function g(n){n&&n.parentNode&&n.parentNode.removeChild(n);}function _$1(l,u,t){var i,r,o,e={};for(o in u)"key"==o?i=u[o]:"ref"==o?r=u[o]:e[o]=u[o];if(arguments.length>2&&(e.children=arguments.length>3?n.call(arguments,2):t),"function"==typeof l&&null!=l.defaultProps)for(o in l.defaultProps) void 0===e[o]&&(e[o]=l.defaultProps[o]);return m$1(l,e,i,r,null)}function m$1(n,t,i,r,o){var e={type:n,props:t,key:i,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==o?++u$2:o,__i:-1,__u:0};return null==o&&null!=l$1.vnode&&l$1.vnode(e),e}function k$1(n){return n.children}function x$1(n,l){this.props=n,this.context=l;}function S(n,l){if(null==l)return n.__?S(n.__,n.__i+1):null;for(var u;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e)return u.__e;return "function"==typeof n.type?S(n):null}function C$1(n){var l,u;if(null!=(n=n.__)&&null!=n.__c){for(n.__e=n.__c.base=null,l=0;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e){n.__e=n.__c.base=u.__e;break}return C$1(n)}}function M(n){(!n.__d&&(n.__d=true)&&i$1.push(n)&&!$.__r++||r$1!=l$1.debounceRendering)&&((r$1=l$1.debounceRendering)||o$1)($);}function $(){for(var n,u,t,r,o,f,c,s=1;i$1.length;)i$1.length>s&&i$1.sort(e$1),n=i$1.shift(),s=i$1.length,n.__d&&(t=void 0,o=(r=(u=n).__v).__e,f=[],c=[],u.__P&&((t=d$1({},r)).__v=r.__v+1,l$1.vnode&&l$1.vnode(t),O(u.__P,t,r,u.__n,u.__P.namespaceURI,32&r.__u?[o]:null,f,null==o?S(r):o,!!(32&r.__u),c),t.__v=r.__v,t.__.__k[t.__i]=t,z$1(f,t,c),t.__e!=o&&C$1(t)));$.__r=0;}function I(n,l,u,t,i,r,o,e,f,c,s){var a,h,y,w,d,g,_=t&&t.__k||v$1,m=l.length;for(f=P(u,l,_,f,m),a=0;a<m;a++)null!=(y=u.__k[a])&&(h=-1==y.__i?p$1:_[y.__i]||p$1,y.__i=a,g=O(n,y,h,i,r,o,e,f,c,s),w=y.__e,y.ref&&h.ref!=y.ref&&(h.ref&&q$1(h.ref,null,y),s.push(y.ref,y.__c||w,y)),null==d&&null!=w&&(d=w),4&y.__u||h.__k===y.__k?f=A$1(y,f,n):"function"==typeof y.type&&void 0!==g?f=g:w&&(f=w.nextSibling),y.__u&=-7);return u.__e=d,f}function P(n,l,u,t,i){var r,o,e,f,c,s=u.length,a=s,h=0;for(n.__k=new Array(i),r=0;r<i;r++)null!=(o=l[r])&&"boolean"!=typeof o&&"function"!=typeof o?(f=r+h,(o=n.__k[r]="string"==typeof o||"number"==typeof o||"bigint"==typeof o||o.constructor==String?m$1(null,o,null,null,null):w$1(o)?m$1(k$1,{children:o},null,null,null):null==o.constructor&&o.__b>0?m$1(o.type,o.props,o.key,o.ref?o.ref:null,o.__v):o).__=n,o.__b=n.__b+1,e=null,-1!=(c=o.__i=L(o,u,f,a))&&(a--,(e=u[c])&&(e.__u|=2)),null==e||null==e.__v?(-1==c&&(i>s?h--:i<s&&h++),"function"!=typeof o.type&&(o.__u|=4)):c!=f&&(c==f-1?h--:c==f+1?h++:(c>f?h--:h++,o.__u|=4))):n.__k[r]=null;if(a)for(r=0;r<s;r++)null!=(e=u[r])&&0==(2&e.__u)&&(e.__e==t&&(t=S(e)),B$1(e,e));return t}function A$1(n,l,u){var t,i;if("function"==typeof n.type){for(t=n.__k,i=0;t&&i<t.length;i++)t[i]&&(t[i].__=n,l=A$1(t[i],l,u));return l}n.__e!=l&&(l&&n.type&&!u.contains(l)&&(l=S(n)),u.insertBefore(n.__e,l||null),l=n.__e);do{l=l&&l.nextSibling;}while(null!=l&&8==l.nodeType);return l}function L(n,l,u,t){var i,r,o=n.key,e=n.type,f=l[u];if(null===f&&null==n.key||f&&o==f.key&&e==f.type&&0==(2&f.__u))return u;if(t>(null!=f&&0==(2&f.__u)?1:0))for(i=u-1,r=u+1;i>=0||r<l.length;){if(i>=0){if((f=l[i])&&0==(2&f.__u)&&o==f.key&&e==f.type)return i;i--;}if(r<l.length){if((f=l[r])&&0==(2&f.__u)&&o==f.key&&e==f.type)return r;r++;}}return  -1}function T$1(n,l,u){"-"==l[0]?n.setProperty(l,null==u?"":u):n[l]=null==u?"":"number"!=typeof u||y$1.test(l)?u:u+"px";}function j$1(n,l,u,t,i){var r,o;n:if("style"==l)if("string"==typeof u)n.style.cssText=u;else {if("string"==typeof t&&(n.style.cssText=t=""),t)for(l in t)u&&l in u||T$1(n.style,l,"");if(u)for(l in u)t&&u[l]==t[l]||T$1(n.style,l,u[l]);}else if("o"==l[0]&&"n"==l[1])r=l!=(l=l.replace(f$2,"$1")),o=l.toLowerCase(),l=o in n||"onFocusOut"==l||"onFocusIn"==l?o.slice(2):l.slice(2),n.l||(n.l={}),n.l[l+r]=u,u?t?u.u=t.u:(u.u=c$1,n.addEventListener(l,r?a$1:s$1,r)):n.removeEventListener(l,r?a$1:s$1,r);else {if("http://www.w3.org/2000/svg"==i)l=l.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=l&&"height"!=l&&"href"!=l&&"list"!=l&&"form"!=l&&"tabIndex"!=l&&"download"!=l&&"rowSpan"!=l&&"colSpan"!=l&&"role"!=l&&"popover"!=l&&l in n)try{n[l]=null==u?"":u;break n}catch(n){}"function"==typeof u||(null==u||false===u&&"-"!=l[4]?n.removeAttribute(l):n.setAttribute(l,"popover"==l&&1==u?"":u));}}function F(n){return function(u){if(this.l){var t=this.l[u.type+n];if(null==u.t)u.t=c$1++;else if(u.t<t.u)return;return t(l$1.event?l$1.event(u):u)}}}function O(n,u,t,i,r,o,e,f,c,s){var a,h,p,v,y,_,m,b,S,C,M,$,P,A,H,L,T,j=u.type;if(null!=u.constructor)return null;128&t.__u&&(c=!!(32&t.__u),o=[f=u.__e=t.__e]),(a=l$1.__b)&&a(u);n:if("function"==typeof j)try{if(b=u.props,S="prototype"in j&&j.prototype.render,C=(a=j.contextType)&&i[a.__c],M=a?C?C.props.value:a.__:i,t.__c?m=(h=u.__c=t.__c).__=h.__E:(S?u.__c=h=new j(b,M):(u.__c=h=new x$1(b,M),h.constructor=j,h.render=D$1),C&&C.sub(h),h.props=b,h.state||(h.state={}),h.context=M,h.__n=i,p=h.__d=!0,h.__h=[],h._sb=[]),S&&null==h.__s&&(h.__s=h.state),S&&null!=j.getDerivedStateFromProps&&(h.__s==h.state&&(h.__s=d$1({},h.__s)),d$1(h.__s,j.getDerivedStateFromProps(b,h.__s))),v=h.props,y=h.state,h.__v=u,p)S&&null==j.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),S&&null!=h.componentDidMount&&h.__h.push(h.componentDidMount);else {if(S&&null==j.getDerivedStateFromProps&&b!==v&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(b,M),!h.__e&&null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(b,h.__s,M)||u.__v==t.__v){for(u.__v!=t.__v&&(h.props=b,h.state=h.__s,h.__d=!1),u.__e=t.__e,u.__k=t.__k,u.__k.some(function(n){n&&(n.__=u);}),$=0;$<h._sb.length;$++)h.__h.push(h._sb[$]);h._sb=[],h.__h.length&&e.push(h);break n}null!=h.componentWillUpdate&&h.componentWillUpdate(b,h.__s,M),S&&null!=h.componentDidUpdate&&h.__h.push(function(){h.componentDidUpdate(v,y,_);});}if(h.context=M,h.props=b,h.__P=n,h.__e=!1,P=l$1.__r,A=0,S){for(h.state=h.__s,h.__d=!1,P&&P(u),a=h.render(h.props,h.state,h.context),H=0;H<h._sb.length;H++)h.__h.push(h._sb[H]);h._sb=[];}else do{h.__d=!1,P&&P(u),a=h.render(h.props,h.state,h.context),h.state=h.__s;}while(h.__d&&++A<25);h.state=h.__s,null!=h.getChildContext&&(i=d$1(d$1({},i),h.getChildContext())),S&&!p&&null!=h.getSnapshotBeforeUpdate&&(_=h.getSnapshotBeforeUpdate(v,y)),L=a,null!=a&&a.type===k$1&&null==a.key&&(L=N(a.props.children)),f=I(n,w$1(L)?L:[L],u,t,i,r,o,e,f,c,s),h.base=u.__e,u.__u&=-161,h.__h.length&&e.push(h),m&&(h.__E=h.__=null);}catch(n){if(u.__v=null,c||null!=o)if(n.then){for(u.__u|=c?160:128;f&&8==f.nodeType&&f.nextSibling;)f=f.nextSibling;o[o.indexOf(f)]=null,u.__e=f;}else for(T=o.length;T--;)g(o[T]);else u.__e=t.__e,u.__k=t.__k;l$1.__e(n,u,t);}else null==o&&u.__v==t.__v?(u.__k=t.__k,u.__e=t.__e):f=u.__e=V(t.__e,u,t,i,r,o,e,c,s);return (a=l$1.diffed)&&a(u),128&u.__u?void 0:f}function z$1(n,u,t){for(var i=0;i<t.length;i++)q$1(t[i],t[++i],t[++i]);l$1.__c&&l$1.__c(u,n),n.some(function(u){try{n=u.__h,u.__h=[],n.some(function(n){n.call(u);});}catch(n){l$1.__e(n,u.__v);}});}function N(n){return "object"!=typeof n||null==n||n.__b&&n.__b>0?n:w$1(n)?n.map(N):d$1({},n)}function V(u,t,i,r,o,e,f,c,s){var a,h,v,y,d,_,m,b=i.props,k=t.props,x=t.type;if("svg"==x?o="http://www.w3.org/2000/svg":"math"==x?o="http://www.w3.org/1998/Math/MathML":o||(o="http://www.w3.org/1999/xhtml"),null!=e)for(a=0;a<e.length;a++)if((d=e[a])&&"setAttribute"in d==!!x&&(x?d.localName==x:3==d.nodeType)){u=d,e[a]=null;break}if(null==u){if(null==x)return document.createTextNode(k);u=document.createElementNS(o,x,k.is&&k),c&&(l$1.__m&&l$1.__m(t,e),c=false),e=null;}if(null==x)b===k||c&&u.data==k||(u.data=k);else {if(e=e&&n.call(u.childNodes),b=i.props||p$1,!c&&null!=e)for(b={},a=0;a<u.attributes.length;a++)b[(d=u.attributes[a]).name]=d.value;for(a in b)if(d=b[a],"children"==a);else if("dangerouslySetInnerHTML"==a)v=d;else if(!(a in k)){if("value"==a&&"defaultValue"in k||"checked"==a&&"defaultChecked"in k)continue;j$1(u,a,null,d,o);}for(a in k)d=k[a],"children"==a?y=d:"dangerouslySetInnerHTML"==a?h=d:"value"==a?_=d:"checked"==a?m=d:c&&"function"!=typeof d||b[a]===d||j$1(u,a,d,b[a],o);if(h)c||v&&(h.__html==v.__html||h.__html==u.innerHTML)||(u.innerHTML=h.__html),t.__k=[];else if(v&&(u.innerHTML=""),I("template"==t.type?u.content:u,w$1(y)?y:[y],t,i,r,"foreignObject"==x?"http://www.w3.org/1999/xhtml":o,e,f,e?e[0]:i.__k&&S(i,0),c,s),null!=e)for(a=e.length;a--;)g(e[a]);c||(a="value","progress"==x&&null==_?u.removeAttribute("value"):null!=_&&(_!==u[a]||"progress"==x&&!_||"option"==x&&_!=b[a])&&j$1(u,a,_,b[a],o),a="checked",null!=m&&m!=u[a]&&j$1(u,a,m,b[a],o));}return u}function q$1(n,u,t){try{if("function"==typeof n){var i="function"==typeof n.__u;i&&n.__u(),i&&null==u||(n.__u=n(u));}else n.current=u;}catch(n){l$1.__e(n,t);}}function B$1(n,u,t){var i,r;if(l$1.unmount&&l$1.unmount(n),(i=n.ref)&&(i.current&&i.current!=n.__e||q$1(i,null,u)),null!=(i=n.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount();}catch(n){l$1.__e(n,u);}i.base=i.__P=null;}if(i=n.__k)for(r=0;r<i.length;r++)i[r]&&B$1(i[r],u,t||"function"!=typeof n.type);t||g(n.__e),n.__c=n.__=n.__e=void 0;}function D$1(n,l,u){return this.constructor(n,u)}function E(u,t,i){var r,o,e,f;t==document&&(t=document.documentElement),l$1.__&&l$1.__(u,t),o=(r="function"=="undefined")?null:t.__k,e=[],f=[],O(t,u=(t).__k=_$1(k$1,null,[u]),o||p$1,p$1,t.namespaceURI,o?null:t.firstChild?n.call(t.childNodes):null,e,o?o.__e:t.firstChild,r,f),z$1(e,u,f);}function K(n){function l(n){var u,t;return this.getChildContext||(u=new Set,(t={})[l.__c]=this,this.getChildContext=function(){return t},this.componentWillUnmount=function(){u=null;},this.shouldComponentUpdate=function(n){this.props.value!=n.value&&u.forEach(function(n){n.__e=true,M(n);});},this.sub=function(n){u.add(n);var l=n.componentWillUnmount;n.componentWillUnmount=function(){u&&u.delete(n),l&&l.call(n);};}),n.children}return l.__c="__cC"+h$1++,l.__=n,l.Provider=l.__l=(l.Consumer=function(n,l){return n.children(l)}).contextType=l,l}n=v$1.slice,l$1={__e:function(n,l,u,t){for(var i,r,o;l=l.__;)if((i=l.__c)&&!i.__)try{if((r=i.constructor)&&null!=r.getDerivedStateFromError&&(i.setState(r.getDerivedStateFromError(n)),o=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(n,t||{}),o=i.__d),o)return i.__E=i}catch(l){n=l;}throw n}},u$2=0,x$1.prototype.setState=function(n,l){var u;u=null!=this.__s&&this.__s!=this.state?this.__s:this.__s=d$1({},this.state),"function"==typeof n&&(n=n(d$1({},u),this.props)),n&&d$1(u,n),null!=n&&this.__v&&(l&&this._sb.push(l),M(this));},x$1.prototype.forceUpdate=function(n){this.__v&&(this.__e=true,n&&this.__h.push(n),M(this));},x$1.prototype.render=k$1,i$1=[],o$1="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,e$1=function(n,l){return n.__v.__b-l.__v.__b},$.__r=0,f$2=/(PointerCapture)$|Capture$/i,c$1=0,s$1=F(false),a$1=F(true),h$1=0;

  var f$1=0;function u$1(e,t,n,o,i,u){t||(t={});var a,c,p=t;if("ref"in p)for(c in p={},t)"ref"==c?a=t[c]:p[c]=t[c];var l={type:e,props:p,key:n,ref:a,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:--f$1,__i:-1,__u:0,__source:i,__self:u};if("function"==typeof e&&(a=e.defaultProps))for(c in a) void 0===p[c]&&(p[c]=a[c]);return l$1.vnode&&l$1.vnode(l),l}

  function count$1(node) {
    var sum = 0,
        children = node.children,
        i = children && children.length;
    if (!i) sum = 1;
    else while (--i >= 0) sum += children[i].value;
    node.value = sum;
  }

  function node_count() {
    return this.eachAfter(count$1);
  }

  function node_each(callback, that) {
    let index = -1;
    for (const node of this) {
      callback.call(that, node, ++index, this);
    }
    return this;
  }

  function node_eachBefore(callback, that) {
    var node = this, nodes = [node], children, i, index = -1;
    while (node = nodes.pop()) {
      callback.call(that, node, ++index, this);
      if (children = node.children) {
        for (i = children.length - 1; i >= 0; --i) {
          nodes.push(children[i]);
        }
      }
    }
    return this;
  }

  function node_eachAfter(callback, that) {
    var node = this, nodes = [node], next = [], children, i, n, index = -1;
    while (node = nodes.pop()) {
      next.push(node);
      if (children = node.children) {
        for (i = 0, n = children.length; i < n; ++i) {
          nodes.push(children[i]);
        }
      }
    }
    while (node = next.pop()) {
      callback.call(that, node, ++index, this);
    }
    return this;
  }

  function node_find(callback, that) {
    let index = -1;
    for (const node of this) {
      if (callback.call(that, node, ++index, this)) {
        return node;
      }
    }
  }

  function node_sum(value) {
    return this.eachAfter(function(node) {
      var sum = +value(node.data) || 0,
          children = node.children,
          i = children && children.length;
      while (--i >= 0) sum += children[i].value;
      node.value = sum;
    });
  }

  function node_sort(compare) {
    return this.eachBefore(function(node) {
      if (node.children) {
        node.children.sort(compare);
      }
    });
  }

  function node_path(end) {
    var start = this,
        ancestor = leastCommonAncestor(start, end),
        nodes = [start];
    while (start !== ancestor) {
      start = start.parent;
      nodes.push(start);
    }
    var k = nodes.length;
    while (end !== ancestor) {
      nodes.splice(k, 0, end);
      end = end.parent;
    }
    return nodes;
  }

  function leastCommonAncestor(a, b) {
    if (a === b) return a;
    var aNodes = a.ancestors(),
        bNodes = b.ancestors(),
        c = null;
    a = aNodes.pop();
    b = bNodes.pop();
    while (a === b) {
      c = a;
      a = aNodes.pop();
      b = bNodes.pop();
    }
    return c;
  }

  function node_ancestors() {
    var node = this, nodes = [node];
    while (node = node.parent) {
      nodes.push(node);
    }
    return nodes;
  }

  function node_descendants() {
    return Array.from(this);
  }

  function node_leaves() {
    var leaves = [];
    this.eachBefore(function(node) {
      if (!node.children) {
        leaves.push(node);
      }
    });
    return leaves;
  }

  function node_links() {
    var root = this, links = [];
    root.each(function(node) {
      if (node !== root) { // Don’t include the root’s parent, if any.
        links.push({source: node.parent, target: node});
      }
    });
    return links;
  }

  function* node_iterator() {
    var node = this, current, next = [node], children, i, n;
    do {
      current = next.reverse(), next = [];
      while (node = current.pop()) {
        yield node;
        if (children = node.children) {
          for (i = 0, n = children.length; i < n; ++i) {
            next.push(children[i]);
          }
        }
      }
    } while (next.length);
  }

  function hierarchy(data, children) {
    if (data instanceof Map) {
      data = [undefined, data];
      if (children === undefined) children = mapChildren;
    } else if (children === undefined) {
      children = objectChildren;
    }

    var root = new Node$1(data),
        node,
        nodes = [root],
        child,
        childs,
        i,
        n;

    while (node = nodes.pop()) {
      if ((childs = children(node.data)) && (n = (childs = Array.from(childs)).length)) {
        node.children = childs;
        for (i = n - 1; i >= 0; --i) {
          nodes.push(child = childs[i] = new Node$1(childs[i]));
          child.parent = node;
          child.depth = node.depth + 1;
        }
      }
    }

    return root.eachBefore(computeHeight);
  }

  function node_copy() {
    return hierarchy(this).eachBefore(copyData);
  }

  function objectChildren(d) {
    return d.children;
  }

  function mapChildren(d) {
    return Array.isArray(d) ? d[1] : null;
  }

  function copyData(node) {
    if (node.data.value !== undefined) node.value = node.data.value;
    node.data = node.data.data;
  }

  function computeHeight(node) {
    var height = 0;
    do node.height = height;
    while ((node = node.parent) && (node.height < ++height));
  }

  function Node$1(data) {
    this.data = data;
    this.depth =
    this.height = 0;
    this.parent = null;
  }

  Node$1.prototype = hierarchy.prototype = {
    constructor: Node$1,
    count: node_count,
    each: node_each,
    eachAfter: node_eachAfter,
    eachBefore: node_eachBefore,
    find: node_find,
    sum: node_sum,
    sort: node_sort,
    path: node_path,
    ancestors: node_ancestors,
    descendants: node_descendants,
    leaves: node_leaves,
    links: node_links,
    copy: node_copy,
    [Symbol.iterator]: node_iterator
  };

  function required(f) {
    if (typeof f !== "function") throw new Error;
    return f;
  }

  function constantZero() {
    return 0;
  }

  function constant$1(x) {
    return function() {
      return x;
    };
  }

  function roundNode(node) {
    node.x0 = Math.round(node.x0);
    node.y0 = Math.round(node.y0);
    node.x1 = Math.round(node.x1);
    node.y1 = Math.round(node.y1);
  }

  function treemapDice(parent, x0, y0, x1, y1) {
    var nodes = parent.children,
        node,
        i = -1,
        n = nodes.length,
        k = parent.value && (x1 - x0) / parent.value;

    while (++i < n) {
      node = nodes[i], node.y0 = y0, node.y1 = y1;
      node.x0 = x0, node.x1 = x0 += node.value * k;
    }
  }

  function treemapSlice(parent, x0, y0, x1, y1) {
    var nodes = parent.children,
        node,
        i = -1,
        n = nodes.length,
        k = parent.value && (y1 - y0) / parent.value;

    while (++i < n) {
      node = nodes[i], node.x0 = x0, node.x1 = x1;
      node.y0 = y0, node.y1 = y0 += node.value * k;
    }
  }

  var phi = (1 + Math.sqrt(5)) / 2;

  function squarifyRatio(ratio, parent, x0, y0, x1, y1) {
    var rows = [],
        nodes = parent.children,
        row,
        nodeValue,
        i0 = 0,
        i1 = 0,
        n = nodes.length,
        dx, dy,
        value = parent.value,
        sumValue,
        minValue,
        maxValue,
        newRatio,
        minRatio,
        alpha,
        beta;

    while (i0 < n) {
      dx = x1 - x0, dy = y1 - y0;

      // Find the next non-empty node.
      do sumValue = nodes[i1++].value; while (!sumValue && i1 < n);
      minValue = maxValue = sumValue;
      alpha = Math.max(dy / dx, dx / dy) / (value * ratio);
      beta = sumValue * sumValue * alpha;
      minRatio = Math.max(maxValue / beta, beta / minValue);

      // Keep adding nodes while the aspect ratio maintains or improves.
      for (; i1 < n; ++i1) {
        sumValue += nodeValue = nodes[i1].value;
        if (nodeValue < minValue) minValue = nodeValue;
        if (nodeValue > maxValue) maxValue = nodeValue;
        beta = sumValue * sumValue * alpha;
        newRatio = Math.max(maxValue / beta, beta / minValue);
        if (newRatio > minRatio) { sumValue -= nodeValue; break; }
        minRatio = newRatio;
      }

      // Position and record the row orientation.
      rows.push(row = {value: sumValue, dice: dx < dy, children: nodes.slice(i0, i1)});
      if (row.dice) treemapDice(row, x0, y0, x1, value ? y0 += dy * sumValue / value : y1);
      else treemapSlice(row, x0, y0, value ? x0 += dx * sumValue / value : x1, y1);
      value -= sumValue, i0 = i1;
    }

    return rows;
  }

  var squarify = (function custom(ratio) {

    function squarify(parent, x0, y0, x1, y1) {
      squarifyRatio(ratio, parent, x0, y0, x1, y1);
    }

    squarify.ratio = function(x) {
      return custom((x = +x) > 1 ? x : 1);
    };

    return squarify;
  })(phi);

  function treemap() {
    var tile = squarify,
        round = false,
        dx = 1,
        dy = 1,
        paddingStack = [0],
        paddingInner = constantZero,
        paddingTop = constantZero,
        paddingRight = constantZero,
        paddingBottom = constantZero,
        paddingLeft = constantZero;

    function treemap(root) {
      root.x0 =
      root.y0 = 0;
      root.x1 = dx;
      root.y1 = dy;
      root.eachBefore(positionNode);
      paddingStack = [0];
      if (round) root.eachBefore(roundNode);
      return root;
    }

    function positionNode(node) {
      var p = paddingStack[node.depth],
          x0 = node.x0 + p,
          y0 = node.y0 + p,
          x1 = node.x1 - p,
          y1 = node.y1 - p;
      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;
      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;
      node.x0 = x0;
      node.y0 = y0;
      node.x1 = x1;
      node.y1 = y1;
      if (node.children) {
        p = paddingStack[node.depth + 1] = paddingInner(node) / 2;
        x0 += paddingLeft(node) - p;
        y0 += paddingTop(node) - p;
        x1 -= paddingRight(node) - p;
        y1 -= paddingBottom(node) - p;
        if (x1 < x0) x0 = x1 = (x0 + x1) / 2;
        if (y1 < y0) y0 = y1 = (y0 + y1) / 2;
        tile(node, x0, y0, x1, y1);
      }
    }

    treemap.round = function(x) {
      return arguments.length ? (round = !!x, treemap) : round;
    };

    treemap.size = function(x) {
      return arguments.length ? (dx = +x[0], dy = +x[1], treemap) : [dx, dy];
    };

    treemap.tile = function(x) {
      return arguments.length ? (tile = required(x), treemap) : tile;
    };

    treemap.padding = function(x) {
      return arguments.length ? treemap.paddingInner(x).paddingOuter(x) : treemap.paddingInner();
    };

    treemap.paddingInner = function(x) {
      return arguments.length ? (paddingInner = typeof x === "function" ? x : constant$1(+x), treemap) : paddingInner;
    };

    treemap.paddingOuter = function(x) {
      return arguments.length ? treemap.paddingTop(x).paddingRight(x).paddingBottom(x).paddingLeft(x) : treemap.paddingTop();
    };

    treemap.paddingTop = function(x) {
      return arguments.length ? (paddingTop = typeof x === "function" ? x : constant$1(+x), treemap) : paddingTop;
    };

    treemap.paddingRight = function(x) {
      return arguments.length ? (paddingRight = typeof x === "function" ? x : constant$1(+x), treemap) : paddingRight;
    };

    treemap.paddingBottom = function(x) {
      return arguments.length ? (paddingBottom = typeof x === "function" ? x : constant$1(+x), treemap) : paddingBottom;
    };

    treemap.paddingLeft = function(x) {
      return arguments.length ? (paddingLeft = typeof x === "function" ? x : constant$1(+x), treemap) : paddingLeft;
    };

    return treemap;
  }

  var treemapResquarify = (function custom(ratio) {

    function resquarify(parent, x0, y0, x1, y1) {
      if ((rows = parent._squarify) && (rows.ratio === ratio)) {
        var rows,
            row,
            nodes,
            i,
            j = -1,
            n,
            m = rows.length,
            value = parent.value;

        while (++j < m) {
          row = rows[j], nodes = row.children;
          for (i = row.value = 0, n = nodes.length; i < n; ++i) row.value += nodes[i].value;
          if (row.dice) treemapDice(row, x0, y0, x1, value ? y0 += (y1 - y0) * row.value / value : y1);
          else treemapSlice(row, x0, y0, value ? x0 += (x1 - x0) * row.value / value : x1, y1);
          value -= row.value;
        }
      } else {
        parent._squarify = rows = squarifyRatio(ratio, parent, x0, y0, x1, y1);
        rows.ratio = ratio;
      }
    }

    resquarify.ratio = function(x) {
      return custom((x = +x) > 1 ? x : 1);
    };

    return resquarify;
  })(phi);

  const isModuleTree = (mod) => "children" in mod;

  let count = 0;
  class Id {
      constructor(id) {
          this._id = id;
          const url = new URL(window.location.href);
          url.hash = id;
          this._href = url.toString();
      }
      get id() {
          return this._id;
      }
      get href() {
          return this._href;
      }
      toString() {
          return `url(${this.href})`;
      }
  }
  function generateUniqueId(name) {
      count += 1;
      const id = ["O", name, count].filter(Boolean).join("-");
      return new Id(id);
  }

  const LABELS = {
      renderedLength: "Rendered",
      gzipLength: "Gzip",
      brotliLength: "Brotli",
  };
  const getAvailableSizeOptions = (options) => {
      const availableSizeProperties = ["renderedLength"];
      if (options.gzip) {
          availableSizeProperties.push("gzipLength");
      }
      if (options.brotli) {
          availableSizeProperties.push("brotliLength");
      }
      return availableSizeProperties;
  };

  var t,r,u,i,o=0,f=[],c=l$1,e=c.__b,a=c.__r,v=c.diffed,l=c.__c,m=c.unmount,s=c.__;function p(n,t){c.__h&&c.__h(r,n,o||t),o=0;var u=r.__H||(r.__H={__:[],__h:[]});return n>=u.__.length&&u.__.push({}),u.__[n]}function d(n){return o=1,h(D,n)}function h(n,u,i){var o=p(t++,2);if(o.t=n,!o.__c&&(o.__=[D(void 0,u),function(n){var t=o.__N?o.__N[0]:o.__[0],r=o.t(t,n);t!==r&&(o.__N=[r,o.__[1]],o.__c.setState({}));}],o.__c=r,!r.__f)){var f=function(n,t,r){if(!o.__c.__H)return  true;var u=o.__c.__H.__.filter(function(n){return !!n.__c});if(u.every(function(n){return !n.__N}))return !c||c.call(this,n,t,r);var i=o.__c.props!==n;return u.forEach(function(n){if(n.__N){var t=n.__[0];n.__=n.__N,n.__N=void 0,t!==n.__[0]&&(i=true);}}),c&&c.call(this,n,t,r)||i};r.__f=true;var c=r.shouldComponentUpdate,e=r.componentWillUpdate;r.componentWillUpdate=function(n,t,r){if(this.__e){var u=c;c=void 0,f(n,t,r),c=u;}e&&e.call(this,n,t,r);},r.shouldComponentUpdate=f;}return o.__N||o.__}function y(n,u){var i=p(t++,3);!c.__s&&C(i.__H,u)&&(i.__=n,i.u=u,r.__H.__h.push(i));}function _(n,u){var i=p(t++,4);!c.__s&&C(i.__H,u)&&(i.__=n,i.u=u,r.__h.push(i));}function A(n){return o=5,T(function(){return {current:n}},[])}function T(n,r){var u=p(t++,7);return C(u.__H,r)&&(u.__=n(),u.__H=r,u.__h=n),u.__}function q(n,t){return o=8,T(function(){return n},t)}function x(n){var u=r.context[n.__c],i=p(t++,9);return i.c=n,u?(null==i.__&&(i.__=true,u.sub(r)),u.props.value):n.__}function j(){for(var n;n=f.shift();)if(n.__P&&n.__H)try{n.__H.__h.forEach(z),n.__H.__h.forEach(B),n.__H.__h=[];}catch(t){n.__H.__h=[],c.__e(t,n.__v);}}c.__b=function(n){r=null,e&&e(n);},c.__=function(n,t){n&&t.__k&&t.__k.__m&&(n.__m=t.__k.__m),s&&s(n,t);},c.__r=function(n){a&&a(n),t=0;var i=(r=n.__c).__H;i&&(u===r?(i.__h=[],r.__h=[],i.__.forEach(function(n){n.__N&&(n.__=n.__N),n.u=n.__N=void 0;})):(i.__h.forEach(z),i.__h.forEach(B),i.__h=[],t=0)),u=r;},c.diffed=function(n){v&&v(n);var t=n.__c;t&&t.__H&&(t.__H.__h.length&&(1!==f.push(t)&&i===c.requestAnimationFrame||((i=c.requestAnimationFrame)||w)(j)),t.__H.__.forEach(function(n){n.u&&(n.__H=n.u),n.u=void 0;})),u=r=null;},c.__c=function(n,t){t.some(function(n){try{n.__h.forEach(z),n.__h=n.__h.filter(function(n){return !n.__||B(n)});}catch(r){t.some(function(n){n.__h&&(n.__h=[]);}),t=[],c.__e(r,n.__v);}}),l&&l(n,t);},c.unmount=function(n){m&&m(n);var t,r=n.__c;r&&r.__H&&(r.__H.__.forEach(function(n){try{z(n);}catch(n){t=n;}}),r.__H=void 0,t&&c.__e(t,r.__v));};var k="function"==typeof requestAnimationFrame;function w(n){var t,r=function(){clearTimeout(u),k&&cancelAnimationFrame(t),setTimeout(n);},u=setTimeout(r,35);k&&(t=requestAnimationFrame(r));}function z(n){var t=r,u=n.__c;"function"==typeof u&&(n.__c=void 0,u()),r=t;}function B(n){var t=r;n.__c=n.__(),r=t;}function C(n,t){return !n||n.length!==t.length||t.some(function(t,r){return t!==n[r]})}function D(n,t){return "function"==typeof t?t(n):t}

  const PLACEHOLDER = "*/**/file.js";
  const SideBar = ({ availableSizeProperties, sizeProperty, setSizeProperty, onExcludeChange, onIncludeChange, }) => {
      const [includeValue, setIncludeValue] = d("");
      const [excludeValue, setExcludeValue] = d("");
      const handleSizePropertyChange = (sizeProp) => () => {
          if (sizeProp !== sizeProperty) {
              setSizeProperty(sizeProp);
          }
      };
      const handleIncludeChange = (event) => {
          const value = event.currentTarget.value;
          setIncludeValue(value);
          onIncludeChange(value);
      };
      const handleExcludeChange = (event) => {
          const value = event.currentTarget.value;
          setExcludeValue(value);
          onExcludeChange(value);
      };
      return (u$1("aside", { className: "sidebar", children: [u$1("div", { className: "size-selectors", children: availableSizeProperties.length > 1 &&
                      availableSizeProperties.map((sizeProp) => {
                          const id = `selector-${sizeProp}`;
                          return (u$1("div", { className: "size-selector", children: [u$1("input", { type: "radio", id: id, checked: sizeProp === sizeProperty, onChange: handleSizePropertyChange(sizeProp) }), u$1("label", { htmlFor: id, children: LABELS[sizeProp] })] }, sizeProp));
                      }) }), u$1("div", { className: "module-filters", children: [u$1("div", { className: "module-filter", children: [u$1("label", { htmlFor: "module-filter-exclude", children: "Exclude" }), u$1("input", { type: "text", id: "module-filter-exclude", value: excludeValue, onInput: handleExcludeChange, placeholder: PLACEHOLDER })] }), u$1("div", { className: "module-filter", children: [u$1("label", { htmlFor: "module-filter-include", children: "Include" }), u$1("input", { type: "text", id: "module-filter-include", value: includeValue, onInput: handleIncludeChange, placeholder: PLACEHOLDER })] })] })] }));
  };

  function getDefaultExportFromCjs (x) {
  	return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
  }

  var utils = {};

  var constants$1;
  var hasRequiredConstants;

  function requireConstants () {
  	if (hasRequiredConstants) return constants$1;
  	hasRequiredConstants = 1;

  	const WIN_SLASH = '\\\\/';
  	const WIN_NO_SLASH = `[^${WIN_SLASH}]`;

  	/**
  	 * Posix glob regex
  	 */

  	const DOT_LITERAL = '\\.';
  	const PLUS_LITERAL = '\\+';
  	const QMARK_LITERAL = '\\?';
  	const SLASH_LITERAL = '\\/';
  	const ONE_CHAR = '(?=.)';
  	const QMARK = '[^/]';
  	const END_ANCHOR = `(?:${SLASH_LITERAL}|$)`;
  	const START_ANCHOR = `(?:^|${SLASH_LITERAL})`;
  	const DOTS_SLASH = `${DOT_LITERAL}{1,2}${END_ANCHOR}`;
  	const NO_DOT = `(?!${DOT_LITERAL})`;
  	const NO_DOTS = `(?!${START_ANCHOR}${DOTS_SLASH})`;
  	const NO_DOT_SLASH = `(?!${DOT_LITERAL}{0,1}${END_ANCHOR})`;
  	const NO_DOTS_SLASH = `(?!${DOTS_SLASH})`;
  	const QMARK_NO_DOT = `[^.${SLASH_LITERAL}]`;
  	const STAR = `${QMARK}*?`;
  	const SEP = '/';

  	const POSIX_CHARS = {
  	  DOT_LITERAL,
  	  PLUS_LITERAL,
  	  QMARK_LITERAL,
  	  SLASH_LITERAL,
  	  ONE_CHAR,
  	  QMARK,
  	  END_ANCHOR,
  	  DOTS_SLASH,
  	  NO_DOT,
  	  NO_DOTS,
  	  NO_DOT_SLASH,
  	  NO_DOTS_SLASH,
  	  QMARK_NO_DOT,
  	  STAR,
  	  START_ANCHOR,
  	  SEP
  	};

  	/**
  	 * Windows glob regex
  	 */

  	const WINDOWS_CHARS = {
  	  ...POSIX_CHARS,

  	  SLASH_LITERAL: `[${WIN_SLASH}]`,
  	  QMARK: WIN_NO_SLASH,
  	  STAR: `${WIN_NO_SLASH}*?`,
  	  DOTS_SLASH: `${DOT_LITERAL}{1,2}(?:[${WIN_SLASH}]|$)`,
  	  NO_DOT: `(?!${DOT_LITERAL})`,
  	  NO_DOTS: `(?!(?:^|[${WIN_SLASH}])${DOT_LITERAL}{1,2}(?:[${WIN_SLASH}]|$))`,
  	  NO_DOT_SLASH: `(?!${DOT_LITERAL}{0,1}(?:[${WIN_SLASH}]|$))`,
  	  NO_DOTS_SLASH: `(?!${DOT_LITERAL}{1,2}(?:[${WIN_SLASH}]|$))`,
  	  QMARK_NO_DOT: `[^.${WIN_SLASH}]`,
  	  START_ANCHOR: `(?:^|[${WIN_SLASH}])`,
  	  END_ANCHOR: `(?:[${WIN_SLASH}]|$)`,
  	  SEP: '\\'
  	};

  	/**
  	 * POSIX Bracket Regex
  	 */

  	const POSIX_REGEX_SOURCE = {
  	  alnum: 'a-zA-Z0-9',
  	  alpha: 'a-zA-Z',
  	  ascii: '\\x00-\\x7F',
  	  blank: ' \\t',
  	  cntrl: '\\x00-\\x1F\\x7F',
  	  digit: '0-9',
  	  graph: '\\x21-\\x7E',
  	  lower: 'a-z',
  	  print: '\\x20-\\x7E ',
  	  punct: '\\-!"#$%&\'()\\*+,./:;<=>?@[\\]^_`{|}~',
  	  space: ' \\t\\r\\n\\v\\f',
  	  upper: 'A-Z',
  	  word: 'A-Za-z0-9_',
  	  xdigit: 'A-Fa-f0-9'
  	};

  	constants$1 = {
  	  MAX_LENGTH: 1024 * 64,
  	  POSIX_REGEX_SOURCE,

  	  // regular expressions
  	  REGEX_BACKSLASH: /\\(?![*+?^${}(|)[\]])/g,
  	  REGEX_NON_SPECIAL_CHARS: /^[^@![\].,$*+?^{}()|\\/]+/,
  	  REGEX_SPECIAL_CHARS: /[-*+?.^${}(|)[\]]/,
  	  REGEX_SPECIAL_CHARS_BACKREF: /(\\?)((\W)(\3*))/g,
  	  REGEX_SPECIAL_CHARS_GLOBAL: /([-*+?.^${}(|)[\]])/g,
  	  REGEX_REMOVE_BACKSLASH: /(?:\[.*?[^\\]\]|\\(?=.))/g,

  	  // Replace globs with equivalent patterns to reduce parsing time.
  	  REPLACEMENTS: {
  	    '***': '*',
  	    '**/**': '**',
  	    '**/**/**': '**'
  	  },

  	  // Digits
  	  CHAR_0: 48, /* 0 */
  	  CHAR_9: 57, /* 9 */

  	  // Alphabet chars.
  	  CHAR_UPPERCASE_A: 65, /* A */
  	  CHAR_LOWERCASE_A: 97, /* a */
  	  CHAR_UPPERCASE_Z: 90, /* Z */
  	  CHAR_LOWERCASE_Z: 122, /* z */

  	  CHAR_LEFT_PARENTHESES: 40, /* ( */
  	  CHAR_RIGHT_PARENTHESES: 41, /* ) */

  	  CHAR_ASTERISK: 42, /* * */

  	  // Non-alphabetic chars.
  	  CHAR_AMPERSAND: 38, /* & */
  	  CHAR_AT: 64, /* @ */
  	  CHAR_BACKWARD_SLASH: 92, /* \ */
  	  CHAR_CARRIAGE_RETURN: 13, /* \r */
  	  CHAR_CIRCUMFLEX_ACCENT: 94, /* ^ */
  	  CHAR_COLON: 58, /* : */
  	  CHAR_COMMA: 44, /* , */
  	  CHAR_DOT: 46, /* . */
  	  CHAR_DOUBLE_QUOTE: 34, /* " */
  	  CHAR_EQUAL: 61, /* = */
  	  CHAR_EXCLAMATION_MARK: 33, /* ! */
  	  CHAR_FORM_FEED: 12, /* \f */
  	  CHAR_FORWARD_SLASH: 47, /* / */
  	  CHAR_GRAVE_ACCENT: 96, /* ` */
  	  CHAR_HASH: 35, /* # */
  	  CHAR_HYPHEN_MINUS: 45, /* - */
  	  CHAR_LEFT_ANGLE_BRACKET: 60, /* < */
  	  CHAR_LEFT_CURLY_BRACE: 123, /* { */
  	  CHAR_LEFT_SQUARE_BRACKET: 91, /* [ */
  	  CHAR_LINE_FEED: 10, /* \n */
  	  CHAR_NO_BREAK_SPACE: 160, /* \u00A0 */
  	  CHAR_PERCENT: 37, /* % */
  	  CHAR_PLUS: 43, /* + */
  	  CHAR_QUESTION_MARK: 63, /* ? */
  	  CHAR_RIGHT_ANGLE_BRACKET: 62, /* > */
  	  CHAR_RIGHT_CURLY_BRACE: 125, /* } */
  	  CHAR_RIGHT_SQUARE_BRACKET: 93, /* ] */
  	  CHAR_SEMICOLON: 59, /* ; */
  	  CHAR_SINGLE_QUOTE: 39, /* ' */
  	  CHAR_SPACE: 32, /*   */
  	  CHAR_TAB: 9, /* \t */
  	  CHAR_UNDERSCORE: 95, /* _ */
  	  CHAR_VERTICAL_LINE: 124, /* | */
  	  CHAR_ZERO_WIDTH_NOBREAK_SPACE: 65279, /* \uFEFF */

  	  /**
  	   * Create EXTGLOB_CHARS
  	   */

  	  extglobChars(chars) {
  	    return {
  	      '!': { type: 'negate', open: '(?:(?!(?:', close: `))${chars.STAR})` },
  	      '?': { type: 'qmark', open: '(?:', close: ')?' },
  	      '+': { type: 'plus', open: '(?:', close: ')+' },
  	      '*': { type: 'star', open: '(?:', close: ')*' },
  	      '@': { type: 'at', open: '(?:', close: ')' }
  	    };
  	  },

  	  /**
  	   * Create GLOB_CHARS
  	   */

  	  globChars(win32) {
  	    return win32 === true ? WINDOWS_CHARS : POSIX_CHARS;
  	  }
  	};
  	return constants$1;
  }

  /*global navigator*/

  var hasRequiredUtils;

  function requireUtils () {
  	if (hasRequiredUtils) return utils;
  	hasRequiredUtils = 1;
  	(function (exports) {

  		const {
  		  REGEX_BACKSLASH,
  		  REGEX_REMOVE_BACKSLASH,
  		  REGEX_SPECIAL_CHARS,
  		  REGEX_SPECIAL_CHARS_GLOBAL
  		} = /*@__PURE__*/ requireConstants();

  		exports.isObject = val => val !== null && typeof val === 'object' && !Array.isArray(val);
  		exports.hasRegexChars = str => REGEX_SPECIAL_CHARS.test(str);
  		exports.isRegexChar = str => str.length === 1 && exports.hasRegexChars(str);
  		exports.escapeRegex = str => str.replace(REGEX_SPECIAL_CHARS_GLOBAL, '\\$1');
  		exports.toPosixSlashes = str => str.replace(REGEX_BACKSLASH, '/');

  		exports.isWindows = () => {
  		  if (typeof navigator !== 'undefined' && navigator.platform) {
  		    const platform = navigator.platform.toLowerCase();
  		    return platform === 'win32' || platform === 'windows';
  		  }

  		  if (typeof process !== 'undefined' && process.platform) {
  		    return process.platform === 'win32';
  		  }

  		  return false;
  		};

  		exports.removeBackslashes = str => {
  		  return str.replace(REGEX_REMOVE_BACKSLASH, match => {
  		    return match === '\\' ? '' : match;
  		  });
  		};

  		exports.escapeLast = (input, char, lastIdx) => {
  		  const idx = input.lastIndexOf(char, lastIdx);
  		  if (idx === -1) return input;
  		  if (input[idx - 1] === '\\') return exports.escapeLast(input, char, idx - 1);
  		  return `${input.slice(0, idx)}\\${input.slice(idx)}`;
  		};

  		exports.removePrefix = (input, state = {}) => {
  		  let output = input;
  		  if (output.startsWith('./')) {
  		    output = output.slice(2);
  		    state.prefix = './';
  		  }
  		  return output;
  		};

  		exports.wrapOutput = (input, state = {}, options = {}) => {
  		  const prepend = options.contains ? '' : '^';
  		  const append = options.contains ? '' : '$';

  		  let output = `${prepend}(?:${input})${append}`;
  		  if (state.negated === true) {
  		    output = `(?:^(?!${output}).*$)`;
  		  }
  		  return output;
  		};

  		exports.basename = (path, { windows } = {}) => {
  		  const segs = path.split(windows ? /[\\/]/ : '/');
  		  const last = segs[segs.length - 1];

  		  if (last === '') {
  		    return segs[segs.length - 2];
  		  }

  		  return last;
  		}; 
  	} (utils));
  	return utils;
  }

  var scan_1;
  var hasRequiredScan;

  function requireScan () {
  	if (hasRequiredScan) return scan_1;
  	hasRequiredScan = 1;

  	const utils = /*@__PURE__*/ requireUtils();
  	const {
  	  CHAR_ASTERISK,             /* * */
  	  CHAR_AT,                   /* @ */
  	  CHAR_BACKWARD_SLASH,       /* \ */
  	  CHAR_COMMA,                /* , */
  	  CHAR_DOT,                  /* . */
  	  CHAR_EXCLAMATION_MARK,     /* ! */
  	  CHAR_FORWARD_SLASH,        /* / */
  	  CHAR_LEFT_CURLY_BRACE,     /* { */
  	  CHAR_LEFT_PARENTHESES,     /* ( */
  	  CHAR_LEFT_SQUARE_BRACKET,  /* [ */
  	  CHAR_PLUS,                 /* + */
  	  CHAR_QUESTION_MARK,        /* ? */
  	  CHAR_RIGHT_CURLY_BRACE,    /* } */
  	  CHAR_RIGHT_PARENTHESES,    /* ) */
  	  CHAR_RIGHT_SQUARE_BRACKET  /* ] */
  	} = /*@__PURE__*/ requireConstants();

  	const isPathSeparator = code => {
  	  return code === CHAR_FORWARD_SLASH || code === CHAR_BACKWARD_SLASH;
  	};

  	const depth = token => {
  	  if (token.isPrefix !== true) {
  	    token.depth = token.isGlobstar ? Infinity : 1;
  	  }
  	};

  	/**
  	 * Quickly scans a glob pattern and returns an object with a handful of
  	 * useful properties, like `isGlob`, `path` (the leading non-glob, if it exists),
  	 * `glob` (the actual pattern), `negated` (true if the path starts with `!` but not
  	 * with `!(`) and `negatedExtglob` (true if the path starts with `!(`).
  	 *
  	 * ```js
  	 * const pm = require('picomatch');
  	 * console.log(pm.scan('foo/bar/*.js'));
  	 * { isGlob: true, input: 'foo/bar/*.js', base: 'foo/bar', glob: '*.js' }
  	 * ```
  	 * @param {String} `str`
  	 * @param {Object} `options`
  	 * @return {Object} Returns an object with tokens and regex source string.
  	 * @api public
  	 */

  	const scan = (input, options) => {
  	  const opts = options || {};

  	  const length = input.length - 1;
  	  const scanToEnd = opts.parts === true || opts.scanToEnd === true;
  	  const slashes = [];
  	  const tokens = [];
  	  const parts = [];

  	  let str = input;
  	  let index = -1;
  	  let start = 0;
  	  let lastIndex = 0;
  	  let isBrace = false;
  	  let isBracket = false;
  	  let isGlob = false;
  	  let isExtglob = false;
  	  let isGlobstar = false;
  	  let braceEscaped = false;
  	  let backslashes = false;
  	  let negated = false;
  	  let negatedExtglob = false;
  	  let finished = false;
  	  let braces = 0;
  	  let prev;
  	  let code;
  	  let token = { value: '', depth: 0, isGlob: false };

  	  const eos = () => index >= length;
  	  const peek = () => str.charCodeAt(index + 1);
  	  const advance = () => {
  	    prev = code;
  	    return str.charCodeAt(++index);
  	  };

  	  while (index < length) {
  	    code = advance();
  	    let next;

  	    if (code === CHAR_BACKWARD_SLASH) {
  	      backslashes = token.backslashes = true;
  	      code = advance();

  	      if (code === CHAR_LEFT_CURLY_BRACE) {
  	        braceEscaped = true;
  	      }
  	      continue;
  	    }

  	    if (braceEscaped === true || code === CHAR_LEFT_CURLY_BRACE) {
  	      braces++;

  	      while (eos() !== true && (code = advance())) {
  	        if (code === CHAR_BACKWARD_SLASH) {
  	          backslashes = token.backslashes = true;
  	          advance();
  	          continue;
  	        }

  	        if (code === CHAR_LEFT_CURLY_BRACE) {
  	          braces++;
  	          continue;
  	        }

  	        if (braceEscaped !== true && code === CHAR_DOT && (code = advance()) === CHAR_DOT) {
  	          isBrace = token.isBrace = true;
  	          isGlob = token.isGlob = true;
  	          finished = true;

  	          if (scanToEnd === true) {
  	            continue;
  	          }

  	          break;
  	        }

  	        if (braceEscaped !== true && code === CHAR_COMMA) {
  	          isBrace = token.isBrace = true;
  	          isGlob = token.isGlob = true;
  	          finished = true;

  	          if (scanToEnd === true) {
  	            continue;
  	          }

  	          break;
  	        }

  	        if (code === CHAR_RIGHT_CURLY_BRACE) {
  	          braces--;

  	          if (braces === 0) {
  	            braceEscaped = false;
  	            isBrace = token.isBrace = true;
  	            finished = true;
  	            break;
  	          }
  	        }
  	      }

  	      if (scanToEnd === true) {
  	        continue;
  	      }

  	      break;
  	    }

  	    if (code === CHAR_FORWARD_SLASH) {
  	      slashes.push(index);
  	      tokens.push(token);
  	      token = { value: '', depth: 0, isGlob: false };

  	      if (finished === true) continue;
  	      if (prev === CHAR_DOT && index === (start + 1)) {
  	        start += 2;
  	        continue;
  	      }

  	      lastIndex = index + 1;
  	      continue;
  	    }

  	    if (opts.noext !== true) {
  	      const isExtglobChar = code === CHAR_PLUS
  	        || code === CHAR_AT
  	        || code === CHAR_ASTERISK
  	        || code === CHAR_QUESTION_MARK
  	        || code === CHAR_EXCLAMATION_MARK;

  	      if (isExtglobChar === true && peek() === CHAR_LEFT_PARENTHESES) {
  	        isGlob = token.isGlob = true;
  	        isExtglob = token.isExtglob = true;
  	        finished = true;
  	        if (code === CHAR_EXCLAMATION_MARK && index === start) {
  	          negatedExtglob = true;
  	        }

  	        if (scanToEnd === true) {
  	          while (eos() !== true && (code = advance())) {
  	            if (code === CHAR_BACKWARD_SLASH) {
  	              backslashes = token.backslashes = true;
  	              code = advance();
  	              continue;
  	            }

  	            if (code === CHAR_RIGHT_PARENTHESES) {
  	              isGlob = token.isGlob = true;
  	              finished = true;
  	              break;
  	            }
  	          }
  	          continue;
  	        }
  	        break;
  	      }
  	    }

  	    if (code === CHAR_ASTERISK) {
  	      if (prev === CHAR_ASTERISK) isGlobstar = token.isGlobstar = true;
  	      isGlob = token.isGlob = true;
  	      finished = true;

  	      if (scanToEnd === true) {
  	        continue;
  	      }
  	      break;
  	    }

  	    if (code === CHAR_QUESTION_MARK) {
  	      isGlob = token.isGlob = true;
  	      finished = true;

  	      if (scanToEnd === true) {
  	        continue;
  	      }
  	      break;
  	    }

  	    if (code === CHAR_LEFT_SQUARE_BRACKET) {
  	      while (eos() !== true && (next = advance())) {
  	        if (next === CHAR_BACKWARD_SLASH) {
  	          backslashes = token.backslashes = true;
  	          advance();
  	          continue;
  	        }

  	        if (next === CHAR_RIGHT_SQUARE_BRACKET) {
  	          isBracket = token.isBracket = true;
  	          isGlob = token.isGlob = true;
  	          finished = true;
  	          break;
  	        }
  	      }

  	      if (scanToEnd === true) {
  	        continue;
  	      }

  	      break;
  	    }

  	    if (opts.nonegate !== true && code === CHAR_EXCLAMATION_MARK && index === start) {
  	      negated = token.negated = true;
  	      start++;
  	      continue;
  	    }

  	    if (opts.noparen !== true && code === CHAR_LEFT_PARENTHESES) {
  	      isGlob = token.isGlob = true;

  	      if (scanToEnd === true) {
  	        while (eos() !== true && (code = advance())) {
  	          if (code === CHAR_LEFT_PARENTHESES) {
  	            backslashes = token.backslashes = true;
  	            code = advance();
  	            continue;
  	          }

  	          if (code === CHAR_RIGHT_PARENTHESES) {
  	            finished = true;
  	            break;
  	          }
  	        }
  	        continue;
  	      }
  	      break;
  	    }

  	    if (isGlob === true) {
  	      finished = true;

  	      if (scanToEnd === true) {
  	        continue;
  	      }

  	      break;
  	    }
  	  }

  	  if (opts.noext === true) {
  	    isExtglob = false;
  	    isGlob = false;
  	  }

  	  let base = str;
  	  let prefix = '';
  	  let glob = '';

  	  if (start > 0) {
  	    prefix = str.slice(0, start);
  	    str = str.slice(start);
  	    lastIndex -= start;
  	  }

  	  if (base && isGlob === true && lastIndex > 0) {
  	    base = str.slice(0, lastIndex);
  	    glob = str.slice(lastIndex);
  	  } else if (isGlob === true) {
  	    base = '';
  	    glob = str;
  	  } else {
  	    base = str;
  	  }

  	  if (base && base !== '' && base !== '/' && base !== str) {
  	    if (isPathSeparator(base.charCodeAt(base.length - 1))) {
  	      base = base.slice(0, -1);
  	    }
  	  }

  	  if (opts.unescape === true) {
  	    if (glob) glob = utils.removeBackslashes(glob);

  	    if (base && backslashes === true) {
  	      base = utils.removeBackslashes(base);
  	    }
  	  }

  	  const state = {
  	    prefix,
  	    input,
  	    start,
  	    base,
  	    glob,
  	    isBrace,
  	    isBracket,
  	    isGlob,
  	    isExtglob,
  	    isGlobstar,
  	    negated,
  	    negatedExtglob
  	  };

  	  if (opts.tokens === true) {
  	    state.maxDepth = 0;
  	    if (!isPathSeparator(code)) {
  	      tokens.push(token);
  	    }
  	    state.tokens = tokens;
  	  }

  	  if (opts.parts === true || opts.tokens === true) {
  	    let prevIndex;

  	    for (let idx = 0; idx < slashes.length; idx++) {
  	      const n = prevIndex ? prevIndex + 1 : start;
  	      const i = slashes[idx];
  	      const value = input.slice(n, i);
  	      if (opts.tokens) {
  	        if (idx === 0 && start !== 0) {
  	          tokens[idx].isPrefix = true;
  	          tokens[idx].value = prefix;
  	        } else {
  	          tokens[idx].value = value;
  	        }
  	        depth(tokens[idx]);
  	        state.maxDepth += tokens[idx].depth;
  	      }
  	      if (idx !== 0 || value !== '') {
  	        parts.push(value);
  	      }
  	      prevIndex = i;
  	    }

  	    if (prevIndex && prevIndex + 1 < input.length) {
  	      const value = input.slice(prevIndex + 1);
  	      parts.push(value);

  	      if (opts.tokens) {
  	        tokens[tokens.length - 1].value = value;
  	        depth(tokens[tokens.length - 1]);
  	        state.maxDepth += tokens[tokens.length - 1].depth;
  	      }
  	    }

  	    state.slashes = slashes;
  	    state.parts = parts;
  	  }

  	  return state;
  	};

  	scan_1 = scan;
  	return scan_1;
  }

  var parse_1;
  var hasRequiredParse;

  function requireParse () {
  	if (hasRequiredParse) return parse_1;
  	hasRequiredParse = 1;

  	const constants = /*@__PURE__*/ requireConstants();
  	const utils = /*@__PURE__*/ requireUtils();

  	/**
  	 * Constants
  	 */

  	const {
  	  MAX_LENGTH,
  	  POSIX_REGEX_SOURCE,
  	  REGEX_NON_SPECIAL_CHARS,
  	  REGEX_SPECIAL_CHARS_BACKREF,
  	  REPLACEMENTS
  	} = constants;

  	/**
  	 * Helpers
  	 */

  	const expandRange = (args, options) => {
  	  if (typeof options.expandRange === 'function') {
  	    return options.expandRange(...args, options);
  	  }

  	  args.sort();
  	  const value = `[${args.join('-')}]`;

  	  try {
  	    /* eslint-disable-next-line no-new */
  	    new RegExp(value);
  	  } catch (ex) {
  	    return args.map(v => utils.escapeRegex(v)).join('..');
  	  }

  	  return value;
  	};

  	/**
  	 * Create the message for a syntax error
  	 */

  	const syntaxError = (type, char) => {
  	  return `Missing ${type}: "${char}" - use "\\\\${char}" to match literal characters`;
  	};

  	/**
  	 * Parse the given input string.
  	 * @param {String} input
  	 * @param {Object} options
  	 * @return {Object}
  	 */

  	const parse = (input, options) => {
  	  if (typeof input !== 'string') {
  	    throw new TypeError('Expected a string');
  	  }

  	  input = REPLACEMENTS[input] || input;

  	  const opts = { ...options };
  	  const max = typeof opts.maxLength === 'number' ? Math.min(MAX_LENGTH, opts.maxLength) : MAX_LENGTH;

  	  let len = input.length;
  	  if (len > max) {
  	    throw new SyntaxError(`Input length: ${len}, exceeds maximum allowed length: ${max}`);
  	  }

  	  const bos = { type: 'bos', value: '', output: opts.prepend || '' };
  	  const tokens = [bos];

  	  const capture = opts.capture ? '' : '?:';

  	  // create constants based on platform, for windows or posix
  	  const PLATFORM_CHARS = constants.globChars(opts.windows);
  	  const EXTGLOB_CHARS = constants.extglobChars(PLATFORM_CHARS);

  	  const {
  	    DOT_LITERAL,
  	    PLUS_LITERAL,
  	    SLASH_LITERAL,
  	    ONE_CHAR,
  	    DOTS_SLASH,
  	    NO_DOT,
  	    NO_DOT_SLASH,
  	    NO_DOTS_SLASH,
  	    QMARK,
  	    QMARK_NO_DOT,
  	    STAR,
  	    START_ANCHOR
  	  } = PLATFORM_CHARS;

  	  const globstar = opts => {
  	    return `(${capture}(?:(?!${START_ANCHOR}${opts.dot ? DOTS_SLASH : DOT_LITERAL}).)*?)`;
  	  };

  	  const nodot = opts.dot ? '' : NO_DOT;
  	  const qmarkNoDot = opts.dot ? QMARK : QMARK_NO_DOT;
  	  let star = opts.bash === true ? globstar(opts) : STAR;

  	  if (opts.capture) {
  	    star = `(${star})`;
  	  }

  	  // minimatch options support
  	  if (typeof opts.noext === 'boolean') {
  	    opts.noextglob = opts.noext;
  	  }

  	  const state = {
  	    input,
  	    index: -1,
  	    start: 0,
  	    dot: opts.dot === true,
  	    consumed: '',
  	    output: '',
  	    prefix: '',
  	    backtrack: false,
  	    negated: false,
  	    brackets: 0,
  	    braces: 0,
  	    parens: 0,
  	    quotes: 0,
  	    globstar: false,
  	    tokens
  	  };

  	  input = utils.removePrefix(input, state);
  	  len = input.length;

  	  const extglobs = [];
  	  const braces = [];
  	  const stack = [];
  	  let prev = bos;
  	  let value;

  	  /**
  	   * Tokenizing helpers
  	   */

  	  const eos = () => state.index === len - 1;
  	  const peek = state.peek = (n = 1) => input[state.index + n];
  	  const advance = state.advance = () => input[++state.index] || '';
  	  const remaining = () => input.slice(state.index + 1);
  	  const consume = (value = '', num = 0) => {
  	    state.consumed += value;
  	    state.index += num;
  	  };

  	  const append = token => {
  	    state.output += token.output != null ? token.output : token.value;
  	    consume(token.value);
  	  };

  	  const negate = () => {
  	    let count = 1;

  	    while (peek() === '!' && (peek(2) !== '(' || peek(3) === '?')) {
  	      advance();
  	      state.start++;
  	      count++;
  	    }

  	    if (count % 2 === 0) {
  	      return false;
  	    }

  	    state.negated = true;
  	    state.start++;
  	    return true;
  	  };

  	  const increment = type => {
  	    state[type]++;
  	    stack.push(type);
  	  };

  	  const decrement = type => {
  	    state[type]--;
  	    stack.pop();
  	  };

  	  /**
  	   * Push tokens onto the tokens array. This helper speeds up
  	   * tokenizing by 1) helping us avoid backtracking as much as possible,
  	   * and 2) helping us avoid creating extra tokens when consecutive
  	   * characters are plain text. This improves performance and simplifies
  	   * lookbehinds.
  	   */

  	  const push = tok => {
  	    if (prev.type === 'globstar') {
  	      const isBrace = state.braces > 0 && (tok.type === 'comma' || tok.type === 'brace');
  	      const isExtglob = tok.extglob === true || (extglobs.length && (tok.type === 'pipe' || tok.type === 'paren'));

  	      if (tok.type !== 'slash' && tok.type !== 'paren' && !isBrace && !isExtglob) {
  	        state.output = state.output.slice(0, -prev.output.length);
  	        prev.type = 'star';
  	        prev.value = '*';
  	        prev.output = star;
  	        state.output += prev.output;
  	      }
  	    }

  	    if (extglobs.length && tok.type !== 'paren') {
  	      extglobs[extglobs.length - 1].inner += tok.value;
  	    }

  	    if (tok.value || tok.output) append(tok);
  	    if (prev && prev.type === 'text' && tok.type === 'text') {
  	      prev.output = (prev.output || prev.value) + tok.value;
  	      prev.value += tok.value;
  	      return;
  	    }

  	    tok.prev = prev;
  	    tokens.push(tok);
  	    prev = tok;
  	  };

  	  const extglobOpen = (type, value) => {
  	    const token = { ...EXTGLOB_CHARS[value], conditions: 1, inner: '' };

  	    token.prev = prev;
  	    token.parens = state.parens;
  	    token.output = state.output;
  	    const output = (opts.capture ? '(' : '') + token.open;

  	    increment('parens');
  	    push({ type, value, output: state.output ? '' : ONE_CHAR });
  	    push({ type: 'paren', extglob: true, value: advance(), output });
  	    extglobs.push(token);
  	  };

  	  const extglobClose = token => {
  	    let output = token.close + (opts.capture ? ')' : '');
  	    let rest;

  	    if (token.type === 'negate') {
  	      let extglobStar = star;

  	      if (token.inner && token.inner.length > 1 && token.inner.includes('/')) {
  	        extglobStar = globstar(opts);
  	      }

  	      if (extglobStar !== star || eos() || /^\)+$/.test(remaining())) {
  	        output = token.close = `)$))${extglobStar}`;
  	      }

  	      if (token.inner.includes('*') && (rest = remaining()) && /^\.[^\\/.]+$/.test(rest)) {
  	        // Any non-magical string (`.ts`) or even nested expression (`.{ts,tsx}`) can follow after the closing parenthesis.
  	        // In this case, we need to parse the string and use it in the output of the original pattern.
  	        // Suitable patterns: `/!(*.d).ts`, `/!(*.d).{ts,tsx}`, `**/!(*-dbg).@(js)`.
  	        //
  	        // Disabling the `fastpaths` option due to a problem with parsing strings as `.ts` in the pattern like `**/!(*.d).ts`.
  	        const expression = parse(rest, { ...options, fastpaths: false }).output;

  	        output = token.close = `)${expression})${extglobStar})`;
  	      }

  	      if (token.prev.type === 'bos') {
  	        state.negatedExtglob = true;
  	      }
  	    }

  	    push({ type: 'paren', extglob: true, value, output });
  	    decrement('parens');
  	  };

  	  /**
  	   * Fast paths
  	   */

  	  if (opts.fastpaths !== false && !/(^[*!]|[/()[\]{}"])/.test(input)) {
  	    let backslashes = false;

  	    let output = input.replace(REGEX_SPECIAL_CHARS_BACKREF, (m, esc, chars, first, rest, index) => {
  	      if (first === '\\') {
  	        backslashes = true;
  	        return m;
  	      }

  	      if (first === '?') {
  	        if (esc) {
  	          return esc + first + (rest ? QMARK.repeat(rest.length) : '');
  	        }
  	        if (index === 0) {
  	          return qmarkNoDot + (rest ? QMARK.repeat(rest.length) : '');
  	        }
  	        return QMARK.repeat(chars.length);
  	      }

  	      if (first === '.') {
  	        return DOT_LITERAL.repeat(chars.length);
  	      }

  	      if (first === '*') {
  	        if (esc) {
  	          return esc + first + (rest ? star : '');
  	        }
  	        return star;
  	      }
  	      return esc ? m : `\\${m}`;
  	    });

  	    if (backslashes === true) {
  	      if (opts.unescape === true) {
  	        output = output.replace(/\\/g, '');
  	      } else {
  	        output = output.replace(/\\+/g, m => {
  	          return m.length % 2 === 0 ? '\\\\' : (m ? '\\' : '');
  	        });
  	      }
  	    }

  	    if (output === input && opts.contains === true) {
  	      state.output = input;
  	      return state;
  	    }

  	    state.output = utils.wrapOutput(output, state, options);
  	    return state;
  	  }

  	  /**
  	   * Tokenize input until we reach end-of-string
  	   */

  	  while (!eos()) {
  	    value = advance();

  	    if (value === '\u0000') {
  	      continue;
  	    }

  	    /**
  	     * Escaped characters
  	     */

  	    if (value === '\\') {
  	      const next = peek();

  	      if (next === '/' && opts.bash !== true) {
  	        continue;
  	      }

  	      if (next === '.' || next === ';') {
  	        continue;
  	      }

  	      if (!next) {
  	        value += '\\';
  	        push({ type: 'text', value });
  	        continue;
  	      }

  	      // collapse slashes to reduce potential for exploits
  	      const match = /^\\+/.exec(remaining());
  	      let slashes = 0;

  	      if (match && match[0].length > 2) {
  	        slashes = match[0].length;
  	        state.index += slashes;
  	        if (slashes % 2 !== 0) {
  	          value += '\\';
  	        }
  	      }

  	      if (opts.unescape === true) {
  	        value = advance();
  	      } else {
  	        value += advance();
  	      }

  	      if (state.brackets === 0) {
  	        push({ type: 'text', value });
  	        continue;
  	      }
  	    }

  	    /**
  	     * If we're inside a regex character class, continue
  	     * until we reach the closing bracket.
  	     */

  	    if (state.brackets > 0 && (value !== ']' || prev.value === '[' || prev.value === '[^')) {
  	      if (opts.posix !== false && value === ':') {
  	        const inner = prev.value.slice(1);
  	        if (inner.includes('[')) {
  	          prev.posix = true;

  	          if (inner.includes(':')) {
  	            const idx = prev.value.lastIndexOf('[');
  	            const pre = prev.value.slice(0, idx);
  	            const rest = prev.value.slice(idx + 2);
  	            const posix = POSIX_REGEX_SOURCE[rest];
  	            if (posix) {
  	              prev.value = pre + posix;
  	              state.backtrack = true;
  	              advance();

  	              if (!bos.output && tokens.indexOf(prev) === 1) {
  	                bos.output = ONE_CHAR;
  	              }
  	              continue;
  	            }
  	          }
  	        }
  	      }

  	      if ((value === '[' && peek() !== ':') || (value === '-' && peek() === ']')) {
  	        value = `\\${value}`;
  	      }

  	      if (value === ']' && (prev.value === '[' || prev.value === '[^')) {
  	        value = `\\${value}`;
  	      }

  	      if (opts.posix === true && value === '!' && prev.value === '[') {
  	        value = '^';
  	      }

  	      prev.value += value;
  	      append({ value });
  	      continue;
  	    }

  	    /**
  	     * If we're inside a quoted string, continue
  	     * until we reach the closing double quote.
  	     */

  	    if (state.quotes === 1 && value !== '"') {
  	      value = utils.escapeRegex(value);
  	      prev.value += value;
  	      append({ value });
  	      continue;
  	    }

  	    /**
  	     * Double quotes
  	     */

  	    if (value === '"') {
  	      state.quotes = state.quotes === 1 ? 0 : 1;
  	      if (opts.keepQuotes === true) {
  	        push({ type: 'text', value });
  	      }
  	      continue;
  	    }

  	    /**
  	     * Parentheses
  	     */

  	    if (value === '(') {
  	      increment('parens');
  	      push({ type: 'paren', value });
  	      continue;
  	    }

  	    if (value === ')') {
  	      if (state.parens === 0 && opts.strictBrackets === true) {
  	        throw new SyntaxError(syntaxError('opening', '('));
  	      }

  	      const extglob = extglobs[extglobs.length - 1];
  	      if (extglob && state.parens === extglob.parens + 1) {
  	        extglobClose(extglobs.pop());
  	        continue;
  	      }

  	      push({ type: 'paren', value, output: state.parens ? ')' : '\\)' });
  	      decrement('parens');
  	      continue;
  	    }

  	    /**
  	     * Square brackets
  	     */

  	    if (value === '[') {
  	      if (opts.nobracket === true || !remaining().includes(']')) {
  	        if (opts.nobracket !== true && opts.strictBrackets === true) {
  	          throw new SyntaxError(syntaxError('closing', ']'));
  	        }

  	        value = `\\${value}`;
  	      } else {
  	        increment('brackets');
  	      }

  	      push({ type: 'bracket', value });
  	      continue;
  	    }

  	    if (value === ']') {
  	      if (opts.nobracket === true || (prev && prev.type === 'bracket' && prev.value.length === 1)) {
  	        push({ type: 'text', value, output: `\\${value}` });
  	        continue;
  	      }

  	      if (state.brackets === 0) {
  	        if (opts.strictBrackets === true) {
  	          throw new SyntaxError(syntaxError('opening', '['));
  	        }

  	        push({ type: 'text', value, output: `\\${value}` });
  	        continue;
  	      }

  	      decrement('brackets');

  	      const prevValue = prev.value.slice(1);
  	      if (prev.posix !== true && prevValue[0] === '^' && !prevValue.includes('/')) {
  	        value = `/${value}`;
  	      }

  	      prev.value += value;
  	      append({ value });

  	      // when literal brackets are explicitly disabled
  	      // assume we should match with a regex character class
  	      if (opts.literalBrackets === false || utils.hasRegexChars(prevValue)) {
  	        continue;
  	      }

  	      const escaped = utils.escapeRegex(prev.value);
  	      state.output = state.output.slice(0, -prev.value.length);

  	      // when literal brackets are explicitly enabled
  	      // assume we should escape the brackets to match literal characters
  	      if (opts.literalBrackets === true) {
  	        state.output += escaped;
  	        prev.value = escaped;
  	        continue;
  	      }

  	      // when the user specifies nothing, try to match both
  	      prev.value = `(${capture}${escaped}|${prev.value})`;
  	      state.output += prev.value;
  	      continue;
  	    }

  	    /**
  	     * Braces
  	     */

  	    if (value === '{' && opts.nobrace !== true) {
  	      increment('braces');

  	      const open = {
  	        type: 'brace',
  	        value,
  	        output: '(',
  	        outputIndex: state.output.length,
  	        tokensIndex: state.tokens.length
  	      };

  	      braces.push(open);
  	      push(open);
  	      continue;
  	    }

  	    if (value === '}') {
  	      const brace = braces[braces.length - 1];

  	      if (opts.nobrace === true || !brace) {
  	        push({ type: 'text', value, output: value });
  	        continue;
  	      }

  	      let output = ')';

  	      if (brace.dots === true) {
  	        const arr = tokens.slice();
  	        const range = [];

  	        for (let i = arr.length - 1; i >= 0; i--) {
  	          tokens.pop();
  	          if (arr[i].type === 'brace') {
  	            break;
  	          }
  	          if (arr[i].type !== 'dots') {
  	            range.unshift(arr[i].value);
  	          }
  	        }

  	        output = expandRange(range, opts);
  	        state.backtrack = true;
  	      }

  	      if (brace.comma !== true && brace.dots !== true) {
  	        const out = state.output.slice(0, brace.outputIndex);
  	        const toks = state.tokens.slice(brace.tokensIndex);
  	        brace.value = brace.output = '\\{';
  	        value = output = '\\}';
  	        state.output = out;
  	        for (const t of toks) {
  	          state.output += (t.output || t.value);
  	        }
  	      }

  	      push({ type: 'brace', value, output });
  	      decrement('braces');
  	      braces.pop();
  	      continue;
  	    }

  	    /**
  	     * Pipes
  	     */

  	    if (value === '|') {
  	      if (extglobs.length > 0) {
  	        extglobs[extglobs.length - 1].conditions++;
  	      }
  	      push({ type: 'text', value });
  	      continue;
  	    }

  	    /**
  	     * Commas
  	     */

  	    if (value === ',') {
  	      let output = value;

  	      const brace = braces[braces.length - 1];
  	      if (brace && stack[stack.length - 1] === 'braces') {
  	        brace.comma = true;
  	        output = '|';
  	      }

  	      push({ type: 'comma', value, output });
  	      continue;
  	    }

  	    /**
  	     * Slashes
  	     */

  	    if (value === '/') {
  	      // if the beginning of the glob is "./", advance the start
  	      // to the current index, and don't add the "./" characters
  	      // to the state. This greatly simplifies lookbehinds when
  	      // checking for BOS characters like "!" and "." (not "./")
  	      if (prev.type === 'dot' && state.index === state.start + 1) {
  	        state.start = state.index + 1;
  	        state.consumed = '';
  	        state.output = '';
  	        tokens.pop();
  	        prev = bos; // reset "prev" to the first token
  	        continue;
  	      }

  	      push({ type: 'slash', value, output: SLASH_LITERAL });
  	      continue;
  	    }

  	    /**
  	     * Dots
  	     */

  	    if (value === '.') {
  	      if (state.braces > 0 && prev.type === 'dot') {
  	        if (prev.value === '.') prev.output = DOT_LITERAL;
  	        const brace = braces[braces.length - 1];
  	        prev.type = 'dots';
  	        prev.output += value;
  	        prev.value += value;
  	        brace.dots = true;
  	        continue;
  	      }

  	      if ((state.braces + state.parens) === 0 && prev.type !== 'bos' && prev.type !== 'slash') {
  	        push({ type: 'text', value, output: DOT_LITERAL });
  	        continue;
  	      }

  	      push({ type: 'dot', value, output: DOT_LITERAL });
  	      continue;
  	    }

  	    /**
  	     * Question marks
  	     */

  	    if (value === '?') {
  	      const isGroup = prev && prev.value === '(';
  	      if (!isGroup && opts.noextglob !== true && peek() === '(' && peek(2) !== '?') {
  	        extglobOpen('qmark', value);
  	        continue;
  	      }

  	      if (prev && prev.type === 'paren') {
  	        const next = peek();
  	        let output = value;

  	        if ((prev.value === '(' && !/[!=<:]/.test(next)) || (next === '<' && !/<([!=]|\w+>)/.test(remaining()))) {
  	          output = `\\${value}`;
  	        }

  	        push({ type: 'text', value, output });
  	        continue;
  	      }

  	      if (opts.dot !== true && (prev.type === 'slash' || prev.type === 'bos')) {
  	        push({ type: 'qmark', value, output: QMARK_NO_DOT });
  	        continue;
  	      }

  	      push({ type: 'qmark', value, output: QMARK });
  	      continue;
  	    }

  	    /**
  	     * Exclamation
  	     */

  	    if (value === '!') {
  	      if (opts.noextglob !== true && peek() === '(') {
  	        if (peek(2) !== '?' || !/[!=<:]/.test(peek(3))) {
  	          extglobOpen('negate', value);
  	          continue;
  	        }
  	      }

  	      if (opts.nonegate !== true && state.index === 0) {
  	        negate();
  	        continue;
  	      }
  	    }

  	    /**
  	     * Plus
  	     */

  	    if (value === '+') {
  	      if (opts.noextglob !== true && peek() === '(' && peek(2) !== '?') {
  	        extglobOpen('plus', value);
  	        continue;
  	      }

  	      if ((prev && prev.value === '(') || opts.regex === false) {
  	        push({ type: 'plus', value, output: PLUS_LITERAL });
  	        continue;
  	      }

  	      if ((prev && (prev.type === 'bracket' || prev.type === 'paren' || prev.type === 'brace')) || state.parens > 0) {
  	        push({ type: 'plus', value });
  	        continue;
  	      }

  	      push({ type: 'plus', value: PLUS_LITERAL });
  	      continue;
  	    }

  	    /**
  	     * Plain text
  	     */

  	    if (value === '@') {
  	      if (opts.noextglob !== true && peek() === '(' && peek(2) !== '?') {
  	        push({ type: 'at', extglob: true, value, output: '' });
  	        continue;
  	      }

  	      push({ type: 'text', value });
  	      continue;
  	    }

  	    /**
  	     * Plain text
  	     */

  	    if (value !== '*') {
  	      if (value === '$' || value === '^') {
  	        value = `\\${value}`;
  	      }

  	      const match = REGEX_NON_SPECIAL_CHARS.exec(remaining());
  	      if (match) {
  	        value += match[0];
  	        state.index += match[0].length;
  	      }

  	      push({ type: 'text', value });
  	      continue;
  	    }

  	    /**
  	     * Stars
  	     */

  	    if (prev && (prev.type === 'globstar' || prev.star === true)) {
  	      prev.type = 'star';
  	      prev.star = true;
  	      prev.value += value;
  	      prev.output = star;
  	      state.backtrack = true;
  	      state.globstar = true;
  	      consume(value);
  	      continue;
  	    }

  	    let rest = remaining();
  	    if (opts.noextglob !== true && /^\([^?]/.test(rest)) {
  	      extglobOpen('star', value);
  	      continue;
  	    }

  	    if (prev.type === 'star') {
  	      if (opts.noglobstar === true) {
  	        consume(value);
  	        continue;
  	      }

  	      const prior = prev.prev;
  	      const before = prior.prev;
  	      const isStart = prior.type === 'slash' || prior.type === 'bos';
  	      const afterStar = before && (before.type === 'star' || before.type === 'globstar');

  	      if (opts.bash === true && (!isStart || (rest[0] && rest[0] !== '/'))) {
  	        push({ type: 'star', value, output: '' });
  	        continue;
  	      }

  	      const isBrace = state.braces > 0 && (prior.type === 'comma' || prior.type === 'brace');
  	      const isExtglob = extglobs.length && (prior.type === 'pipe' || prior.type === 'paren');
  	      if (!isStart && prior.type !== 'paren' && !isBrace && !isExtglob) {
  	        push({ type: 'star', value, output: '' });
  	        continue;
  	      }

  	      // strip consecutive `/**/`
  	      while (rest.slice(0, 3) === '/**') {
  	        const after = input[state.index + 4];
  	        if (after && after !== '/') {
  	          break;
  	        }
  	        rest = rest.slice(3);
  	        consume('/**', 3);
  	      }

  	      if (prior.type === 'bos' && eos()) {
  	        prev.type = 'globstar';
  	        prev.value += value;
  	        prev.output = globstar(opts);
  	        state.output = prev.output;
  	        state.globstar = true;
  	        consume(value);
  	        continue;
  	      }

  	      if (prior.type === 'slash' && prior.prev.type !== 'bos' && !afterStar && eos()) {
  	        state.output = state.output.slice(0, -(prior.output + prev.output).length);
  	        prior.output = `(?:${prior.output}`;

  	        prev.type = 'globstar';
  	        prev.output = globstar(opts) + (opts.strictSlashes ? ')' : '|$)');
  	        prev.value += value;
  	        state.globstar = true;
  	        state.output += prior.output + prev.output;
  	        consume(value);
  	        continue;
  	      }

  	      if (prior.type === 'slash' && prior.prev.type !== 'bos' && rest[0] === '/') {
  	        const end = rest[1] !== void 0 ? '|$' : '';

  	        state.output = state.output.slice(0, -(prior.output + prev.output).length);
  	        prior.output = `(?:${prior.output}`;

  	        prev.type = 'globstar';
  	        prev.output = `${globstar(opts)}${SLASH_LITERAL}|${SLASH_LITERAL}${end})`;
  	        prev.value += value;

  	        state.output += prior.output + prev.output;
  	        state.globstar = true;

  	        consume(value + advance());

  	        push({ type: 'slash', value: '/', output: '' });
  	        continue;
  	      }

  	      if (prior.type === 'bos' && rest[0] === '/') {
  	        prev.type = 'globstar';
  	        prev.value += value;
  	        prev.output = `(?:^|${SLASH_LITERAL}|${globstar(opts)}${SLASH_LITERAL})`;
  	        state.output = prev.output;
  	        state.globstar = true;
  	        consume(value + advance());
  	        push({ type: 'slash', value: '/', output: '' });
  	        continue;
  	      }

  	      // remove single star from output
  	      state.output = state.output.slice(0, -prev.output.length);

  	      // reset previous token to globstar
  	      prev.type = 'globstar';
  	      prev.output = globstar(opts);
  	      prev.value += value;

  	      // reset output with globstar
  	      state.output += prev.output;
  	      state.globstar = true;
  	      consume(value);
  	      continue;
  	    }

  	    const token = { type: 'star', value, output: star };

  	    if (opts.bash === true) {
  	      token.output = '.*?';
  	      if (prev.type === 'bos' || prev.type === 'slash') {
  	        token.output = nodot + token.output;
  	      }
  	      push(token);
  	      continue;
  	    }

  	    if (prev && (prev.type === 'bracket' || prev.type === 'paren') && opts.regex === true) {
  	      token.output = value;
  	      push(token);
  	      continue;
  	    }

  	    if (state.index === state.start || prev.type === 'slash' || prev.type === 'dot') {
  	      if (prev.type === 'dot') {
  	        state.output += NO_DOT_SLASH;
  	        prev.output += NO_DOT_SLASH;

  	      } else if (opts.dot === true) {
  	        state.output += NO_DOTS_SLASH;
  	        prev.output += NO_DOTS_SLASH;

  	      } else {
  	        state.output += nodot;
  	        prev.output += nodot;
  	      }

  	      if (peek() !== '*') {
  	        state.output += ONE_CHAR;
  	        prev.output += ONE_CHAR;
  	      }
  	    }

  	    push(token);
  	  }

  	  while (state.brackets > 0) {
  	    if (opts.strictBrackets === true) throw new SyntaxError(syntaxError('closing', ']'));
  	    state.output = utils.escapeLast(state.output, '[');
  	    decrement('brackets');
  	  }

  	  while (state.parens > 0) {
  	    if (opts.strictBrackets === true) throw new SyntaxError(syntaxError('closing', ')'));
  	    state.output = utils.escapeLast(state.output, '(');
  	    decrement('parens');
  	  }

  	  while (state.braces > 0) {
  	    if (opts.strictBrackets === true) throw new SyntaxError(syntaxError('closing', '}'));
  	    state.output = utils.escapeLast(state.output, '{');
  	    decrement('braces');
  	  }

  	  if (opts.strictSlashes !== true && (prev.type === 'star' || prev.type === 'bracket')) {
  	    push({ type: 'maybe_slash', value: '', output: `${SLASH_LITERAL}?` });
  	  }

  	  // rebuild the output if we had to backtrack at any point
  	  if (state.backtrack === true) {
  	    state.output = '';

  	    for (const token of state.tokens) {
  	      state.output += token.output != null ? token.output : token.value;

  	      if (token.suffix) {
  	        state.output += token.suffix;
  	      }
  	    }
  	  }

  	  return state;
  	};

  	/**
  	 * Fast paths for creating regular expressions for common glob patterns.
  	 * This can significantly speed up processing and has very little downside
  	 * impact when none of the fast paths match.
  	 */

  	parse.fastpaths = (input, options) => {
  	  const opts = { ...options };
  	  const max = typeof opts.maxLength === 'number' ? Math.min(MAX_LENGTH, opts.maxLength) : MAX_LENGTH;
  	  const len = input.length;
  	  if (len > max) {
  	    throw new SyntaxError(`Input length: ${len}, exceeds maximum allowed length: ${max}`);
  	  }

  	  input = REPLACEMENTS[input] || input;

  	  // create constants based on platform, for windows or posix
  	  const {
  	    DOT_LITERAL,
  	    SLASH_LITERAL,
  	    ONE_CHAR,
  	    DOTS_SLASH,
  	    NO_DOT,
  	    NO_DOTS,
  	    NO_DOTS_SLASH,
  	    STAR,
  	    START_ANCHOR
  	  } = constants.globChars(opts.windows);

  	  const nodot = opts.dot ? NO_DOTS : NO_DOT;
  	  const slashDot = opts.dot ? NO_DOTS_SLASH : NO_DOT;
  	  const capture = opts.capture ? '' : '?:';
  	  const state = { negated: false, prefix: '' };
  	  let star = opts.bash === true ? '.*?' : STAR;

  	  if (opts.capture) {
  	    star = `(${star})`;
  	  }

  	  const globstar = opts => {
  	    if (opts.noglobstar === true) return star;
  	    return `(${capture}(?:(?!${START_ANCHOR}${opts.dot ? DOTS_SLASH : DOT_LITERAL}).)*?)`;
  	  };

  	  const create = str => {
  	    switch (str) {
  	      case '*':
  	        return `${nodot}${ONE_CHAR}${star}`;

  	      case '.*':
  	        return `${DOT_LITERAL}${ONE_CHAR}${star}`;

  	      case '*.*':
  	        return `${nodot}${star}${DOT_LITERAL}${ONE_CHAR}${star}`;

  	      case '*/*':
  	        return `${nodot}${star}${SLASH_LITERAL}${ONE_CHAR}${slashDot}${star}`;

  	      case '**':
  	        return nodot + globstar(opts);

  	      case '**/*':
  	        return `(?:${nodot}${globstar(opts)}${SLASH_LITERAL})?${slashDot}${ONE_CHAR}${star}`;

  	      case '**/*.*':
  	        return `(?:${nodot}${globstar(opts)}${SLASH_LITERAL})?${slashDot}${star}${DOT_LITERAL}${ONE_CHAR}${star}`;

  	      case '**/.*':
  	        return `(?:${nodot}${globstar(opts)}${SLASH_LITERAL})?${DOT_LITERAL}${ONE_CHAR}${star}`;

  	      default: {
  	        const match = /^(.*?)\.(\w+)$/.exec(str);
  	        if (!match) return;

  	        const source = create(match[1]);
  	        if (!source) return;

  	        return source + DOT_LITERAL + match[2];
  	      }
  	    }
  	  };

  	  const output = utils.removePrefix(input, state);
  	  let source = create(output);

  	  if (source && opts.strictSlashes !== true) {
  	    source += `${SLASH_LITERAL}?`;
  	  }

  	  return source;
  	};

  	parse_1 = parse;
  	return parse_1;
  }

  var picomatch_1$1;
  var hasRequiredPicomatch$1;

  function requirePicomatch$1 () {
  	if (hasRequiredPicomatch$1) return picomatch_1$1;
  	hasRequiredPicomatch$1 = 1;

  	const scan = /*@__PURE__*/ requireScan();
  	const parse = /*@__PURE__*/ requireParse();
  	const utils = /*@__PURE__*/ requireUtils();
  	const constants = /*@__PURE__*/ requireConstants();
  	const isObject = val => val && typeof val === 'object' && !Array.isArray(val);

  	/**
  	 * Creates a matcher function from one or more glob patterns. The
  	 * returned function takes a string to match as its first argument,
  	 * and returns true if the string is a match. The returned matcher
  	 * function also takes a boolean as the second argument that, when true,
  	 * returns an object with additional information.
  	 *
  	 * ```js
  	 * const picomatch = require('picomatch');
  	 * // picomatch(glob[, options]);
  	 *
  	 * const isMatch = picomatch('*.!(*a)');
  	 * console.log(isMatch('a.a')); //=> false
  	 * console.log(isMatch('a.b')); //=> true
  	 * ```
  	 * @name picomatch
  	 * @param {String|Array} `globs` One or more glob patterns.
  	 * @param {Object=} `options`
  	 * @return {Function=} Returns a matcher function.
  	 * @api public
  	 */

  	const picomatch = (glob, options, returnState = false) => {
  	  if (Array.isArray(glob)) {
  	    const fns = glob.map(input => picomatch(input, options, returnState));
  	    const arrayMatcher = str => {
  	      for (const isMatch of fns) {
  	        const state = isMatch(str);
  	        if (state) return state;
  	      }
  	      return false;
  	    };
  	    return arrayMatcher;
  	  }

  	  const isState = isObject(glob) && glob.tokens && glob.input;

  	  if (glob === '' || (typeof glob !== 'string' && !isState)) {
  	    throw new TypeError('Expected pattern to be a non-empty string');
  	  }

  	  const opts = options || {};
  	  const posix = opts.windows;
  	  const regex = isState
  	    ? picomatch.compileRe(glob, options)
  	    : picomatch.makeRe(glob, options, false, true);

  	  const state = regex.state;
  	  delete regex.state;

  	  let isIgnored = () => false;
  	  if (opts.ignore) {
  	    const ignoreOpts = { ...options, ignore: null, onMatch: null, onResult: null };
  	    isIgnored = picomatch(opts.ignore, ignoreOpts, returnState);
  	  }

  	  const matcher = (input, returnObject = false) => {
  	    const { isMatch, match, output } = picomatch.test(input, regex, options, { glob, posix });
  	    const result = { glob, state, regex, posix, input, output, match, isMatch };

  	    if (typeof opts.onResult === 'function') {
  	      opts.onResult(result);
  	    }

  	    if (isMatch === false) {
  	      result.isMatch = false;
  	      return returnObject ? result : false;
  	    }

  	    if (isIgnored(input)) {
  	      if (typeof opts.onIgnore === 'function') {
  	        opts.onIgnore(result);
  	      }
  	      result.isMatch = false;
  	      return returnObject ? result : false;
  	    }

  	    if (typeof opts.onMatch === 'function') {
  	      opts.onMatch(result);
  	    }
  	    return returnObject ? result : true;
  	  };

  	  if (returnState) {
  	    matcher.state = state;
  	  }

  	  return matcher;
  	};

  	/**
  	 * Test `input` with the given `regex`. This is used by the main
  	 * `picomatch()` function to test the input string.
  	 *
  	 * ```js
  	 * const picomatch = require('picomatch');
  	 * // picomatch.test(input, regex[, options]);
  	 *
  	 * console.log(picomatch.test('foo/bar', /^(?:([^/]*?)\/([^/]*?))$/));
  	 * // { isMatch: true, match: [ 'foo/', 'foo', 'bar' ], output: 'foo/bar' }
  	 * ```
  	 * @param {String} `input` String to test.
  	 * @param {RegExp} `regex`
  	 * @return {Object} Returns an object with matching info.
  	 * @api public
  	 */

  	picomatch.test = (input, regex, options, { glob, posix } = {}) => {
  	  if (typeof input !== 'string') {
  	    throw new TypeError('Expected input to be a string');
  	  }

  	  if (input === '') {
  	    return { isMatch: false, output: '' };
  	  }

  	  const opts = options || {};
  	  const format = opts.format || (posix ? utils.toPosixSlashes : null);
  	  let match = input === glob;
  	  let output = (match && format) ? format(input) : input;

  	  if (match === false) {
  	    output = format ? format(input) : input;
  	    match = output === glob;
  	  }

  	  if (match === false || opts.capture === true) {
  	    if (opts.matchBase === true || opts.basename === true) {
  	      match = picomatch.matchBase(input, regex, options, posix);
  	    } else {
  	      match = regex.exec(output);
  	    }
  	  }

  	  return { isMatch: Boolean(match), match, output };
  	};

  	/**
  	 * Match the basename of a filepath.
  	 *
  	 * ```js
  	 * const picomatch = require('picomatch');
  	 * // picomatch.matchBase(input, glob[, options]);
  	 * console.log(picomatch.matchBase('foo/bar.js', '*.js'); // true
  	 * ```
  	 * @param {String} `input` String to test.
  	 * @param {RegExp|String} `glob` Glob pattern or regex created by [.makeRe](#makeRe).
  	 * @return {Boolean}
  	 * @api public
  	 */

  	picomatch.matchBase = (input, glob, options) => {
  	  const regex = glob instanceof RegExp ? glob : picomatch.makeRe(glob, options);
  	  return regex.test(utils.basename(input));
  	};

  	/**
  	 * Returns true if **any** of the given glob `patterns` match the specified `string`.
  	 *
  	 * ```js
  	 * const picomatch = require('picomatch');
  	 * // picomatch.isMatch(string, patterns[, options]);
  	 *
  	 * console.log(picomatch.isMatch('a.a', ['b.*', '*.a'])); //=> true
  	 * console.log(picomatch.isMatch('a.a', 'b.*')); //=> false
  	 * ```
  	 * @param {String|Array} str The string to test.
  	 * @param {String|Array} patterns One or more glob patterns to use for matching.
  	 * @param {Object} [options] See available [options](#options).
  	 * @return {Boolean} Returns true if any patterns match `str`
  	 * @api public
  	 */

  	picomatch.isMatch = (str, patterns, options) => picomatch(patterns, options)(str);

  	/**
  	 * Parse a glob pattern to create the source string for a regular
  	 * expression.
  	 *
  	 * ```js
  	 * const picomatch = require('picomatch');
  	 * const result = picomatch.parse(pattern[, options]);
  	 * ```
  	 * @param {String} `pattern`
  	 * @param {Object} `options`
  	 * @return {Object} Returns an object with useful properties and output to be used as a regex source string.
  	 * @api public
  	 */

  	picomatch.parse = (pattern, options) => {
  	  if (Array.isArray(pattern)) return pattern.map(p => picomatch.parse(p, options));
  	  return parse(pattern, { ...options, fastpaths: false });
  	};

  	/**
  	 * Scan a glob pattern to separate the pattern into segments.
  	 *
  	 * ```js
  	 * const picomatch = require('picomatch');
  	 * // picomatch.scan(input[, options]);
  	 *
  	 * const result = picomatch.scan('!./foo/*.js');
  	 * console.log(result);
  	 * { prefix: '!./',
  	 *   input: '!./foo/*.js',
  	 *   start: 3,
  	 *   base: 'foo',
  	 *   glob: '*.js',
  	 *   isBrace: false,
  	 *   isBracket: false,
  	 *   isGlob: true,
  	 *   isExtglob: false,
  	 *   isGlobstar: false,
  	 *   negated: true }
  	 * ```
  	 * @param {String} `input` Glob pattern to scan.
  	 * @param {Object} `options`
  	 * @return {Object} Returns an object with
  	 * @api public
  	 */

  	picomatch.scan = (input, options) => scan(input, options);

  	/**
  	 * Compile a regular expression from the `state` object returned by the
  	 * [parse()](#parse) method.
  	 *
  	 * @param {Object} `state`
  	 * @param {Object} `options`
  	 * @param {Boolean} `returnOutput` Intended for implementors, this argument allows you to return the raw output from the parser.
  	 * @param {Boolean} `returnState` Adds the state to a `state` property on the returned regex. Useful for implementors and debugging.
  	 * @return {RegExp}
  	 * @api public
  	 */

  	picomatch.compileRe = (state, options, returnOutput = false, returnState = false) => {
  	  if (returnOutput === true) {
  	    return state.output;
  	  }

  	  const opts = options || {};
  	  const prepend = opts.contains ? '' : '^';
  	  const append = opts.contains ? '' : '$';

  	  let source = `${prepend}(?:${state.output})${append}`;
  	  if (state && state.negated === true) {
  	    source = `^(?!${source}).*$`;
  	  }

  	  const regex = picomatch.toRegex(source, options);
  	  if (returnState === true) {
  	    regex.state = state;
  	  }

  	  return regex;
  	};

  	/**
  	 * Create a regular expression from a parsed glob pattern.
  	 *
  	 * ```js
  	 * const picomatch = require('picomatch');
  	 * const state = picomatch.parse('*.js');
  	 * // picomatch.compileRe(state[, options]);
  	 *
  	 * console.log(picomatch.compileRe(state));
  	 * //=> /^(?:(?!\.)(?=.)[^/]*?\.js)$/
  	 * ```
  	 * @param {String} `state` The object returned from the `.parse` method.
  	 * @param {Object} `options`
  	 * @param {Boolean} `returnOutput` Implementors may use this argument to return the compiled output, instead of a regular expression. This is not exposed on the options to prevent end-users from mutating the result.
  	 * @param {Boolean} `returnState` Implementors may use this argument to return the state from the parsed glob with the returned regular expression.
  	 * @return {RegExp} Returns a regex created from the given pattern.
  	 * @api public
  	 */

  	picomatch.makeRe = (input, options = {}, returnOutput = false, returnState = false) => {
  	  if (!input || typeof input !== 'string') {
  	    throw new TypeError('Expected a non-empty string');
  	  }

  	  let parsed = { negated: false, fastpaths: true };

  	  if (options.fastpaths !== false && (input[0] === '.' || input[0] === '*')) {
  	    parsed.output = parse.fastpaths(input, options);
  	  }

  	  if (!parsed.output) {
  	    parsed = parse(input, options);
  	  }

  	  return picomatch.compileRe(parsed, options, returnOutput, returnState);
  	};

  	/**
  	 * Create a regular expression from the given regex source string.
  	 *
  	 * ```js
  	 * const picomatch = require('picomatch');
  	 * // picomatch.toRegex(source[, options]);
  	 *
  	 * const { output } = picomatch.parse('*.js');
  	 * console.log(picomatch.toRegex(output));
  	 * //=> /^(?:(?!\.)(?=.)[^/]*?\.js)$/
  	 * ```
  	 * @param {String} `source` Regular expression source string.
  	 * @param {Object} `options`
  	 * @return {RegExp}
  	 * @api public
  	 */

  	picomatch.toRegex = (source, options) => {
  	  try {
  	    const opts = options || {};
  	    return new RegExp(source, opts.flags || (opts.nocase ? 'i' : ''));
  	  } catch (err) {
  	    if (options && options.debug === true) throw err;
  	    return /$^/;
  	  }
  	};

  	/**
  	 * Picomatch constants.
  	 * @return {Object}
  	 */

  	picomatch.constants = constants;

  	/**
  	 * Expose "picomatch"
  	 */

  	picomatch_1$1 = picomatch;
  	return picomatch_1$1;
  }

  var picomatch_1;
  var hasRequiredPicomatch;

  function requirePicomatch () {
  	if (hasRequiredPicomatch) return picomatch_1;
  	hasRequiredPicomatch = 1;

  	const pico = /*@__PURE__*/ requirePicomatch$1();
  	const utils = /*@__PURE__*/ requireUtils();

  	function picomatch(glob, options, returnState = false) {
  	  // default to os.platform()
  	  if (options && (options.windows === null || options.windows === undefined)) {
  	    // don't mutate the original options object
  	    options = { ...options, windows: utils.isWindows() };
  	  }

  	  return pico(glob, options, returnState);
  	}

  	Object.assign(picomatch, pico);
  	picomatch_1 = picomatch;
  	return picomatch_1;
  }

  var picomatchExports = /*@__PURE__*/ requirePicomatch();
  var pm = /*@__PURE__*/getDefaultExportFromCjs(picomatchExports);

  function isArray(arg) {
      return Array.isArray(arg);
  }
  function ensureArray(thing) {
      if (isArray(thing))
          return thing;
      if (thing == null)
          return [];
      return [thing];
  }
  const globToTest = (glob) => {
      const pattern = glob;
      const fn = pm(pattern, { dot: true });
      return {
          test: (what) => {
              const result = fn(what);
              return result;
          },
      };
  };
  const testTrue = {
      test: () => true,
  };
  const getMatcher = (filter) => {
      const bundleTest = "bundle" in filter && filter.bundle != null ? globToTest(filter.bundle) : testTrue;
      const fileTest = "file" in filter && filter.file != null ? globToTest(filter.file) : testTrue;
      return { bundleTest, fileTest };
  };
  const createFilter = (include, exclude) => {
      const includeMatchers = ensureArray(include).map(getMatcher);
      const excludeMatchers = ensureArray(exclude).map(getMatcher);
      return (bundleId, id) => {
          for (let i = 0; i < excludeMatchers.length; ++i) {
              const { bundleTest, fileTest } = excludeMatchers[i];
              if (bundleTest.test(bundleId) && fileTest.test(id))
                  return false;
          }
          for (let i = 0; i < includeMatchers.length; ++i) {
              const { bundleTest, fileTest } = includeMatchers[i];
              if (bundleTest.test(bundleId) && fileTest.test(id))
                  return true;
          }
          return !includeMatchers.length;
      };
  };

  const throttleFilter = (callback, limit) => {
      let waiting = false;
      return (val) => {
          if (!waiting) {
              callback(val);
              waiting = true;
              setTimeout(() => {
                  waiting = false;
              }, limit);
          }
      };
  };
  const prepareFilter = (filt) => {
      if (filt === "")
          return [];
      return (filt
          .split(",")
          // remove spaces before and after
          .map((entry) => entry.trim())
          // unquote "
          .map((entry) => entry.startsWith('"') && entry.endsWith('"') ? entry.substring(1, entry.length - 1) : entry)
          // unquote '
          .map((entry) => entry.startsWith("'") && entry.endsWith("'") ? entry.substring(1, entry.length - 1) : entry)
          // remove empty strings
          .filter((entry) => entry)
          // parse bundle:file
          .map((entry) => entry.split(":"))
          // normalize entry just in case
          .flatMap((entry) => {
          if (entry.length === 0)
              return [];
          let bundle = null;
          let file = null;
          if (entry.length === 1 && entry[0]) {
              file = entry[0];
              return [{ file, bundle }];
          }
          bundle = entry[0] || null;
          file = entry.slice(1).join(":") || null;
          return [{ bundle, file }];
      }));
  };
  const useFilter = () => {
      const [includeFilter, setIncludeFilter] = d("");
      const [excludeFilter, setExcludeFilter] = d("");
      const setIncludeFilterTrottled = T(() => throttleFilter(setIncludeFilter, 200), []);
      const setExcludeFilterTrottled = T(() => throttleFilter(setExcludeFilter, 200), []);
      const isIncluded = T(() => createFilter(prepareFilter(includeFilter), prepareFilter(excludeFilter)), [includeFilter, excludeFilter]);
      const getModuleFilterMultiplier = q((bundleId, data) => {
          return isIncluded(bundleId, data.id) ? 1 : 0;
      }, [isIncluded]);
      return {
          getModuleFilterMultiplier,
          includeFilter,
          excludeFilter,
          setExcludeFilter: setExcludeFilterTrottled,
          setIncludeFilter: setIncludeFilterTrottled,
      };
  };

  function ascending(a, b) {
    return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;
  }

  function descending(a, b) {
    return a == null || b == null ? NaN
      : b < a ? -1
      : b > a ? 1
      : b >= a ? 0
      : NaN;
  }

  function bisector(f) {
    let compare1, compare2, delta;

    // If an accessor is specified, promote it to a comparator. In this case we
    // can test whether the search value is (self-) comparable. We can’t do this
    // for a comparator (except for specific, known comparators) because we can’t
    // tell if the comparator is symmetric, and an asymmetric comparator can’t be
    // used to test whether a single value is comparable.
    if (f.length !== 2) {
      compare1 = ascending;
      compare2 = (d, x) => ascending(f(d), x);
      delta = (d, x) => f(d) - x;
    } else {
      compare1 = f === ascending || f === descending ? f : zero$1;
      compare2 = f;
      delta = f;
    }

    function left(a, x, lo = 0, hi = a.length) {
      if (lo < hi) {
        if (compare1(x, x) !== 0) return hi;
        do {
          const mid = (lo + hi) >>> 1;
          if (compare2(a[mid], x) < 0) lo = mid + 1;
          else hi = mid;
        } while (lo < hi);
      }
      return lo;
    }

    function right(a, x, lo = 0, hi = a.length) {
      if (lo < hi) {
        if (compare1(x, x) !== 0) return hi;
        do {
          const mid = (lo + hi) >>> 1;
          if (compare2(a[mid], x) <= 0) lo = mid + 1;
          else hi = mid;
        } while (lo < hi);
      }
      return lo;
    }

    function center(a, x, lo = 0, hi = a.length) {
      const i = left(a, x, lo, hi - 1);
      return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;
    }

    return {left, center, right};
  }

  function zero$1() {
    return 0;
  }

  function number$1(x) {
    return x === null ? NaN : +x;
  }

  const ascendingBisect = bisector(ascending);
  const bisectRight = ascendingBisect.right;
  bisector(number$1).center;

  class InternMap extends Map {
    constructor(entries, key = keyof) {
      super();
      Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});
      if (entries != null) for (const [key, value] of entries) this.set(key, value);
    }
    get(key) {
      return super.get(intern_get(this, key));
    }
    has(key) {
      return super.has(intern_get(this, key));
    }
    set(key, value) {
      return super.set(intern_set(this, key), value);
    }
    delete(key) {
      return super.delete(intern_delete(this, key));
    }
  }

  function intern_get({_intern, _key}, value) {
    const key = _key(value);
    return _intern.has(key) ? _intern.get(key) : value;
  }

  function intern_set({_intern, _key}, value) {
    const key = _key(value);
    if (_intern.has(key)) return _intern.get(key);
    _intern.set(key, value);
    return value;
  }

  function intern_delete({_intern, _key}, value) {
    const key = _key(value);
    if (_intern.has(key)) {
      value = _intern.get(key);
      _intern.delete(key);
    }
    return value;
  }

  function keyof(value) {
    return value !== null && typeof value === "object" ? value.valueOf() : value;
  }

  function identity$2(x) {
    return x;
  }

  function group(values, ...keys) {
    return nest(values, identity$2, identity$2, keys);
  }

  function nest(values, map, reduce, keys) {
    return (function regroup(values, i) {
      if (i >= keys.length) return reduce(values);
      const groups = new InternMap();
      const keyof = keys[i++];
      let index = -1;
      for (const value of values) {
        const key = keyof(value, ++index, values);
        const group = groups.get(key);
        if (group) group.push(value);
        else groups.set(key, [value]);
      }
      for (const [key, values] of groups) {
        groups.set(key, regroup(values, i));
      }
      return map(groups);
    })(values, 0);
  }

  const e10 = Math.sqrt(50),
      e5 = Math.sqrt(10),
      e2 = Math.sqrt(2);

  function tickSpec(start, stop, count) {
    const step = (stop - start) / Math.max(0, count),
        power = Math.floor(Math.log10(step)),
        error = step / Math.pow(10, power),
        factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;
    let i1, i2, inc;
    if (power < 0) {
      inc = Math.pow(10, -power) / factor;
      i1 = Math.round(start * inc);
      i2 = Math.round(stop * inc);
      if (i1 / inc < start) ++i1;
      if (i2 / inc > stop) --i2;
      inc = -inc;
    } else {
      inc = Math.pow(10, power) * factor;
      i1 = Math.round(start / inc);
      i2 = Math.round(stop / inc);
      if (i1 * inc < start) ++i1;
      if (i2 * inc > stop) --i2;
    }
    if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);
    return [i1, i2, inc];
  }

  function ticks(start, stop, count) {
    stop = +stop, start = +start, count = +count;
    if (!(count > 0)) return [];
    if (start === stop) return [start];
    const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);
    if (!(i2 >= i1)) return [];
    const n = i2 - i1 + 1, ticks = new Array(n);
    if (reverse) {
      if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) / -inc;
      else for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) * inc;
    } else {
      if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) / -inc;
      else for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) * inc;
    }
    return ticks;
  }

  function tickIncrement(start, stop, count) {
    stop = +stop, start = +start, count = +count;
    return tickSpec(start, stop, count)[2];
  }

  function tickStep(start, stop, count) {
    stop = +stop, start = +start, count = +count;
    const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);
    return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);
  }

  const TOP_PADDING = 20;
  const PADDING = 2;

  const Node = ({ node, onMouseOver, onClick, selected }) => {
      const { getModuleColor } = x(StaticContext);
      const { backgroundColor, fontColor } = getModuleColor(node);
      const { x0, x1, y1, y0, data, children = null } = node;
      const textRef = A(null);
      const textRectRef = A();
      const width = x1 - x0;
      const height = y1 - y0;
      const textProps = {
          "font-size": "0.7em",
          "dominant-baseline": "middle",
          "text-anchor": "middle",
          x: width / 2,
      };
      if (children != null) {
          textProps.y = (TOP_PADDING + PADDING) / 2;
      }
      else {
          textProps.y = height / 2;
      }
      _(() => {
          if (width == 0 || height == 0 || !textRef.current) {
              return;
          }
          if (textRectRef.current == null) {
              textRectRef.current = textRef.current.getBoundingClientRect();
          }
          let scale = 1;
          if (children != null) {
              scale = Math.min((width * 0.9) / textRectRef.current.width, Math.min(height, TOP_PADDING + PADDING) / textRectRef.current.height);
              scale = Math.min(1, scale);
              textRef.current.setAttribute("y", String(Math.min(TOP_PADDING + PADDING, height) / 2 / scale));
              textRef.current.setAttribute("x", String(width / 2 / scale));
          }
          else {
              scale = Math.min((width * 0.9) / textRectRef.current.width, (height * 0.9) / textRectRef.current.height);
              scale = Math.min(1, scale);
              textRef.current.setAttribute("y", String(height / 2 / scale));
              textRef.current.setAttribute("x", String(width / 2 / scale));
          }
          textRef.current.setAttribute("transform", `scale(${scale.toFixed(2)})`);
      }, [children, height, width]);
      if (width == 0 || height == 0) {
          return null;
      }
      return (u$1("g", { className: "node", transform: `translate(${x0},${y0})`, onClick: (event) => {
              event.stopPropagation();
              onClick(node);
          }, onMouseOver: (event) => {
              event.stopPropagation();
              onMouseOver(node);
          }, children: [u$1("rect", { fill: backgroundColor, rx: 2, ry: 2, width: x1 - x0, height: y1 - y0, stroke: selected ? "#fff" : undefined, "stroke-width": selected ? 2 : undefined }), u$1("text", Object.assign({ ref: textRef, fill: fontColor, onClick: (event) => {
                      var _a;
                      if (((_a = window.getSelection()) === null || _a === void 0 ? void 0 : _a.toString()) !== "") {
                          event.stopPropagation();
                      }
                  } }, textProps, { children: data.name }))] }));
  };

  const TreeMap = ({ root, onNodeHover, selectedNode, onNodeClick, }) => {
      const { width, height, getModuleIds } = x(StaticContext);
      console.time("layering");
      // this will make groups by height
      const nestedData = T(() => {
          const nestedDataMap = group(root.descendants(), (d) => d.height);
          const nestedData = Array.from(nestedDataMap, ([key, values]) => ({
              key,
              values,
          }));
          nestedData.sort((a, b) => b.key - a.key);
          return nestedData;
      }, [root]);
      console.timeEnd("layering");
      return (u$1("svg", { xmlns: "http://www.w3.org/2000/svg", viewBox: `0 0 ${width} ${height}`, children: nestedData.map(({ key, values }) => {
              return (u$1("g", { className: "layer", children: values.map((node) => {
                      return (u$1(Node, { node: node, onMouseOver: onNodeHover, selected: selectedNode === node, onClick: onNodeClick }, getModuleIds(node.data).nodeUid.id));
                  }) }, key));
          }) }));
  };

  var bytes = {exports: {}};

  /*!
   * bytes
   * Copyright(c) 2012-2014 TJ Holowaychuk
   * Copyright(c) 2015 Jed Watson
   * MIT Licensed
   */

  var hasRequiredBytes;

  function requireBytes () {
  	if (hasRequiredBytes) return bytes.exports;
  	hasRequiredBytes = 1;

  	/**
  	 * Module exports.
  	 * @public
  	 */

  	bytes.exports = bytes$1;
  	bytes.exports.format = format;
  	bytes.exports.parse = parse;

  	/**
  	 * Module variables.
  	 * @private
  	 */

  	var formatThousandsRegExp = /\B(?=(\d{3})+(?!\d))/g;

  	var formatDecimalsRegExp = /(?:\.0*|(\.[^0]+)0+)$/;

  	var map = {
  	  b:  1,
  	  kb: 1 << 10,
  	  mb: 1 << 20,
  	  gb: 1 << 30,
  	  tb: Math.pow(1024, 4),
  	  pb: Math.pow(1024, 5),
  	};

  	var parseRegExp = /^((-|\+)?(\d+(?:\.\d+)?)) *(kb|mb|gb|tb|pb)$/i;

  	/**
  	 * Convert the given value in bytes into a string or parse to string to an integer in bytes.
  	 *
  	 * @param {string|number} value
  	 * @param {{
  	 *  case: [string],
  	 *  decimalPlaces: [number]
  	 *  fixedDecimals: [boolean]
  	 *  thousandsSeparator: [string]
  	 *  unitSeparator: [string]
  	 *  }} [options] bytes options.
  	 *
  	 * @returns {string|number|null}
  	 */

  	function bytes$1(value, options) {
  	  if (typeof value === 'string') {
  	    return parse(value);
  	  }

  	  if (typeof value === 'number') {
  	    return format(value, options);
  	  }

  	  return null;
  	}

  	/**
  	 * Format the given value in bytes into a string.
  	 *
  	 * If the value is negative, it is kept as such. If it is a float,
  	 * it is rounded.
  	 *
  	 * @param {number} value
  	 * @param {object} [options]
  	 * @param {number} [options.decimalPlaces=2]
  	 * @param {number} [options.fixedDecimals=false]
  	 * @param {string} [options.thousandsSeparator=]
  	 * @param {string} [options.unit=]
  	 * @param {string} [options.unitSeparator=]
  	 *
  	 * @returns {string|null}
  	 * @public
  	 */

  	function format(value, options) {
  	  if (!Number.isFinite(value)) {
  	    return null;
  	  }

  	  var mag = Math.abs(value);
  	  var thousandsSeparator = (options && options.thousandsSeparator) || '';
  	  var unitSeparator = (options && options.unitSeparator) || '';
  	  var decimalPlaces = (options && options.decimalPlaces !== undefined) ? options.decimalPlaces : 2;
  	  var fixedDecimals = Boolean(options && options.fixedDecimals);
  	  var unit = (options && options.unit) || '';

  	  if (!unit || !map[unit.toLowerCase()]) {
  	    if (mag >= map.pb) {
  	      unit = 'PB';
  	    } else if (mag >= map.tb) {
  	      unit = 'TB';
  	    } else if (mag >= map.gb) {
  	      unit = 'GB';
  	    } else if (mag >= map.mb) {
  	      unit = 'MB';
  	    } else if (mag >= map.kb) {
  	      unit = 'KB';
  	    } else {
  	      unit = 'B';
  	    }
  	  }

  	  var val = value / map[unit.toLowerCase()];
  	  var str = val.toFixed(decimalPlaces);

  	  if (!fixedDecimals) {
  	    str = str.replace(formatDecimalsRegExp, '$1');
  	  }

  	  if (thousandsSeparator) {
  	    str = str.split('.').map(function (s, i) {
  	      return i === 0
  	        ? s.replace(formatThousandsRegExp, thousandsSeparator)
  	        : s
  	    }).join('.');
  	  }

  	  return str + unitSeparator + unit;
  	}

  	/**
  	 * Parse the string value into an integer in bytes.
  	 *
  	 * If no unit is given, it is assumed the value is in bytes.
  	 *
  	 * @param {number|string} val
  	 *
  	 * @returns {number|null}
  	 * @public
  	 */

  	function parse(val) {
  	  if (typeof val === 'number' && !isNaN(val)) {
  	    return val;
  	  }

  	  if (typeof val !== 'string') {
  	    return null;
  	  }

  	  // Test if the string passed is valid
  	  var results = parseRegExp.exec(val);
  	  var floatValue;
  	  var unit = 'b';

  	  if (!results) {
  	    // Nothing could be extracted from the given string
  	    floatValue = parseInt(val, 10);
  	    unit = 'b';
  	  } else {
  	    // Retrieve the value and the unit
  	    floatValue = parseFloat(results[1]);
  	    unit = results[4].toLowerCase();
  	  }

  	  if (isNaN(floatValue)) {
  	    return null;
  	  }

  	  return Math.floor(map[unit] * floatValue);
  	}
  	return bytes.exports;
  }

  var bytesExports = requireBytes();

  const Tooltip_marginX = 10;
  const Tooltip_marginY = 30;
  const SOURCEMAP_RENDERED = (u$1("span", { children: [" ", u$1("b", { children: LABELS.renderedLength }), " is a number of characters in the file after individual and ", u$1("br", {}), " ", "whole bundle transformations according to sourcemap."] }));
  const RENDRED = (u$1("span", { children: [u$1("b", { children: LABELS.renderedLength }), " is a byte size of individual file after transformations and treeshake."] }));
  const COMPRESSED = (u$1("span", { children: [u$1("b", { children: LABELS.gzipLength }), " and ", u$1("b", { children: LABELS.brotliLength }), " is a byte size of individual file after individual transformations,", u$1("br", {}), " treeshake and compression."] }));
  const Tooltip = ({ node, visible, root, sizeProperty, }) => {
      const { availableSizeProperties, getModuleSize, data } = x(StaticContext);
      const ref = A(null);
      const [style, setStyle] = d({});
      const content = T(() => {
          if (!node)
              return null;
          const mainSize = getModuleSize(node.data, sizeProperty);
          const percentageNum = (100 * mainSize) / getModuleSize(root.data, sizeProperty);
          const percentage = percentageNum.toFixed(2);
          const percentageString = percentage + "%";
          const path = node
              .ancestors()
              .reverse()
              .map((d) => d.data.name)
              .join("/");
          let dataNode = null;
          if (!isModuleTree(node.data)) {
              const mainUid = data.nodeParts[node.data.uid].metaUid;
              dataNode = data.nodeMetas[mainUid];
          }
          return (u$1(k$1, { children: [u$1("div", { children: path }), availableSizeProperties.map((sizeProp) => {
                      if (sizeProp === sizeProperty) {
                          return (u$1("div", { children: [u$1("b", { children: [LABELS[sizeProp], ": ", bytesExports.format(mainSize)] }), " ", "(", percentageString, ")"] }, sizeProp));
                      }
                      else {
                          return (u$1("div", { children: [LABELS[sizeProp], ": ", bytesExports.format(getModuleSize(node.data, sizeProp))] }, sizeProp));
                      }
                  }), u$1("br", {}), dataNode && dataNode.importedBy.length > 0 && (u$1("div", { children: [u$1("div", { children: [u$1("b", { children: "Imported By" }), ":"] }), dataNode.importedBy.map(({ uid }) => {
                              const id = data.nodeMetas[uid].id;
                              return u$1("div", { children: id }, id);
                          })] })), u$1("br", {}), u$1("small", { children: data.options.sourcemap ? SOURCEMAP_RENDERED : RENDRED }), (data.options.gzip || data.options.brotli) && (u$1(k$1, { children: [u$1("br", {}), u$1("small", { children: COMPRESSED })] }))] }));
      }, [availableSizeProperties, data, getModuleSize, node, root.data, sizeProperty]);
      const updatePosition = (mouseCoords) => {
          if (!ref.current)
              return;
          const pos = {
              left: mouseCoords.x + Tooltip_marginX,
              top: mouseCoords.y + Tooltip_marginY,
          };
          const boundingRect = ref.current.getBoundingClientRect();
          if (pos.left + boundingRect.width > window.innerWidth) {
              // Shifting horizontally
              pos.left = Math.max(0, window.innerWidth - boundingRect.width);
          }
          if (pos.top + boundingRect.height > window.innerHeight) {
              // Flipping vertically
              pos.top = Math.max(0, mouseCoords.y - Tooltip_marginY - boundingRect.height);
          }
          setStyle(pos);
      };
      y(() => {
          const handleMouseMove = (event) => {
              updatePosition({
                  x: event.pageX,
                  y: event.pageY,
              });
          };
          document.addEventListener("mousemove", handleMouseMove, true);
          return () => {
              document.removeEventListener("mousemove", handleMouseMove, true);
          };
      }, []);
      return (u$1("div", { className: `tooltip ${visible ? "" : "tooltip-hidden"}`, ref: ref, style: style, children: content }));
  };

  const Chart = ({ root, sizeProperty, selectedNode, setSelectedNode, }) => {
      const [showTooltip, setShowTooltip] = d(false);
      const [tooltipNode, setTooltipNode] = d(undefined);
      y(() => {
          const handleMouseOut = () => {
              setShowTooltip(false);
          };
          document.addEventListener("mouseover", handleMouseOut);
          return () => {
              document.removeEventListener("mouseover", handleMouseOut);
          };
      }, []);
      return (u$1(k$1, { children: [u$1(TreeMap, { root: root, onNodeHover: (node) => {
                      setTooltipNode(node);
                      setShowTooltip(true);
                  }, selectedNode: selectedNode, onNodeClick: (node) => {
                      setSelectedNode(selectedNode === node ? undefined : node);
                  } }), u$1(Tooltip, { visible: showTooltip, node: tooltipNode, root: root, sizeProperty: sizeProperty })] }));
  };

  const Main = () => {
      const { availableSizeProperties, rawHierarchy, getModuleSize, layout, data } = x(StaticContext);
      const [sizeProperty, setSizeProperty] = d(availableSizeProperties[0]);
      const [selectedNode, setSelectedNode] = d(undefined);
      const { getModuleFilterMultiplier, setExcludeFilter, setIncludeFilter } = useFilter();
      console.time("getNodeSizeMultiplier");
      const getNodeSizeMultiplier = T(() => {
          const selectedMultiplier = 1; // selectedSize < rootSize * increaseFactor ? (rootSize * increaseFactor) / selectedSize : rootSize / selectedSize;
          const nonSelectedMultiplier = 0; // 1 / selectedMultiplier
          if (selectedNode === undefined) {
              return () => 1;
          }
          else if (isModuleTree(selectedNode.data)) {
              const leaves = new Set(selectedNode.leaves().map((d) => d.data));
              return (node) => {
                  if (leaves.has(node)) {
                      return selectedMultiplier;
                  }
                  return nonSelectedMultiplier;
              };
          }
          else {
              return (node) => {
                  if (node === selectedNode.data) {
                      return selectedMultiplier;
                  }
                  return nonSelectedMultiplier;
              };
          }
      }, [getModuleSize, rawHierarchy.data, selectedNode, sizeProperty]);
      console.timeEnd("getNodeSizeMultiplier");
      console.time("root hierarchy compute");
      // root here always be the same as rawHierarchy even after layouting
      const root = T(() => {
          const rootWithSizesAndSorted = rawHierarchy
              .sum((node) => {
              var _a;
              if (isModuleTree(node))
                  return 0;
              const meta = data.nodeMetas[data.nodeParts[node.uid].metaUid];
              /* eslint-disable typescript/no-non-null-asserted-optional-chain typescript/no-extra-non-null-assertion */
              const bundleId = (_a = Object.entries(meta.moduleParts).find(([, uid]) => uid == node.uid)) === null || _a === void 0 ? void 0 : _a[0];
              const ownSize = getModuleSize(node, sizeProperty);
              const zoomMultiplier = getNodeSizeMultiplier(node);
              const filterMultiplier = getModuleFilterMultiplier(bundleId, meta);
              return ownSize * zoomMultiplier * filterMultiplier;
          })
              .sort((a, b) => getModuleSize(a.data, sizeProperty) - getModuleSize(b.data, sizeProperty));
          return layout(rootWithSizesAndSorted);
      }, [
          data,
          getModuleFilterMultiplier,
          getModuleSize,
          getNodeSizeMultiplier,
          layout,
          rawHierarchy,
          sizeProperty,
      ]);
      console.timeEnd("root hierarchy compute");
      return (u$1(k$1, { children: [u$1(SideBar, { sizeProperty: sizeProperty, availableSizeProperties: availableSizeProperties, setSizeProperty: setSizeProperty, onExcludeChange: setExcludeFilter, onIncludeChange: setIncludeFilter }), u$1(Chart, { root: root, sizeProperty: sizeProperty, selectedNode: selectedNode, setSelectedNode: setSelectedNode })] }));
  };

  function initRange(domain, range) {
    switch (arguments.length) {
      case 0: break;
      case 1: this.range(domain); break;
      default: this.range(range).domain(domain); break;
    }
    return this;
  }

  function initInterpolator(domain, interpolator) {
    switch (arguments.length) {
      case 0: break;
      case 1: {
        if (typeof domain === "function") this.interpolator(domain);
        else this.range(domain);
        break;
      }
      default: {
        this.domain(domain);
        if (typeof interpolator === "function") this.interpolator(interpolator);
        else this.range(interpolator);
        break;
      }
    }
    return this;
  }

  function define(constructor, factory, prototype) {
    constructor.prototype = factory.prototype = prototype;
    prototype.constructor = constructor;
  }

  function extend(parent, definition) {
    var prototype = Object.create(parent.prototype);
    for (var key in definition) prototype[key] = definition[key];
    return prototype;
  }

  function Color() {}

  var darker = 0.7;
  var brighter = 1 / darker;

  var reI = "\\s*([+-]?\\d+)\\s*",
      reN = "\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",
      reP = "\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",
      reHex = /^#([0-9a-f]{3,8})$/,
      reRgbInteger = new RegExp(`^rgb\\(${reI},${reI},${reI}\\)$`),
      reRgbPercent = new RegExp(`^rgb\\(${reP},${reP},${reP}\\)$`),
      reRgbaInteger = new RegExp(`^rgba\\(${reI},${reI},${reI},${reN}\\)$`),
      reRgbaPercent = new RegExp(`^rgba\\(${reP},${reP},${reP},${reN}\\)$`),
      reHslPercent = new RegExp(`^hsl\\(${reN},${reP},${reP}\\)$`),
      reHslaPercent = new RegExp(`^hsla\\(${reN},${reP},${reP},${reN}\\)$`);

  var named = {
    aliceblue: 0xf0f8ff,
    antiquewhite: 0xfaebd7,
    aqua: 0x00ffff,
    aquamarine: 0x7fffd4,
    azure: 0xf0ffff,
    beige: 0xf5f5dc,
    bisque: 0xffe4c4,
    black: 0x000000,
    blanchedalmond: 0xffebcd,
    blue: 0x0000ff,
    blueviolet: 0x8a2be2,
    brown: 0xa52a2a,
    burlywood: 0xdeb887,
    cadetblue: 0x5f9ea0,
    chartreuse: 0x7fff00,
    chocolate: 0xd2691e,
    coral: 0xff7f50,
    cornflowerblue: 0x6495ed,
    cornsilk: 0xfff8dc,
    crimson: 0xdc143c,
    cyan: 0x00ffff,
    darkblue: 0x00008b,
    darkcyan: 0x008b8b,
    darkgoldenrod: 0xb8860b,
    darkgray: 0xa9a9a9,
    darkgreen: 0x006400,
    darkgrey: 0xa9a9a9,
    darkkhaki: 0xbdb76b,
    darkmagenta: 0x8b008b,
    darkolivegreen: 0x556b2f,
    darkorange: 0xff8c00,
    darkorchid: 0x9932cc,
    darkred: 0x8b0000,
    darksalmon: 0xe9967a,
    darkseagreen: 0x8fbc8f,
    darkslateblue: 0x483d8b,
    darkslategray: 0x2f4f4f,
    darkslategrey: 0x2f4f4f,
    darkturquoise: 0x00ced1,
    darkviolet: 0x9400d3,
    deeppink: 0xff1493,
    deepskyblue: 0x00bfff,
    dimgray: 0x696969,
    dimgrey: 0x696969,
    dodgerblue: 0x1e90ff,
    firebrick: 0xb22222,
    floralwhite: 0xfffaf0,
    forestgreen: 0x228b22,
    fuchsia: 0xff00ff,
    gainsboro: 0xdcdcdc,
    ghostwhite: 0xf8f8ff,
    gold: 0xffd700,
    goldenrod: 0xdaa520,
    gray: 0x808080,
    green: 0x008000,
    greenyellow: 0xadff2f,
    grey: 0x808080,
    honeydew: 0xf0fff0,
    hotpink: 0xff69b4,
    indianred: 0xcd5c5c,
    indigo: 0x4b0082,
    ivory: 0xfffff0,
    khaki: 0xf0e68c,
    lavender: 0xe6e6fa,
    lavenderblush: 0xfff0f5,
    lawngreen: 0x7cfc00,
    lemonchiffon: 0xfffacd,
    lightblue: 0xadd8e6,
    lightcoral: 0xf08080,
    lightcyan: 0xe0ffff,
    lightgoldenrodyellow: 0xfafad2,
    lightgray: 0xd3d3d3,
    lightgreen: 0x90ee90,
    lightgrey: 0xd3d3d3,
    lightpink: 0xffb6c1,
    lightsalmon: 0xffa07a,
    lightseagreen: 0x20b2aa,
    lightskyblue: 0x87cefa,
    lightslategray: 0x778899,
    lightslategrey: 0x778899,
    lightsteelblue: 0xb0c4de,
    lightyellow: 0xffffe0,
    lime: 0x00ff00,
    limegreen: 0x32cd32,
    linen: 0xfaf0e6,
    magenta: 0xff00ff,
    maroon: 0x800000,
    mediumaquamarine: 0x66cdaa,
    mediumblue: 0x0000cd,
    mediumorchid: 0xba55d3,
    mediumpurple: 0x9370db,
    mediumseagreen: 0x3cb371,
    mediumslateblue: 0x7b68ee,
    mediumspringgreen: 0x00fa9a,
    mediumturquoise: 0x48d1cc,
    mediumvioletred: 0xc71585,
    midnightblue: 0x191970,
    mintcream: 0xf5fffa,
    mistyrose: 0xffe4e1,
    moccasin: 0xffe4b5,
    navajowhite: 0xffdead,
    navy: 0x000080,
    oldlace: 0xfdf5e6,
    olive: 0x808000,
    olivedrab: 0x6b8e23,
    orange: 0xffa500,
    orangered: 0xff4500,
    orchid: 0xda70d6,
    palegoldenrod: 0xeee8aa,
    palegreen: 0x98fb98,
    paleturquoise: 0xafeeee,
    palevioletred: 0xdb7093,
    papayawhip: 0xffefd5,
    peachpuff: 0xffdab9,
    peru: 0xcd853f,
    pink: 0xffc0cb,
    plum: 0xdda0dd,
    powderblue: 0xb0e0e6,
    purple: 0x800080,
    rebeccapurple: 0x663399,
    red: 0xff0000,
    rosybrown: 0xbc8f8f,
    royalblue: 0x4169e1,
    saddlebrown: 0x8b4513,
    salmon: 0xfa8072,
    sandybrown: 0xf4a460,
    seagreen: 0x2e8b57,
    seashell: 0xfff5ee,
    sienna: 0xa0522d,
    silver: 0xc0c0c0,
    skyblue: 0x87ceeb,
    slateblue: 0x6a5acd,
    slategray: 0x708090,
    slategrey: 0x708090,
    snow: 0xfffafa,
    springgreen: 0x00ff7f,
    steelblue: 0x4682b4,
    tan: 0xd2b48c,
    teal: 0x008080,
    thistle: 0xd8bfd8,
    tomato: 0xff6347,
    turquoise: 0x40e0d0,
    violet: 0xee82ee,
    wheat: 0xf5deb3,
    white: 0xffffff,
    whitesmoke: 0xf5f5f5,
    yellow: 0xffff00,
    yellowgreen: 0x9acd32
  };

  define(Color, color, {
    copy(channels) {
      return Object.assign(new this.constructor, this, channels);
    },
    displayable() {
      return this.rgb().displayable();
    },
    hex: color_formatHex, // Deprecated! Use color.formatHex.
    formatHex: color_formatHex,
    formatHex8: color_formatHex8,
    formatHsl: color_formatHsl,
    formatRgb: color_formatRgb,
    toString: color_formatRgb
  });

  function color_formatHex() {
    return this.rgb().formatHex();
  }

  function color_formatHex8() {
    return this.rgb().formatHex8();
  }

  function color_formatHsl() {
    return hslConvert(this).formatHsl();
  }

  function color_formatRgb() {
    return this.rgb().formatRgb();
  }

  function color(format) {
    var m, l;
    format = (format + "").trim().toLowerCase();
    return (m = reHex.exec(format)) ? (l = m[1].length, m = parseInt(m[1], 16), l === 6 ? rgbn(m) // #ff0000
        : l === 3 ? new Rgb((m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), ((m & 0xf) << 4) | (m & 0xf), 1) // #f00
        : l === 8 ? rgba(m >> 24 & 0xff, m >> 16 & 0xff, m >> 8 & 0xff, (m & 0xff) / 0xff) // #ff000000
        : l === 4 ? rgba((m >> 12 & 0xf) | (m >> 8 & 0xf0), (m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), (((m & 0xf) << 4) | (m & 0xf)) / 0xff) // #f000
        : null) // invalid hex
        : (m = reRgbInteger.exec(format)) ? new Rgb(m[1], m[2], m[3], 1) // rgb(255, 0, 0)
        : (m = reRgbPercent.exec(format)) ? new Rgb(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, 1) // rgb(100%, 0%, 0%)
        : (m = reRgbaInteger.exec(format)) ? rgba(m[1], m[2], m[3], m[4]) // rgba(255, 0, 0, 1)
        : (m = reRgbaPercent.exec(format)) ? rgba(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, m[4]) // rgb(100%, 0%, 0%, 1)
        : (m = reHslPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, 1) // hsl(120, 50%, 50%)
        : (m = reHslaPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, m[4]) // hsla(120, 50%, 50%, 1)
        : named.hasOwnProperty(format) ? rgbn(named[format]) // eslint-disable-line no-prototype-builtins
        : format === "transparent" ? new Rgb(NaN, NaN, NaN, 0)
        : null;
  }

  function rgbn(n) {
    return new Rgb(n >> 16 & 0xff, n >> 8 & 0xff, n & 0xff, 1);
  }

  function rgba(r, g, b, a) {
    if (a <= 0) r = g = b = NaN;
    return new Rgb(r, g, b, a);
  }

  function rgbConvert(o) {
    if (!(o instanceof Color)) o = color(o);
    if (!o) return new Rgb;
    o = o.rgb();
    return new Rgb(o.r, o.g, o.b, o.opacity);
  }

  function rgb$1(r, g, b, opacity) {
    return arguments.length === 1 ? rgbConvert(r) : new Rgb(r, g, b, opacity == null ? 1 : opacity);
  }

  function Rgb(r, g, b, opacity) {
    this.r = +r;
    this.g = +g;
    this.b = +b;
    this.opacity = +opacity;
  }

  define(Rgb, rgb$1, extend(Color, {
    brighter(k) {
      k = k == null ? brighter : Math.pow(brighter, k);
      return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);
    },
    darker(k) {
      k = k == null ? darker : Math.pow(darker, k);
      return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);
    },
    rgb() {
      return this;
    },
    clamp() {
      return new Rgb(clampi(this.r), clampi(this.g), clampi(this.b), clampa(this.opacity));
    },
    displayable() {
      return (-0.5 <= this.r && this.r < 255.5)
          && (-0.5 <= this.g && this.g < 255.5)
          && (-0.5 <= this.b && this.b < 255.5)
          && (0 <= this.opacity && this.opacity <= 1);
    },
    hex: rgb_formatHex, // Deprecated! Use color.formatHex.
    formatHex: rgb_formatHex,
    formatHex8: rgb_formatHex8,
    formatRgb: rgb_formatRgb,
    toString: rgb_formatRgb
  }));

  function rgb_formatHex() {
    return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}`;
  }

  function rgb_formatHex8() {
    return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}${hex((isNaN(this.opacity) ? 1 : this.opacity) * 255)}`;
  }

  function rgb_formatRgb() {
    const a = clampa(this.opacity);
    return `${a === 1 ? "rgb(" : "rgba("}${clampi(this.r)}, ${clampi(this.g)}, ${clampi(this.b)}${a === 1 ? ")" : `, ${a})`}`;
  }

  function clampa(opacity) {
    return isNaN(opacity) ? 1 : Math.max(0, Math.min(1, opacity));
  }

  function clampi(value) {
    return Math.max(0, Math.min(255, Math.round(value) || 0));
  }

  function hex(value) {
    value = clampi(value);
    return (value < 16 ? "0" : "") + value.toString(16);
  }

  function hsla(h, s, l, a) {
    if (a <= 0) h = s = l = NaN;
    else if (l <= 0 || l >= 1) h = s = NaN;
    else if (s <= 0) h = NaN;
    return new Hsl(h, s, l, a);
  }

  function hslConvert(o) {
    if (o instanceof Hsl) return new Hsl(o.h, o.s, o.l, o.opacity);
    if (!(o instanceof Color)) o = color(o);
    if (!o) return new Hsl;
    if (o instanceof Hsl) return o;
    o = o.rgb();
    var r = o.r / 255,
        g = o.g / 255,
        b = o.b / 255,
        min = Math.min(r, g, b),
        max = Math.max(r, g, b),
        h = NaN,
        s = max - min,
        l = (max + min) / 2;
    if (s) {
      if (r === max) h = (g - b) / s + (g < b) * 6;
      else if (g === max) h = (b - r) / s + 2;
      else h = (r - g) / s + 4;
      s /= l < 0.5 ? max + min : 2 - max - min;
      h *= 60;
    } else {
      s = l > 0 && l < 1 ? 0 : h;
    }
    return new Hsl(h, s, l, o.opacity);
  }

  function hsl(h, s, l, opacity) {
    return arguments.length === 1 ? hslConvert(h) : new Hsl(h, s, l, opacity == null ? 1 : opacity);
  }

  function Hsl(h, s, l, opacity) {
    this.h = +h;
    this.s = +s;
    this.l = +l;
    this.opacity = +opacity;
  }

  define(Hsl, hsl, extend(Color, {
    brighter(k) {
      k = k == null ? brighter : Math.pow(brighter, k);
      return new Hsl(this.h, this.s, this.l * k, this.opacity);
    },
    darker(k) {
      k = k == null ? darker : Math.pow(darker, k);
      return new Hsl(this.h, this.s, this.l * k, this.opacity);
    },
    rgb() {
      var h = this.h % 360 + (this.h < 0) * 360,
          s = isNaN(h) || isNaN(this.s) ? 0 : this.s,
          l = this.l,
          m2 = l + (l < 0.5 ? l : 1 - l) * s,
          m1 = 2 * l - m2;
      return new Rgb(
        hsl2rgb(h >= 240 ? h - 240 : h + 120, m1, m2),
        hsl2rgb(h, m1, m2),
        hsl2rgb(h < 120 ? h + 240 : h - 120, m1, m2),
        this.opacity
      );
    },
    clamp() {
      return new Hsl(clamph(this.h), clampt(this.s), clampt(this.l), clampa(this.opacity));
    },
    displayable() {
      return (0 <= this.s && this.s <= 1 || isNaN(this.s))
          && (0 <= this.l && this.l <= 1)
          && (0 <= this.opacity && this.opacity <= 1);
    },
    formatHsl() {
      const a = clampa(this.opacity);
      return `${a === 1 ? "hsl(" : "hsla("}${clamph(this.h)}, ${clampt(this.s) * 100}%, ${clampt(this.l) * 100}%${a === 1 ? ")" : `, ${a})`}`;
    }
  }));

  function clamph(value) {
    value = (value || 0) % 360;
    return value < 0 ? value + 360 : value;
  }

  function clampt(value) {
    return Math.max(0, Math.min(1, value || 0));
  }

  /* From FvD 13.37, CSS Color Module Level 3 */
  function hsl2rgb(h, m1, m2) {
    return (h < 60 ? m1 + (m2 - m1) * h / 60
        : h < 180 ? m2
        : h < 240 ? m1 + (m2 - m1) * (240 - h) / 60
        : m1) * 255;
  }

  var constant = x => () => x;

  function linear$1(a, d) {
    return function(t) {
      return a + t * d;
    };
  }

  function exponential(a, b, y) {
    return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function(t) {
      return Math.pow(a + t * b, y);
    };
  }

  function gamma(y) {
    return (y = +y) === 1 ? nogamma : function(a, b) {
      return b - a ? exponential(a, b, y) : constant(isNaN(a) ? b : a);
    };
  }

  function nogamma(a, b) {
    var d = b - a;
    return d ? linear$1(a, d) : constant(isNaN(a) ? b : a);
  }

  var rgb = (function rgbGamma(y) {
    var color = gamma(y);

    function rgb(start, end) {
      var r = color((start = rgb$1(start)).r, (end = rgb$1(end)).r),
          g = color(start.g, end.g),
          b = color(start.b, end.b),
          opacity = nogamma(start.opacity, end.opacity);
      return function(t) {
        start.r = r(t);
        start.g = g(t);
        start.b = b(t);
        start.opacity = opacity(t);
        return start + "";
      };
    }

    rgb.gamma = rgbGamma;

    return rgb;
  })(1);

  function numberArray(a, b) {
    if (!b) b = [];
    var n = a ? Math.min(b.length, a.length) : 0,
        c = b.slice(),
        i;
    return function(t) {
      for (i = 0; i < n; ++i) c[i] = a[i] * (1 - t) + b[i] * t;
      return c;
    };
  }

  function isNumberArray(x) {
    return ArrayBuffer.isView(x) && !(x instanceof DataView);
  }

  function genericArray(a, b) {
    var nb = b ? b.length : 0,
        na = a ? Math.min(nb, a.length) : 0,
        x = new Array(na),
        c = new Array(nb),
        i;

    for (i = 0; i < na; ++i) x[i] = interpolate(a[i], b[i]);
    for (; i < nb; ++i) c[i] = b[i];

    return function(t) {
      for (i = 0; i < na; ++i) c[i] = x[i](t);
      return c;
    };
  }

  function date(a, b) {
    var d = new Date;
    return a = +a, b = +b, function(t) {
      return d.setTime(a * (1 - t) + b * t), d;
    };
  }

  function interpolateNumber(a, b) {
    return a = +a, b = +b, function(t) {
      return a * (1 - t) + b * t;
    };
  }

  function object(a, b) {
    var i = {},
        c = {},
        k;

    if (a === null || typeof a !== "object") a = {};
    if (b === null || typeof b !== "object") b = {};

    for (k in b) {
      if (k in a) {
        i[k] = interpolate(a[k], b[k]);
      } else {
        c[k] = b[k];
      }
    }

    return function(t) {
      for (k in i) c[k] = i[k](t);
      return c;
    };
  }

  var reA = /[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,
      reB = new RegExp(reA.source, "g");

  function zero(b) {
    return function() {
      return b;
    };
  }

  function one(b) {
    return function(t) {
      return b(t) + "";
    };
  }

  function string(a, b) {
    var bi = reA.lastIndex = reB.lastIndex = 0, // scan index for next number in b
        am, // current match in a
        bm, // current match in b
        bs, // string preceding current number in b, if any
        i = -1, // index in s
        s = [], // string constants and placeholders
        q = []; // number interpolators

    // Coerce inputs to strings.
    a = a + "", b = b + "";

    // Interpolate pairs of numbers in a & b.
    while ((am = reA.exec(a))
        && (bm = reB.exec(b))) {
      if ((bs = bm.index) > bi) { // a string precedes the next number in b
        bs = b.slice(bi, bs);
        if (s[i]) s[i] += bs; // coalesce with previous string
        else s[++i] = bs;
      }
      if ((am = am[0]) === (bm = bm[0])) { // numbers in a & b match
        if (s[i]) s[i] += bm; // coalesce with previous string
        else s[++i] = bm;
      } else { // interpolate non-matching numbers
        s[++i] = null;
        q.push({i: i, x: interpolateNumber(am, bm)});
      }
      bi = reB.lastIndex;
    }

    // Add remains of b.
    if (bi < b.length) {
      bs = b.slice(bi);
      if (s[i]) s[i] += bs; // coalesce with previous string
      else s[++i] = bs;
    }

    // Special optimization for only a single match.
    // Otherwise, interpolate each of the numbers and rejoin the string.
    return s.length < 2 ? (q[0]
        ? one(q[0].x)
        : zero(b))
        : (b = q.length, function(t) {
            for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);
            return s.join("");
          });
  }

  function interpolate(a, b) {
    var t = typeof b, c;
    return b == null || t === "boolean" ? constant(b)
        : (t === "number" ? interpolateNumber
        : t === "string" ? ((c = color(b)) ? (b = c, rgb) : string)
        : b instanceof color ? rgb
        : b instanceof Date ? date
        : isNumberArray(b) ? numberArray
        : Array.isArray(b) ? genericArray
        : typeof b.valueOf !== "function" && typeof b.toString !== "function" || isNaN(b) ? object
        : interpolateNumber)(a, b);
  }

  function interpolateRound(a, b) {
    return a = +a, b = +b, function(t) {
      return Math.round(a * (1 - t) + b * t);
    };
  }

  function constants(x) {
    return function() {
      return x;
    };
  }

  function number(x) {
    return +x;
  }

  var unit = [0, 1];

  function identity$1(x) {
    return x;
  }

  function normalize(a, b) {
    return (b -= (a = +a))
        ? function(x) { return (x - a) / b; }
        : constants(isNaN(b) ? NaN : 0.5);
  }

  function clamper(a, b) {
    var t;
    if (a > b) t = a, a = b, b = t;
    return function(x) { return Math.max(a, Math.min(b, x)); };
  }

  // normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].
  // interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].
  function bimap(domain, range, interpolate) {
    var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];
    if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);
    else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);
    return function(x) { return r0(d0(x)); };
  }

  function polymap(domain, range, interpolate) {
    var j = Math.min(domain.length, range.length) - 1,
        d = new Array(j),
        r = new Array(j),
        i = -1;

    // Reverse descending domains.
    if (domain[j] < domain[0]) {
      domain = domain.slice().reverse();
      range = range.slice().reverse();
    }

    while (++i < j) {
      d[i] = normalize(domain[i], domain[i + 1]);
      r[i] = interpolate(range[i], range[i + 1]);
    }

    return function(x) {
      var i = bisectRight(domain, x, 1, j) - 1;
      return r[i](d[i](x));
    };
  }

  function copy$1(source, target) {
    return target
        .domain(source.domain())
        .range(source.range())
        .interpolate(source.interpolate())
        .clamp(source.clamp())
        .unknown(source.unknown());
  }

  function transformer$1() {
    var domain = unit,
        range = unit,
        interpolate$1 = interpolate,
        transform,
        untransform,
        unknown,
        clamp = identity$1,
        piecewise,
        output,
        input;

    function rescale() {
      var n = Math.min(domain.length, range.length);
      if (clamp !== identity$1) clamp = clamper(domain[0], domain[n - 1]);
      piecewise = n > 2 ? polymap : bimap;
      output = input = null;
      return scale;
    }

    function scale(x) {
      return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate$1)))(transform(clamp(x)));
    }

    scale.invert = function(y) {
      return clamp(untransform((input || (input = piecewise(range, domain.map(transform), interpolateNumber)))(y)));
    };

    scale.domain = function(_) {
      return arguments.length ? (domain = Array.from(_, number), rescale()) : domain.slice();
    };

    scale.range = function(_) {
      return arguments.length ? (range = Array.from(_), rescale()) : range.slice();
    };

    scale.rangeRound = function(_) {
      return range = Array.from(_), interpolate$1 = interpolateRound, rescale();
    };

    scale.clamp = function(_) {
      return arguments.length ? (clamp = _ ? true : identity$1, rescale()) : clamp !== identity$1;
    };

    scale.interpolate = function(_) {
      return arguments.length ? (interpolate$1 = _, rescale()) : interpolate$1;
    };

    scale.unknown = function(_) {
      return arguments.length ? (unknown = _, scale) : unknown;
    };

    return function(t, u) {
      transform = t, untransform = u;
      return rescale();
    };
  }

  function continuous() {
    return transformer$1()(identity$1, identity$1);
  }

  function formatDecimal(x) {
    return Math.abs(x = Math.round(x)) >= 1e21
        ? x.toLocaleString("en").replace(/,/g, "")
        : x.toString(10);
  }

  // Computes the decimal coefficient and exponent of the specified number x with
  // significant digits p, where x is positive and p is in [1, 21] or undefined.
  // For example, formatDecimalParts(1.23) returns ["123", 0].
  function formatDecimalParts(x, p) {
    if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf("e")) < 0) return null; // NaN, ±Infinity
    var i, coefficient = x.slice(0, i);

    // The string returned by toExponential either has the form \d\.\d+e[-+]\d+
    // (e.g., 1.2e+3) or the form \de[-+]\d+ (e.g., 1e+3).
    return [
      coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,
      +x.slice(i + 1)
    ];
  }

  function exponent(x) {
    return x = formatDecimalParts(Math.abs(x)), x ? x[1] : NaN;
  }

  function formatGroup(grouping, thousands) {
    return function(value, width) {
      var i = value.length,
          t = [],
          j = 0,
          g = grouping[0],
          length = 0;

      while (i > 0 && g > 0) {
        if (length + g + 1 > width) g = Math.max(1, width - length);
        t.push(value.substring(i -= g, i + g));
        if ((length += g + 1) > width) break;
        g = grouping[j = (j + 1) % grouping.length];
      }

      return t.reverse().join(thousands);
    };
  }

  function formatNumerals(numerals) {
    return function(value) {
      return value.replace(/[0-9]/g, function(i) {
        return numerals[+i];
      });
    };
  }

  // [[fill]align][sign][symbol][0][width][,][.precision][~][type]
  var re = /^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;

  function formatSpecifier(specifier) {
    if (!(match = re.exec(specifier))) throw new Error("invalid format: " + specifier);
    var match;
    return new FormatSpecifier({
      fill: match[1],
      align: match[2],
      sign: match[3],
      symbol: match[4],
      zero: match[5],
      width: match[6],
      comma: match[7],
      precision: match[8] && match[8].slice(1),
      trim: match[9],
      type: match[10]
    });
  }

  formatSpecifier.prototype = FormatSpecifier.prototype; // instanceof

  function FormatSpecifier(specifier) {
    this.fill = specifier.fill === undefined ? " " : specifier.fill + "";
    this.align = specifier.align === undefined ? ">" : specifier.align + "";
    this.sign = specifier.sign === undefined ? "-" : specifier.sign + "";
    this.symbol = specifier.symbol === undefined ? "" : specifier.symbol + "";
    this.zero = !!specifier.zero;
    this.width = specifier.width === undefined ? undefined : +specifier.width;
    this.comma = !!specifier.comma;
    this.precision = specifier.precision === undefined ? undefined : +specifier.precision;
    this.trim = !!specifier.trim;
    this.type = specifier.type === undefined ? "" : specifier.type + "";
  }

  FormatSpecifier.prototype.toString = function() {
    return this.fill
        + this.align
        + this.sign
        + this.symbol
        + (this.zero ? "0" : "")
        + (this.width === undefined ? "" : Math.max(1, this.width | 0))
        + (this.comma ? "," : "")
        + (this.precision === undefined ? "" : "." + Math.max(0, this.precision | 0))
        + (this.trim ? "~" : "")
        + this.type;
  };

  // Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.
  function formatTrim(s) {
    out: for (var n = s.length, i = 1, i0 = -1, i1; i < n; ++i) {
      switch (s[i]) {
        case ".": i0 = i1 = i; break;
        case "0": if (i0 === 0) i0 = i; i1 = i; break;
        default: if (!+s[i]) break out; if (i0 > 0) i0 = 0; break;
      }
    }
    return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;
  }

  var prefixExponent;

  function formatPrefixAuto(x, p) {
    var d = formatDecimalParts(x, p);
    if (!d) return x + "";
    var coefficient = d[0],
        exponent = d[1],
        i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1,
        n = coefficient.length;
    return i === n ? coefficient
        : i > n ? coefficient + new Array(i - n + 1).join("0")
        : i > 0 ? coefficient.slice(0, i) + "." + coefficient.slice(i)
        : "0." + new Array(1 - i).join("0") + formatDecimalParts(x, Math.max(0, p + i - 1))[0]; // less than 1y!
  }

  function formatRounded(x, p) {
    var d = formatDecimalParts(x, p);
    if (!d) return x + "";
    var coefficient = d[0],
        exponent = d[1];
    return exponent < 0 ? "0." + new Array(-exponent).join("0") + coefficient
        : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + "." + coefficient.slice(exponent + 1)
        : coefficient + new Array(exponent - coefficient.length + 2).join("0");
  }

  var formatTypes = {
    "%": (x, p) => (x * 100).toFixed(p),
    "b": (x) => Math.round(x).toString(2),
    "c": (x) => x + "",
    "d": formatDecimal,
    "e": (x, p) => x.toExponential(p),
    "f": (x, p) => x.toFixed(p),
    "g": (x, p) => x.toPrecision(p),
    "o": (x) => Math.round(x).toString(8),
    "p": (x, p) => formatRounded(x * 100, p),
    "r": formatRounded,
    "s": formatPrefixAuto,
    "X": (x) => Math.round(x).toString(16).toUpperCase(),
    "x": (x) => Math.round(x).toString(16)
  };

  function identity(x) {
    return x;
  }

  var map = Array.prototype.map,
      prefixes = ["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];

  function formatLocale(locale) {
    var group = locale.grouping === undefined || locale.thousands === undefined ? identity : formatGroup(map.call(locale.grouping, Number), locale.thousands + ""),
        currencyPrefix = locale.currency === undefined ? "" : locale.currency[0] + "",
        currencySuffix = locale.currency === undefined ? "" : locale.currency[1] + "",
        decimal = locale.decimal === undefined ? "." : locale.decimal + "",
        numerals = locale.numerals === undefined ? identity : formatNumerals(map.call(locale.numerals, String)),
        percent = locale.percent === undefined ? "%" : locale.percent + "",
        minus = locale.minus === undefined ? "−" : locale.minus + "",
        nan = locale.nan === undefined ? "NaN" : locale.nan + "";

    function newFormat(specifier) {
      specifier = formatSpecifier(specifier);

      var fill = specifier.fill,
          align = specifier.align,
          sign = specifier.sign,
          symbol = specifier.symbol,
          zero = specifier.zero,
          width = specifier.width,
          comma = specifier.comma,
          precision = specifier.precision,
          trim = specifier.trim,
          type = specifier.type;

      // The "n" type is an alias for ",g".
      if (type === "n") comma = true, type = "g";

      // The "" type, and any invalid type, is an alias for ".12~g".
      else if (!formatTypes[type]) precision === undefined && (precision = 12), trim = true, type = "g";

      // If zero fill is specified, padding goes after sign and before digits.
      if (zero || (fill === "0" && align === "=")) zero = true, fill = "0", align = "=";

      // Compute the prefix and suffix.
      // For SI-prefix, the suffix is lazily computed.
      var prefix = symbol === "$" ? currencyPrefix : symbol === "#" && /[boxX]/.test(type) ? "0" + type.toLowerCase() : "",
          suffix = symbol === "$" ? currencySuffix : /[%p]/.test(type) ? percent : "";

      // What format function should we use?
      // Is this an integer type?
      // Can this type generate exponential notation?
      var formatType = formatTypes[type],
          maybeSuffix = /[defgprs%]/.test(type);

      // Set the default precision if not specified,
      // or clamp the specified precision to the supported range.
      // For significant precision, it must be in [1, 21].
      // For fixed precision, it must be in [0, 20].
      precision = precision === undefined ? 6
          : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision))
          : Math.max(0, Math.min(20, precision));

      function format(value) {
        var valuePrefix = prefix,
            valueSuffix = suffix,
            i, n, c;

        if (type === "c") {
          valueSuffix = formatType(value) + valueSuffix;
          value = "";
        } else {
          value = +value;

          // Determine the sign. -0 is not less than 0, but 1 / -0 is!
          var valueNegative = value < 0 || 1 / value < 0;

          // Perform the initial formatting.
          value = isNaN(value) ? nan : formatType(Math.abs(value), precision);

          // Trim insignificant zeros.
          if (trim) value = formatTrim(value);

          // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.
          if (valueNegative && +value === 0 && sign !== "+") valueNegative = false;

          // Compute the prefix and suffix.
          valuePrefix = (valueNegative ? (sign === "(" ? sign : minus) : sign === "-" || sign === "(" ? "" : sign) + valuePrefix;
          valueSuffix = (type === "s" ? prefixes[8 + prefixExponent / 3] : "") + valueSuffix + (valueNegative && sign === "(" ? ")" : "");

          // Break the formatted value into the integer “value” part that can be
          // grouped, and fractional or exponential “suffix” part that is not.
          if (maybeSuffix) {
            i = -1, n = value.length;
            while (++i < n) {
              if (c = value.charCodeAt(i), 48 > c || c > 57) {
                valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;
                value = value.slice(0, i);
                break;
              }
            }
          }
        }

        // If the fill character is not "0", grouping is applied before padding.
        if (comma && !zero) value = group(value, Infinity);

        // Compute the padding.
        var length = valuePrefix.length + value.length + valueSuffix.length,
            padding = length < width ? new Array(width - length + 1).join(fill) : "";

        // If the fill character is "0", grouping is applied after padding.
        if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = "";

        // Reconstruct the final output based on the desired alignment.
        switch (align) {
          case "<": value = valuePrefix + value + valueSuffix + padding; break;
          case "=": value = valuePrefix + padding + value + valueSuffix; break;
          case "^": value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length); break;
          default: value = padding + valuePrefix + value + valueSuffix; break;
        }

        return numerals(value);
      }

      format.toString = function() {
        return specifier + "";
      };

      return format;
    }

    function formatPrefix(specifier, value) {
      var f = newFormat((specifier = formatSpecifier(specifier), specifier.type = "f", specifier)),
          e = Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3,
          k = Math.pow(10, -e),
          prefix = prefixes[8 + e / 3];
      return function(value) {
        return f(k * value) + prefix;
      };
    }

    return {
      format: newFormat,
      formatPrefix: formatPrefix
    };
  }

  var locale;
  var format;
  var formatPrefix;

  defaultLocale({
    thousands: ",",
    grouping: [3],
    currency: ["$", ""]
  });

  function defaultLocale(definition) {
    locale = formatLocale(definition);
    format = locale.format;
    formatPrefix = locale.formatPrefix;
    return locale;
  }

  function precisionFixed(step) {
    return Math.max(0, -exponent(Math.abs(step)));
  }

  function precisionPrefix(step, value) {
    return Math.max(0, Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3 - exponent(Math.abs(step)));
  }

  function precisionRound(step, max) {
    step = Math.abs(step), max = Math.abs(max) - step;
    return Math.max(0, exponent(max) - exponent(step)) + 1;
  }

  function tickFormat(start, stop, count, specifier) {
    var step = tickStep(start, stop, count),
        precision;
    specifier = formatSpecifier(specifier == null ? ",f" : specifier);
    switch (specifier.type) {
      case "s": {
        var value = Math.max(Math.abs(start), Math.abs(stop));
        if (specifier.precision == null && !isNaN(precision = precisionPrefix(step, value))) specifier.precision = precision;
        return formatPrefix(specifier, value);
      }
      case "":
      case "e":
      case "g":
      case "p":
      case "r": {
        if (specifier.precision == null && !isNaN(precision = precisionRound(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === "e");
        break;
      }
      case "f":
      case "%": {
        if (specifier.precision == null && !isNaN(precision = precisionFixed(step))) specifier.precision = precision - (specifier.type === "%") * 2;
        break;
      }
    }
    return format(specifier);
  }

  function linearish(scale) {
    var domain = scale.domain;

    scale.ticks = function(count) {
      var d = domain();
      return ticks(d[0], d[d.length - 1], count == null ? 10 : count);
    };

    scale.tickFormat = function(count, specifier) {
      var d = domain();
      return tickFormat(d[0], d[d.length - 1], count == null ? 10 : count, specifier);
    };

    scale.nice = function(count) {
      if (count == null) count = 10;

      var d = domain();
      var i0 = 0;
      var i1 = d.length - 1;
      var start = d[i0];
      var stop = d[i1];
      var prestep;
      var step;
      var maxIter = 10;

      if (stop < start) {
        step = start, start = stop, stop = step;
        step = i0, i0 = i1, i1 = step;
      }
      
      while (maxIter-- > 0) {
        step = tickIncrement(start, stop, count);
        if (step === prestep) {
          d[i0] = start;
          d[i1] = stop;
          return domain(d);
        } else if (step > 0) {
          start = Math.floor(start / step) * step;
          stop = Math.ceil(stop / step) * step;
        } else if (step < 0) {
          start = Math.ceil(start * step) / step;
          stop = Math.floor(stop * step) / step;
        } else {
          break;
        }
        prestep = step;
      }

      return scale;
    };

    return scale;
  }

  function linear() {
    var scale = continuous();

    scale.copy = function() {
      return copy$1(scale, linear());
    };

    initRange.apply(scale, arguments);

    return linearish(scale);
  }

  function transformer() {
    var x0 = 0,
        x1 = 1,
        t0,
        t1,
        k10,
        transform,
        interpolator = identity$1,
        clamp = false,
        unknown;

    function scale(x) {
      return x == null || isNaN(x = +x) ? unknown : interpolator(k10 === 0 ? 0.5 : (x = (transform(x) - t0) * k10, clamp ? Math.max(0, Math.min(1, x)) : x));
    }

    scale.domain = function(_) {
      return arguments.length ? ([x0, x1] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0), scale) : [x0, x1];
    };

    scale.clamp = function(_) {
      return arguments.length ? (clamp = !!_, scale) : clamp;
    };

    scale.interpolator = function(_) {
      return arguments.length ? (interpolator = _, scale) : interpolator;
    };

    function range(interpolate) {
      return function(_) {
        var r0, r1;
        return arguments.length ? ([r0, r1] = _, interpolator = interpolate(r0, r1), scale) : [interpolator(0), interpolator(1)];
      };
    }

    scale.range = range(interpolate);

    scale.rangeRound = range(interpolateRound);

    scale.unknown = function(_) {
      return arguments.length ? (unknown = _, scale) : unknown;
    };

    return function(t) {
      transform = t, t0 = t(x0), t1 = t(x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0);
      return scale;
    };
  }

  function copy(source, target) {
    return target
        .domain(source.domain())
        .interpolator(source.interpolator())
        .clamp(source.clamp())
        .unknown(source.unknown());
  }

  function sequential() {
    var scale = linearish(transformer()(identity$1));

    scale.copy = function() {
      return copy(scale, sequential());
    };

    return initInterpolator.apply(scale, arguments);
  }

  const COLOR_BASE = "#cecece";

  // https://www.w3.org/TR/WCAG20/#relativeluminancedef
  const rc = 0.2126;
  const gc = 0.7152;
  const bc = 0.0722;
  // low-gamma adjust coefficient
  const lowc = 1 / 12.92;
  function adjustGamma(p) {
      return Math.pow((p + 0.055) / 1.055, 2.4);
  }
  function relativeLuminance(o) {
      const rsrgb = o.r / 255;
      const gsrgb = o.g / 255;
      const bsrgb = o.b / 255;
      const r = rsrgb <= 0.03928 ? rsrgb * lowc : adjustGamma(rsrgb);
      const g = gsrgb <= 0.03928 ? gsrgb * lowc : adjustGamma(gsrgb);
      const b = bsrgb <= 0.03928 ? bsrgb * lowc : adjustGamma(bsrgb);
      return r * rc + g * gc + b * bc;
  }
  const createRainbowColor = (root) => {
      const colorParentMap = new Map();
      colorParentMap.set(root, COLOR_BASE);
      if (root.children != null) {
          const colorScale = sequential([0, root.children.length], (n) => hsl(360 * n, 0.3, 0.85));
          root.children.forEach((c, id) => {
              colorParentMap.set(c, colorScale(id).toString());
          });
      }
      const colorMap = new Map();
      const lightScale = linear().domain([0, root.height]).range([0.9, 0.3]);
      const getBackgroundColor = (node) => {
          const parents = node.ancestors();
          const colorStr = parents.length === 1
              ? colorParentMap.get(parents[0])
              : colorParentMap.get(parents[parents.length - 2]);
          const hslColor = hsl(colorStr);
          hslColor.l = lightScale(node.depth);
          return hslColor;
      };
      return (node) => {
          if (!colorMap.has(node)) {
              const backgroundColor = getBackgroundColor(node);
              const l = relativeLuminance(backgroundColor.rgb());
              const fontColor = l > 0.19 ? "#000" : "#fff";
              colorMap.set(node, {
                  backgroundColor: backgroundColor.toString(),
                  fontColor,
              });
          }
          return colorMap.get(node);
      };
  };

  const StaticContext = K({});
  const drawChart = (parentNode, data, width, height) => {
      const availableSizeProperties = getAvailableSizeOptions(data.options);
      console.time("layout create");
      const layout = treemap()
          .size([width, height])
          .paddingOuter(PADDING)
          .paddingTop(TOP_PADDING)
          .paddingInner(PADDING)
          .round(true)
          .tile(treemapResquarify);
      console.timeEnd("layout create");
      console.time("rawHierarchy create");
      const rawHierarchy = hierarchy(data.tree);
      console.timeEnd("rawHierarchy create");
      const nodeSizesCache = new Map();
      const nodeIdsCache = new Map();
      const getModuleSize = (node, sizeKey) => { var _a, _b; return (_b = (_a = nodeSizesCache.get(node)) === null || _a === void 0 ? void 0 : _a[sizeKey]) !== null && _b !== void 0 ? _b : 0; };
      console.time("rawHierarchy eachAfter cache");
      rawHierarchy.eachAfter((node) => {
          var _a;
          const nodeData = node.data;
          nodeIdsCache.set(nodeData, {
              nodeUid: generateUniqueId("node"),
              clipUid: generateUniqueId("clip"),
          });
          const sizes = { renderedLength: 0, gzipLength: 0, brotliLength: 0 };
          if (isModuleTree(nodeData)) {
              for (const sizeKey of availableSizeProperties) {
                  sizes[sizeKey] = nodeData.children.reduce((acc, child) => getModuleSize(child, sizeKey) + acc, 0);
              }
          }
          else {
              for (const sizeKey of availableSizeProperties) {
                  sizes[sizeKey] = (_a = data.nodeParts[nodeData.uid][sizeKey]) !== null && _a !== void 0 ? _a : 0;
              }
          }
          nodeSizesCache.set(nodeData, sizes);
      });
      console.timeEnd("rawHierarchy eachAfter cache");
      const getModuleIds = (node) => nodeIdsCache.get(node);
      console.time("color");
      const getModuleColor = createRainbowColor(rawHierarchy);
      console.timeEnd("color");
      E(u$1(StaticContext.Provider, { value: {
              data,
              availableSizeProperties,
              width,
              height,
              getModuleSize,
              getModuleIds,
              getModuleColor,
              rawHierarchy,
              layout,
          }, children: u$1(Main, {}) }), parentNode);
  };

  exports.StaticContext = StaticContext;
  exports.default = drawChart;

  Object.defineProperty(exports, '__esModule', { value: true });

  return exports;

})({});

  /*-->*/
  </script>
  <script>
    /*<!--*/
    const data = {"version":2,"tree":{"name":"root","children":[{"name":"luminar-ui.cjs.js","children":[{"name":"Users/ahmedabdulla/Luminar/node_modules/.pnpm","children":[{"name":"clsx@2.1.1/node_modules/clsx/dist/clsx.mjs","uid":"faa9c923-1"},{"name":"tailwind-merge@3.3.1/node_modules/tailwind-merge/dist/bundle-mjs.mjs","uid":"faa9c923-3"},{"name":"zustand@4.5.7_@types+react@19.1.8_immer@10.1.1_react@19.1.0/node_modules/zustand/esm","children":[{"uid":"faa9c923-13","name":"vanilla.mjs"},{"uid":"faa9c923-43","name":"index.mjs"},{"uid":"faa9c923-45","name":"middleware.mjs"}]},{"name":"use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store","children":[{"name":"cjs","children":[{"uid":"faa9c923-25","name":"use-sync-external-store-shim.production.js"},{"uid":"faa9c923-29","name":"use-sync-external-store-shim.development.js"},{"name":"use-sync-external-store-shim","children":[{"uid":"faa9c923-33","name":"with-selector.production.js"},{"uid":"faa9c923-37","name":"with-selector.development.js"}]}]},{"name":"shim","children":[{"uid":"faa9c923-31","name":"index.js"},{"uid":"faa9c923-39","name":"with-selector.js"}]}]},{"name":"axios@1.10.0/node_modules/axios","children":[{"name":"lib","children":[{"name":"helpers","children":[{"uid":"faa9c923-47","name":"bind.js"},{"uid":"faa9c923-53","name":"null.js"},{"uid":"faa9c923-55","name":"toFormData.js"},{"uid":"faa9c923-57","name":"AxiosURLSearchParams.js"},{"uid":"faa9c923-59","name":"buildURL.js"},{"uid":"faa9c923-77","name":"toURLEncodedForm.js"},{"uid":"faa9c923-79","name":"formDataToJSON.js"},{"uid":"faa9c923-83","name":"parseHeaders.js"},{"uid":"faa9c923-95","name":"parseProtocol.js"},{"uid":"faa9c923-97","name":"speedometer.js"},{"uid":"faa9c923-99","name":"throttle.js"},{"uid":"faa9c923-101","name":"progressEventReducer.js"},{"uid":"faa9c923-103","name":"isURLSameOrigin.js"},{"uid":"faa9c923-105","name":"cookies.js"},{"uid":"faa9c923-107","name":"isAbsoluteURL.js"},{"uid":"faa9c923-109","name":"combineURLs.js"},{"uid":"faa9c923-115","name":"resolveConfig.js"},{"uid":"faa9c923-119","name":"composeSignals.js"},{"uid":"faa9c923-121","name":"trackStream.js"},{"uid":"faa9c923-131","name":"validator.js"},{"uid":"faa9c923-137","name":"spread.js"},{"uid":"faa9c923-139","name":"isAxiosError.js"},{"uid":"faa9c923-141","name":"HttpStatusCode.js"}]},{"uid":"faa9c923-49","name":"utils.js"},{"name":"core","children":[{"uid":"faa9c923-51","name":"AxiosError.js"},{"uid":"faa9c923-61","name":"InterceptorManager.js"},{"uid":"faa9c923-85","name":"AxiosHeaders.js"},{"uid":"faa9c923-87","name":"transformData.js"},{"uid":"faa9c923-93","name":"settle.js"},{"uid":"faa9c923-111","name":"buildFullPath.js"},{"uid":"faa9c923-113","name":"mergeConfig.js"},{"uid":"faa9c923-127","name":"dispatchRequest.js"},{"uid":"faa9c923-133","name":"Axios.js"}]},{"name":"defaults","children":[{"uid":"faa9c923-63","name":"transitional.js"},{"uid":"faa9c923-81","name":"index.js"}]},{"name":"platform","children":[{"name":"browser","children":[{"name":"classes","children":[{"uid":"faa9c923-65","name":"URLSearchParams.js"},{"uid":"faa9c923-67","name":"FormData.js"},{"uid":"faa9c923-69","name":"Blob.js"}]},{"uid":"faa9c923-71","name":"index.js"}]},{"name":"common/utils.js","uid":"faa9c923-73"},{"uid":"faa9c923-75","name":"index.js"}]},{"name":"cancel","children":[{"uid":"faa9c923-89","name":"isCancel.js"},{"uid":"faa9c923-91","name":"CanceledError.js"},{"uid":"faa9c923-135","name":"CancelToken.js"}]},{"name":"adapters","children":[{"uid":"faa9c923-117","name":"xhr.js"},{"uid":"faa9c923-123","name":"fetch.js"},{"uid":"faa9c923-125","name":"adapters.js"}]},{"name":"env/data.js","uid":"faa9c923-129"},{"uid":"faa9c923-143","name":"axios.js"}]},{"uid":"faa9c923-145","name":"index.js"}]},{"name":"@radix-ui+react-compose-refs@1.1.2_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-compose-refs/dist/index.mjs","uid":"faa9c923-173"},{"name":"@radix-ui+react-slot@1.2.3_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs","uid":"faa9c923-175"},{"name":"class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs","uid":"faa9c923-177"},{"name":"react-hook-form@7.60.0_react@19.1.0/node_modules/react-hook-form/dist/index.esm.mjs","uid":"faa9c923-269"}]},{"name":"src","children":[{"name":"lib","children":[{"uid":"faa9c923-5","name":"utils.ts"},{"uid":"faa9c923-7","name":"prop-validation.ts"},{"uid":"faa9c923-9","name":"component-utilities.ts"},{"uid":"faa9c923-147","name":"toast.ts"},{"name":"api","children":[{"uid":"faa9c923-149","name":"client.ts"},{"name":"endpoints","children":[{"uid":"faa9c923-151","name":"auth.api.ts"},{"uid":"faa9c923-153","name":"training.api.ts"},{"uid":"faa9c923-155","name":"vendor.api.ts"},{"uid":"faa9c923-157","name":"wins.api.ts"},{"uid":"faa9c923-159","name":"users.api.ts"},{"uid":"faa9c923-161","name":"email.api.ts"}]},{"uid":"faa9c923-163","name":"index.ts"}]},{"name":"stores/auth.store.ts","uid":"faa9c923-165"},{"name":"query","children":[{"uid":"faa9c923-169","name":"client.ts"},{"uid":"faa9c923-171","name":"provider.tsx"},{"name":"hooks","children":[{"uid":"faa9c923-335","name":"useUsers.ts"},{"uid":"faa9c923-337","name":"useEmail.ts"},{"uid":"faa9c923-339","name":"useTraining.ts"}]}]},{"uid":"faa9c923-179","name":"glassmorphism.ts"},{"uid":"faa9c923-181","name":"glass-utils.ts"},{"uid":"faa9c923-187","name":"micro-interactions.ts"},{"uid":"faa9c923-217","name":"performance-monitor.tsx"},{"uid":"faa9c923-285","name":"theme-manager.ts"},{"name":"routing","children":[{"uid":"faa9c923-347","name":"types.ts"},{"uid":"faa9c923-349","name":"utils.ts"},{"uid":"faa9c923-351","name":"guards.tsx"},{"uid":"faa9c923-353","name":"router-factory.tsx"},{"uid":"faa9c923-355","name":"hooks.ts"},{"uid":"faa9c923-357","name":"components.tsx"},{"uid":"faa9c923-359","name":"route-guards.ts"},{"uid":"faa9c923-361","name":"navigation-utils.ts"},{"uid":"faa9c923-363","name":"index.ts"}]}]},{"name":"providers","children":[{"uid":"faa9c923-11","name":"theme-provider.tsx"},{"uid":"faa9c923-167","name":"auth-provider.tsx"}]},{"uid":"faa9c923-183","name":"design-system.ts"},{"name":"components","children":[{"name":"ui","children":[{"name":"actions","children":[{"uid":"faa9c923-185","name":"button.tsx"},{"uid":"faa9c923-189","name":"button-advanced.tsx"},{"uid":"faa9c923-193","name":"icon-button.tsx"},{"uid":"faa9c923-195","name":"fab.tsx"},{"uid":"faa9c923-197","name":"dropdown.tsx"},{"uid":"faa9c923-199","name":"command-palette.tsx"},{"uid":"faa9c923-203","name":"command.tsx"},{"uid":"faa9c923-205","name":"stateful-button.tsx"},{"uid":"faa9c923-207","name":"index.ts"}]},{"name":"utilities/icon.tsx","uid":"faa9c923-191"},{"name":"display","children":[{"uid":"faa9c923-209","name":"loading-spinner.tsx"},{"uid":"faa9c923-211","name":"accordion.tsx"},{"uid":"faa9c923-213","name":"avatar.tsx"},{"uid":"faa9c923-215","name":"badge.tsx"},{"uid":"faa9c923-219","name":"card.tsx"},{"uid":"faa9c923-221","name":"carousel.tsx"},{"uid":"faa9c923-223","name":"counter.tsx"},{"uid":"faa9c923-231","name":"data-grid.tsx"},{"uid":"faa9c923-233","name":"data-table.tsx"},{"uid":"faa9c923-235","name":"metric-card.tsx"},{"uid":"faa9c923-237","name":"progress-ring.tsx"},{"uid":"faa9c923-239","name":"skeleton-advanced.tsx"},{"uid":"faa9c923-241","name":"stats.tsx"},{"uid":"faa9c923-243","name":"table.tsx"},{"uid":"faa9c923-245","name":"tag.tsx"},{"uid":"faa9c923-247","name":"text.tsx"},{"uid":"faa9c923-249","name":"timeline.tsx"},{"uid":"faa9c923-251","name":"transfer-list.tsx"},{"uid":"faa9c923-253","name":"tree-view.tsx"},{"uid":"faa9c923-255","name":"progress-bar.tsx"},{"uid":"faa9c923-257","name":"skeleton.tsx"},{"uid":"faa9c923-259","name":"index.ts"}]},{"name":"forms","children":[{"uid":"faa9c923-225","name":"input.tsx"},{"uid":"faa9c923-227","name":"select.tsx"},{"uid":"faa9c923-229","name":"checkbox.tsx"},{"uid":"faa9c923-261","name":"textarea.tsx"},{"uid":"faa9c923-263","name":"radio-group.tsx"},{"uid":"faa9c923-265","name":"switch.tsx"},{"uid":"faa9c923-267","name":"slider.tsx"},{"uid":"faa9c923-271","name":"form.tsx"},{"uid":"faa9c923-273","name":"label.tsx"},{"uid":"faa9c923-275","name":"autocomplete.tsx"},{"uid":"faa9c923-277","name":"combobox.tsx"},{"uid":"faa9c923-279","name":"date-picker.tsx"},{"uid":"faa9c923-281","name":"date-range-picker.tsx"},{"uid":"faa9c923-283","name":"color-picker.tsx"},{"uid":"faa9c923-287","name":"advanced-color-picker.tsx"},{"uid":"faa9c923-289","name":"file-upload.tsx"},{"uid":"faa9c923-291","name":"chip-input.tsx"},{"uid":"faa9c923-293","name":"rating.tsx"},{"uid":"faa9c923-295","name":"index.ts"}]},{"name":"feedback","children":[{"uid":"faa9c923-297","name":"alert-dialog.tsx"},{"uid":"faa9c923-299","name":"alert.tsx"},{"uid":"faa9c923-301","name":"banner.tsx"},{"uid":"faa9c923-303","name":"drawer.tsx"},{"uid":"faa9c923-305","name":"modal-advanced.tsx"},{"uid":"faa9c923-307","name":"popover.tsx"},{"uid":"faa9c923-309","name":"toast.tsx"},{"uid":"faa9c923-311","name":"tooltip.tsx"},{"uid":"faa9c923-313","name":"index.ts"}]},{"name":"email","children":[{"uid":"faa9c923-315","name":"email-icons.tsx"},{"uid":"faa9c923-317","name":"email-compose.tsx"},{"uid":"faa9c923-319","name":"email-list.tsx"},{"uid":"faa9c923-321","name":"index.ts"}]},{"uid":"faa9c923-329","name":"search-bar.tsx"}]},{"name":"integration","children":[{"name":"providers/IntegrationProvider.tsx","uid":"faa9c923-323"},{"name":"widgets/AMNAWidget.tsx","uid":"faa9c923-327"}]}]},{"name":"types","children":[{"uid":"faa9c923-201","name":"component-props.ts"},{"uid":"faa9c923-331","name":"ai.ts"}]},{"name":"hooks","children":[{"uid":"faa9c923-325","name":"useAMNAState.ts"},{"uid":"faa9c923-333","name":"useAuthHooks.ts"},{"name":"api","children":[{"uid":"faa9c923-341","name":"users.hooks.ts"},{"uid":"faa9c923-343","name":"training.hooks.ts"},{"uid":"faa9c923-345","name":"index.tsx"}]}]},{"uid":"faa9c923-365","name":"index.ts"}]},{"uid":"faa9c923-15","name":"\u0000commonjsHelpers.js"},{"name":"\u0000/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store","children":[{"name":"shim","children":[{"uid":"faa9c923-17","name":"with-selector.js?commonjs-module"},{"uid":"faa9c923-21","name":"index.js?commonjs-module"},{"uid":"faa9c923-41","name":"with-selector.js?commonjs-es-import"}]},{"name":"cjs","children":[{"name":"use-sync-external-store-shim","children":[{"uid":"faa9c923-19","name":"with-selector.production.js?commonjs-exports"},{"uid":"faa9c923-35","name":"with-selector.development.js?commonjs-exports"}]},{"uid":"faa9c923-23","name":"use-sync-external-store-shim.production.js?commonjs-exports"},{"uid":"faa9c923-27","name":"use-sync-external-store-shim.development.js?commonjs-exports"}]}]}]}],"isRoot":true},"nodeParts":{"faa9c923-1":{"renderedLength":362,"gzipLength":228,"brotliLength":184,"metaUid":"faa9c923-0"},"faa9c923-3":{"renderedLength":90323,"gzipLength":14804,"brotliLength":12551,"metaUid":"faa9c923-2"},"faa9c923-5":{"renderedLength":60,"gzipLength":75,"brotliLength":64,"metaUid":"faa9c923-4"},"faa9c923-7":{"renderedLength":7380,"gzipLength":1842,"brotliLength":1593,"metaUid":"faa9c923-6"},"faa9c923-9":{"renderedLength":6741,"gzipLength":1976,"brotliLength":1711,"metaUid":"faa9c923-8"},"faa9c923-11":{"renderedLength":3219,"gzipLength":956,"brotliLength":798,"metaUid":"faa9c923-10"},"faa9c923-13":{"renderedLength":1400,"gzipLength":586,"brotliLength":514,"metaUid":"faa9c923-12"},"faa9c923-15":{"renderedLength":140,"gzipLength":142,"brotliLength":102,"metaUid":"faa9c923-14"},"faa9c923-17":{"renderedLength":33,"gzipLength":53,"brotliLength":37,"metaUid":"faa9c923-16"},"faa9c923-19":{"renderedLength":33,"gzipLength":53,"brotliLength":30,"metaUid":"faa9c923-18"},"faa9c923-21":{"renderedLength":25,"gzipLength":45,"brotliLength":29,"metaUid":"faa9c923-20"},"faa9c923-23":{"renderedLength":45,"gzipLength":65,"brotliLength":49,"metaUid":"faa9c923-22"},"faa9c923-25":{"renderedLength":2347,"gzipLength":782,"brotliLength":650,"metaUid":"faa9c923-24"},"faa9c923-27":{"renderedLength":46,"gzipLength":66,"brotliLength":41,"metaUid":"faa9c923-26"},"faa9c923-29":{"renderedLength":3885,"gzipLength":1179,"brotliLength":975,"metaUid":"faa9c923-28"},"faa9c923-31":{"renderedLength":325,"gzipLength":187,"brotliLength":153,"metaUid":"faa9c923-30"},"faa9c923-33":{"renderedLength":2978,"gzipLength":925,"brotliLength":783,"metaUid":"faa9c923-32"},"faa9c923-35":{"renderedLength":34,"gzipLength":54,"brotliLength":31,"metaUid":"faa9c923-34"},"faa9c923-37":{"renderedLength":3838,"gzipLength":1077,"brotliLength":909,"metaUid":"faa9c923-36"},"faa9c923-39":{"renderedLength":365,"gzipLength":179,"brotliLength":141,"metaUid":"faa9c923-38"},"faa9c923-41":{"renderedLength":143,"gzipLength":125,"brotliLength":112,"metaUid":"faa9c923-40"},"faa9c923-43":{"renderedLength":1531,"gzipLength":640,"brotliLength":528,"metaUid":"faa9c923-42"},"faa9c923-45":{"renderedLength":9607,"gzipLength":1920,"brotliLength":1729,"metaUid":"faa9c923-44"},"faa9c923-47":{"renderedLength":103,"gzipLength":99,"brotliLength":74,"metaUid":"faa9c923-46"},"faa9c923-49":{"renderedLength":18331,"gzipLength":5386,"brotliLength":4740,"metaUid":"faa9c923-48"},"faa9c923-51":{"renderedLength":2492,"gzipLength":977,"brotliLength":817,"metaUid":"faa9c923-50"},"faa9c923-53":{"renderedLength":60,"gzipLength":80,"brotliLength":56,"metaUid":"faa9c923-52"},"faa9c923-55":{"renderedLength":5869,"gzipLength":1905,"brotliLength":1684,"metaUid":"faa9c923-54"},"faa9c923-57":{"renderedLength":1350,"gzipLength":628,"brotliLength":513,"metaUid":"faa9c923-56"},"faa9c923-59":{"renderedLength":1527,"gzipLength":669,"brotliLength":594,"metaUid":"faa9c923-58"},"faa9c923-61":{"renderedLength":1483,"gzipLength":595,"brotliLength":488,"metaUid":"faa9c923-60"},"faa9c923-63":{"renderedLength":116,"gzipLength":113,"brotliLength":87,"metaUid":"faa9c923-62"},"faa9c923-65":{"renderedLength":106,"gzipLength":84,"brotliLength":72,"metaUid":"faa9c923-64"},"faa9c923-67":{"renderedLength":69,"gzipLength":73,"brotliLength":55,"metaUid":"faa9c923-66"},"faa9c923-69":{"renderedLength":57,"gzipLength":69,"brotliLength":56,"metaUid":"faa9c923-68"},"faa9c923-71":{"renderedLength":205,"gzipLength":161,"brotliLength":132,"metaUid":"faa9c923-70"},"faa9c923-73":{"renderedLength":1470,"gzipLength":640,"brotliLength":484,"metaUid":"faa9c923-72"},"faa9c923-75":{"renderedLength":49,"gzipLength":58,"brotliLength":53,"metaUid":"faa9c923-74"},"faa9c923-77":{"renderedLength":400,"gzipLength":269,"brotliLength":208,"metaUid":"faa9c923-76"},"faa9c923-79":{"renderedLength":2098,"gzipLength":852,"brotliLength":729,"metaUid":"faa9c923-78"},"faa9c923-81":{"renderedLength":4125,"gzipLength":1438,"brotliLength":1256,"metaUid":"faa9c923-80"},"faa9c923-83":{"renderedLength":1338,"gzipLength":692,"brotliLength":574,"metaUid":"faa9c923-82"},"faa9c923-85":{"renderedLength":7344,"gzipLength":2208,"brotliLength":1963,"metaUid":"faa9c923-84"},"faa9c923-87":{"renderedLength":618,"gzipLength":327,"brotliLength":293,"metaUid":"faa9c923-86"},"faa9c923-89":{"renderedLength":70,"gzipLength":82,"brotliLength":74,"metaUid":"faa9c923-88"},"faa9c923-91":{"renderedLength":580,"gzipLength":318,"brotliLength":270,"metaUid":"faa9c923-90"},"faa9c923-93":{"renderedLength":768,"gzipLength":349,"brotliLength":285,"metaUid":"faa9c923-92"},"faa9c923-95":{"renderedLength":120,"gzipLength":127,"brotliLength":98,"metaUid":"faa9c923-94"},"faa9c923-97":{"renderedLength":1047,"gzipLength":462,"brotliLength":400,"metaUid":"faa9c923-96"},"faa9c923-99":{"renderedLength":837,"gzipLength":367,"brotliLength":323,"metaUid":"faa9c923-98"},"faa9c923-101":{"renderedLength":1101,"gzipLength":468,"brotliLength":427,"metaUid":"faa9c923-100"},"faa9c923-103":{"renderedLength":382,"gzipLength":238,"brotliLength":196,"metaUid":"faa9c923-102"},"faa9c923-105":{"renderedLength":969,"gzipLength":472,"brotliLength":359,"metaUid":"faa9c923-104"},"faa9c923-107":{"renderedLength":530,"gzipLength":354,"brotliLength":253,"metaUid":"faa9c923-106"},"faa9c923-109":{"renderedLength":351,"gzipLength":208,"brotliLength":161,"metaUid":"faa9c923-108"},"faa9c923-111":{"renderedLength":641,"gzipLength":315,"brotliLength":259,"metaUid":"faa9c923-110"},"faa9c923-113":{"renderedLength":3334,"gzipLength":943,"brotliLength":804,"metaUid":"faa9c923-112"},"faa9c923-115":{"renderedLength":1778,"gzipLength":823,"brotliLength":686,"metaUid":"faa9c923-114"},"faa9c923-117":{"renderedLength":6125,"gzipLength":1891,"brotliLength":1641,"metaUid":"faa9c923-116"},"faa9c923-119":{"renderedLength":1208,"gzipLength":497,"brotliLength":446,"metaUid":"faa9c923-118"},"faa9c923-121":{"renderedLength":1654,"gzipLength":623,"brotliLength":570,"metaUid":"faa9c923-120"},"faa9c923-123":{"renderedLength":6207,"gzipLength":2027,"brotliLength":1799,"metaUid":"faa9c923-122"},"faa9c923-125":{"renderedLength":1790,"gzipLength":770,"brotliLength":653,"metaUid":"faa9c923-124"},"faa9c923-127":{"renderedLength":1870,"gzipLength":649,"brotliLength":551,"metaUid":"faa9c923-126"},"faa9c923-129":{"renderedLength":27,"gzipLength":47,"brotliLength":31,"metaUid":"faa9c923-128"},"faa9c923-131":{"renderedLength":2723,"gzipLength":1009,"brotliLength":883,"metaUid":"faa9c923-130"},"faa9c923-133":{"renderedLength":6466,"gzipLength":1982,"brotliLength":1744,"metaUid":"faa9c923-132"},"faa9c923-135":{"renderedLength":2718,"gzipLength":900,"brotliLength":782,"metaUid":"faa9c923-134"},"faa9c923-137":{"renderedLength":535,"gzipLength":301,"brotliLength":242,"metaUid":"faa9c923-136"},"faa9c923-139":{"renderedLength":310,"gzipLength":205,"brotliLength":162,"metaUid":"faa9c923-138"},"faa9c923-141":{"renderedLength":1573,"gzipLength":803,"brotliLength":622,"metaUid":"faa9c923-140"},"faa9c923-143":{"renderedLength":1702,"gzipLength":648,"brotliLength":557,"metaUid":"faa9c923-142"},"faa9c923-145":{"renderedLength":400,"gzipLength":254,"brotliLength":207,"metaUid":"faa9c923-144"},"faa9c923-147":{"renderedLength":612,"gzipLength":263,"brotliLength":220,"metaUid":"faa9c923-146"},"faa9c923-149":{"renderedLength":10608,"gzipLength":2841,"brotliLength":2456,"metaUid":"faa9c923-148"},"faa9c923-151":{"renderedLength":3345,"gzipLength":686,"brotliLength":614,"metaUid":"faa9c923-150"},"faa9c923-153":{"renderedLength":4253,"gzipLength":704,"brotliLength":639,"metaUid":"faa9c923-152"},"faa9c923-155":{"renderedLength":4562,"gzipLength":771,"brotliLength":666,"metaUid":"faa9c923-154"},"faa9c923-157":{"renderedLength":4023,"gzipLength":805,"brotliLength":709,"metaUid":"faa9c923-156"},"faa9c923-159":{"renderedLength":7975,"gzipLength":1166,"brotliLength":1023,"metaUid":"faa9c923-158"},"faa9c923-161":{"renderedLength":5023,"gzipLength":715,"brotliLength":632,"metaUid":"faa9c923-160"},"faa9c923-163":{"renderedLength":133,"gzipLength":103,"brotliLength":83,"metaUid":"faa9c923-162"},"faa9c923-165":{"renderedLength":6786,"gzipLength":1611,"brotliLength":1361,"metaUid":"faa9c923-164"},"faa9c923-167":{"renderedLength":7124,"gzipLength":1681,"brotliLength":1461,"metaUid":"faa9c923-166"},"faa9c923-169":{"renderedLength":10373,"gzipLength":1965,"brotliLength":1621,"metaUid":"faa9c923-168"},"faa9c923-171":{"renderedLength":338,"gzipLength":222,"brotliLength":179,"metaUid":"faa9c923-170"},"faa9c923-173":{"renderedLength":803,"gzipLength":344,"brotliLength":302,"metaUid":"faa9c923-172"},"faa9c923-175":{"renderedLength":3623,"gzipLength":1059,"brotliLength":950,"metaUid":"faa9c923-174"},"faa9c923-177":{"renderedLength":2365,"gzipLength":627,"brotliLength":558,"metaUid":"faa9c923-176"},"faa9c923-179":{"renderedLength":3844,"gzipLength":1139,"brotliLength":958,"metaUid":"faa9c923-178"},"faa9c923-181":{"renderedLength":1050,"gzipLength":368,"brotliLength":296,"metaUid":"faa9c923-180"},"faa9c923-183":{"renderedLength":5938,"gzipLength":1382,"brotliLength":1198,"metaUid":"faa9c923-182"},"faa9c923-185":{"renderedLength":9037,"gzipLength":2411,"brotliLength":2126,"metaUid":"faa9c923-184"},"faa9c923-187":{"renderedLength":993,"gzipLength":393,"brotliLength":343,"metaUid":"faa9c923-186"},"faa9c923-189":{"renderedLength":7720,"gzipLength":2241,"brotliLength":1958,"metaUid":"faa9c923-188"},"faa9c923-191":{"renderedLength":2937,"gzipLength":799,"brotliLength":730,"metaUid":"faa9c923-190"},"faa9c923-193":{"renderedLength":2059,"gzipLength":804,"brotliLength":691,"metaUid":"faa9c923-192"},"faa9c923-195":{"renderedLength":7187,"gzipLength":2005,"brotliLength":1736,"metaUid":"faa9c923-194"},"faa9c923-197":{"renderedLength":3433,"gzipLength":1182,"brotliLength":1012,"metaUid":"faa9c923-196"},"faa9c923-199":{"renderedLength":13943,"gzipLength":2945,"brotliLength":2565,"metaUid":"faa9c923-198"},"faa9c923-201":{"renderedLength":775,"gzipLength":347,"brotliLength":294,"metaUid":"faa9c923-200"},"faa9c923-203":{"renderedLength":10119,"gzipLength":2577,"brotliLength":2219,"metaUid":"faa9c923-202"},"faa9c923-205":{"renderedLength":5423,"gzipLength":1240,"brotliLength":1071,"metaUid":"faa9c923-204"},"faa9c923-207":{"renderedLength":0,"gzipLength":0,"brotliLength":0,"metaUid":"faa9c923-206"},"faa9c923-209":{"renderedLength":4735,"gzipLength":1152,"brotliLength":1016,"metaUid":"faa9c923-208"},"faa9c923-211":{"renderedLength":3521,"gzipLength":1064,"brotliLength":930,"metaUid":"faa9c923-210"},"faa9c923-213":{"renderedLength":4852,"gzipLength":1380,"brotliLength":1212,"metaUid":"faa9c923-212"},"faa9c923-215":{"renderedLength":3450,"gzipLength":1101,"brotliLength":950,"metaUid":"faa9c923-214"},"faa9c923-217":{"renderedLength":3690,"gzipLength":1183,"brotliLength":1026,"metaUid":"faa9c923-216"},"faa9c923-219":{"renderedLength":4382,"gzipLength":1429,"brotliLength":1243,"metaUid":"faa9c923-218"},"faa9c923-221":{"renderedLength":10750,"gzipLength":2469,"brotliLength":2184,"metaUid":"faa9c923-220"},"faa9c923-223":{"renderedLength":7322,"gzipLength":1698,"brotliLength":1473,"metaUid":"faa9c923-222"},"faa9c923-225":{"renderedLength":6316,"gzipLength":1716,"brotliLength":1474,"metaUid":"faa9c923-224"},"faa9c923-227":{"renderedLength":5795,"gzipLength":1616,"brotliLength":1398,"metaUid":"faa9c923-226"},"faa9c923-229":{"renderedLength":2343,"gzipLength":844,"brotliLength":737,"metaUid":"faa9c923-228"},"faa9c923-231":{"renderedLength":19290,"gzipLength":3830,"brotliLength":3393,"metaUid":"faa9c923-230"},"faa9c923-233":{"renderedLength":16369,"gzipLength":3579,"brotliLength":3162,"metaUid":"faa9c923-232"},"faa9c923-235":{"renderedLength":2520,"gzipLength":733,"brotliLength":639,"metaUid":"faa9c923-234"},"faa9c923-237":{"renderedLength":6336,"gzipLength":1556,"brotliLength":1388,"metaUid":"faa9c923-236"},"faa9c923-239":{"renderedLength":11113,"gzipLength":1840,"brotliLength":1631,"metaUid":"faa9c923-238"},"faa9c923-241":{"renderedLength":6454,"gzipLength":1202,"brotliLength":1055,"metaUid":"faa9c923-240"},"faa9c923-243":{"renderedLength":9720,"gzipLength":2160,"brotliLength":1900,"metaUid":"faa9c923-242"},"faa9c923-245":{"renderedLength":1720,"gzipLength":747,"brotliLength":646,"metaUid":"faa9c923-244"},"faa9c923-247":{"renderedLength":4297,"gzipLength":1209,"brotliLength":1081,"metaUid":"faa9c923-246"},"faa9c923-249":{"renderedLength":6433,"gzipLength":1686,"brotliLength":1477,"metaUid":"faa9c923-248"},"faa9c923-251":{"renderedLength":10496,"gzipLength":2211,"brotliLength":1942,"metaUid":"faa9c923-250"},"faa9c923-253":{"renderedLength":8835,"gzipLength":2197,"brotliLength":1933,"metaUid":"faa9c923-252"},"faa9c923-255":{"renderedLength":3066,"gzipLength":1106,"brotliLength":962,"metaUid":"faa9c923-254"},"faa9c923-257":{"renderedLength":2803,"gzipLength":942,"brotliLength":825,"metaUid":"faa9c923-256"},"faa9c923-259":{"renderedLength":0,"gzipLength":0,"brotliLength":0,"metaUid":"faa9c923-258"},"faa9c923-261":{"renderedLength":3827,"gzipLength":1167,"brotliLength":995,"metaUid":"faa9c923-260"},"faa9c923-263":{"renderedLength":6055,"gzipLength":1419,"brotliLength":1248,"metaUid":"faa9c923-262"},"faa9c923-265":{"renderedLength":3268,"gzipLength":1049,"brotliLength":909,"metaUid":"faa9c923-264"},"faa9c923-267":{"renderedLength":5891,"gzipLength":1480,"brotliLength":1304,"metaUid":"faa9c923-266"},"faa9c923-269":{"renderedLength":1105,"gzipLength":560,"brotliLength":459,"metaUid":"faa9c923-268"},"faa9c923-271":{"renderedLength":7934,"gzipLength":1522,"brotliLength":1329,"metaUid":"faa9c923-270"},"faa9c923-273":{"renderedLength":7292,"gzipLength":1615,"brotliLength":1394,"metaUid":"faa9c923-272"},"faa9c923-275":{"renderedLength":12515,"gzipLength":3015,"brotliLength":2650,"metaUid":"faa9c923-274"},"faa9c923-277":{"renderedLength":10034,"gzipLength":2562,"brotliLength":2219,"metaUid":"faa9c923-276"},"faa9c923-279":{"renderedLength":15292,"gzipLength":3138,"brotliLength":2790,"metaUid":"faa9c923-278"},"faa9c923-281":{"renderedLength":14374,"gzipLength":3085,"brotliLength":2730,"metaUid":"faa9c923-280"},"faa9c923-283":{"renderedLength":13536,"gzipLength":3216,"brotliLength":2833,"metaUid":"faa9c923-282"},"faa9c923-285":{"renderedLength":10934,"gzipLength":2988,"brotliLength":2617,"metaUid":"faa9c923-284"},"faa9c923-287":{"renderedLength":12532,"gzipLength":3352,"brotliLength":2776,"metaUid":"faa9c923-286"},"faa9c923-289":{"renderedLength":11778,"gzipLength":2966,"brotliLength":2604,"metaUid":"faa9c923-288"},"faa9c923-291":{"renderedLength":12052,"gzipLength":3069,"brotliLength":2676,"metaUid":"faa9c923-290"},"faa9c923-293":{"renderedLength":4675,"gzipLength":1277,"brotliLength":1120,"metaUid":"faa9c923-292"},"faa9c923-295":{"renderedLength":0,"gzipLength":0,"brotliLength":0,"metaUid":"faa9c923-294"},"faa9c923-297":{"renderedLength":7292,"gzipLength":1816,"brotliLength":1556,"metaUid":"faa9c923-296"},"faa9c923-299":{"renderedLength":4962,"gzipLength":1263,"brotliLength":1094,"metaUid":"faa9c923-298"},"faa9c923-301":{"renderedLength":1761,"gzipLength":743,"brotliLength":633,"metaUid":"faa9c923-300"},"faa9c923-303":{"renderedLength":5083,"gzipLength":1504,"brotliLength":1340,"metaUid":"faa9c923-302"},"faa9c923-305":{"renderedLength":2828,"gzipLength":1036,"brotliLength":885,"metaUid":"faa9c923-304"},"faa9c923-307":{"renderedLength":9765,"gzipLength":2017,"brotliLength":1747,"metaUid":"faa9c923-306"},"faa9c923-309":{"renderedLength":3568,"gzipLength":1165,"brotliLength":1016,"metaUid":"faa9c923-308"},"faa9c923-311":{"renderedLength":4926,"gzipLength":1351,"brotliLength":1179,"metaUid":"faa9c923-310"},"faa9c923-313":{"renderedLength":0,"gzipLength":0,"brotliLength":0,"metaUid":"faa9c923-312"},"faa9c923-315":{"renderedLength":11748,"gzipLength":2797,"brotliLength":2445,"metaUid":"faa9c923-314"},"faa9c923-317":{"renderedLength":21654,"gzipLength":3340,"brotliLength":2897,"metaUid":"faa9c923-316"},"faa9c923-319":{"renderedLength":20855,"gzipLength":3940,"brotliLength":3498,"metaUid":"faa9c923-318"},"faa9c923-321":{"renderedLength":0,"gzipLength":0,"brotliLength":0,"metaUid":"faa9c923-320"},"faa9c923-323":{"renderedLength":278,"gzipLength":168,"brotliLength":149,"metaUid":"faa9c923-322"},"faa9c923-325":{"renderedLength":3449,"gzipLength":701,"brotliLength":618,"metaUid":"faa9c923-324"},"faa9c923-327":{"renderedLength":7491,"gzipLength":2001,"brotliLength":1723,"metaUid":"faa9c923-326"},"faa9c923-329":{"renderedLength":13551,"gzipLength":3387,"brotliLength":2960,"metaUid":"faa9c923-328"},"faa9c923-331":{"renderedLength":1247,"gzipLength":611,"brotliLength":504,"metaUid":"faa9c923-330"},"faa9c923-333":{"renderedLength":7835,"gzipLength":1559,"brotliLength":1379,"metaUid":"faa9c923-332"},"faa9c923-335":{"renderedLength":12858,"gzipLength":1702,"brotliLength":1504,"metaUid":"faa9c923-334"},"faa9c923-337":{"renderedLength":13714,"gzipLength":1706,"brotliLength":1489,"metaUid":"faa9c923-336"},"faa9c923-339":{"renderedLength":14031,"gzipLength":1785,"brotliLength":1608,"metaUid":"faa9c923-338"},"faa9c923-341":{"renderedLength":6081,"gzipLength":1037,"brotliLength":939,"metaUid":"faa9c923-340"},"faa9c923-343":{"renderedLength":9977,"gzipLength":1439,"brotliLength":1274,"metaUid":"faa9c923-342"},"faa9c923-345":{"renderedLength":1124,"gzipLength":426,"brotliLength":356,"metaUid":"faa9c923-344"},"faa9c923-347":{"renderedLength":231,"gzipLength":192,"brotliLength":147,"metaUid":"faa9c923-346"},"faa9c923-349":{"renderedLength":6827,"gzipLength":2062,"brotliLength":1797,"metaUid":"faa9c923-348"},"faa9c923-351":{"renderedLength":4710,"gzipLength":1133,"brotliLength":977,"metaUid":"faa9c923-350"},"faa9c923-353":{"renderedLength":10871,"gzipLength":2853,"brotliLength":2438,"metaUid":"faa9c923-352"},"faa9c923-355":{"renderedLength":8667,"gzipLength":2109,"brotliLength":1823,"metaUid":"faa9c923-354"},"faa9c923-357":{"renderedLength":24533,"gzipLength":5596,"brotliLength":4925,"metaUid":"faa9c923-356"},"faa9c923-359":{"renderedLength":2570,"gzipLength":750,"brotliLength":671,"metaUid":"faa9c923-358"},"faa9c923-361":{"renderedLength":4861,"gzipLength":1598,"brotliLength":1371,"metaUid":"faa9c923-360"},"faa9c923-363":{"renderedLength":8526,"gzipLength":2416,"brotliLength":1973,"metaUid":"faa9c923-362"},"faa9c923-365":{"renderedLength":0,"gzipLength":0,"brotliLength":0,"metaUid":"faa9c923-364"}},"nodeMetas":{"faa9c923-0":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs","moduleParts":{"luminar-ui.cjs.js":"faa9c923-1"},"imported":[],"importedBy":[{"uid":"faa9c923-4"},{"uid":"faa9c923-8"},{"uid":"faa9c923-176"}]},"faa9c923-2":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/dist/bundle-mjs.mjs","moduleParts":{"luminar-ui.cjs.js":"faa9c923-3"},"imported":[],"importedBy":[{"uid":"faa9c923-4"},{"uid":"faa9c923-8"}]},"faa9c923-4":{"id":"/src/lib/utils.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-5"},"imported":[{"uid":"faa9c923-0"},{"uid":"faa9c923-2"}],"importedBy":[{"uid":"faa9c923-364"},{"uid":"faa9c923-328"},{"uid":"faa9c923-184"},{"uid":"faa9c923-188"},{"uid":"faa9c923-192"},{"uid":"faa9c923-194"},{"uid":"faa9c923-196"},{"uid":"faa9c923-198"},{"uid":"faa9c923-202"},{"uid":"faa9c923-204"},{"uid":"faa9c923-208"},{"uid":"faa9c923-210"},{"uid":"faa9c923-212"},{"uid":"faa9c923-214"},{"uid":"faa9c923-218"},{"uid":"faa9c923-220"},{"uid":"faa9c923-222"},{"uid":"faa9c923-230"},{"uid":"faa9c923-232"},{"uid":"faa9c923-236"},{"uid":"faa9c923-238"},{"uid":"faa9c923-242"},{"uid":"faa9c923-246"},{"uid":"faa9c923-248"},{"uid":"faa9c923-250"},{"uid":"faa9c923-252"},{"uid":"faa9c923-254"},{"uid":"faa9c923-256"},{"uid":"faa9c923-224"},{"uid":"faa9c923-260"},{"uid":"faa9c923-228"},{"uid":"faa9c923-262"},{"uid":"faa9c923-226"},{"uid":"faa9c923-264"},{"uid":"faa9c923-266"},{"uid":"faa9c923-270"},{"uid":"faa9c923-272"},{"uid":"faa9c923-274"},{"uid":"faa9c923-276"},{"uid":"faa9c923-278"},{"uid":"faa9c923-280"},{"uid":"faa9c923-282"},{"uid":"faa9c923-286"},{"uid":"faa9c923-288"},{"uid":"faa9c923-290"},{"uid":"faa9c923-292"},{"uid":"faa9c923-296"},{"uid":"faa9c923-298"},{"uid":"faa9c923-302"},{"uid":"faa9c923-304"},{"uid":"faa9c923-306"},{"uid":"faa9c923-308"},{"uid":"faa9c923-310"},{"uid":"faa9c923-314"},{"uid":"faa9c923-316"},{"uid":"faa9c923-318"},{"uid":"faa9c923-180"},{"uid":"faa9c923-190"},{"uid":"faa9c923-178"}]},"faa9c923-6":{"id":"/src/lib/prop-validation.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-7"},"imported":[{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-8":{"id":"/src/lib/component-utilities.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-9"},"imported":[{"uid":"faa9c923-0"},{"uid":"faa9c923-2"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-10":{"id":"/src/providers/theme-provider.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-11"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-12":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/zustand@4.5.7_@types+react@19.1.8_immer@10.1.1_react@19.1.0/node_modules/zustand/esm/vanilla.mjs","moduleParts":{"luminar-ui.cjs.js":"faa9c923-13"},"imported":[],"importedBy":[{"uid":"faa9c923-42"}]},"faa9c923-14":{"id":"\u0000commonjsHelpers.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-15"},"imported":[],"importedBy":[{"uid":"faa9c923-40"},{"uid":"faa9c923-38"},{"uid":"faa9c923-32"},{"uid":"faa9c923-36"},{"uid":"faa9c923-30"},{"uid":"faa9c923-24"},{"uid":"faa9c923-28"}]},"faa9c923-16":{"id":"\u0000/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/shim/with-selector.js?commonjs-module","moduleParts":{"luminar-ui.cjs.js":"faa9c923-17"},"imported":[],"importedBy":[{"uid":"faa9c923-38"}]},"faa9c923-18":{"id":"\u0000/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.js?commonjs-exports","moduleParts":{"luminar-ui.cjs.js":"faa9c923-19"},"imported":[],"importedBy":[{"uid":"faa9c923-32"}]},"faa9c923-20":{"id":"\u0000/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/shim/index.js?commonjs-module","moduleParts":{"luminar-ui.cjs.js":"faa9c923-21"},"imported":[],"importedBy":[{"uid":"faa9c923-30"}]},"faa9c923-22":{"id":"\u0000/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.js?commonjs-exports","moduleParts":{"luminar-ui.cjs.js":"faa9c923-23"},"imported":[],"importedBy":[{"uid":"faa9c923-24"}]},"faa9c923-24":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-25"},"imported":[{"uid":"faa9c923-14"},{"uid":"faa9c923-22"},{"uid":"faa9c923-385"}],"importedBy":[{"uid":"faa9c923-30"}]},"faa9c923-26":{"id":"\u0000/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js?commonjs-exports","moduleParts":{"luminar-ui.cjs.js":"faa9c923-27"},"imported":[],"importedBy":[{"uid":"faa9c923-28"}]},"faa9c923-28":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-29"},"imported":[{"uid":"faa9c923-14"},{"uid":"faa9c923-26"},{"uid":"faa9c923-385"}],"importedBy":[{"uid":"faa9c923-30"}]},"faa9c923-30":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/shim/index.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-31"},"imported":[{"uid":"faa9c923-14"},{"uid":"faa9c923-20"},{"uid":"faa9c923-24"},{"uid":"faa9c923-28"}],"importedBy":[{"uid":"faa9c923-32"},{"uid":"faa9c923-36"}]},"faa9c923-32":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-33"},"imported":[{"uid":"faa9c923-14"},{"uid":"faa9c923-18"},{"uid":"faa9c923-385"},{"uid":"faa9c923-30"}],"importedBy":[{"uid":"faa9c923-38"}]},"faa9c923-34":{"id":"\u0000/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js?commonjs-exports","moduleParts":{"luminar-ui.cjs.js":"faa9c923-35"},"imported":[],"importedBy":[{"uid":"faa9c923-36"}]},"faa9c923-36":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-37"},"imported":[{"uid":"faa9c923-14"},{"uid":"faa9c923-34"},{"uid":"faa9c923-385"},{"uid":"faa9c923-30"}],"importedBy":[{"uid":"faa9c923-38"}]},"faa9c923-38":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/shim/with-selector.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-39"},"imported":[{"uid":"faa9c923-14"},{"uid":"faa9c923-16"},{"uid":"faa9c923-32"},{"uid":"faa9c923-36"}],"importedBy":[{"uid":"faa9c923-40"}]},"faa9c923-40":{"id":"\u0000/Users/<USER>/Luminar/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.1.0/node_modules/use-sync-external-store/shim/with-selector.js?commonjs-es-import","moduleParts":{"luminar-ui.cjs.js":"faa9c923-41"},"imported":[{"uid":"faa9c923-14"},{"uid":"faa9c923-38"}],"importedBy":[{"uid":"faa9c923-42"}]},"faa9c923-42":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/zustand@4.5.7_@types+react@19.1.8_immer@10.1.1_react@19.1.0/node_modules/zustand/esm/index.mjs","moduleParts":{"luminar-ui.cjs.js":"faa9c923-43"},"imported":[{"uid":"faa9c923-12"},{"uid":"faa9c923-368"},{"uid":"faa9c923-40"}],"importedBy":[{"uid":"faa9c923-164"}]},"faa9c923-44":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/zustand@4.5.7_@types+react@19.1.8_immer@10.1.1_react@19.1.0/node_modules/zustand/esm/middleware.mjs","moduleParts":{"luminar-ui.cjs.js":"faa9c923-45"},"imported":[],"importedBy":[{"uid":"faa9c923-164"}]},"faa9c923-46":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/bind.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-47"},"imported":[],"importedBy":[{"uid":"faa9c923-142"},{"uid":"faa9c923-48"}]},"faa9c923-48":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/utils.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-49"},"imported":[{"uid":"faa9c923-46"}],"importedBy":[{"uid":"faa9c923-142"},{"uid":"faa9c923-132"},{"uid":"faa9c923-112"},{"uid":"faa9c923-80"},{"uid":"faa9c923-78"},{"uid":"faa9c923-90"},{"uid":"faa9c923-54"},{"uid":"faa9c923-50"},{"uid":"faa9c923-138"},{"uid":"faa9c923-84"},{"uid":"faa9c923-124"},{"uid":"faa9c923-58"},{"uid":"faa9c923-60"},{"uid":"faa9c923-76"},{"uid":"faa9c923-82"},{"uid":"faa9c923-116"},{"uid":"faa9c923-122"},{"uid":"faa9c923-86"},{"uid":"faa9c923-100"},{"uid":"faa9c923-114"},{"uid":"faa9c923-118"},{"uid":"faa9c923-104"}]},"faa9c923-50":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/core/AxiosError.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-51"},"imported":[{"uid":"faa9c923-48"}],"importedBy":[{"uid":"faa9c923-142"},{"uid":"faa9c923-80"},{"uid":"faa9c923-90"},{"uid":"faa9c923-54"},{"uid":"faa9c923-124"},{"uid":"faa9c923-130"},{"uid":"faa9c923-116"},{"uid":"faa9c923-122"},{"uid":"faa9c923-92"},{"uid":"faa9c923-118"}]},"faa9c923-52":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/null.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-53"},"imported":[],"importedBy":[{"uid":"faa9c923-54"},{"uid":"faa9c923-124"}]},"faa9c923-54":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/toFormData.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-55"},"imported":[{"uid":"faa9c923-48"},{"uid":"faa9c923-50"},{"uid":"faa9c923-52"}],"importedBy":[{"uid":"faa9c923-142"},{"uid":"faa9c923-80"},{"uid":"faa9c923-76"},{"uid":"faa9c923-56"}]},"faa9c923-56":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/AxiosURLSearchParams.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-57"},"imported":[{"uid":"faa9c923-54"}],"importedBy":[{"uid":"faa9c923-58"},{"uid":"faa9c923-64"}]},"faa9c923-58":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/buildURL.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-59"},"imported":[{"uid":"faa9c923-48"},{"uid":"faa9c923-56"}],"importedBy":[{"uid":"faa9c923-132"},{"uid":"faa9c923-114"}]},"faa9c923-60":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/core/InterceptorManager.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-61"},"imported":[{"uid":"faa9c923-48"}],"importedBy":[{"uid":"faa9c923-132"}]},"faa9c923-62":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/defaults/transitional.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-63"},"imported":[],"importedBy":[{"uid":"faa9c923-80"},{"uid":"faa9c923-116"}]},"faa9c923-64":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/platform/browser/classes/URLSearchParams.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-65"},"imported":[{"uid":"faa9c923-56"}],"importedBy":[{"uid":"faa9c923-70"}]},"faa9c923-66":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/platform/browser/classes/FormData.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-67"},"imported":[],"importedBy":[{"uid":"faa9c923-70"}]},"faa9c923-68":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/platform/browser/classes/Blob.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-69"},"imported":[],"importedBy":[{"uid":"faa9c923-70"}]},"faa9c923-70":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/platform/browser/index.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-71"},"imported":[{"uid":"faa9c923-64"},{"uid":"faa9c923-66"},{"uid":"faa9c923-68"}],"importedBy":[{"uid":"faa9c923-74"}]},"faa9c923-72":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/platform/common/utils.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-73"},"imported":[],"importedBy":[{"uid":"faa9c923-74"}]},"faa9c923-74":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/platform/index.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-75"},"imported":[{"uid":"faa9c923-70"},{"uid":"faa9c923-72"}],"importedBy":[{"uid":"faa9c923-80"},{"uid":"faa9c923-76"},{"uid":"faa9c923-116"},{"uid":"faa9c923-122"},{"uid":"faa9c923-114"},{"uid":"faa9c923-102"},{"uid":"faa9c923-104"}]},"faa9c923-76":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/toURLEncodedForm.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-77"},"imported":[{"uid":"faa9c923-48"},{"uid":"faa9c923-54"},{"uid":"faa9c923-74"}],"importedBy":[{"uid":"faa9c923-80"}]},"faa9c923-78":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/formDataToJSON.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-79"},"imported":[{"uid":"faa9c923-48"}],"importedBy":[{"uid":"faa9c923-142"},{"uid":"faa9c923-80"}]},"faa9c923-80":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/defaults/index.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-81"},"imported":[{"uid":"faa9c923-48"},{"uid":"faa9c923-50"},{"uid":"faa9c923-62"},{"uid":"faa9c923-54"},{"uid":"faa9c923-76"},{"uid":"faa9c923-74"},{"uid":"faa9c923-78"}],"importedBy":[{"uid":"faa9c923-142"},{"uid":"faa9c923-126"},{"uid":"faa9c923-86"}]},"faa9c923-82":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/parseHeaders.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-83"},"imported":[{"uid":"faa9c923-48"}],"importedBy":[{"uid":"faa9c923-84"}]},"faa9c923-84":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/core/AxiosHeaders.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-85"},"imported":[{"uid":"faa9c923-48"},{"uid":"faa9c923-82"}],"importedBy":[{"uid":"faa9c923-142"},{"uid":"faa9c923-132"},{"uid":"faa9c923-112"},{"uid":"faa9c923-126"},{"uid":"faa9c923-116"},{"uid":"faa9c923-122"},{"uid":"faa9c923-86"},{"uid":"faa9c923-114"}]},"faa9c923-86":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/core/transformData.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-87"},"imported":[{"uid":"faa9c923-48"},{"uid":"faa9c923-80"},{"uid":"faa9c923-84"}],"importedBy":[{"uid":"faa9c923-126"}]},"faa9c923-88":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/cancel/isCancel.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-89"},"imported":[],"importedBy":[{"uid":"faa9c923-142"},{"uid":"faa9c923-126"}]},"faa9c923-90":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/cancel/CanceledError.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-91"},"imported":[{"uid":"faa9c923-50"},{"uid":"faa9c923-48"}],"importedBy":[{"uid":"faa9c923-142"},{"uid":"faa9c923-134"},{"uid":"faa9c923-126"},{"uid":"faa9c923-116"},{"uid":"faa9c923-118"}]},"faa9c923-92":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/core/settle.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-93"},"imported":[{"uid":"faa9c923-50"}],"importedBy":[{"uid":"faa9c923-116"},{"uid":"faa9c923-122"}]},"faa9c923-94":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/parseProtocol.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-95"},"imported":[],"importedBy":[{"uid":"faa9c923-116"}]},"faa9c923-96":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/speedometer.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-97"},"imported":[],"importedBy":[{"uid":"faa9c923-100"}]},"faa9c923-98":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/throttle.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-99"},"imported":[],"importedBy":[{"uid":"faa9c923-100"}]},"faa9c923-100":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/progressEventReducer.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-101"},"imported":[{"uid":"faa9c923-96"},{"uid":"faa9c923-98"},{"uid":"faa9c923-48"}],"importedBy":[{"uid":"faa9c923-116"},{"uid":"faa9c923-122"}]},"faa9c923-102":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/isURLSameOrigin.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-103"},"imported":[{"uid":"faa9c923-74"}],"importedBy":[{"uid":"faa9c923-114"}]},"faa9c923-104":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/cookies.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-105"},"imported":[{"uid":"faa9c923-48"},{"uid":"faa9c923-74"}],"importedBy":[{"uid":"faa9c923-114"}]},"faa9c923-106":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/isAbsoluteURL.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-107"},"imported":[],"importedBy":[{"uid":"faa9c923-110"}]},"faa9c923-108":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/combineURLs.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-109"},"imported":[],"importedBy":[{"uid":"faa9c923-110"}]},"faa9c923-110":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/core/buildFullPath.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-111"},"imported":[{"uid":"faa9c923-106"},{"uid":"faa9c923-108"}],"importedBy":[{"uid":"faa9c923-132"},{"uid":"faa9c923-114"}]},"faa9c923-112":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/core/mergeConfig.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-113"},"imported":[{"uid":"faa9c923-48"},{"uid":"faa9c923-84"}],"importedBy":[{"uid":"faa9c923-142"},{"uid":"faa9c923-132"},{"uid":"faa9c923-114"}]},"faa9c923-114":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/resolveConfig.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-115"},"imported":[{"uid":"faa9c923-74"},{"uid":"faa9c923-48"},{"uid":"faa9c923-102"},{"uid":"faa9c923-104"},{"uid":"faa9c923-110"},{"uid":"faa9c923-112"},{"uid":"faa9c923-84"},{"uid":"faa9c923-58"}],"importedBy":[{"uid":"faa9c923-116"},{"uid":"faa9c923-122"}]},"faa9c923-116":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/adapters/xhr.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-117"},"imported":[{"uid":"faa9c923-48"},{"uid":"faa9c923-92"},{"uid":"faa9c923-62"},{"uid":"faa9c923-50"},{"uid":"faa9c923-90"},{"uid":"faa9c923-94"},{"uid":"faa9c923-74"},{"uid":"faa9c923-84"},{"uid":"faa9c923-100"},{"uid":"faa9c923-114"}],"importedBy":[{"uid":"faa9c923-124"}]},"faa9c923-118":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/composeSignals.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-119"},"imported":[{"uid":"faa9c923-90"},{"uid":"faa9c923-50"},{"uid":"faa9c923-48"}],"importedBy":[{"uid":"faa9c923-122"}]},"faa9c923-120":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/trackStream.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-121"},"imported":[],"importedBy":[{"uid":"faa9c923-122"}]},"faa9c923-122":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/adapters/fetch.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-123"},"imported":[{"uid":"faa9c923-74"},{"uid":"faa9c923-48"},{"uid":"faa9c923-50"},{"uid":"faa9c923-118"},{"uid":"faa9c923-120"},{"uid":"faa9c923-84"},{"uid":"faa9c923-100"},{"uid":"faa9c923-114"},{"uid":"faa9c923-92"}],"importedBy":[{"uid":"faa9c923-124"}]},"faa9c923-124":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/adapters/adapters.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-125"},"imported":[{"uid":"faa9c923-48"},{"uid":"faa9c923-52"},{"uid":"faa9c923-116"},{"uid":"faa9c923-122"},{"uid":"faa9c923-50"}],"importedBy":[{"uid":"faa9c923-142"},{"uid":"faa9c923-126"}]},"faa9c923-126":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/core/dispatchRequest.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-127"},"imported":[{"uid":"faa9c923-86"},{"uid":"faa9c923-88"},{"uid":"faa9c923-80"},{"uid":"faa9c923-90"},{"uid":"faa9c923-84"},{"uid":"faa9c923-124"}],"importedBy":[{"uid":"faa9c923-132"}]},"faa9c923-128":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/env/data.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-129"},"imported":[],"importedBy":[{"uid":"faa9c923-142"},{"uid":"faa9c923-130"}]},"faa9c923-130":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/validator.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-131"},"imported":[{"uid":"faa9c923-128"},{"uid":"faa9c923-50"}],"importedBy":[{"uid":"faa9c923-132"}]},"faa9c923-132":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/core/Axios.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-133"},"imported":[{"uid":"faa9c923-48"},{"uid":"faa9c923-58"},{"uid":"faa9c923-60"},{"uid":"faa9c923-126"},{"uid":"faa9c923-112"},{"uid":"faa9c923-110"},{"uid":"faa9c923-130"},{"uid":"faa9c923-84"}],"importedBy":[{"uid":"faa9c923-142"}]},"faa9c923-134":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/cancel/CancelToken.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-135"},"imported":[{"uid":"faa9c923-90"}],"importedBy":[{"uid":"faa9c923-142"}]},"faa9c923-136":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/spread.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-137"},"imported":[],"importedBy":[{"uid":"faa9c923-142"}]},"faa9c923-138":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/isAxiosError.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-139"},"imported":[{"uid":"faa9c923-48"}],"importedBy":[{"uid":"faa9c923-142"}]},"faa9c923-140":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/helpers/HttpStatusCode.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-141"},"imported":[],"importedBy":[{"uid":"faa9c923-142"}]},"faa9c923-142":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/axios.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-143"},"imported":[{"uid":"faa9c923-48"},{"uid":"faa9c923-46"},{"uid":"faa9c923-132"},{"uid":"faa9c923-112"},{"uid":"faa9c923-80"},{"uid":"faa9c923-78"},{"uid":"faa9c923-90"},{"uid":"faa9c923-134"},{"uid":"faa9c923-88"},{"uid":"faa9c923-128"},{"uid":"faa9c923-54"},{"uid":"faa9c923-50"},{"uid":"faa9c923-136"},{"uid":"faa9c923-138"},{"uid":"faa9c923-84"},{"uid":"faa9c923-124"},{"uid":"faa9c923-140"}],"importedBy":[{"uid":"faa9c923-144"}]},"faa9c923-144":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/axios@1.10.0/node_modules/axios/index.js","moduleParts":{"luminar-ui.cjs.js":"faa9c923-145"},"imported":[{"uid":"faa9c923-142"}],"importedBy":[{"uid":"faa9c923-148"}]},"faa9c923-146":{"id":"/src/lib/toast.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-147"},"imported":[],"importedBy":[{"uid":"faa9c923-164"},{"uid":"faa9c923-168"},{"uid":"faa9c923-148"},{"uid":"faa9c923-334"},{"uid":"faa9c923-336"},{"uid":"faa9c923-338"}]},"faa9c923-148":{"id":"/src/lib/api/client.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-149"},"imported":[{"uid":"faa9c923-144"},{"uid":"faa9c923-146"}],"importedBy":[{"uid":"faa9c923-162"},{"uid":"faa9c923-150"},{"uid":"faa9c923-152"},{"uid":"faa9c923-154"},{"uid":"faa9c923-156"},{"uid":"faa9c923-158"},{"uid":"faa9c923-160"}]},"faa9c923-150":{"id":"/src/lib/api/endpoints/auth.api.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-151"},"imported":[{"uid":"faa9c923-148"}],"importedBy":[{"uid":"faa9c923-162"}]},"faa9c923-152":{"id":"/src/lib/api/endpoints/training.api.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-153"},"imported":[{"uid":"faa9c923-148"}],"importedBy":[{"uid":"faa9c923-162"}]},"faa9c923-154":{"id":"/src/lib/api/endpoints/vendor.api.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-155"},"imported":[{"uid":"faa9c923-148"}],"importedBy":[{"uid":"faa9c923-162"}]},"faa9c923-156":{"id":"/src/lib/api/endpoints/wins.api.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-157"},"imported":[{"uid":"faa9c923-148"}],"importedBy":[{"uid":"faa9c923-162"}]},"faa9c923-158":{"id":"/src/lib/api/endpoints/users.api.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-159"},"imported":[{"uid":"faa9c923-148"}],"importedBy":[{"uid":"faa9c923-162"}]},"faa9c923-160":{"id":"/src/lib/api/endpoints/email.api.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-161"},"imported":[{"uid":"faa9c923-148"}],"importedBy":[{"uid":"faa9c923-162"}]},"faa9c923-162":{"id":"/src/lib/api/index.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-163"},"imported":[{"uid":"faa9c923-148"},{"uid":"faa9c923-150"},{"uid":"faa9c923-152"},{"uid":"faa9c923-154"},{"uid":"faa9c923-156"},{"uid":"faa9c923-158"},{"uid":"faa9c923-160"}],"importedBy":[{"uid":"faa9c923-364"},{"uid":"faa9c923-164"},{"uid":"faa9c923-334"},{"uid":"faa9c923-336"},{"uid":"faa9c923-338"}]},"faa9c923-164":{"id":"/src/lib/stores/auth.store.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-165"},"imported":[{"uid":"faa9c923-42"},{"uid":"faa9c923-44"},{"uid":"faa9c923-162"},{"uid":"faa9c923-146"}],"importedBy":[{"uid":"faa9c923-166"}]},"faa9c923-166":{"id":"/src/providers/auth-provider.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-167"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-164"}],"importedBy":[{"uid":"faa9c923-364"},{"uid":"faa9c923-332"}]},"faa9c923-168":{"id":"/src/lib/query/client.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-169"},"imported":[{"uid":"faa9c923-370"},{"uid":"faa9c923-146"}],"importedBy":[{"uid":"faa9c923-170"},{"uid":"faa9c923-367"},{"uid":"faa9c923-379"},{"uid":"faa9c923-334"},{"uid":"faa9c923-336"},{"uid":"faa9c923-338"}]},"faa9c923-170":{"id":"/src/lib/query/provider.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-171"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-370"},{"uid":"faa9c923-371"},{"uid":"faa9c923-168"}],"importedBy":[{"uid":"faa9c923-364"},{"uid":"faa9c923-367"}]},"faa9c923-172":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-compose-refs/dist/index.mjs","moduleParts":{"luminar-ui.cjs.js":"faa9c923-173"},"imported":[{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-174"}]},"faa9c923-174":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs","moduleParts":{"luminar-ui.cjs.js":"faa9c923-175"},"imported":[{"uid":"faa9c923-368"},{"uid":"faa9c923-172"},{"uid":"faa9c923-369"}],"importedBy":[{"uid":"faa9c923-184"}]},"faa9c923-176":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs","moduleParts":{"luminar-ui.cjs.js":"faa9c923-177"},"imported":[{"uid":"faa9c923-0"}],"importedBy":[{"uid":"faa9c923-184"},{"uid":"faa9c923-314"},{"uid":"faa9c923-316"},{"uid":"faa9c923-318"}]},"faa9c923-178":{"id":"/src/lib/glassmorphism.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-179"},"imported":[{"uid":"faa9c923-4"}],"importedBy":[{"uid":"faa9c923-180"}]},"faa9c923-180":{"id":"/src/lib/glass-utils.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-181"},"imported":[{"uid":"faa9c923-4"},{"uid":"faa9c923-178"}],"importedBy":[{"uid":"faa9c923-184"},{"uid":"faa9c923-188"},{"uid":"faa9c923-192"},{"uid":"faa9c923-194"},{"uid":"faa9c923-196"},{"uid":"faa9c923-218"},{"uid":"faa9c923-224"},{"uid":"faa9c923-306"},{"uid":"faa9c923-308"}]},"faa9c923-182":{"id":"/src/design-system.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-183"},"imported":[],"importedBy":[{"uid":"faa9c923-184"},{"uid":"faa9c923-188"},{"uid":"faa9c923-218"},{"uid":"faa9c923-234"},{"uid":"faa9c923-240"},{"uid":"faa9c923-244"},{"uid":"faa9c923-224"},{"uid":"faa9c923-270"},{"uid":"faa9c923-296"},{"uid":"faa9c923-300"}]},"faa9c923-184":{"id":"/src/components/ui/actions/button.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-185"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-174"},{"uid":"faa9c923-176"},{"uid":"faa9c923-375"},{"uid":"faa9c923-374"},{"uid":"faa9c923-4"},{"uid":"faa9c923-180"},{"uid":"faa9c923-182"}],"importedBy":[{"uid":"faa9c923-206"},{"uid":"faa9c923-220"},{"uid":"faa9c923-230"},{"uid":"faa9c923-232"},{"uid":"faa9c923-250"},{"uid":"faa9c923-278"},{"uid":"faa9c923-280"},{"uid":"faa9c923-282"},{"uid":"faa9c923-302"},{"uid":"faa9c923-316"},{"uid":"faa9c923-318"}]},"faa9c923-186":{"id":"/src/lib/micro-interactions.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-187"},"imported":[{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-188"},{"uid":"faa9c923-202"},{"uid":"faa9c923-218"},{"uid":"faa9c923-272"},{"uid":"faa9c923-276"}]},"faa9c923-188":{"id":"/src/components/ui/actions/button-advanced.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-189"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-182"},{"uid":"faa9c923-180"},{"uid":"faa9c923-186"}],"importedBy":[{"uid":"faa9c923-206"},{"uid":"faa9c923-204"},{"uid":"faa9c923-270"},{"uid":"faa9c923-276"},{"uid":"faa9c923-296"}]},"faa9c923-190":{"id":"/src/components/ui/utilities/icon.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-191"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-192"},{"uid":"faa9c923-198"},{"uid":"faa9c923-224"},{"uid":"faa9c923-278"},{"uid":"faa9c923-280"},{"uid":"faa9c923-288"},{"uid":"faa9c923-298"},{"uid":"faa9c923-308"}]},"faa9c923-192":{"id":"/src/components/ui/actions/icon-button.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-193"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-190"},{"uid":"faa9c923-180"}],"importedBy":[{"uid":"faa9c923-206"}]},"faa9c923-194":{"id":"/src/components/ui/actions/fab.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-195"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-374"},{"uid":"faa9c923-180"}],"importedBy":[{"uid":"faa9c923-206"}]},"faa9c923-196":{"id":"/src/components/ui/actions/dropdown.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-197"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-180"}],"importedBy":[{"uid":"faa9c923-206"},{"uid":"faa9c923-232"},{"uid":"faa9c923-242"}]},"faa9c923-198":{"id":"/src/components/ui/actions/command-palette.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-199"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-190"}],"importedBy":[{"uid":"faa9c923-206"}]},"faa9c923-200":{"id":"/src/types/component-props.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-201"},"imported":[],"importedBy":[{"uid":"faa9c923-366"},{"uid":"faa9c923-202"},{"uid":"faa9c923-272"},{"uid":"faa9c923-276"},{"uid":"faa9c923-290"},{"uid":"faa9c923-296"},{"uid":"faa9c923-304"}]},"faa9c923-202":{"id":"/src/components/ui/actions/command.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-203"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-374"},{"uid":"faa9c923-186"},{"uid":"faa9c923-200"}],"importedBy":[{"uid":"faa9c923-206"}]},"faa9c923-204":{"id":"/src/components/ui/actions/stateful-button.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-205"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-375"},{"uid":"faa9c923-374"},{"uid":"faa9c923-4"},{"uid":"faa9c923-188"}],"importedBy":[{"uid":"faa9c923-206"}]},"faa9c923-206":{"id":"/src/components/ui/actions/index.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-207"},"imported":[{"uid":"faa9c923-184"},{"uid":"faa9c923-188"},{"uid":"faa9c923-192"},{"uid":"faa9c923-194"},{"uid":"faa9c923-196"},{"uid":"faa9c923-198"},{"uid":"faa9c923-202"},{"uid":"faa9c923-204"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-208":{"id":"/src/components/ui/display/loading-spinner.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-209"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-210":{"id":"/src/components/ui/display/accordion.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-211"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-212":{"id":"/src/components/ui/display/avatar.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-213"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-214":{"id":"/src/components/ui/display/badge.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-215"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"}],"importedBy":[{"uid":"faa9c923-258"},{"uid":"faa9c923-250"},{"uid":"faa9c923-274"}]},"faa9c923-216":{"id":"/src/lib/performance-monitor.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-217"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-218"}]},"faa9c923-218":{"id":"/src/components/ui/display/card.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-219"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-180"},{"uid":"faa9c923-182"},{"uid":"faa9c923-186"},{"uid":"faa9c923-216"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-220":{"id":"/src/components/ui/display/carousel.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-221"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-184"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-222":{"id":"/src/components/ui/display/counter.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-223"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-224":{"id":"/src/components/ui/forms/input.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-225"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-190"},{"uid":"faa9c923-182"},{"uid":"faa9c923-180"}],"importedBy":[{"uid":"faa9c923-294"},{"uid":"faa9c923-230"},{"uid":"faa9c923-232"},{"uid":"faa9c923-242"},{"uid":"faa9c923-250"},{"uid":"faa9c923-270"},{"uid":"faa9c923-274"},{"uid":"faa9c923-278"},{"uid":"faa9c923-280"},{"uid":"faa9c923-282"}]},"faa9c923-226":{"id":"/src/components/ui/forms/select.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-227"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"}],"importedBy":[{"uid":"faa9c923-294"},{"uid":"faa9c923-230"},{"uid":"faa9c923-270"}]},"faa9c923-228":{"id":"/src/components/ui/forms/checkbox.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-229"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"}],"importedBy":[{"uid":"faa9c923-294"},{"uid":"faa9c923-230"},{"uid":"faa9c923-232"},{"uid":"faa9c923-250"},{"uid":"faa9c923-252"},{"uid":"faa9c923-270"}]},"faa9c923-230":{"id":"/src/components/ui/display/data-grid.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-231"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-184"},{"uid":"faa9c923-224"},{"uid":"faa9c923-226"},{"uid":"faa9c923-228"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-232":{"id":"/src/components/ui/display/data-table.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-233"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-224"},{"uid":"faa9c923-184"},{"uid":"faa9c923-228"},{"uid":"faa9c923-196"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-234":{"id":"/src/components/ui/display/metric-card.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-235"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-182"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-236":{"id":"/src/components/ui/display/progress-ring.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-237"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-238":{"id":"/src/components/ui/display/skeleton-advanced.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-239"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-240":{"id":"/src/components/ui/display/stats.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-241"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-182"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-242":{"id":"/src/components/ui/display/table.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-243"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-224"},{"uid":"faa9c923-196"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-244":{"id":"/src/components/ui/display/tag.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-245"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-182"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-246":{"id":"/src/components/ui/display/text.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-247"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-248":{"id":"/src/components/ui/display/timeline.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-249"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-250":{"id":"/src/components/ui/display/transfer-list.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-251"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-224"},{"uid":"faa9c923-184"},{"uid":"faa9c923-228"},{"uid":"faa9c923-214"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-252":{"id":"/src/components/ui/display/tree-view.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-253"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-228"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-254":{"id":"/src/components/ui/display/progress-bar.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-255"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-256":{"id":"/src/components/ui/display/skeleton.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-257"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-258"}]},"faa9c923-258":{"id":"/src/components/ui/display/index.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-259"},"imported":[{"uid":"faa9c923-208"},{"uid":"faa9c923-210"},{"uid":"faa9c923-212"},{"uid":"faa9c923-214"},{"uid":"faa9c923-218"},{"uid":"faa9c923-220"},{"uid":"faa9c923-222"},{"uid":"faa9c923-230"},{"uid":"faa9c923-232"},{"uid":"faa9c923-234"},{"uid":"faa9c923-236"},{"uid":"faa9c923-238"},{"uid":"faa9c923-240"},{"uid":"faa9c923-242"},{"uid":"faa9c923-244"},{"uid":"faa9c923-246"},{"uid":"faa9c923-248"},{"uid":"faa9c923-250"},{"uid":"faa9c923-252"},{"uid":"faa9c923-254"},{"uid":"faa9c923-256"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-260":{"id":"/src/components/ui/forms/textarea.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-261"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-294"},{"uid":"faa9c923-270"}]},"faa9c923-262":{"id":"/src/components/ui/forms/radio-group.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-263"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-294"}]},"faa9c923-264":{"id":"/src/components/ui/forms/switch.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-265"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-294"},{"uid":"faa9c923-270"}]},"faa9c923-266":{"id":"/src/components/ui/forms/slider.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-267"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-294"}]},"faa9c923-268":{"id":"/Users/<USER>/Luminar/node_modules/.pnpm/react-hook-form@7.60.0_react@19.1.0/node_modules/react-hook-form/dist/index.esm.mjs","moduleParts":{"luminar-ui.cjs.js":"faa9c923-269"},"imported":[{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-270"}]},"faa9c923-270":{"id":"/src/components/ui/forms/form.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-271"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-268"},{"uid":"faa9c923-182"},{"uid":"faa9c923-188"},{"uid":"faa9c923-224"},{"uid":"faa9c923-260"},{"uid":"faa9c923-226"},{"uid":"faa9c923-228"},{"uid":"faa9c923-264"}],"importedBy":[{"uid":"faa9c923-294"}]},"faa9c923-272":{"id":"/src/components/ui/forms/label.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-273"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-186"},{"uid":"faa9c923-200"}],"importedBy":[{"uid":"faa9c923-294"}]},"faa9c923-274":{"id":"/src/components/ui/forms/autocomplete.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-275"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-224"},{"uid":"faa9c923-214"}],"importedBy":[{"uid":"faa9c923-294"}]},"faa9c923-276":{"id":"/src/components/ui/forms/combobox.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-277"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-374"},{"uid":"faa9c923-188"},{"uid":"faa9c923-186"},{"uid":"faa9c923-200"}],"importedBy":[{"uid":"faa9c923-294"}]},"faa9c923-278":{"id":"/src/components/ui/forms/date-picker.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-279"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-184"},{"uid":"faa9c923-224"},{"uid":"faa9c923-190"}],"importedBy":[{"uid":"faa9c923-294"}]},"faa9c923-280":{"id":"/src/components/ui/forms/date-range-picker.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-281"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-184"},{"uid":"faa9c923-224"},{"uid":"faa9c923-190"}],"importedBy":[{"uid":"faa9c923-294"}]},"faa9c923-282":{"id":"/src/components/ui/forms/color-picker.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-283"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-224"},{"uid":"faa9c923-184"}],"importedBy":[{"uid":"faa9c923-294"}]},"faa9c923-284":{"id":"/src/lib/theme-manager.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-285"},"imported":[],"importedBy":[{"uid":"faa9c923-286"}]},"faa9c923-286":{"id":"/src/components/ui/forms/advanced-color-picker.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-287"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-375"},{"uid":"faa9c923-374"},{"uid":"faa9c923-4"},{"uid":"faa9c923-284"}],"importedBy":[{"uid":"faa9c923-294"}]},"faa9c923-288":{"id":"/src/components/ui/forms/file-upload.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-289"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-190"}],"importedBy":[{"uid":"faa9c923-294"}]},"faa9c923-290":{"id":"/src/components/ui/forms/chip-input.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-291"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-374"},{"uid":"faa9c923-200"}],"importedBy":[{"uid":"faa9c923-294"}]},"faa9c923-292":{"id":"/src/components/ui/forms/rating.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-293"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"}],"importedBy":[{"uid":"faa9c923-294"}]},"faa9c923-294":{"id":"/src/components/ui/forms/index.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-295"},"imported":[{"uid":"faa9c923-224"},{"uid":"faa9c923-260"},{"uid":"faa9c923-228"},{"uid":"faa9c923-262"},{"uid":"faa9c923-226"},{"uid":"faa9c923-264"},{"uid":"faa9c923-266"},{"uid":"faa9c923-270"},{"uid":"faa9c923-272"},{"uid":"faa9c923-274"},{"uid":"faa9c923-276"},{"uid":"faa9c923-278"},{"uid":"faa9c923-280"},{"uid":"faa9c923-282"},{"uid":"faa9c923-286"},{"uid":"faa9c923-288"},{"uid":"faa9c923-290"},{"uid":"faa9c923-292"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-296":{"id":"/src/components/ui/feedback/alert-dialog.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-297"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-182"},{"uid":"faa9c923-188"},{"uid":"faa9c923-200"}],"importedBy":[{"uid":"faa9c923-312"}]},"faa9c923-298":{"id":"/src/components/ui/feedback/alert.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-299"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-190"}],"importedBy":[{"uid":"faa9c923-312"}]},"faa9c923-300":{"id":"/src/components/ui/feedback/banner.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-301"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-182"}],"importedBy":[{"uid":"faa9c923-312"}]},"faa9c923-302":{"id":"/src/components/ui/feedback/drawer.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-303"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-184"},{"uid":"faa9c923-380"}],"importedBy":[{"uid":"faa9c923-312"}]},"faa9c923-304":{"id":"/src/components/ui/feedback/modal-advanced.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-305"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-200"}],"importedBy":[{"uid":"faa9c923-312"}]},"faa9c923-306":{"id":"/src/components/ui/feedback/popover.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-307"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-380"},{"uid":"faa9c923-180"}],"importedBy":[{"uid":"faa9c923-312"}]},"faa9c923-308":{"id":"/src/components/ui/feedback/toast.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-309"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-190"},{"uid":"faa9c923-180"}],"importedBy":[{"uid":"faa9c923-312"}]},"faa9c923-310":{"id":"/src/components/ui/feedback/tooltip.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-311"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"},{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-312"}]},"faa9c923-312":{"id":"/src/components/ui/feedback/index.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-313"},"imported":[{"uid":"faa9c923-296"},{"uid":"faa9c923-298"},{"uid":"faa9c923-300"},{"uid":"faa9c923-302"},{"uid":"faa9c923-304"},{"uid":"faa9c923-306"},{"uid":"faa9c923-308"},{"uid":"faa9c923-310"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-314":{"id":"/src/components/ui/email/email-icons.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-315"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-375"},{"uid":"faa9c923-176"},{"uid":"faa9c923-374"},{"uid":"faa9c923-4"}],"importedBy":[{"uid":"faa9c923-320"},{"uid":"faa9c923-316"},{"uid":"faa9c923-318"}]},"faa9c923-316":{"id":"/src/components/ui/email/email-compose.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-317"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-375"},{"uid":"faa9c923-176"},{"uid":"faa9c923-374"},{"uid":"faa9c923-4"},{"uid":"faa9c923-184"},{"uid":"faa9c923-314"}],"importedBy":[{"uid":"faa9c923-320"}]},"faa9c923-318":{"id":"/src/components/ui/email/email-list.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-319"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-375"},{"uid":"faa9c923-176"},{"uid":"faa9c923-374"},{"uid":"faa9c923-4"},{"uid":"faa9c923-184"},{"uid":"faa9c923-314"}],"importedBy":[{"uid":"faa9c923-320"}]},"faa9c923-320":{"id":"/src/components/ui/email/index.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-321"},"imported":[{"uid":"faa9c923-314"},{"uid":"faa9c923-316"},{"uid":"faa9c923-318"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-322":{"id":"/src/components/integration/providers/IntegrationProvider.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-323"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-381"},{"uid":"faa9c923-382"}],"importedBy":[{"uid":"faa9c923-326"}]},"faa9c923-324":{"id":"/src/hooks/useAMNAState.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-325"},"imported":[{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-326"}]},"faa9c923-326":{"id":"/src/components/integration/widgets/AMNAWidget.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-327"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-372"},{"uid":"faa9c923-373"},{"uid":"faa9c923-322"},{"uid":"faa9c923-324"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-328":{"id":"/src/components/ui/search-bar.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-329"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-374"},{"uid":"faa9c923-375"},{"uid":"faa9c923-4"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-330":{"id":"/src/types/ai.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-331"},"imported":[],"importedBy":[{"uid":"faa9c923-366"}]},"faa9c923-332":{"id":"/src/hooks/useAuthHooks.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-333"},"imported":[{"uid":"faa9c923-368"},{"uid":"faa9c923-166"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-334":{"id":"/src/lib/query/hooks/useUsers.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-335"},"imported":[{"uid":"faa9c923-370"},{"uid":"faa9c923-162"},{"uid":"faa9c923-168"},{"uid":"faa9c923-146"}],"importedBy":[{"uid":"faa9c923-379"}]},"faa9c923-336":{"id":"/src/lib/query/hooks/useEmail.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-337"},"imported":[{"uid":"faa9c923-370"},{"uid":"faa9c923-162"},{"uid":"faa9c923-168"},{"uid":"faa9c923-146"}],"importedBy":[{"uid":"faa9c923-379"}]},"faa9c923-338":{"id":"/src/lib/query/hooks/useTraining.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-339"},"imported":[{"uid":"faa9c923-370"},{"uid":"faa9c923-162"},{"uid":"faa9c923-168"},{"uid":"faa9c923-146"}],"importedBy":[{"uid":"faa9c923-379"}]},"faa9c923-340":{"id":"/src/hooks/api/users.hooks.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-341"},"imported":[{"uid":"faa9c923-370"}],"importedBy":[{"uid":"faa9c923-344"}]},"faa9c923-342":{"id":"/src/hooks/api/training.hooks.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-343"},"imported":[{"uid":"faa9c923-370"},{"uid":"faa9c923-383"}],"importedBy":[{"uid":"faa9c923-344"}]},"faa9c923-344":{"id":"/src/hooks/api/index.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-345"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-340"},{"uid":"faa9c923-342"},{"uid":"faa9c923-368"},{"uid":"faa9c923-370"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-346":{"id":"/src/lib/routing/types.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-347"},"imported":[],"importedBy":[{"uid":"faa9c923-362"}]},"faa9c923-348":{"id":"/src/lib/routing/utils.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-349"},"imported":[],"importedBy":[{"uid":"faa9c923-362"},{"uid":"faa9c923-354"},{"uid":"faa9c923-356"}]},"faa9c923-350":{"id":"/src/lib/routing/guards.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-351"},"imported":[{"uid":"faa9c923-369"}],"importedBy":[{"uid":"faa9c923-362"},{"uid":"faa9c923-352"}]},"faa9c923-352":{"id":"/src/lib/routing/router-factory.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-353"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-384"},{"uid":"faa9c923-350"}],"importedBy":[{"uid":"faa9c923-362"}]},"faa9c923-354":{"id":"/src/lib/routing/hooks.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-355"},"imported":[{"uid":"faa9c923-384"},{"uid":"faa9c923-368"},{"uid":"faa9c923-348"}],"importedBy":[{"uid":"faa9c923-362"},{"uid":"faa9c923-356"}]},"faa9c923-356":{"id":"/src/lib/routing/components.tsx","moduleParts":{"luminar-ui.cjs.js":"faa9c923-357"},"imported":[{"uid":"faa9c923-369"},{"uid":"faa9c923-368"},{"uid":"faa9c923-384"},{"uid":"faa9c923-354"},{"uid":"faa9c923-348"}],"importedBy":[{"uid":"faa9c923-362"}]},"faa9c923-358":{"id":"/src/lib/routing/route-guards.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-359"},"imported":[{"uid":"faa9c923-384"}],"importedBy":[{"uid":"faa9c923-362"}]},"faa9c923-360":{"id":"/src/lib/routing/navigation-utils.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-361"},"imported":[{"uid":"faa9c923-384"}],"importedBy":[{"uid":"faa9c923-362"}]},"faa9c923-362":{"id":"/src/lib/routing/index.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-363"},"imported":[{"uid":"faa9c923-346"},{"uid":"faa9c923-348"},{"uid":"faa9c923-350"},{"uid":"faa9c923-352"},{"uid":"faa9c923-354"},{"uid":"faa9c923-356"},{"uid":"faa9c923-358"},{"uid":"faa9c923-360"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-364":{"id":"/src/index.ts","moduleParts":{"luminar-ui.cjs.js":"faa9c923-365"},"imported":[{"uid":"faa9c923-4"},{"uid":"faa9c923-6"},{"uid":"faa9c923-8"},{"uid":"faa9c923-10"},{"uid":"faa9c923-166"},{"uid":"faa9c923-170"},{"uid":"faa9c923-206"},{"uid":"faa9c923-258"},{"uid":"faa9c923-294"},{"uid":"faa9c923-312"},{"uid":"faa9c923-320"},{"uid":"faa9c923-326"},{"uid":"faa9c923-328"},{"uid":"faa9c923-366"},{"uid":"faa9c923-332"},{"uid":"faa9c923-162"},{"uid":"faa9c923-367"},{"uid":"faa9c923-344"},{"uid":"faa9c923-362"}],"importedBy":[],"isEntry":true},"faa9c923-366":{"id":"/src/types/index.ts","moduleParts":{},"imported":[{"uid":"faa9c923-200"},{"uid":"faa9c923-376"},{"uid":"faa9c923-377"},{"uid":"faa9c923-330"},{"uid":"faa9c923-378"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-367":{"id":"/src/lib/query/index.ts","moduleParts":{},"imported":[{"uid":"faa9c923-170"},{"uid":"faa9c923-379"},{"uid":"faa9c923-168"},{"uid":"faa9c923-370"}],"importedBy":[{"uid":"faa9c923-364"}]},"faa9c923-368":{"id":"react","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-6"},{"uid":"faa9c923-10"},{"uid":"faa9c923-166"},{"uid":"faa9c923-326"},{"uid":"faa9c923-328"},{"uid":"faa9c923-332"},{"uid":"faa9c923-344"},{"uid":"faa9c923-184"},{"uid":"faa9c923-188"},{"uid":"faa9c923-192"},{"uid":"faa9c923-194"},{"uid":"faa9c923-196"},{"uid":"faa9c923-198"},{"uid":"faa9c923-202"},{"uid":"faa9c923-204"},{"uid":"faa9c923-208"},{"uid":"faa9c923-210"},{"uid":"faa9c923-212"},{"uid":"faa9c923-214"},{"uid":"faa9c923-218"},{"uid":"faa9c923-220"},{"uid":"faa9c923-222"},{"uid":"faa9c923-230"},{"uid":"faa9c923-232"},{"uid":"faa9c923-236"},{"uid":"faa9c923-238"},{"uid":"faa9c923-240"},{"uid":"faa9c923-242"},{"uid":"faa9c923-246"},{"uid":"faa9c923-248"},{"uid":"faa9c923-250"},{"uid":"faa9c923-252"},{"uid":"faa9c923-254"},{"uid":"faa9c923-256"},{"uid":"faa9c923-224"},{"uid":"faa9c923-260"},{"uid":"faa9c923-228"},{"uid":"faa9c923-262"},{"uid":"faa9c923-226"},{"uid":"faa9c923-264"},{"uid":"faa9c923-266"},{"uid":"faa9c923-270"},{"uid":"faa9c923-272"},{"uid":"faa9c923-274"},{"uid":"faa9c923-276"},{"uid":"faa9c923-278"},{"uid":"faa9c923-280"},{"uid":"faa9c923-282"},{"uid":"faa9c923-286"},{"uid":"faa9c923-288"},{"uid":"faa9c923-290"},{"uid":"faa9c923-292"},{"uid":"faa9c923-296"},{"uid":"faa9c923-298"},{"uid":"faa9c923-302"},{"uid":"faa9c923-304"},{"uid":"faa9c923-306"},{"uid":"faa9c923-308"},{"uid":"faa9c923-310"},{"uid":"faa9c923-314"},{"uid":"faa9c923-316"},{"uid":"faa9c923-318"},{"uid":"faa9c923-322"},{"uid":"faa9c923-324"},{"uid":"faa9c923-354"},{"uid":"faa9c923-356"},{"uid":"faa9c923-42"},{"uid":"faa9c923-174"},{"uid":"faa9c923-186"},{"uid":"faa9c923-190"},{"uid":"faa9c923-216"},{"uid":"faa9c923-268"},{"uid":"faa9c923-172"},{"uid":"faa9c923-385"}],"isExternal":true},"faa9c923-369":{"id":"react/jsx-runtime","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-10"},{"uid":"faa9c923-166"},{"uid":"faa9c923-170"},{"uid":"faa9c923-326"},{"uid":"faa9c923-328"},{"uid":"faa9c923-344"},{"uid":"faa9c923-184"},{"uid":"faa9c923-188"},{"uid":"faa9c923-192"},{"uid":"faa9c923-194"},{"uid":"faa9c923-196"},{"uid":"faa9c923-198"},{"uid":"faa9c923-202"},{"uid":"faa9c923-204"},{"uid":"faa9c923-208"},{"uid":"faa9c923-210"},{"uid":"faa9c923-212"},{"uid":"faa9c923-214"},{"uid":"faa9c923-218"},{"uid":"faa9c923-220"},{"uid":"faa9c923-222"},{"uid":"faa9c923-230"},{"uid":"faa9c923-232"},{"uid":"faa9c923-234"},{"uid":"faa9c923-236"},{"uid":"faa9c923-238"},{"uid":"faa9c923-240"},{"uid":"faa9c923-242"},{"uid":"faa9c923-244"},{"uid":"faa9c923-246"},{"uid":"faa9c923-248"},{"uid":"faa9c923-250"},{"uid":"faa9c923-252"},{"uid":"faa9c923-254"},{"uid":"faa9c923-256"},{"uid":"faa9c923-224"},{"uid":"faa9c923-260"},{"uid":"faa9c923-228"},{"uid":"faa9c923-262"},{"uid":"faa9c923-226"},{"uid":"faa9c923-264"},{"uid":"faa9c923-266"},{"uid":"faa9c923-270"},{"uid":"faa9c923-272"},{"uid":"faa9c923-274"},{"uid":"faa9c923-276"},{"uid":"faa9c923-278"},{"uid":"faa9c923-280"},{"uid":"faa9c923-282"},{"uid":"faa9c923-286"},{"uid":"faa9c923-288"},{"uid":"faa9c923-290"},{"uid":"faa9c923-292"},{"uid":"faa9c923-296"},{"uid":"faa9c923-298"},{"uid":"faa9c923-300"},{"uid":"faa9c923-302"},{"uid":"faa9c923-304"},{"uid":"faa9c923-306"},{"uid":"faa9c923-308"},{"uid":"faa9c923-310"},{"uid":"faa9c923-314"},{"uid":"faa9c923-316"},{"uid":"faa9c923-318"},{"uid":"faa9c923-322"},{"uid":"faa9c923-350"},{"uid":"faa9c923-352"},{"uid":"faa9c923-356"},{"uid":"faa9c923-174"},{"uid":"faa9c923-190"},{"uid":"faa9c923-216"}],"isExternal":true},"faa9c923-370":{"id":"@tanstack/react-query","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-170"},{"uid":"faa9c923-367"},{"uid":"faa9c923-344"},{"uid":"faa9c923-168"},{"uid":"faa9c923-340"},{"uid":"faa9c923-342"},{"uid":"faa9c923-334"},{"uid":"faa9c923-336"},{"uid":"faa9c923-338"}],"isExternal":true},"faa9c923-371":{"id":"@tanstack/react-query-devtools","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-170"}],"isExternal":true},"faa9c923-372":{"id":"@mui/material","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-326"}],"isExternal":true},"faa9c923-373":{"id":"@mui/icons-material","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-326"}],"isExternal":true},"faa9c923-374":{"id":"lucide-react","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-328"},{"uid":"faa9c923-184"},{"uid":"faa9c923-194"},{"uid":"faa9c923-198"},{"uid":"faa9c923-202"},{"uid":"faa9c923-204"},{"uid":"faa9c923-210"},{"uid":"faa9c923-214"},{"uid":"faa9c923-220"},{"uid":"faa9c923-222"},{"uid":"faa9c923-242"},{"uid":"faa9c923-248"},{"uid":"faa9c923-250"},{"uid":"faa9c923-252"},{"uid":"faa9c923-228"},{"uid":"faa9c923-226"},{"uid":"faa9c923-274"},{"uid":"faa9c923-276"},{"uid":"faa9c923-278"},{"uid":"faa9c923-280"},{"uid":"faa9c923-282"},{"uid":"faa9c923-286"},{"uid":"faa9c923-288"},{"uid":"faa9c923-290"},{"uid":"faa9c923-292"},{"uid":"faa9c923-296"},{"uid":"faa9c923-298"},{"uid":"faa9c923-302"},{"uid":"faa9c923-304"},{"uid":"faa9c923-308"},{"uid":"faa9c923-314"},{"uid":"faa9c923-316"},{"uid":"faa9c923-318"}],"isExternal":true},"faa9c923-375":{"id":"framer-motion","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-328"},{"uid":"faa9c923-184"},{"uid":"faa9c923-188"},{"uid":"faa9c923-192"},{"uid":"faa9c923-194"},{"uid":"faa9c923-196"},{"uid":"faa9c923-198"},{"uid":"faa9c923-202"},{"uid":"faa9c923-204"},{"uid":"faa9c923-208"},{"uid":"faa9c923-210"},{"uid":"faa9c923-212"},{"uid":"faa9c923-214"},{"uid":"faa9c923-218"},{"uid":"faa9c923-220"},{"uid":"faa9c923-222"},{"uid":"faa9c923-230"},{"uid":"faa9c923-232"},{"uid":"faa9c923-236"},{"uid":"faa9c923-238"},{"uid":"faa9c923-242"},{"uid":"faa9c923-246"},{"uid":"faa9c923-248"},{"uid":"faa9c923-250"},{"uid":"faa9c923-252"},{"uid":"faa9c923-254"},{"uid":"faa9c923-256"},{"uid":"faa9c923-224"},{"uid":"faa9c923-260"},{"uid":"faa9c923-228"},{"uid":"faa9c923-262"},{"uid":"faa9c923-226"},{"uid":"faa9c923-264"},{"uid":"faa9c923-266"},{"uid":"faa9c923-270"},{"uid":"faa9c923-272"},{"uid":"faa9c923-274"},{"uid":"faa9c923-276"},{"uid":"faa9c923-278"},{"uid":"faa9c923-280"},{"uid":"faa9c923-282"},{"uid":"faa9c923-286"},{"uid":"faa9c923-288"},{"uid":"faa9c923-290"},{"uid":"faa9c923-292"},{"uid":"faa9c923-296"},{"uid":"faa9c923-298"},{"uid":"faa9c923-302"},{"uid":"faa9c923-304"},{"uid":"faa9c923-306"},{"uid":"faa9c923-308"},{"uid":"faa9c923-310"},{"uid":"faa9c923-314"},{"uid":"faa9c923-316"},{"uid":"faa9c923-318"},{"uid":"faa9c923-190"}],"isExternal":true},"faa9c923-376":{"id":"/src/types/component-api.ts","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-366"}]},"faa9c923-377":{"id":"/src/types/component-documentation.ts","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-366"}]},"faa9c923-378":{"id":"/src/types/research.ts","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-366"}]},"faa9c923-379":{"id":"/src/lib/query/hooks/index.ts","moduleParts":{},"imported":[{"uid":"faa9c923-334"},{"uid":"faa9c923-336"},{"uid":"faa9c923-338"},{"uid":"faa9c923-168"}],"importedBy":[{"uid":"faa9c923-367"}]},"faa9c923-380":{"id":"react-dom","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-302"},{"uid":"faa9c923-306"}],"isExternal":true},"faa9c923-381":{"id":"/src/services/integration/integrationClient.ts","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-322"}]},"faa9c923-382":{"id":"/src/services/integration/websocketClient.ts","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-322"}]},"faa9c923-383":{"id":"@luminar/shared-core","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-342"}],"isExternal":true},"faa9c923-384":{"id":"@tanstack/react-router","moduleParts":{},"imported":[],"importedBy":[{"uid":"faa9c923-352"},{"uid":"faa9c923-354"},{"uid":"faa9c923-356"},{"uid":"faa9c923-358"},{"uid":"faa9c923-360"}],"isExternal":true},"faa9c923-385":{"id":"\u0000react?commonjs-external","moduleParts":{},"imported":[{"uid":"faa9c923-368"}],"importedBy":[{"uid":"faa9c923-32"},{"uid":"faa9c923-36"},{"uid":"faa9c923-24"},{"uid":"faa9c923-28"}]}},"env":{"rollup":"4.41.1"},"options":{"gzip":true,"brotli":true,"sourcemap":false}};

    const run = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      const chartNode = document.querySelector("main");
      drawChart.default(chartNode, data, width, height);
    };

    window.addEventListener('resize', run);

    document.addEventListener('DOMContentLoaded', run);
    /*-->*/
  </script>
</body>
</html>

