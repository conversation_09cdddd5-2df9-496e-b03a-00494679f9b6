// Async testing utilities
export const waitForCondition = async (
  condition: () => boolean,
  options = { timeout: 5000, interval: 100 }
) => {
  const { timeout, interval } = options
  const startTime = Date.now()
  
  while (!condition()) {
    if (Date.now() - startTime > timeout) {
      throw new Error(`Condition not met within ${timeout}ms`)
    }
    await new Promise(resolve => setTimeout(resolve, interval))
  }
}

export const waitForPromise = async <T>(
  promise: Promise<T>,
  timeout = 5000
): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(() => reject(new Error(`Promise timeout after ${timeout}ms`)), timeout)
    ),
  ])
}

// Advanced async utilities
export const waitForMultiple = async (
  conditions: Array<() => boolean>,
  options = { timeout: 5000, interval: 100 }
) => {
  await Promise.all(
    conditions.map(condition => waitForCondition(condition, options))
  )
}

export const waitForAny = async (
  conditions: Array<() => boolean>,
  options = { timeout: 5000, interval: 100 }
) => {
  const { timeout, interval } = options
  const startTime = Date.now()
  
  while (true) {
    const metIndex = conditions.findIndex(condition => condition())
    if (metIndex !== -1) {
      return metIndex
    }
    
    if (Date.now() - startTime > timeout) {
      throw new Error(`No condition met within ${timeout}ms`)
    }
    
    await new Promise(resolve => setTimeout(resolve, interval))
  }
}

// Debounce/throttle testing
export const waitForDebounce = async (ms = 300) => {
  await new Promise(resolve => setTimeout(resolve, ms))
}

export const waitForThrottle = async (ms = 100) => {
  await new Promise(resolve => setTimeout(resolve, ms))
}

// Animation frame utilities
export const waitForAnimationFrame = () => {
  return new Promise(resolve => requestAnimationFrame(resolve))
}

export const waitForAnimationFrames = async (count: number) => {
  for (let i = 0; i < count; i++) {
    await waitForAnimationFrame()
  }
}

// Microtask utilities
export const flushMicrotasks = async () => {
  await new Promise(resolve => process.nextTick(resolve))
}

export const waitForMicrotasks = async () => {
  await Promise.resolve()
  await Promise.resolve()
}

// Event loop utilities
export const waitForEventLoop = async (cycles = 1) => {
  for (let i = 0; i < cycles; i++) {
    await new Promise(resolve => setImmediate(resolve))
  }
}

// Polling utilities
export const poll = async <T>(
  fn: () => T | Promise<T>,
  validator: (result: T) => boolean,
  options = { timeout: 5000, interval: 100 }
): Promise<T> => {
  const { timeout, interval } = options
  const startTime = Date.now()
  
  while (true) {
    const result = await fn()
    if (validator(result)) {
      return result
    }
    
    if (Date.now() - startTime > timeout) {
      throw new Error(`Polling timeout after ${timeout}ms`)
    }
    
    await new Promise(resolve => setTimeout(resolve, interval))
  }
}

// Timeout utilities
export const withTimeout = async <T>(
  promise: Promise<T>,
  ms: number,
  errorMessage?: string
): Promise<T> => {
  const timeout = new Promise<never>((_, reject) => {
    setTimeout(() => {
      reject(new Error(errorMessage || `Timeout after ${ms}ms`))
    }, ms)
  })
  
  return Promise.race([promise, timeout])
}

// Retry with exponential backoff
export const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  options = {
    maxAttempts: 3,
    initialDelay: 100,
    maxDelay: 5000,
    factor: 2,
  }
): Promise<T> => {
  let delay = options.initialDelay
  let lastError: Error | null = null
  
  for (let attempt = 0; attempt < options.maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      
      if (attempt < options.maxAttempts - 1) {
        await new Promise(resolve => setTimeout(resolve, delay))
        delay = Math.min(delay * options.factor, options.maxDelay)
      }
    }
  }
  
  throw lastError
}