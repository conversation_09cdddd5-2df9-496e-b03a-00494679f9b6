import type { Plugin } from 'vite';
import type { RuntimeConfig } from './types';

/**
 * Vite plugin for runtime configuration support
 */
export function runtimeConfigPlugin(): Plugin {
  return {
    name: 'luminar:runtime-config',
    transformIndexHtml(html) {
      return html.replace(
        '</head>',
        `<script>
          window.__LUMINAR_CONFIG__ = {
            remotes: {},
            featureFlags: {},
            environment: 'development'
          };
        </script>
        </head>`
      );
    },
    configureServer(server) {
      server.middlewares.use('/api/config', async (_req, res) => {
        const config: RuntimeConfig = {
          remotes: {},
          featureFlags: {},
          environment: process.env.NODE_ENV as any || 'development',
        };
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify(config));
      });
    },
  };
}

/**
 * Vite plugin for health check endpoints
 */
export function healthCheckPlugin(appName: string): Plugin {
  return {
    name: 'luminar:health-check',
    configureServer(server) {
      server.middlewares.use(`/api/apps/${appName}/health`, (_req, res) => {
        const health = {
          status: 'healthy',
          app: appName,
          timestamp: new Date().toISOString(),
          version: process.env.npm_package_version || '0.0.0',
          uptime: process.uptime(),
        };
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify(health));
      });
    },
  };
}

/**
 * Vite plugin for Module Federation analytics
 */
export function analyticsPlugin(): Plugin {
  const events: any[] = [];

  return {
    name: 'luminar:mf-analytics',
    transformIndexHtml(html) {
      return html.replace(
        '</body>',
        `<script>
          // Track module loading
          if (window.performance && window.performance.getEntriesByType) {
            window.addEventListener('load', () => {
              const resources = window.performance.getEntriesByType('resource');
              const remoteModules = resources.filter(r => 
                r.name.includes('remoteEntry.js') || 
                r.name.includes('_app.') ||
                r.name.includes('remote_')
              );
              
              remoteModules.forEach(module => {
                console.log('Remote module loaded:', {
                  name: module.name,
                  duration: module.duration,
                  size: module.transferSize,
                  cached: module.transferSize === 0
                });
              });
            });
          }
        </script>
        </body>`
      );
    },
    configureServer(server) {
      server.middlewares.use('/api/analytics/events', (req, res) => {
        if (req.method === 'POST') {
          let body = '';
          req.on('data', chunk => body += chunk);
          req.on('end', () => {
            try {
              const event = JSON.parse(body);
              events.push({ ...event, timestamp: new Date().toISOString() });
              res.statusCode = 204;
              res.end();
            } catch (error) {
              res.statusCode = 400;
              res.end('Invalid JSON');
            }
          });
        } else if (req.method === 'GET') {
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify(events));
        }
      });
    },
  };
}

/**
 * Combined Luminar Module Federation plugins
 */
export function luminarModuleFederationPlugins(appName: string): Plugin[] {
  return [
    runtimeConfigPlugin(),
    healthCheckPlugin(appName),
    analyticsPlugin(),
  ];
}