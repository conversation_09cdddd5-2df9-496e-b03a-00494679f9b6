import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { RuntimeConfig, ConfigUpdate, FeatureFlag } from './types';
import { fetchConfig, mergeConfigs } from './utils';

interface ConfigState {
  config: RuntimeConfig | null;
  isLoading: boolean;
  error: Error | null;
  lastUpdated: number | null;
  updateHistory: ConfigUpdate[];
  
  // Actions
  loadConfig: () => Promise<void>;
  updateConfig: (update: ConfigUpdate) => void;
  setFeatureFlag: (key: string, flag: Partial<FeatureFlag>) => void;
  isFeatureEnabled: (key: string) => boolean;
  getFeatureVariant: (key: string, variant: string) => any;
  refreshConfig: () => Promise<void>;
  clearError: () => void;
}

export const useConfigStore = create<ConfigState>()(
  devtools(
    persist(
      (set, get) => ({
        config: null,
        isLoading: false,
        error: null,
        lastUpdated: null,
        updateHistory: [],

        loadConfig: async () => {
          set({ isLoading: true, error: null });
          try {
            const config = await fetchConfig();
            set({
              config,
              isLoading: false,
              lastUpdated: Date.now(),
            });
          } catch (error) {
            set({
              error: error as Error,
              isLoading: false,
            });
          }
        },

        updateConfig: (update) => {
          const currentConfig = get().config;
          if (!currentConfig) return;

          const newConfig = update.type === 'full'
            ? update.data as RuntimeConfig
            : mergeConfigs(currentConfig, update.data);

          set((state) => ({
            config: newConfig,
            lastUpdated: Date.now(),
            updateHistory: [...state.updateHistory.slice(-9), update],
          }));
        },

        setFeatureFlag: (key, flag) => {
          const currentConfig = get().config;
          if (!currentConfig) return;

          set(() => ({
            config: {
              ...currentConfig,
              features: {
                ...currentConfig.features,
                [key]: {
                  ...currentConfig.features[key],
                  ...flag,
                },
              },
            },
            lastUpdated: Date.now(),
          }));
        },

        isFeatureEnabled: (key) => {
          const config = get().config;
          if (!config) return false;
          
          const flag = config.features[key];
          if (!flag) return false;
          
          // Check basic enabled state
          if (!flag.enabled) return false;
          
          // Check rollout percentage
          if (flag.rolloutPercentage !== undefined) {
            const random = Math.random() * 100;
            if (random > flag.rolloutPercentage) return false;
          }
          
          // Check conditions
          if (flag.conditions && flag.conditions.length > 0) {
            // TODO: Implement condition evaluation
          }
          
          return true;
        },

        getFeatureVariant: (key, variant) => {
          const config = get().config;
          if (!config) return null;
          
          const flag = config.features[key];
          if (!flag || !flag.enabled || !flag.variants) return null;
          
          return flag.variants[variant];
        },

        refreshConfig: async () => {
          await get().loadConfig();
        },

        clearError: () => {
          set({ error: null });
        },
      }),
      {
        name: 'luminar-config',
        partialize: (state) => ({ config: state.config }),
      }
    )
  )
);