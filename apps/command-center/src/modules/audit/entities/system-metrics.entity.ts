import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

@Entity('system_metrics')
export class SystemMetrics {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100 })
  @Index()
  metricName: string;

  @Column({ type: 'varchar', length: 50 })
  @Index()
  metricType: string; // 'gauge', 'counter', 'histogram', 'summary'

  @Column({ type: 'decimal', precision: 15, scale: 6 })
  value: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  unit?: string;

  @Column({ type: 'jsonb', default: {} })
  labels: Record<string, string>;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Index()
  source?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Index()
  component?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Index()
  environment?: string;

  @CreateDateColumn()
  @Index()
  timestamp: Date;

  @Column({ type: 'decimal', precision: 15, scale: 6, nullable: true })
  previousValue?: number;

  @Column({ type: 'decimal', precision: 15, scale: 6, nullable: true })
  deltaValue?: number;

  @Column({ type: 'decimal', precision: 15, scale: 6, nullable: true })
  percentChange?: number;

  @Column({
    type: 'enum',
    enum: ['normal', 'warning', 'critical'],
    default: 'normal',
  })
  @Index()
  alertLevel: 'normal' | 'warning' | 'critical';

  @Column({ type: 'jsonb', nullable: true })
  thresholds?: {
    warning?: number;
    critical?: number;
    min?: number;
    max?: number;
  };

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'jsonb', nullable: true })
  additionalData?: Record<string, any>;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'int', default: 0 })
  alertCount: number;

  @Column({ type: 'timestamp', nullable: true })
  lastAlertAt?: Date;

  @Column({ type: 'varchar', length: 100, nullable: true })
  aggregationPeriod?: string; // '1m', '5m', '1h', '1d', etc.

  @Column({ type: 'varchar', length: 50, nullable: true })
  aggregationMethod?: string; // 'avg', 'sum', 'min', 'max', 'count'
}