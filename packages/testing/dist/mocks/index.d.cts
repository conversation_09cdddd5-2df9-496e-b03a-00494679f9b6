import * as msw from 'msw';
import * as msw_node from 'msw/node';
import * as msw_browser from 'msw/browser';

declare class MockAPI {
    private baseUrl;
    private handlers;
    constructor(baseUrl?: string);
    get(path: string, response: any, options?: {
        delay?: number;
        status?: number;
    }): this;
    post(path: string, handler: (body: any) => any, options?: {
        delay?: number;
    }): this;
    put(path: string, handler: (params: any, body: any) => any, options?: {
        delay?: number;
    }): this;
    delete(path: string, response?: any, options?: {
        delay?: number;
    }): this;
    error(path: string, error: {
        message: string;
        code?: string;
    }, status?: number): this;
    getHandlers(): any[];
    paginated(path: string, itemGenerator: () => any, totalItems?: number): this;
    crud(resource: string, generator: () => any): this;
    fileUpload(path: string, handler?: (file: File) => any): this;
}
declare const createMockAuthAPI: () => MockAPI;
declare const createMockUserAPI: () => MockAPI;

declare const defaultHandlers: msw.HttpHandler[];
declare const createErrorHandlers: (endpoints: string[]) => msw.HttpHandler[];
declare const createDelayedHandlers: (_delay: number) => msw.HttpHandler[];
declare const createCrudHandlers: <T>(resource: string, generator: () => T, baseUrl?: string) => msw.HttpHandler[];
declare const createWebSocketHandlers: () => never[];
declare const createGraphQLHandlers: (endpoint?: string) => msw.HttpHandler[];

declare const server: msw_node.SetupServerApi;
declare const startMockServer: () => void;
declare const stopMockServer: () => void;
declare const resetMockServer: () => void;
declare const addMockHandler: (...handlers: any[]) => void;
declare const configureMockServer: (options: {
    onUnhandledRequest?: "bypass" | "warn" | "error";
    quiet?: boolean;
}) => void;
declare const setServerState: (key: string, value: any) => void;
declare const getServerState: (key: string) => any;
declare const clearServerState: () => void;
declare const mockScenarios: {
    networkError: () => void;
    slowNetwork: (_delayMs?: number) => void;
    serverErrors: () => void;
    reset: () => void;
};

declare const worker: msw_browser.SetupWorker;
declare const startMockWorker: () => Promise<ServiceWorkerRegistration | undefined>;
declare const stopMockWorker: () => void;
declare const enableMockingInDevelopment: () => void;
declare const enableMockingForStorybook: () => void;

export { MockAPI, addMockHandler, clearServerState, configureMockServer, createCrudHandlers, createDelayedHandlers, createErrorHandlers, createGraphQLHandlers, createMockAuthAPI, createMockUserAPI, createWebSocketHandlers, defaultHandlers, enableMockingForStorybook, enableMockingInDevelopment, getServerState, mockScenarios, resetMockServer, server, setServerState, startMockServer, startMockWorker, stopMockServer, stopMockWorker, worker };
