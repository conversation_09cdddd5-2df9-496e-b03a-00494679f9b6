import { http, delay, HttpResponse } from 'msw';
import { faker } from '@faker-js/faker';
import { setupServer } from 'msw/node';
import { setupWorker } from 'msw/browser';

// src/mocks/api.ts
var MockAPI = class {
  baseUrl;
  handlers = [];
  constructor(baseUrl = "http://localhost:3000/api") {
    this.baseUrl = baseUrl;
  }
  // Add a GET endpoint
  get(path, response, options = {}) {
    const handler = http.get(`${this.baseUrl}${path}`, async () => {
      if (options.delay) {
        await new Promise((resolve) => setTimeout(resolve, options.delay));
      }
      return HttpResponse.json(response, { status: options.status || 200 });
    });
    this.handlers.push(handler);
    return this;
  }
  // Add a POST endpoint
  post(path, handler, options = {}) {
    const mockHandler = http.post(`${this.baseUrl}${path}`, async ({ request }) => {
      const body = await request.json();
      if (options.delay) {
        await new Promise((resolve) => setTimeout(resolve, options.delay));
      }
      const response = await handler(body);
      return HttpResponse.json(response);
    });
    this.handlers.push(mockHandler);
    return this;
  }
  // Add a PUT endpoint
  put(path, handler, options = {}) {
    const mockHandler = http.put(`${this.baseUrl}${path}`, async ({ params, request }) => {
      const body = await request.json();
      if (options.delay) {
        await new Promise((resolve) => setTimeout(resolve, options.delay));
      }
      const response = await handler(params, body);
      return HttpResponse.json(response);
    });
    this.handlers.push(mockHandler);
    return this;
  }
  // Add a DELETE endpoint
  delete(path, response = { success: true }, options = {}) {
    const handler = http.delete(`${this.baseUrl}${path}`, async () => {
      if (options.delay) {
        await new Promise((resolve) => setTimeout(resolve, options.delay));
      }
      return HttpResponse.json(response);
    });
    this.handlers.push(handler);
    return this;
  }
  // Add error response
  error(path, error, status = 500) {
    const handler = http.get(`${this.baseUrl}${path}`, () => {
      return HttpResponse.json({ error }, { status });
    });
    this.handlers.push(handler);
    return this;
  }
  // Get all handlers
  getHandlers() {
    return this.handlers;
  }
  // Create paginated response
  paginated(path, itemGenerator, totalItems = 100) {
    const handler = http.get(`${this.baseUrl}${path}`, ({ request }) => {
      const url = new URL(request.url);
      const page = parseInt(url.searchParams.get("page") || "1");
      const limit = parseInt(url.searchParams.get("limit") || "10");
      const offset = (page - 1) * limit;
      const items = Array.from({ length: Math.min(limit, totalItems - offset) }, itemGenerator);
      return HttpResponse.json({
        data: items,
        pagination: {
          page,
          limit,
          total: totalItems,
          totalPages: Math.ceil(totalItems / limit),
          hasNext: page < Math.ceil(totalItems / limit),
          hasPrev: page > 1
        }
      });
    });
    this.handlers.push(handler);
    return this;
  }
  // Create CRUD endpoints for a resource
  crud(resource, generator) {
    const items = /* @__PURE__ */ new Map();
    this.get(`/${resource}`, () => {
      return Array.from(items.values());
    });
    this.handlers.push(
      http.get(`${this.baseUrl}/${resource}/:id`, ({ params }) => {
        const item = items.get(params.id);
        if (!item) {
          return HttpResponse.json({ error: "Not found" }, { status: 404 });
        }
        return HttpResponse.json(item);
      })
    );
    this.post(`/${resource}`, (body) => {
      const id = Date.now().toString();
      const item = { ...generator(), ...body, id };
      items.set(id, item);
      return item;
    });
    this.handlers.push(
      http.put(`${this.baseUrl}/${resource}/:id`, async ({ params, request }) => {
        const body = await request.json();
        const id = params.id;
        const existing = items.get(id);
        if (!existing) {
          return HttpResponse.json({ error: "Not found" }, { status: 404 });
        }
        const updated = { ...existing, ...body };
        items.set(id, updated);
        return HttpResponse.json(updated);
      })
    );
    this.handlers.push(
      http.delete(`${this.baseUrl}/${resource}/:id`, ({ params }) => {
        const id = params.id;
        if (!items.has(id)) {
          return HttpResponse.json({ error: "Not found" }, { status: 404 });
        }
        items.delete(id);
        return new HttpResponse(null, { status: 204 });
      })
    );
    return this;
  }
  // Create file upload endpoint
  fileUpload(path, handler) {
    const uploadHandler = http.post(`${this.baseUrl}${path}`, async ({ request }) => {
      const formData = await request.formData();
      const file = formData.get("file");
      if (!file) {
        return HttpResponse.json({ error: "No file provided" }, { status: 400 });
      }
      const response = handler ? await handler(file) : {
        id: Date.now().toString(),
        filename: file.name,
        size: file.size,
        type: file.type,
        url: `/uploads/${file.name}`
      };
      return HttpResponse.json(response);
    });
    this.handlers.push(uploadHandler);
    return this;
  }
};
var createMockAuthAPI = () => {
  return new MockAPI().post("/auth/login", ({ email, password }) => {
    if (email === "<EMAIL>" && password === "password") {
      return {
        user: { id: "1", email, name: "Test User" },
        token: "mock-jwt-token"
      };
    }
    throw new Error("Invalid credentials");
  }).post("/auth/logout", () => ({ success: true })).get("/auth/me", { id: "1", email: "<EMAIL>", name: "Test User" });
};
var createMockUserAPI = () => {
  return new MockAPI().crud("users", () => ({
    name: "John Doe",
    email: "<EMAIL>",
    role: "user"
  }));
};
var createMockUser = (overrides = {}) => ({
  id: faker.string.uuid(),
  email: faker.internet.email(),
  name: faker.person.fullName(),
  avatar: faker.image.avatar(),
  role: "user",
  createdAt: faker.date.past().toISOString(),
  updatedAt: faker.date.recent().toISOString(),
  ...overrides
});
var createMockApiResponse = (data, overrides = {}) => ({
  data,
  status: "success",
  message: "Operation successful",
  timestamp: (/* @__PURE__ */ new Date()).toISOString(),
  ...overrides
});
var createMockProduct = (overrides = {}) => ({
  id: faker.string.uuid(),
  name: faker.commerce.productName(),
  description: faker.commerce.productDescription(),
  price: parseFloat(faker.commerce.price()),
  image: faker.image.url(),
  category: faker.commerce.department(),
  inStock: faker.datatype.boolean(),
  rating: faker.number.float({ min: 1, max: 5, fractionDigits: 1 }),
  ...overrides
});
var createMockProducts = (count, overrides = {}) => {
  return Array.from({ length: count }, () => createMockProduct(overrides));
};

// src/mocks/handlers.ts
var API_BASE_URL = process.env.VITE_API_URL || "http://localhost:3000/api";
var defaultHandlers = [
  // Auth endpoints
  http.post(`${API_BASE_URL}/auth/login`, async ({ request }) => {
    const body = await request.json();
    await delay(300);
    if (body.email === "<EMAIL>" && body.password === "password") {
      return HttpResponse.json(
        createMockApiResponse({
          user: createMockUser({ email: body.email }),
          token: "mock-jwt-token"
        })
      );
    }
    return HttpResponse.json(
      { error: "Invalid credentials" },
      { status: 401 }
    );
  }),
  http.post(`${API_BASE_URL}/auth/logout`, async () => {
    await delay(100);
    return HttpResponse.json({ success: true });
  }),
  http.get(`${API_BASE_URL}/auth/me`, async () => {
    await delay(200);
    return HttpResponse.json(
      createMockApiResponse(createMockUser())
    );
  }),
  // User endpoints
  http.get(`${API_BASE_URL}/users`, async ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    await delay(300);
    const users = Array.from({ length: limit }, () => createMockUser());
    return HttpResponse.json(
      createMockApiResponse({
        data: users,
        pagination: {
          page,
          limit,
          total: 100,
          totalPages: 10
        }
      })
    );
  }),
  http.get(`${API_BASE_URL}/users/:id`, async ({ params }) => {
    await delay(200);
    return HttpResponse.json(
      createMockApiResponse(createMockUser({ id: params.id }))
    );
  }),
  http.post(`${API_BASE_URL}/users`, async ({ request }) => {
    const body = await request.json();
    await delay(300);
    return HttpResponse.json(
      createMockApiResponse(createMockUser(body)),
      { status: 201 }
    );
  }),
  http.put(`${API_BASE_URL}/users/:id`, async ({ request, params }) => {
    const body = await request.json();
    await delay(300);
    return HttpResponse.json(
      createMockApiResponse(createMockUser({ ...body, id: params.id }))
    );
  }),
  http.delete(`${API_BASE_URL}/users/:id`, async () => {
    await delay(200);
    return HttpResponse.json({ success: true });
  }),
  // Products endpoints
  http.get(`${API_BASE_URL}/products`, async () => {
    await delay(400);
    return HttpResponse.json(
      createMockApiResponse(createMockProducts(20))
    );
  }),
  // Generic error handler
  http.get(`${API_BASE_URL}/error`, () => {
    return HttpResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  })
];
var createErrorHandlers = (endpoints) => {
  return endpoints.map(
    (endpoint) => http.get(`${API_BASE_URL}${endpoint}`, () => {
      return HttpResponse.json(
        { error: "Test error" },
        { status: 500 }
      );
    })
  );
};
var createDelayedHandlers = (_delay) => {
  return defaultHandlers.map((handler) => {
    return handler;
  });
};
var createCrudHandlers = (resource, generator, baseUrl = API_BASE_URL) => {
  const resourceUrl = `${baseUrl}/${resource}`;
  return [
    // List
    http.get(resourceUrl, async () => {
      await delay(200);
      const items = Array.from({ length: 10 }, generator);
      return HttpResponse.json(createMockApiResponse(items));
    }),
    // Get by ID
    http.get(`${resourceUrl}/:id`, async ({ params }) => {
      await delay(150);
      return HttpResponse.json(
        createMockApiResponse({ ...generator(), id: params.id })
      );
    }),
    // Create
    http.post(resourceUrl, async ({ request }) => {
      const body = await request.json();
      await delay(250);
      return HttpResponse.json(
        createMockApiResponse({ ...generator(), ...body }),
        { status: 201 }
      );
    }),
    // Update
    http.put(`${resourceUrl}/:id`, async ({ request, params }) => {
      const body = await request.json();
      await delay(200);
      return HttpResponse.json(
        createMockApiResponse({ ...generator(), ...body, id: params.id })
      );
    }),
    // Delete
    http.delete(`${resourceUrl}/:id`, async () => {
      await delay(150);
      return new HttpResponse(null, { status: 204 });
    })
  ];
};
var createWebSocketHandlers = () => {
  return [];
};
var createGraphQLHandlers = (endpoint = `${API_BASE_URL}/graphql`) => {
  return [
    http.post(endpoint, async ({ request }) => {
      const body = await request.json();
      const { query, variables } = body;
      if (query.includes("GetUser")) {
        return HttpResponse.json({
          data: {
            user: createMockUser(variables?.id ? { id: variables.id } : {})
          }
        });
      }
      return HttpResponse.json({
        errors: [{ message: "Query not mocked" }]
      });
    })
  ];
};
var server = setupServer(...defaultHandlers);
var startMockServer = () => {
  server.listen({
    onUnhandledRequest: "bypass"
    // or 'warn' or 'error'
  });
};
var stopMockServer = () => {
  server.close();
};
var resetMockServer = () => {
  server.resetHandlers();
};
var addMockHandler = (...handlers) => {
  server.use(...handlers);
};
var configureMockServer = (options) => {
  server.listen(options);
};
var serverState = {};
var setServerState = (key, value) => {
  serverState[key] = value;
};
var getServerState = (key) => {
  return serverState[key];
};
var clearServerState = () => {
  serverState = {};
};
var mockScenarios = {
  // Simulate network failure
  networkError: () => {
    server.use(
      http.all("*", () => {
        return HttpResponse.error();
      })
    );
  },
  // Simulate slow network
  slowNetwork: (_delayMs = 3e3) => {
    server.use(
      ...defaultHandlers.map((handler) => {
        return handler;
      })
    );
  },
  // Simulate server errors
  serverErrors: () => {
    server.use(
      http.all("*", () => {
        return HttpResponse.json(
          { error: "Internal Server Error" },
          { status: 500 }
        );
      })
    );
  },
  // Reset to default
  reset: () => {
    server.resetHandlers(...defaultHandlers);
  }
};
var worker = setupWorker(...defaultHandlers);
var startMockWorker = async () => {
  if (typeof window !== "undefined") {
    return worker.start({
      onUnhandledRequest: "bypass",
      serviceWorker: {
        url: "/mockServiceWorker.js"
      }
    });
  }
  return void 0;
};
var stopMockWorker = () => {
  if (typeof window !== "undefined") {
    worker.stop();
  }
};
var enableMockingInDevelopment = () => {
  if (process.env.NODE_ENV === "development" && process.env.VITE_ENABLE_MOCKING === "true") {
    startMockWorker().then(() => {
      console.log("[MSW] Mocking enabled");
    });
  }
};
var enableMockingForStorybook = () => {
  if (typeof window !== "undefined" && window.location.pathname.includes("iframe.html")) {
    startMockWorker();
  }
};

export { MockAPI, addMockHandler, clearServerState, configureMockServer, createCrudHandlers, createDelayedHandlers, createErrorHandlers, createGraphQLHandlers, createMockAuthAPI, createMockUserAPI, createWebSocketHandlers, defaultHandlers, enableMockingForStorybook, enableMockingInDevelopment, getServerState, mockScenarios, resetMockServer, server, setServerState, startMockServer, startMockWorker, stopMockServer, stopMockWorker, worker };
//# sourceMappingURL=index.js.map
//# sourceMappingURL=index.js.map