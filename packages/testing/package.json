{"name": "@luminar/testing", "version": "1.0.0", "description": "Shared testing utilities for Luminar monorepo", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./react": {"types": "./dist/react/index.d.ts", "import": "./dist/react/index.js", "require": "./dist/react/index.cjs"}, "./mocks": {"types": "./dist/mocks/index.d.ts", "import": "./dist/mocks/index.js", "require": "./dist/mocks/index.cjs"}}, "files": ["dist", "src"], "scripts": {"dev": "tsup --watch", "build": "tsup", "clean": "rm -rf dist node_modules", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "typecheck": "tsc --noEmit"}, "dependencies": {"@faker-js/faker": "^9.3.0", "@tanstack/react-query": "^5.62.3", "@tanstack/react-router": "^1.58.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.2", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "happy-dom": "^15.11.7", "jsdom": "^25.0.1", "msw": "^2.7.0", "react-router-dom": "^6.27.0", "vitest": "^2.1.8", "vitest-mock-extended": "^2.0.2"}, "devDependencies": {"@types/node": "^22.5.4", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "react": "^19.0.0", "react-dom": "^19.0.0", "tsup": "^8.3.5", "typescript": "^5.7.2"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-dom": {"optional": true}}}