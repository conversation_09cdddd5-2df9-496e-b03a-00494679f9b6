{"name": "@luminar/runtime-config", "version": "1.0.0", "private": true, "description": "Runtime configuration system for Luminar platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit"}, "dependencies": {"axios": "^1.10.0", "zustand": "^5.0.6"}, "devDependencies": {"@types/node": "^22.5.4", "typescript": "^5.7.2"}, "peerDependencies": {"react": "^19.0.0"}}