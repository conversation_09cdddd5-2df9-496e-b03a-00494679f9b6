import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';

import { AuditLogService } from '../services/audit-log.service';

@Injectable()
export class AuditLogInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AuditLogInterceptor.name);

  constructor(private readonly auditLogService: AuditLogService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    
    const startTime = Date.now();
    const requestId = this.generateRequestId();
    
    // Extract request information
    const requestInfo = this.extractRequestInfo(request, requestId);

    return next.handle().pipe(
      tap((data) => {
        // Log successful request
        this.logRequest(requestInfo, response, startTime, 'success', data);
      }),
      catchError((error) => {
        // Log failed request
        this.logRequest(requestInfo, response, startTime, 'error', null, error);
        throw error;
      }),
    );
  }

  private extractRequestInfo(request: Request, requestId: string) {
    return {
      requestId,
      method: request.method,
      url: request.url,
      path: request.path,
      query: request.query,
      headers: this.sanitizeHeaders(request.headers),
      userId: this.extractUserId(request),
      ipAddress: this.getClientIp(request),
      userAgent: request.get('User-Agent'),
      body: this.sanitizeBody(request.body),
      timestamp: new Date(),
    };
  }

  private async logRequest(
    requestInfo: any,
    response: Response,
    startTime: number,
    status: 'success' | 'error',
    responseData?: any,
    error?: Error,
  ) {
    try {
      const duration = Date.now() - startTime;
      const statusCode = response.statusCode;

      const auditLogData = {
        eventType: 'http_request',
        userId: requestInfo.userId,
        action: `${requestInfo.method} ${requestInfo.path}`,
        resource: requestInfo.path,
        metadata: {
          requestId: requestInfo.requestId,
          httpMethod: requestInfo.method,
          url: requestInfo.url,
          query: requestInfo.query,
          headers: requestInfo.headers,
          statusCode,
          duration,
          responseSize: this.getResponseSize(responseData),
          userAgent: requestInfo.userAgent,
          ...(error && {
            error: {
              name: error.name,
              message: error.message,
              stack: error.stack,
            },
          }),
        },
        severity: this.determineSeverity(statusCode, duration, error),
        ipAddress: requestInfo.ipAddress,
        userAgent: requestInfo.userAgent,
        timestamp: requestInfo.timestamp,
      };

      await this.auditLogService.createAuditLog(auditLogData);
    } catch (auditError) {
      this.logger.error('Failed to create audit log:', auditError);
    }
  }

  private sanitizeHeaders(headers: Record<string, any>): Record<string, any> {
    const sensitiveHeaders = [
      'authorization',
      'cookie',
      'x-api-key',
      'x-auth-token',
    ];

    const sanitized = { ...headers };
    
    for (const header of sensitiveHeaders) {
      if (sanitized[header]) {
        sanitized[header] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  private sanitizeBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sensitiveFields = [
      'password',
      'token',
      'secret',
      'key',
      'authorization',
      'credentials',
    ];

    const sanitized = { ...body };

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  private extractUserId(request: Request): string | undefined {
    // Try different ways to extract user ID
    const user = (request as any).user;
    
    if (user) {
      return user.id || user.userId || user.sub;
    }

    // Try from JWT token in authorization header
    const authHeader = request.get('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        // In a real implementation, you would decode the JWT token
        // const decoded = jwt.decode(token);
        // return decoded.sub || decoded.userId;
      } catch (error) {
        // Ignore JWT decode errors
      }
    }

    return undefined;
  }

  private getClientIp(request: Request): string {
    return (
      request.get('x-forwarded-for') ||
      request.get('x-real-ip') ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }

  private getResponseSize(responseData: any): number {
    if (!responseData) return 0;
    
    try {
      return JSON.stringify(responseData).length;
    } catch {
      return 0;
    }
  }

  private determineSeverity(
    statusCode: number,
    duration: number,
    error?: Error,
  ): 'info' | 'warning' | 'error' | 'critical' {
    if (error || statusCode >= 500) {
      return 'error';
    }

    if (statusCode >= 400) {
      return 'warning';
    }

    if (duration > 5000) { // Slow requests over 5 seconds
      return 'warning';
    }

    return 'info';
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}