import { renderHook, RenderHookOptions, RenderHookResult } from '@testing-library/react'
import { ReactNode } from 'react'
import { createWrapper } from './providers'

interface CustomRenderHookOptions<TProps> extends Omit<RenderHookOptions<TProps>, 'wrapper'> {
  providers?: Array<React.ComponentType<{ children: ReactNode }>>
  providerProps?: Record<string, any>
}

export function renderHookWithContext<TProps, TResult>(
  hook: (props: TProps) => TResult,
  options: CustomRenderHookOptions<TProps> = {}
): RenderHookResult<TResult, TProps> {
  const { providers = [], providerProps = {}, ...renderOptions } = options
  
  const wrapper = createWrapper({ providers, providerProps })
  
  return renderHook(hook, {
    wrapper,
    ...renderOptions,
  })
}

// Hook testing utilities
export const waitForHookUpdate = async (
  _result: { current: any },
  condition: () => boolean,
  options = { timeout: 1000, interval: 50 }
) => {
  const { timeout, interval } = options
  const startTime = Date.now()
  
  while (!condition() && Date.now() - startTime < timeout) {
    await new Promise(resolve => setTimeout(resolve, interval))
  }
  
  if (!condition()) {
    throw new Error(`Hook update condition not met within ${timeout}ms`)
  }
}

// Common hook test patterns
export const testHookWithAsyncState = async <T,>(
  hook: () => { data: T | null; loading: boolean; error: Error | null },
  expectedData: T
) => {
  const { result, rerender } = renderHook(hook)
  
  // Initial state
  expect(result.current.loading).toBe(true)
  expect(result.current.data).toBe(null)
  expect(result.current.error).toBe(null)
  
  // Wait for data
  await waitForHookUpdate(result, () => !result.current.loading)
  
  // Final state
  expect(result.current.loading).toBe(false)
  expect(result.current.data).toEqual(expectedData)
  expect(result.current.error).toBe(null)
  
  return { result, rerender }
}

export const testHookErrorState = async <T,>(
  hook: () => { data: T | null; loading: boolean; error: Error | null },
  expectedError: string
) => {
  const { result } = renderHook(hook)
  
  // Wait for error
  await waitForHookUpdate(result, () => result.current.error !== null)
  
  expect(result.current.loading).toBe(false)
  expect(result.current.data).toBe(null)
  expect(result.current.error?.message).toBe(expectedError)
}

// Hook lifecycle helpers
export const trackHookRenders = <T,>(hook: () => T) => {
  let renderCount = 0
  const values: T[] = []
  
  const TrackedHook = () => {
    const result = hook()
    renderCount++
    values.push(result)
    return result
  }
  
  const { result, rerender } = renderHook(TrackedHook)
  
  return {
    result,
    rerender,
    getRenderCount: () => renderCount,
    getValues: () => values,
    getLastValue: () => values[values.length - 1],
  }
}