import { A as AuthConfig, L as LoginCredentials, U as User, a as AuthTokens, R as RegisterData, b as AuthError } from './types--xQR_mKr.js';
export { d as AuthContextValue, e as AuthProviderProps, c as AuthState } from './types--xQR_mKr.js';
export { a as AuthContext, A as AuthProvider, u as useAuth, u as useAuthContext } from './index-b49DoYKT.js';
export { CrossAppNavigation, OAuthButtons, PermissionGate, ProtectedRoute, UnifiedLoginPage } from './components.js';
import 'react';

declare class AuthClient {
    private api;
    private config;
    private refreshPromise;
    constructor(config: AuthConfig);
    private setupInterceptors;
    private handleApiError;
    private getStorage;
    private setTokens;
    private getAccessToken;
    private getRefreshToken;
    private setUser;
    private getUser;
    private clearAuth;
    login(credentials: LoginCredentials): Promise<{
        user: User;
        tokens: AuthTokens;
    }>;
    register(data: RegisterData): Promise<{
        user: User;
        tokens: AuthTokens;
    }>;
    logout(): Promise<void>;
    refreshTokens(): Promise<AuthTokens>;
    getCurrentUser(): Promise<User>;
    updateProfile(data: Partial<User>): Promise<User>;
    changePassword(currentPassword: string, newPassword: string): Promise<void>;
    requestPasswordReset(email: string): Promise<void>;
    resetPassword(token: string, newPassword: string): Promise<void>;
    isTokenExpired(token?: string): boolean;
    validateSession(): Promise<boolean>;
    getStoredUser(): User | null;
    getStoredTokens(): AuthTokens | null;
    clearSession(): void;
}

interface TokenPayload {
    sub: string;
    email: string;
    roles: string[];
    permissions: string[];
    exp: number;
    iat: number;
    [key: string]: any;
}
declare function decodeToken(token: string): TokenPayload | null;
declare function isTokenExpired(token: string): boolean;
declare function getTokenExpirationTime(token: string): Date | null;
declare function getTokenRemainingTime(token: string): number;
declare function hasRoleWithHierarchy(userRoles: string[], requiredRole: string): boolean;
declare function hasPermissionPattern(userPermissions: string[], pattern: string): boolean;
declare function getStorageItem(key: string, storage?: Storage): string | null;
declare function setStorageItem(key: string, value: string, storage?: Storage): boolean;
declare function removeStorageItem(key: string, storage?: Storage): boolean;
declare class SessionTimer {
    private timer;
    private warningTimer;
    start(expirationTime: Date, onExpire: () => void, onWarning?: () => void, warningMinutes?: number): void;
    stop(): void;
    getRemainingTime(expirationTime: Date): number;
}
declare function getAuthRedirectUrl(defaultUrl?: string, locationState?: any): string;
declare class AuthSyncManager {
    private onAuthChange;
    private channel;
    private storageKey;
    constructor(onAuthChange: (event: 'login' | 'logout' | 'refresh') => void);
    private handleStorageChange;
    broadcast(type: 'login' | 'logout' | 'refresh'): void;
    destroy(): void;
}

declare function useRequireAuth(options?: {
    redirectTo?: string;
    roles?: string[];
    permissions?: string[];
}): {
    isAuthenticated: boolean;
    isLoading: boolean;
    user: User | null;
    isAuthorized: boolean;
};
declare function useRedirectIfAuthenticated(redirectTo?: string): void;
declare function usePermissions(): {
    user: User | null;
    roles: string[];
    permissions: string[];
    hasRole: (role: string) => boolean;
    hasPermission: (permission: string) => boolean;
    hasAnyRole: (roles: string[]) => boolean;
    hasAllRoles: (roles: string[]) => boolean;
    hasAnyPermission: (permissions: string[]) => boolean;
    hasAllPermissions: (permissions: string[]) => boolean;
    isAdmin: boolean;
    isManager: boolean;
    isUser: boolean;
};
declare function useAuthGuard(condition: 'authenticated' | 'unauthenticated' | 'loading'): boolean;
declare function useAuthError(handler?: (error: Error) => void): AuthError | null;

interface CrossAppTokens extends AuthTokens {
    targetApp: string;
    permissions: string[];
}
interface CrossAppConfig {
    appId: string;
    apiBaseUrl: string;
    allowedOrigins: string[];
    tokenExpiryMinutes: number;
}
interface CrossAppRequest {
    targetAppId: string;
    permissions?: string[];
    sessionData?: Record<string, any>;
}
declare class CrossAppAuthClient {
    private authClient;
    private api;
    private crossAppTokens;
    constructor(authClient: AuthClient);
    private setupInterceptors;
    /**
     * Generate a cross-app token for accessing another Luminar app
     */
    generateCrossAppToken(request: CrossAppRequest): Promise<CrossAppTokens>;
    /**
     * Get cached cross-app token or generate new one
     */
    getCrossAppToken(appId: string, permissions?: string[]): Promise<string>;
    /**
     * Verify a cross-app token
     */
    verifyToken(token: string, appId: string): Promise<{
        valid: boolean;
        payload?: {
            userId: string;
            email: string;
            roles: string[];
            permissions: string[];
            appId: string;
            sessionId: string;
        };
        error?: string;
    }>;
    /**
     * Refresh a cross-app token
     */
    refreshCrossAppToken(appId: string): Promise<CrossAppTokens>;
    /**
     * Get available apps configuration
     */
    getAvailableApps(): Promise<CrossAppConfig[]>;
    /**
     * Get user permissions for specific app
     */
    getUserPermissionsForApp(appId: string): Promise<string[]>;
    /**
     * Logout from all apps
     */
    logoutFromAllApps(): Promise<void>;
    /**
     * Navigate to another app with SSO
     */
    navigateToApp(appId: string, redirectPath?: string, permissions?: string[]): Promise<string>;
    /**
     * Validate app origin for CORS
     */
    isOriginAllowed(appId: string, origin: string): boolean;
    /**
     * Clear cached tokens for specific app
     */
    clearAppToken(appId: string): void;
    /**
     * Clear all cached cross-app tokens
     */
    clearAllAppTokens(): void;
    /**
     * Get cached token info
     */
    getCachedTokenInfo(appId: string): CrossAppTokens | null;
    /**
     * Check if user has permission for specific app action
     */
    hasAppPermission(appId: string, permission: string): Promise<boolean>;
    private getAppConfig;
    private notifyAppLogout;
    /**
     * Auto-refresh cross-app tokens before they expire
     */
    startAutoRefresh(intervalMinutes?: number): void;
}
declare const createSSOUrl: (appBaseUrl: string, token: string, redirectPath?: string) => string;
declare const extractSSOToken: () => string | null;
declare const extractSSORedirect: () => string;

interface CorsHeaders {
    'Access-Control-Allow-Origin'?: string;
    'Access-Control-Allow-Methods'?: string;
    'Access-Control-Allow-Headers'?: string;
    'Access-Control-Expose-Headers'?: string;
    'Access-Control-Allow-Credentials'?: string;
    'Access-Control-Max-Age'?: string;
}
interface CorsValidationResult {
    allowed: boolean;
    headers?: CorsHeaders;
    reason?: string;
}
declare class CorsHelper {
    private config;
    private appId;
    constructor(config: AuthConfig, appId: string);
    /**
     * Validate if current origin is allowed for cross-app requests
     */
    validateCurrentOrigin(): boolean;
    /**
     * Check if an origin is allowed for this app
     */
    isOriginAllowed(origin: string): boolean;
    /**
     * Get CORS headers for cross-app requests
     */
    getCorsHeaders(targetOrigin?: string): CorsHeaders;
    /**
     * Validate CORS request against backend
     */
    validateCorsRequest(origin: string, method: string, headers: string[]): Promise<CorsValidationResult>;
    /**
     * Create preflight request for complex CORS requests
     */
    sendPreflightRequest(targetUrl: string, method: string, headers: string[]): Promise<CorsValidationResult>;
    /**
     * Setup CORS for cross-app communication
     */
    setupCrossAppCors(): void;
    /**
     * Send message to another L&D app
     */
    sendCrossAppMessage(targetOrigin: string, message: any, targetWindow?: Window): boolean;
    /**
     * Create CORS-compliant fetch wrapper
     */
    createCorsAwareFetch(): (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>;
    private getAllowedOrigins;
    private getAllowedHeaders;
    private getExposedHeaders;
    private getCurrentOrigin;
    private isDevelopment;
    private handleCrossAppMessage;
    private handleAuthStateChange;
    private handleUserLogout;
    private handlePermissionUpdate;
}
declare const createCorsHelper: (config: AuthConfig, appId: string) => CorsHelper;
declare const isOriginAllowed: (origin: string, allowedOrigins: string[]) => boolean;
declare const extractOriginFromUrl: (url: string) => string;
declare const isSameOrigin: (url1: string, url2: string) => boolean;
declare const buildCorsUrl: (baseUrl: string, path: string, params?: Record<string, string>) => string;

interface OAuthProvider {
    name: string;
    authUrl: string;
    tokenUrl: string;
    userInfoUrl: string;
    clientId: string;
    clientSecret?: string;
    scope: string;
    redirectUri: string;
}
interface OAuthConfig {
    providers: {
        google?: OAuthProvider;
        github?: OAuthProvider;
        microsoft?: OAuthProvider;
        [key: string]: OAuthProvider | undefined;
    };
    onSuccess?: (user: User, tokens: AuthTokens) => void;
    onError?: (error: AuthError) => void;
}
interface OAuthState {
    state: string;
    codeVerifier?: string;
    provider: string;
    timestamp: number;
}
declare class OAuthClient {
    private api;
    private config;
    private authConfig;
    private stateKey;
    constructor(authConfig: AuthConfig, oauthConfig: OAuthConfig);
    /**
     * Initiate OAuth flow
     */
    initiateOAuth(provider: string): Promise<void>;
    /**
     * Handle OAuth callback
     */
    handleCallback(code: string, state: string): Promise<{
        user: User;
        tokens: AuthTokens;
    }>;
    /**
     * Exchange authorization code for tokens
     */
    private exchangeCodeForTokens;
    /**
     * Get user info from OAuth provider
     */
    private getUserInfo;
    /**
     * Link OAuth account to existing user
     */
    linkAccount(provider: string, userId: string): Promise<void>;
    /**
     * Unlink OAuth account
     */
    unlinkAccount(provider: string, userId: string): Promise<void>;
    /**
     * Get linked accounts for user
     */
    getLinkedAccounts(userId: string): Promise<string[]>;
    /**
     * Refresh OAuth tokens
     */
    refreshOAuthTokens(provider: string, refreshToken: string): Promise<AuthTokens>;
    /**
     * Generate random state for CSRF protection
     */
    private generateState;
    /**
     * Generate PKCE code verifier
     */
    private generateCodeVerifier;
    /**
     * Generate PKCE code challenge
     */
    private generateCodeChallenge;
    /**
     * Base64 URL encode
     */
    private base64UrlEncode;
}
declare const defaultOAuthProviders: Partial<OAuthConfig['providers']>;

export { AuthClient, AuthConfig, AuthError, AuthSyncManager, AuthTokens, type CorsHeaders, CorsHelper, type CorsValidationResult, CrossAppAuthClient, type CrossAppConfig, type CrossAppRequest, type CrossAppTokens, LoginCredentials, OAuthClient, type OAuthConfig, type OAuthProvider, type OAuthState, RegisterData, SessionTimer, type TokenPayload, User, buildCorsUrl, createCorsHelper, createSSOUrl, decodeToken, defaultOAuthProviders, extractOriginFromUrl, extractSSORedirect, extractSSOToken, getAuthRedirectUrl, getStorageItem, getTokenExpirationTime, getTokenRemainingTime, hasPermissionPattern, hasRoleWithHierarchy, isOriginAllowed, isSameOrigin, isTokenExpired, removeStorageItem, setStorageItem, useAuthError, useAuthGuard, usePermissions, useRedirectIfAuthenticated, useRequireAuth };
