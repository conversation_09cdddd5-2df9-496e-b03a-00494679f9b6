import React, { useEffect, useState } from 'react';
import { useNavigate, useSearch } from '@tanstack/react-router';
import { useAuth } from '../hooks';
import { OAuthClient } from '../oauth-client';
import { AuthError } from '../types';

interface OAuthCallbackProps {
  provider: string;
  onSuccess?: () => void;
  onError?: (error: AuthError) => void;
  loadingComponent?: React.ReactNode;
  errorComponent?: (error: AuthError) => React.ReactNode;
}

export const OAuthCallback: React.FC<OAuthCallbackProps> = ({
  provider,
  onSuccess,
  onError,
  loadingComponent,
  errorComponent
}) => {
  const searchParams = useSearch();
  const navigate = useNavigate();
  const { login } = useAuth();
  const [error, setError] = useState<AuthError | null>(null);
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    const handleCallback = async () => {
      const code = (searchParams as any)?.code;
      const state = (searchParams as any)?.state;
      const error = (searchParams as any)?.error;
      const errorDescription = (searchParams as any)?.error_description;

      // Handle OAuth errors
      if (error) {
        const authError = new AuthError(
          errorDescription || `OAuth error: ${error}`,
          'OAUTH_ERROR'
        );
        setError(authError);
        setIsProcessing(false);
        onError?.(authError);
        return;
      }

      // Validate required parameters
      if (!code || !state) {
        const authError = new AuthError(
          'Missing required OAuth parameters',
          'INVALID_CALLBACK'
        );
        setError(authError);
        setIsProcessing(false);
        onError?.(authError);
        return;
      }

      try {
        // Get auth config from context or environment
        const authConfig = {
          apiBaseUrl: process.env.VITE_API_URL || '/api',
          authEndpoint: '/auth'
        };

        // Initialize OAuth client
        const oauthClient = new OAuthClient(authConfig, {
          providers: {
            [provider]: {
              name: provider,
              authUrl: '', // Not needed for callback
              tokenUrl: '', // Will be configured on server
              userInfoUrl: '', // Will be configured on server
              clientId: process.env[`VITE_${provider.toUpperCase()}_CLIENT_ID`] || '',
              scope: '',
              redirectUri: `${window.location.origin}/auth/callback/${provider}`
            }
          }
        });

        // Handle the callback
        const { user, tokens } = await oauthClient.handleCallback(code, state);

        // Update auth state
        await login({
          email: user.email,
          password: '' // OAuth doesn't use password
        });

        // Handle linking flow if present
        const linkingStateStr = localStorage.getItem('oauth_linking');
        if (linkingStateStr) {
          const linkingState = JSON.parse(linkingStateStr);
          localStorage.removeItem('oauth_linking');
          
          // Navigate to account settings
          navigate({ to: '/settings/accounts' });
        } else {
          // Normal login flow
          onSuccess?.();
          navigate({ to: '/' });
        }
      } catch (err) {
        const authError = err as AuthError;
        setError(authError);
        setIsProcessing(false);
        onError?.(authError);
      }
    };

    handleCallback();
  }, [searchParams, provider, navigate, login, onSuccess, onError]);

  // Render loading state
  if (isProcessing) {
    return loadingComponent || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 mb-4">
            <svg className="animate-spin h-12 w-12 text-primary" fill="none" viewBox="0 0 24 24">
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Completing sign in with {provider}...
          </h2>
          <p className="text-gray-600">
            Please wait while we authenticate your account.
          </p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return errorComponent?.(error) || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md w-full">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-red-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Authentication failed
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error.message}</p>
                  {error.code && (
                    <p className="mt-1 text-xs">Error code: {error.code}</p>
                  )}
                </div>
                <div className="mt-4">
                  <button
                    type="button"
                    onClick={() => navigate({ to: '/login' })}
                    className="text-sm font-medium text-red-600 hover:text-red-500"
                  >
                    Return to login →
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};