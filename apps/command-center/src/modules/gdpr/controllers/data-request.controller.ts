import {
  Controller,
  Get,
  Post,
  Put,
  Query,
  Param,
  Body,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsEnum, IsBoolean } from 'class-validator';

import { DataPortabilityService } from '../services/data-portability.service';
import { GdprService } from '../services/gdpr.service';

export class CreateDataExportRequestDto {
  @IsString()
  userId: string;

  @IsString()
  requestedBy: string;

  @IsEnum(['json', 'csv', 'xml', 'pdf'])
  exportFormat: 'json' | 'csv' | 'xml' | 'pdf';

  @IsArray()
  @IsString({ each: true })
  dataTypes: string[];

  @IsOptional()
  @IsBoolean()
  includeMetadata?: boolean;

  @IsOptional()
  dateRange?: {
    start: string;
    end: string;
  };
}

export class CreateDataDeletionRequestDto {
  @IsString()
  userId: string;

  @IsString()
  requestedBy: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  dataTypes?: string[];

  @IsOptional()
  @IsString()
  reason?: string;

  @IsOptional()
  @IsBoolean()
  hardDelete?: boolean;
}

export class CreateDataAccessRequestDto {
  @IsString()
  userId: string;

  @IsString()
  requestedBy: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  specificData?: string[];

  @IsOptional()
  @IsString()
  purpose?: string;
}

@ApiTags('Data Subject Rights')
@ApiBearerAuth()
@Controller('data-requests')
export class DataRequestController {
  constructor(
    private readonly dataPortabilityService: DataPortabilityService,
    private readonly gdprService: GdprService,
  ) {}

  @Post('export')
  @ApiOperation({ summary: 'Create data export request' })
  @ApiResponse({ status: 201, description: 'Data export request created successfully' })
  async createExportRequest(@Body(ValidationPipe) exportDto: CreateDataExportRequestDto) {
    const dateRange = exportDto.dateRange ? {
      start: new Date(exportDto.dateRange.start),
      end: new Date(exportDto.dateRange.end),
    } : undefined;

    const request = await this.dataPortabilityService.createExportRequest({
      userId: exportDto.userId,
      requestedBy: exportDto.requestedBy,
      exportFormat: exportDto.exportFormat,
      dataTypes: exportDto.dataTypes,
      includeMetadata: exportDto.includeMetadata,
      dateRange,
    });

    return {
      success: true,
      data: request,
      message: 'Data export request created successfully',
    };
  }

  @Post('deletion')
  @ApiOperation({ summary: 'Create data deletion request' })
  @ApiResponse({ status: 201, description: 'Data deletion request created successfully' })
  async createDeletionRequest(@Body(ValidationPipe) deletionDto: CreateDataDeletionRequestDto) {
    const request = await this.gdprService.createDataDeletionRequest({
      userId: deletionDto.userId,
      requestedBy: deletionDto.requestedBy,
      dataTypes: deletionDto.dataTypes,
      reason: deletionDto.reason,
      hardDelete: deletionDto.hardDelete,
    });

    return {
      success: true,
      data: request,
      message: 'Data deletion request created successfully',
    };
  }

  @Post('access')
  @ApiOperation({ summary: 'Create data access request' })
  @ApiResponse({ status: 201, description: 'Data access request created successfully' })
  async createAccessRequest(@Body(ValidationPipe) accessDto: CreateDataAccessRequestDto) {
    const request = await this.gdprService.createDataAccessRequest({
      userId: accessDto.userId,
      requestedBy: accessDto.requestedBy,
      specificData: accessDto.specificData,
      purpose: accessDto.purpose,
    });

    return {
      success: true,
      data: request,
      message: 'Data access request created successfully',
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get data requests' })
  @ApiResponse({ status: 200, description: 'Data requests retrieved successfully' })
  async getDataRequests(
    @Query('userId') userId?: string,
    @Query('requestType') requestType?: string,
    @Query('status') status?: string,
    @Query('limit') limit?: number
  ) {
    let requests: any[] = [];

    if (!requestType || requestType === 'export') {
      const exportRequests = await this.dataPortabilityService.getExportRequests(userId, status);
      requests = [...requests, ...exportRequests];
    }

    if (!requestType || requestType === 'deletion' || requestType === 'access') {
      const otherRequests = await this.gdprService.getDataRequests({
        userId,
        requestType: requestType === 'export' ? undefined : requestType,
        status,
        limit,
      });
      requests = [...requests, ...otherRequests];
    }

    // Sort by creation date
    requests.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // Apply limit if specified
    if (limit) {
      requests = requests.slice(0, limit);
    }

    return {
      success: true,
      data: requests,
      total: requests.length,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get data request by ID' })
  @ApiResponse({ status: 200, description: 'Data request retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Data request not found' })
  async getDataRequestById(@Param('id') id: string) {
    // Try to find in export requests first
    let request = await this.dataPortabilityService.getExportRequestById(id);
    
    // If not found, try other types
    if (!request) {
      request = await this.gdprService.getDataRequestById(id);
    }

    if (!request) {
      return {
        success: false,
        message: 'Data request not found',
      };
    }

    return {
      success: true,
      data: request,
    };
  }

  @Put(':id/status')
  @ApiOperation({ summary: 'Update data request status' })
  @ApiResponse({ status: 200, description: 'Data request status updated successfully' })
  @ApiResponse({ status: 404, description: 'Data request not found' })
  async updateRequestStatus(
    @Param('id') id: string,
    @Body() body: { status: string; notes?: string }
  ) {
    const updated = await this.gdprService.updateDataRequestStatus(id, body.status, body.notes);

    if (!updated) {
      return {
        success: false,
        message: 'Data request not found',
      };
    }

    return {
      success: true,
      data: updated,
      message: 'Data request status updated successfully',
    };
  }

  @Get('user/:userId/export-data')
  @ApiOperation({ summary: 'Export user data directly' })
  @ApiResponse({ status: 200, description: 'User data exported successfully' })
  async exportUserData(
    @Param('userId') userId: string,
    @Query('format') format: 'json' | 'csv' | 'xml' | 'pdf' = 'json',
    @Query('dataTypes') dataTypes?: string,
    @Query('includeMetadata') includeMetadata?: boolean
  ) {
    const dataTypesArray = dataTypes ? dataTypes.split(',') : ['profile', 'activities', 'preferences'];

    const exportedData = await this.dataPortabilityService.exportUserData(userId, {
      format,
      dataTypes: dataTypesArray,
      includeMetadata,
    });

    return {
      success: true,
      data: exportedData,
      message: 'User data exported successfully',
    };
  }

  @Get('user/:userId/portability-report')
  @ApiOperation({ summary: 'Generate data portability report for user' })
  @ApiResponse({ status: 200, description: 'Data portability report generated successfully' })
  async generatePortabilityReport(@Param('userId') userId: string) {
    const report = await this.dataPortabilityService.generatePortabilityReport(userId);

    return {
      success: true,
      data: report,
    };
  }

  @Post('validate-export')
  @ApiOperation({ summary: 'Validate data export request' })
  @ApiResponse({ status: 200, description: 'Export request validated' })
  async validateExportRequest(@Body() exportRequest: CreateDataExportRequestDto) {
    const dateRange = exportRequest.dateRange ? {
      start: new Date(exportRequest.dateRange.start),
      end: new Date(exportRequest.dateRange.end),
    } : undefined;

    const validation = await this.dataPortabilityService.validateExportRequest({
      userId: exportRequest.userId,
      requestedBy: exportRequest.requestedBy,
      exportFormat: exportRequest.exportFormat,
      dataTypes: exportRequest.dataTypes,
      includeMetadata: exportRequest.includeMetadata,
      dateRange,
    });

    return {
      success: true,
      data: validation,
    };
  }

  @Get('analytics/summary')
  @ApiOperation({ summary: 'Get data requests analytics' })
  @ApiResponse({ status: 200, description: 'Data requests analytics retrieved successfully' })
  async getRequestsAnalytics(@Query('days') days?: number) {
    const analytics = await this.gdprService.getDataRequestsAnalytics(days || 30);

    return {
      success: true,
      data: analytics,
    };
  }

  @Get('types/available')
  @ApiOperation({ summary: 'Get available data types for export' })
  @ApiResponse({ status: 200, description: 'Available data types retrieved successfully' })
  async getAvailableDataTypes() {
    const dataTypes = [
      {
        type: 'profile',
        name: 'Profile Information',
        description: 'Basic user profile data including name, email, preferences',
        estimatedSize: 'Small (< 1KB)',
      },
      {
        type: 'activities',
        name: 'Activity Logs',
        description: 'User activity history, login logs, action history',
        estimatedSize: 'Medium (1-100KB)',
      },
      {
        type: 'preferences',
        name: 'User Preferences',
        description: 'Application settings, notification preferences, customizations',
        estimatedSize: 'Small (< 1KB)',
      },
      {
        type: 'communications',
        name: 'Communications',
        description: 'Email communications, notifications sent to user',
        estimatedSize: 'Large (100KB+)',
      },
      {
        type: 'documents',
        name: 'Documents',
        description: 'Uploaded files, generated reports, shared documents',
        estimatedSize: 'Variable',
      },
      {
        type: 'analytics',
        name: 'Analytics Data',
        description: 'Usage analytics, performance metrics, aggregated data',
        estimatedSize: 'Medium (1-100KB)',
      },
    ];

    return {
      success: true,
      data: dataTypes,
    };
  }

  @Post('bulk-process')
  @ApiOperation({ summary: 'Process multiple data requests' })
  @ApiResponse({ status: 200, description: 'Bulk processing initiated successfully' })
  async bulkProcessRequests(@Body() body: { requestIds: string[]; action: string }) {
    const results = await this.gdprService.bulkProcessDataRequests(body.requestIds, body.action);

    return {
      success: true,
      data: results,
      message: `Bulk processing completed: ${results.successful} successful, ${results.failed} failed`,
    };
  }

  @Get('compliance/status')
  @ApiOperation({ summary: 'Get data requests compliance status' })
  @ApiResponse({ status: 200, description: 'Compliance status retrieved successfully' })
  async getComplianceStatus() {
    const status = await this.gdprService.getDataRequestsComplianceStatus();

    return {
      success: true,
      data: status,
    };
  }
}