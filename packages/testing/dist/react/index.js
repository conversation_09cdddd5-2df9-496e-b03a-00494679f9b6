import '@testing-library/jest-dom/vitest';
import { cleanup, render, renderHook, screen, within, waitFor } from '@testing-library/react';
import { afterEach, beforeAll, vi } from 'vitest';
import userEvent from '@testing-library/user-event';
import { jsx, Fragment } from 'react/jsx-runtime';
import React from 'react';

var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
afterEach(() => {
  cleanup();
});
beforeAll(() => {
  Object.defineProperty(window, "matchMedia", {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      // deprecated
      removeListener: vi.fn(),
      // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn()
    }))
  });
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }));
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn()
  }));
  window.scrollTo = vi.fn();
  if (!global.fetch) {
    global.fetch = vi.fn();
  }
  const originalError = console.error;
  console.error = (...args) => {
    if (typeof args[0] === "string" && (args[0].includes("Warning: ReactDOM.render") || args[0].includes("Warning: An invalid form control") || args[0].includes("Not implemented: HTMLFormElement.prototype.submit"))) {
      return;
    }
    originalError.call(console, ...args);
  };
});
var mockLocalStorage = () => {
  const storage = {};
  return {
    getItem: vi.fn((key) => storage[key] || null),
    setItem: vi.fn((key, value) => {
      storage[key] = value;
    }),
    removeItem: vi.fn((key) => {
      delete storage[key];
    }),
    clear: vi.fn(() => {
      Object.keys(storage).forEach((key) => delete storage[key]);
    }),
    get length() {
      return Object.keys(storage).length;
    },
    key: vi.fn((index) => {
      const keys = Object.keys(storage);
      return keys[index] || null;
    })
  };
};
var setupBrowserMocks = () => {
  const localStorage = mockLocalStorage();
  const sessionStorage = mockLocalStorage();
  Object.defineProperty(window, "localStorage", {
    value: localStorage,
    writable: true
  });
  Object.defineProperty(window, "sessionStorage", {
    value: sessionStorage,
    writable: true
  });
  global.URL = URL;
  global.URLSearchParams = URLSearchParams;
  Object.defineProperty(window, "crypto", {
    value: {
      randomUUID: () => "12345678-1234-1234-1234-123456789012",
      getRandomValues: (arr) => {
        for (let i = 0; i < arr.length; i++) {
          arr[i] = Math.floor(Math.random() * 256);
        }
        return arr;
      }
    }
  });
};
var MemoryRouter;
try {
  const routerModule = __require("react-router-dom");
  MemoryRouter = routerModule.MemoryRouter;
} catch (error) {
  MemoryRouter = ({ children }) => /* @__PURE__ */ jsx(Fragment, { children });
}
var createWrapper = (options = {}) => {
  const { routerProps = {}, route = "/", providers = [], providerProps = {} } = options;
  return ({ children }) => {
    if (route && routerProps.initialEntries === void 0) {
      routerProps.initialEntries = [route];
    }
    let wrapped = children;
    wrapped = /* @__PURE__ */ jsx(MemoryRouter, { ...routerProps, children: wrapped });
    providers.reverse().forEach((Provider) => {
      const props = providerProps[Provider.displayName || Provider.name] || {};
      wrapped = /* @__PURE__ */ jsx(Provider, { ...props, children: wrapped });
    });
    return wrapped;
  };
};
function customRender(ui, options = {}) {
  const { mockData, ...renderOptions } = options;
  const user = userEvent.setup();
  if (mockData) {
    Object.entries(mockData).forEach(([key, value]) => {
      window[key] = value;
    });
  }
  const wrapper = createWrapper(options);
  const renderResult = render(ui, { wrapper, ...renderOptions });
  return {
    ...renderResult,
    user
  };
}
var renderWithRouter = (ui, routerProps, renderOptions) => {
  const options = { ...renderOptions };
  if (routerProps) {
    options.routerProps = routerProps;
  }
  return customRender(ui, options);
};
var renderWithProviders = (ui, providers = [], renderOptions) => {
  return customRender(ui, { ...renderOptions, providers });
};
var renderHookWithProviders = (hook, options) => {
  const TestComponent = ({ hookProps }) => {
    const result = hook(hookProps);
    return /* @__PURE__ */ jsx("div", { "data-testid": "hook-result", children: JSON.stringify(result) });
  };
  return {
    ...customRender(/* @__PURE__ */ jsx(TestComponent, { hookProps: {} }), options),
    result: {
      current: hook({})
    }
  };
};
var renderAndGetElements = (ui, options) => {
  const renderResult = customRender(ui, options);
  return {
    ...renderResult
  };
};
var createWrapper2 = (options = {}) => {
  const { providers = [], providerProps = {} } = options;
  return ({ children }) => {
    let wrapped = children;
    providers.reverse().forEach((Provider) => {
      const props = providerProps[Provider.displayName || Provider.name] || {};
      wrapped = /* @__PURE__ */ jsx(Provider, { ...props, children: wrapped });
    });
    return /* @__PURE__ */ jsx(Fragment, { children: wrapped });
  };
};
var TestProviders = ({ children }) => {
  return /* @__PURE__ */ jsx(Fragment, { children });
};
var createContextWrapper = (Context, value) => {
  return ({ children }) => /* @__PURE__ */ jsx(Context.Provider, { value, children });
};
var createMockProvider = (name, defaultValue) => {
  const Context = React.createContext(defaultValue);
  const Provider = ({
    children,
    value = {}
  }) => {
    const mergedValue = { ...defaultValue, ...value };
    return /* @__PURE__ */ jsx(Context.Provider, { value: mergedValue, children });
  };
  Provider.displayName = name;
  return { Context, Provider };
};
var CombinedProviders = ({ children, providers = [], providerProps = {} }) => {
  const Wrapper = createWrapper2({ providers, providerProps });
  return /* @__PURE__ */ jsx(Wrapper, { children });
};

// src/react/hooks.ts
function renderHookWithContext(hook, options = {}) {
  const { providers = [], providerProps = {}, ...renderOptions } = options;
  const wrapper = createWrapper2({ providers, providerProps });
  return renderHook(hook, {
    wrapper,
    ...renderOptions
  });
}
var waitForHookUpdate = async (_result, condition, options = { timeout: 1e3, interval: 50 }) => {
  const { timeout, interval } = options;
  const startTime = Date.now();
  while (!condition() && Date.now() - startTime < timeout) {
    await new Promise((resolve) => setTimeout(resolve, interval));
  }
  if (!condition()) {
    throw new Error(`Hook update condition not met within ${timeout}ms`);
  }
};
var testHookWithAsyncState = async (hook, expectedData) => {
  const { result, rerender } = renderHook(hook);
  expect(result.current.loading).toBe(true);
  expect(result.current.data).toBe(null);
  expect(result.current.error).toBe(null);
  await waitForHookUpdate(result, () => !result.current.loading);
  expect(result.current.loading).toBe(false);
  expect(result.current.data).toEqual(expectedData);
  expect(result.current.error).toBe(null);
  return { result, rerender };
};
var testHookErrorState = async (hook, expectedError) => {
  const { result } = renderHook(hook);
  await waitForHookUpdate(result, () => result.current.error !== null);
  expect(result.current.loading).toBe(false);
  expect(result.current.data).toBe(null);
  expect(result.current.error?.message).toBe(expectedError);
};
var trackHookRenders = (hook) => {
  let renderCount = 0;
  const values = [];
  const TrackedHook = () => {
    const result2 = hook();
    renderCount++;
    values.push(result2);
    return result2;
  };
  const { result, rerender } = renderHook(TrackedHook);
  return {
    result,
    rerender,
    getRenderCount: () => renderCount,
    getValues: () => values,
    getLastValue: () => values[values.length - 1]
  };
};
var queries = {
  // Get element by data-testid with better error messages
  getElement: (testId, container) => {
    const withinContext = container ? within(container) : screen;
    try {
      return withinContext.getByTestId(testId);
    } catch (error) {
      throw new Error(`Element with testId="${testId}" not found`);
    }
  },
  // Find element with retry
  findElement: async (testId, options = { timeout: 3e3 }) => {
    return await screen.findByTestId(testId, {}, { timeout: options.timeout || 3e3 });
  },
  // Query element (returns null if not found)
  queryElement: (testId, container) => {
    const withinContext = container ? within(container) : screen;
    return withinContext.queryByTestId(testId);
  },
  // Get all elements by testId
  getAllElements: (testId, container) => {
    const withinContext = container ? within(container) : screen;
    return withinContext.getAllByTestId(testId);
  },
  // Text queries with normalization
  getByTextContent: (text, container) => {
    const withinContext = container ? within(container) : screen;
    return withinContext.getByText(text, {
      normalizer: (str) => str.trim().replace(/\s+/g, " ")
    });
  },
  // Role queries with accessible name
  getByRoleWithName: (role, name) => {
    return screen.getByRole(role, { name });
  },
  // Complex queries
  getButtonByText: (text) => {
    return screen.getByRole("button", { name: text });
  },
  getInputByLabel: (label) => {
    return screen.getByLabelText(label);
  },
  getLinkByText: (text) => {
    return screen.getByRole("link", { name: text });
  },
  // Form-specific queries
  getFormField: (name) => {
    return screen.getByRole("textbox", { name }) || screen.getByRole("combobox", { name }) || screen.getByRole("spinbutton", { name });
  },
  // Table queries
  getTableCell: (rowIndex, colIndex) => {
    const rows = screen.getAllByRole("row");
    const cells = within(rows[rowIndex]).getAllByRole("cell");
    return cells[colIndex];
  },
  // List queries
  getListItems: (listTestId) => {
    const list = screen.getByTestId(listTestId);
    return within(list).getAllByRole("listitem");
  }
};
var waitForElement = async (testId, options = { timeout: 3e3 }) => {
  return await waitFor(
    () => {
      const element = screen.getByTestId(testId);
      expect(element).toBeInTheDocument();
      return element;
    },
    options
  );
};
var waitForElementToBeRemoved = async (testId, options = { timeout: 3e3 }) => {
  return await waitFor(
    () => {
      expect(screen.queryByTestId(testId)).not.toBeInTheDocument();
    },
    options
  );
};
var waitForText = async (text, options = { timeout: 3e3 }) => {
  return await waitFor(
    () => {
      const element = screen.getByText(text);
      expect(element).toBeInTheDocument();
      return element;
    },
    options
  );
};
var assertElementExists = (testId) => {
  const element = screen.getByTestId(testId);
  expect(element).toBeInTheDocument();
  return element;
};
var assertElementNotExists = (testId) => {
  const element = screen.queryByTestId(testId);
  expect(element).not.toBeInTheDocument();
};
var assertElementVisible = (testId) => {
  const element = screen.getByTestId(testId);
  expect(element).toBeVisible();
  return element;
};
var assertElementHidden = (testId) => {
  const element = screen.getByTestId(testId);
  expect(element).not.toBeVisible();
  return element;
};
var getInteractiveElement = (testId, options = {}) => {
  const element = screen.getByTestId(testId);
  if (options.disabled !== void 0) {
    if (options.disabled) {
      expect(element).toBeDisabled();
    } else {
      expect(element).toBeEnabled();
    }
  }
  return element;
};

export { CombinedProviders, TestProviders, assertElementExists, assertElementHidden, assertElementNotExists, assertElementVisible, createContextWrapper, createMockProvider, createWrapper2 as createWrapper, getInteractiveElement, mockLocalStorage, queries, customRender as render, renderAndGetElements, renderHookWithContext, renderHookWithProviders, renderWithProviders, renderWithRouter, setupBrowserMocks, testHookErrorState, testHookWithAsyncState, trackHookRenders, waitForElement, waitForElementToBeRemoved, waitForHookUpdate, waitForText };
//# sourceMappingURL=index.js.map
//# sourceMappingURL=index.js.map