import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

@Entity('activity_logs')
export class ActivityLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Index()
  userId?: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  action: string;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  resource: string;

  @Column({
    type: 'enum',
    enum: ['user_action', 'system_event', 'api_call', 'security_event'],
    default: 'user_action',
  })
  @Index()
  type: 'user_action' | 'system_event' | 'api_call' | 'security_event';

  @Column({ type: 'jsonb', default: {} })
  metadata: Record<string, any>;

  @Column({ type: 'varchar', length: 45, nullable: true })
  @Index()
  ipAddress?: string;

  @Column({ type: 'text', nullable: true })
  userAgent?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  sessionId?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  requestId?: string;

  @CreateDateColumn()
  @Index()
  timestamp: Date;

  @Column({
    type: 'enum',
    enum: ['success', 'failure', 'warning'],
    default: 'success',
  })
  @Index()
  status: 'success' | 'failure' | 'warning';

  @Column({ type: 'int', nullable: true })
  durationMs?: number;

  @Column({ type: 'varchar', length: 10, nullable: true })
  httpMethod?: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  endpoint?: string;

  @Column({ type: 'int', nullable: true })
  statusCode?: number;

  @Column({ type: 'int', nullable: true })
  responseSize?: number;

  @Column({ type: 'text', nullable: true })
  errorMessage?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  deviceType?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  browser?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  operatingSystem?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Index()
  geolocation?: string;

  @Column({ type: 'boolean', default: false })
  isSensitive: boolean;

  @Column({ type: 'jsonb', nullable: true })
  additionalContext?: Record<string, any>;
}