{"version": 3, "sources": ["../../src/react/setup.ts", "../../src/react/render.tsx", "../../src/react/providers.tsx", "../../src/react/hooks.ts", "../../src/react/queries.ts"], "names": ["rtlRender", "createWrapper", "jsx", "Fragment", "result"], "mappings": ";;;;;;;;;;;;;AAKA,SAAA,CAAU,MAAM;AACd,EAAQ,OAAA,EAAA;AACV,CAAC,CAAA;AAGD,SAAA,CAAU,MAAM;AAEd,EAAO,MAAA,CAAA,cAAA,CAAe,QAAQ,YAAc,EAAA;AAAA,IAC1C,QAAU,EAAA,IAAA;AAAA,IACV,OAAO,EAAG,CAAA,EAAA,EAAK,CAAA,kBAAA,CAAmB,CAAC,KAAmB,MAAA;AAAA,MACpD,OAAS,EAAA,KAAA;AAAA,MACT,KAAO,EAAA,KAAA;AAAA,MACP,QAAU,EAAA,IAAA;AAAA,MACV,WAAA,EAAa,GAAG,EAAG,EAAA;AAAA;AAAA,MACnB,cAAA,EAAgB,GAAG,EAAG,EAAA;AAAA;AAAA,MACtB,gBAAA,EAAkB,GAAG,EAAG,EAAA;AAAA,MACxB,mBAAA,EAAqB,GAAG,EAAG,EAAA;AAAA,MAC3B,aAAA,EAAe,GAAG,EAAG;AAAA,KACrB,CAAA;AAAA,GACH,CAAA;AAGD,EAAA,MAAA,CAAO,oBAAuB,GAAA,EAAA,CAAG,EAAG,EAAA,CAAE,mBAAmB,OAAO;AAAA,IAC9D,OAAA,EAAS,GAAG,EAAG,EAAA;AAAA,IACf,SAAA,EAAW,GAAG,EAAG,EAAA;AAAA,IACjB,UAAA,EAAY,GAAG,EAAG;AAAA,GAClB,CAAA,CAAA;AAGF,EAAA,MAAA,CAAO,cAAiB,GAAA,EAAA,CAAG,EAAG,EAAA,CAAE,mBAAmB,OAAO;AAAA,IACxD,OAAA,EAAS,GAAG,EAAG,EAAA;AAAA,IACf,SAAA,EAAW,GAAG,EAAG,EAAA;AAAA,IACjB,UAAA,EAAY,GAAG,EAAG;AAAA,GAClB,CAAA,CAAA;AAGF,EAAO,MAAA,CAAA,QAAA,GAAW,GAAG,EAAG,EAAA;AAGxB,EAAI,IAAA,CAAC,OAAO,KAAO,EAAA;AACjB,IAAO,MAAA,CAAA,KAAA,GAAQ,GAAG,EAAG,EAAA;AAAA;AAIvB,EAAA,MAAM,gBAAgB,OAAQ,CAAA,KAAA;AAC9B,EAAQ,OAAA,CAAA,KAAA,GAAQ,IAAI,IAAgB,KAAA;AAClC,IACE,IAAA,OAAO,KAAK,CAAC,CAAA,KAAM,aAClB,IAAK,CAAA,CAAC,CAAE,CAAA,QAAA,CAAS,0BAA0B,CAAA,IAC1C,KAAK,CAAC,CAAA,CAAE,SAAS,kCAAkC,CAAA,IACnD,KAAK,CAAC,CAAA,CAAE,QAAS,CAAA,mDAAmD,CACtE,CAAA,EAAA;AACA,MAAA;AAAA;AAEF,IAAc,aAAA,CAAA,IAAA,CAAK,OAAS,EAAA,GAAG,IAAI,CAAA;AAAA,GACrC;AACF,CAAC,CAAA;AAGM,IAAM,mBAAmB,MAAM;AACpC,EAAA,MAAM,UAAkC,EAAC;AAEzC,EAAO,OAAA;AAAA,IACL,OAAA,EAAS,GAAG,EAAG,CAAA,CAAC,QAAgB,OAAQ,CAAA,GAAG,KAAK,IAAI,CAAA;AAAA,IACpD,OAAS,EAAA,EAAA,CAAG,EAAG,CAAA,CAAC,KAAa,KAAkB,KAAA;AAC7C,MAAA,OAAA,CAAQ,GAAG,CAAI,GAAA,KAAA;AAAA,KAChB,CAAA;AAAA,IACD,UAAY,EAAA,EAAA,CAAG,EAAG,CAAA,CAAC,GAAgB,KAAA;AACjC,MAAA,OAAO,QAAQ,GAAG,CAAA;AAAA,KACnB,CAAA;AAAA,IACD,KAAA,EAAO,EAAG,CAAA,EAAA,CAAG,MAAM;AACjB,MAAO,MAAA,CAAA,IAAA,CAAK,OAAO,CAAE,CAAA,OAAA,CAAQ,SAAO,OAAO,OAAA,CAAQ,GAAG,CAAC,CAAA;AAAA,KACxD,CAAA;AAAA,IACD,IAAI,MAAS,GAAA;AACX,MAAO,OAAA,MAAA,CAAO,IAAK,CAAA,OAAO,CAAE,CAAA,MAAA;AAAA,KAC9B;AAAA,IACA,GAAK,EAAA,EAAA,CAAG,EAAG,CAAA,CAAC,KAAkB,KAAA;AAC5B,MAAM,MAAA,IAAA,GAAO,MAAO,CAAA,IAAA,CAAK,OAAO,CAAA;AAChC,MAAO,OAAA,IAAA,CAAK,KAAK,CAAK,IAAA,IAAA;AAAA,KACvB;AAAA,GACH;AACF;AAGO,IAAM,oBAAoB,MAAM;AACrC,EAAA,MAAM,eAAe,gBAAiB,EAAA;AACtC,EAAA,MAAM,iBAAiB,gBAAiB,EAAA;AAExC,EAAO,MAAA,CAAA,cAAA,CAAe,QAAQ,cAAgB,EAAA;AAAA,IAC5C,KAAO,EAAA,YAAA;AAAA,IACP,QAAU,EAAA;AAAA,GACX,CAAA;AAED,EAAO,MAAA,CAAA,cAAA,CAAe,QAAQ,gBAAkB,EAAA;AAAA,IAC9C,KAAO,EAAA,cAAA;AAAA,IACP,QAAU,EAAA;AAAA,GACX,CAAA;AAGD,EAAA,MAAA,CAAO,GAAM,GAAA,GAAA;AACb,EAAA,MAAA,CAAO,eAAkB,GAAA,eAAA;AAGzB,EAAO,MAAA,CAAA,cAAA,CAAe,QAAQ,QAAU,EAAA;AAAA,IACtC,KAAO,EAAA;AAAA,MACL,YAAY,MAAM,sCAAA;AAAA,MAClB,eAAA,EAAiB,CAAC,GAAa,KAAA;AAC7B,QAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,GAAA,CAAI,QAAQ,CAAK,EAAA,EAAA;AACnC,UAAA,GAAA,CAAI,CAAC,CAAI,GAAA,IAAA,CAAK,MAAM,IAAK,CAAA,MAAA,KAAW,GAAG,CAAA;AAAA;AAEzC,QAAO,OAAA,GAAA;AAAA;AACT;AACF,GACD,CAAA;AACH;AClHA,IAAI,YAAA;AAEJ,IAAI;AACF,EAAM,MAAA,YAAA,GAAe,UAAQ,kBAAkB,CAAA;AAC/C,EAAA,YAAA,GAAe,YAAa,CAAA,YAAA;AAC9B,CAAA,CAAA,OAAS,KAAO,EAAA;AAEd,EAAA,YAAA,GAAe,CAAC,EAAE,QAAS,EAAA,qCAAkC,QAAS,EAAA,CAAA;AACxE;AAqBA,IAAM,aAAgB,GAAA,CAAC,OAA+B,GAAA,EAAO,KAAA;AAC3D,EAAA,MAAM,EAAE,WAAA,GAAc,EAAC,EAAG,KAAQ,GAAA,GAAA,EAAK,SAAY,GAAA,EAAI,EAAA,aAAA,GAAgB,EAAC,EAAM,GAAA,OAAA;AAE9E,EAAO,OAAA,CAAC,EAAE,QAAA,EAAwC,KAAA;AAEhD,IAAI,IAAA,KAAA,IAAS,WAAY,CAAA,cAAA,KAAmB,MAAW,EAAA;AACrD,MAAY,WAAA,CAAA,cAAA,GAAiB,CAAC,KAAK,CAAA;AAAA;AAIrC,IAAA,IAAI,OAAU,GAAA,QAAA;AAGd,IAAA,OAAA,mBAAW,GAAA,CAAA,YAAA,EAAA,EAAc,GAAG,WAAA,EAAc,QAAQ,EAAA,OAAA,EAAA,CAAA;AAGlD,IAAA,SAAA,CAAU,OAAQ,EAAA,CAAE,OAAQ,CAAA,CAAC,QAAa,KAAA;AACxC,MAAA,MAAM,QAAQ,aAAc,CAAA,QAAA,CAAS,eAAe,QAAS,CAAA,IAAI,KAAK,EAAC;AACvE,MAAA,OAAA,mBAAW,GAAA,CAAA,QAAA,EAAA,EAAU,GAAG,KAAA,EAAQ,QAAQ,EAAA,OAAA,EAAA,CAAA;AAAA,KACzC,CAAA;AAED,IAAO,OAAA,OAAA;AAAA,GACT;AACF,CAAA;AAGA,SAAS,YACP,CAAA,EAAA,EACA,OAA+B,GAAA,EAC8B,EAAA;AAC7D,EAAA,MAAM,EAAE,QAAA,EAAU,GAAG,aAAA,EAAkB,GAAA,OAAA;AAGvC,EAAM,MAAA,IAAA,GAAO,UAAU,KAAM,EAAA;AAG7B,EAAA,IAAI,QAAU,EAAA;AACZ,IAAO,MAAA,CAAA,OAAA,CAAQ,QAAQ,CAAE,CAAA,OAAA,CAAQ,CAAC,CAAC,GAAA,EAAK,KAAK,CAAM,KAAA;AACjD,MAAC,MAAA,CAAe,GAAG,CAAI,GAAA,KAAA;AAAA,KACxB,CAAA;AAAA;AAGH,EAAM,MAAA,OAAA,GAAU,cAAc,OAAO,CAAA;AAErC,EAAA,MAAM,eAAeA,MAAU,CAAA,EAAA,EAAI,EAAE,OAAS,EAAA,GAAG,eAAe,CAAA;AAEhE,EAAO,OAAA;AAAA,IACL,GAAG,YAAA;AAAA,IACH;AAAA,GACF;AACF;AAMO,IAAM,gBAAmB,GAAA,CAC9B,EACA,EAAA,WAAA,EACA,aACgE,KAAA;AAChE,EAAM,MAAA,OAAA,GAA+B,EAAE,GAAG,aAAc,EAAA;AACxD,EAAA,IAAI,WAAa,EAAA;AACf,IAAA,OAAA,CAAQ,WAAc,GAAA,WAAA;AAAA;AAExB,EAAO,OAAA,YAAA,CAAa,IAAI,OAAO,CAAA;AACjC;AAEO,IAAM,sBAAsB,CACjC,EAAA,EACA,SAA8C,GAAA,IAC9C,aACgE,KAAA;AAChE,EAAA,OAAO,aAAa,EAAI,EAAA,EAAE,GAAG,aAAA,EAAe,WAAW,CAAA;AACzD;AAGa,IAAA,uBAAA,GAA0B,CACrC,IAAA,EACA,OACqG,KAAA;AACrG,EAAA,MAAM,aAAgB,GAAA,CAAC,EAAE,SAAA,EAAuC,KAAA;AAC9D,IAAM,MAAA,MAAA,GAAS,KAAK,SAAS,CAAA;AAC7B,IAAA,2BAAQ,KAAI,EAAA,EAAA,aAAA,EAAY,eAAe,QAAK,EAAA,IAAA,CAAA,SAAA,CAAU,MAAM,CAAE,EAAA,CAAA;AAAA,GAChE;AAEA,EAAO,OAAA;AAAA,IACL,GAAG,6BAAc,GAAA,CAAA,aAAA,EAAA,EAAc,WAAW,EAAC,EAAa,GAAI,OAAO,CAAA;AAAA,IACnE,MAAQ,EAAA;AAAA,MACN,OAAA,EAAS,IAAK,CAAA,EAAY;AAAA;AAC5B,GACF;AACF;AAGa,IAAA,oBAAA,GAAuB,CAClC,EAAA,EACA,OACgE,KAAA;AAChE,EAAM,MAAA,YAAA,GAAe,YAAa,CAAA,EAAA,EAAI,OAAO,CAAA;AAE7C,EAAO,OAAA;AAAA,IACL,GAAG;AAAA,GACL;AACF;ACnIO,IAAMC,cAAgB,GAAA,CAAC,OAA2B,GAAA,EAAO,KAAA;AAC9D,EAAA,MAAM,EAAE,SAAY,GAAA,IAAI,aAAgB,GAAA,IAAO,GAAA,OAAA;AAE/C,EAAO,OAAA,CAAC,EAAE,QAAA,EAAwC,KAAA;AAChD,IAAA,IAAI,OAAU,GAAA,QAAA;AAGd,IAAA,SAAA,CAAU,OAAQ,EAAA,CAAE,OAAQ,CAAA,CAAC,QAAa,KAAA;AACxC,MAAA,MAAM,QAAQ,aAAc,CAAA,QAAA,CAAS,eAAe,QAAS,CAAA,IAAI,KAAK,EAAC;AACvE,MAAA,OAAA,mBAAUC,GAAAA,CAAC,QAAU,EAAA,EAAA,GAAG,OAAQ,QAAQ,EAAA,OAAA,EAAA,CAAA;AAAA,KACzC,CAAA;AAED,IAAA,uBAAOA,GAAAA,CAAAC,QAAA,EAAA,EAAG,QAAQ,EAAA,OAAA,EAAA,CAAA;AAAA,GACpB;AACF;AAGO,IAAM,aAAmD,GAAA,CAAC,EAAE,QAAA,EAAe,KAAA;AAChF,EAAA,uBAAOD,GAAAA,CAAAC,QAAA,EAAA,EAAG,QAAS,EAAA,CAAA;AACrB;AAGa,IAAA,oBAAA,GAAuB,CAClC,OAAA,EACA,KACG,KAAA;AACH,EAAO,OAAA,CAAC,EAAE,QAAA,EACR,qBAAAD,IAAC,OAAQ,CAAA,QAAA,EAAR,EAAiB,KAAA,EAAe,QAAS,EAAA,CAAA;AAE9C;AAGa,IAAA,kBAAA,GAAqB,CAChC,IAAA,EACA,YACG,KAAA;AACH,EAAM,MAAA,OAAA,GAAU,KAAM,CAAA,aAAA,CAAiB,YAAY,CAAA;AAEnD,EAAA,MAAM,WAAkE,CAAC;AAAA,IACvE,QAAA;AAAA,IACA,QAAQ;AAAC,GACL,KAAA;AACJ,IAAA,MAAM,WAAc,GAAA,EAAE,GAAG,YAAA,EAAc,GAAG,KAAM,EAAA;AAChD,IAAA,uBAAOA,GAAC,CAAA,OAAA,CAAQ,UAAR,EAAiB,KAAA,EAAO,aAAc,QAAS,EAAA,CAAA;AAAA,GACzD;AAEA,EAAA,QAAA,CAAS,WAAc,GAAA,IAAA;AAEvB,EAAO,OAAA,EAAE,SAAS,QAAS,EAAA;AAC7B;AAGa,IAAA,iBAAA,GAIR,CAAC,EAAE,QAAU,EAAA,SAAA,GAAY,EAAI,EAAA,aAAA,GAAgB,EAAC,EAAQ,KAAA;AACzD,EAAA,MAAM,OAAUD,GAAAA,cAAAA,CAAc,EAAE,SAAA,EAAW,eAAe,CAAA;AAC1D,EAAO,uBAAAC,GAAC,CAAA,OAAA,EAAA,EAAS,QAAS,EAAA,CAAA;AAC5B;;;ACzDO,SAAS,qBACd,CAAA,IAAA,EACA,OAA2C,GAAA,EACR,EAAA;AACnC,EAAM,MAAA,EAAE,YAAY,EAAC,EAAG,gBAAgB,EAAC,EAAG,GAAG,aAAA,EAAkB,GAAA,OAAA;AAEjE,EAAA,MAAM,OAAUD,GAAAA,cAAAA,CAAc,EAAE,SAAA,EAAW,eAAe,CAAA;AAE1D,EAAA,OAAO,WAAW,IAAM,EAAA;AAAA,IACtB,OAAA;AAAA,IACA,GAAG;AAAA,GACJ,CAAA;AACH;AAGa,IAAA,iBAAA,GAAoB,OAC/B,OAAA,EACA,SACA,EAAA,OAAA,GAAU,EAAE,OAAS,EAAA,GAAA,EAAM,QAAU,EAAA,EAAA,EAClC,KAAA;AACH,EAAM,MAAA,EAAE,OAAS,EAAA,QAAA,EAAa,GAAA,OAAA;AAC9B,EAAM,MAAA,SAAA,GAAY,KAAK,GAAI,EAAA;AAE3B,EAAA,OAAO,CAAC,SAAU,EAAA,IAAK,KAAK,GAAI,EAAA,GAAI,YAAY,OAAS,EAAA;AACvD,IAAA,MAAM,IAAI,OAAQ,CAAA,CAAA,OAAA,KAAW,UAAW,CAAA,OAAA,EAAS,QAAQ,CAAC,CAAA;AAAA;AAG5D,EAAI,IAAA,CAAC,WAAa,EAAA;AAChB,IAAA,MAAM,IAAI,KAAA,CAAM,CAAwC,qCAAA,EAAA,OAAO,CAAI,EAAA,CAAA,CAAA;AAAA;AAEvE;AAGa,IAAA,sBAAA,GAAyB,OACpC,IAAA,EACA,YACG,KAAA;AACH,EAAA,MAAM,EAAE,MAAA,EAAQ,QAAS,EAAA,GAAI,WAAW,IAAI,CAAA;AAG5C,EAAA,MAAA,CAAO,MAAO,CAAA,OAAA,CAAQ,OAAO,CAAA,CAAE,KAAK,IAAI,CAAA;AACxC,EAAA,MAAA,CAAO,MAAO,CAAA,OAAA,CAAQ,IAAI,CAAA,CAAE,KAAK,IAAI,CAAA;AACrC,EAAA,MAAA,CAAO,MAAO,CAAA,OAAA,CAAQ,KAAK,CAAA,CAAE,KAAK,IAAI,CAAA;AAGtC,EAAA,MAAM,kBAAkB,MAAQ,EAAA,MAAM,CAAC,MAAA,CAAO,QAAQ,OAAO,CAAA;AAG7D,EAAA,MAAA,CAAO,MAAO,CAAA,OAAA,CAAQ,OAAO,CAAA,CAAE,KAAK,KAAK,CAAA;AACzC,EAAA,MAAA,CAAO,MAAO,CAAA,OAAA,CAAQ,IAAI,CAAA,CAAE,QAAQ,YAAY,CAAA;AAChD,EAAA,MAAA,CAAO,MAAO,CAAA,OAAA,CAAQ,KAAK,CAAA,CAAE,KAAK,IAAI,CAAA;AAEtC,EAAO,OAAA,EAAE,QAAQ,QAAS,EAAA;AAC5B;AAEa,IAAA,kBAAA,GAAqB,OAChC,IAAA,EACA,aACG,KAAA;AACH,EAAA,MAAM,EAAE,MAAA,EAAW,GAAA,UAAA,CAAW,IAAI,CAAA;AAGlC,EAAA,MAAM,kBAAkB,MAAQ,EAAA,MAAM,MAAO,CAAA,OAAA,CAAQ,UAAU,IAAI,CAAA;AAEnE,EAAA,MAAA,CAAO,MAAO,CAAA,OAAA,CAAQ,OAAO,CAAA,CAAE,KAAK,KAAK,CAAA;AACzC,EAAA,MAAA,CAAO,MAAO,CAAA,OAAA,CAAQ,IAAI,CAAA,CAAE,KAAK,IAAI,CAAA;AACrC,EAAA,MAAA,CAAO,OAAO,OAAQ,CAAA,KAAA,EAAO,OAAO,CAAA,CAAE,KAAK,aAAa,CAAA;AAC1D;AAGa,IAAA,gBAAA,GAAmB,CAAK,IAAkB,KAAA;AACrD,EAAA,IAAI,WAAc,GAAA,CAAA;AAClB,EAAA,MAAM,SAAc,EAAC;AAErB,EAAA,MAAM,cAAc,MAAM;AACxB,IAAA,MAAMG,UAAS,IAAK,EAAA;AACpB,IAAA,WAAA,EAAA;AACA,IAAA,MAAA,CAAO,KAAKA,OAAM,CAAA;AAClB,IAAOA,OAAAA,OAAAA;AAAA,GACT;AAEA,EAAA,MAAM,EAAE,MAAA,EAAQ,QAAS,EAAA,GAAI,WAAW,WAAW,CAAA;AAEnD,EAAO,OAAA;AAAA,IACL,MAAA;AAAA,IACA,QAAA;AAAA,IACA,gBAAgB,MAAM,WAAA;AAAA,IACtB,WAAW,MAAM,MAAA;AAAA,IACjB,YAAc,EAAA,MAAM,MAAO,CAAA,MAAA,CAAO,SAAS,CAAC;AAAA,GAC9C;AACF;AChGO,IAAM,OAAU,GAAA;AAAA;AAAA,EAErB,UAAA,EAAY,CAAC,MAAA,EAAgB,SAA4B,KAAA;AACvD,IAAA,MAAM,aAAgB,GAAA,SAAA,GAAY,MAAO,CAAA,SAAS,CAAI,GAAA,MAAA;AACtD,IAAI,IAAA;AACF,MAAO,OAAA,aAAA,CAAc,YAAY,MAAM,CAAA;AAAA,aAChC,KAAO,EAAA;AACd,MAAA,MAAM,IAAI,KAAA,CAAM,CAAwB,qBAAA,EAAA,MAAM,CAAa,WAAA,CAAA,CAAA;AAAA;AAC7D,GACF;AAAA;AAAA,EAGA,aAAa,OAAO,MAAA,EAAgB,UAAgC,EAAE,OAAA,EAAS,KAAW,KAAA;AACxF,IAAO,OAAA,MAAM,MAAO,CAAA,YAAA,CAAa,MAAQ,EAAA,EAAI,EAAA,EAAE,OAAS,EAAA,OAAA,CAAQ,OAAW,IAAA,GAAA,EAAM,CAAA;AAAA,GACnF;AAAA;AAAA,EAGA,YAAA,EAAc,CAAC,MAAA,EAAgB,SAA4B,KAAA;AACzD,IAAA,MAAM,aAAgB,GAAA,SAAA,GAAY,MAAO,CAAA,SAAS,CAAI,GAAA,MAAA;AACtD,IAAO,OAAA,aAAA,CAAc,cAAc,MAAM,CAAA;AAAA,GAC3C;AAAA;AAAA,EAGA,cAAA,EAAgB,CAAC,MAAA,EAAgB,SAA4B,KAAA;AAC3D,IAAA,MAAM,aAAgB,GAAA,SAAA,GAAY,MAAO,CAAA,SAAS,CAAI,GAAA,MAAA;AACtD,IAAO,OAAA,aAAA,CAAc,eAAe,MAAM,CAAA;AAAA,GAC5C;AAAA;AAAA,EAGA,gBAAA,EAAkB,CAAC,IAAA,EAAuB,SAA4B,KAAA;AACpE,IAAA,MAAM,aAAgB,GAAA,SAAA,GAAY,MAAO,CAAA,SAAS,CAAI,GAAA,MAAA;AACtD,IAAO,OAAA,aAAA,CAAc,UAAU,IAAM,EAAA;AAAA,MACnC,UAAA,EAAY,CAAC,GAAQ,KAAA,GAAA,CAAI,MAAO,CAAA,OAAA,CAAQ,QAAQ,GAAG;AAAA,KACpD,CAAA;AAAA,GACH;AAAA;AAAA,EAGA,iBAAA,EAAmB,CAAC,IAAA,EAAc,IAA0B,KAAA;AAC1D,IAAA,OAAO,MAAO,CAAA,SAAA,CAAU,IAAM,EAAA,EAAE,MAAM,CAAA;AAAA,GACxC;AAAA;AAAA,EAGA,eAAA,EAAiB,CAAC,IAA0B,KAAA;AAC1C,IAAA,OAAO,OAAO,SAAU,CAAA,QAAA,EAAU,EAAE,IAAA,EAAM,MAAM,CAAA;AAAA,GAClD;AAAA,EAEA,eAAA,EAAiB,CAAC,KAA2B,KAAA;AAC3C,IAAO,OAAA,MAAA,CAAO,eAAe,KAAK,CAAA;AAAA,GACpC;AAAA,EAEA,aAAA,EAAe,CAAC,IAA0B,KAAA;AACxC,IAAA,OAAO,OAAO,SAAU,CAAA,MAAA,EAAQ,EAAE,IAAA,EAAM,MAAM,CAAA;AAAA,GAChD;AAAA;AAAA,EAGA,YAAA,EAAc,CAAC,IAAiB,KAAA;AAC9B,IAAA,OAAO,OAAO,SAAU,CAAA,SAAA,EAAW,EAAE,IAAK,EAAC,KACpC,MAAO,CAAA,SAAA,CAAU,YAAY,EAAE,IAAA,EAAM,CACrC,IAAA,MAAA,CAAO,UAAU,YAAc,EAAA,EAAE,MAAM,CAAA;AAAA,GAChD;AAAA;AAAA,EAGA,YAAA,EAAc,CAAC,QAAA,EAAkB,QAAqB,KAAA;AACpD,IAAM,MAAA,IAAA,GAAO,MAAO,CAAA,YAAA,CAAa,KAAK,CAAA;AACtC,IAAA,MAAM,QAAQ,MAAO,CAAA,IAAA,CAAK,QAAQ,CAAC,CAAA,CAAE,aAAa,MAAM,CAAA;AACxD,IAAA,OAAO,MAAM,QAAQ,CAAA;AAAA,GACvB;AAAA;AAAA,EAGA,YAAA,EAAc,CAAC,UAAuB,KAAA;AACpC,IAAM,MAAA,IAAA,GAAO,MAAO,CAAA,WAAA,CAAY,UAAU,CAAA;AAC1C,IAAA,OAAO,MAAO,CAAA,IAAI,CAAE,CAAA,YAAA,CAAa,UAAU,CAAA;AAAA;AAE/C;AAGO,IAAM,iBAAiB,OAC5B,MAAA,EACA,UAAU,EAAE,OAAA,EAAS,KAClB,KAAA;AACH,EAAA,OAAO,MAAM,OAAA;AAAA,IACX,MAAM;AACJ,MAAM,MAAA,OAAA,GAAU,MAAO,CAAA,WAAA,CAAY,MAAM,CAAA;AACzC,MAAO,MAAA,CAAA,OAAO,EAAE,iBAAkB,EAAA;AAClC,MAAO,OAAA,OAAA;AAAA,KACT;AAAA,IACA;AAAA,GACF;AACF;AAEO,IAAM,4BAA4B,OACvC,MAAA,EACA,UAAU,EAAE,OAAA,EAAS,KAClB,KAAA;AACH,EAAA,OAAO,MAAM,OAAA;AAAA,IACX,MAAM;AACJ,MAAA,MAAA,CAAO,OAAO,aAAc,CAAA,MAAM,CAAC,CAAA,CAAE,IAAI,iBAAkB,EAAA;AAAA,KAC7D;AAAA,IACA;AAAA,GACF;AACF;AAEO,IAAM,cAAc,OACzB,IAAA,EACA,UAAU,EAAE,OAAA,EAAS,KAClB,KAAA;AACH,EAAA,OAAO,MAAM,OAAA;AAAA,IACX,MAAM;AACJ,MAAM,MAAA,OAAA,GAAU,MAAO,CAAA,SAAA,CAAU,IAAI,CAAA;AACrC,MAAO,MAAA,CAAA,OAAO,EAAE,iBAAkB,EAAA;AAClC,MAAO,OAAA,OAAA;AAAA,KACT;AAAA,IACA;AAAA,GACF;AACF;AAGa,IAAA,mBAAA,GAAsB,CAAC,MAAmB,KAAA;AACrD,EAAM,MAAA,OAAA,GAAU,MAAO,CAAA,WAAA,CAAY,MAAM,CAAA;AACzC,EAAO,MAAA,CAAA,OAAO,EAAE,iBAAkB,EAAA;AAClC,EAAO,OAAA,OAAA;AACT;AAEa,IAAA,sBAAA,GAAyB,CAAC,MAAmB,KAAA;AACxD,EAAM,MAAA,OAAA,GAAU,MAAO,CAAA,aAAA,CAAc,MAAM,CAAA;AAC3C,EAAO,MAAA,CAAA,OAAO,CAAE,CAAA,GAAA,CAAI,iBAAkB,EAAA;AACxC;AAEa,IAAA,oBAAA,GAAuB,CAAC,MAAmB,KAAA;AACtD,EAAM,MAAA,OAAA,GAAU,MAAO,CAAA,WAAA,CAAY,MAAM,CAAA;AACzC,EAAO,MAAA,CAAA,OAAO,EAAE,WAAY,EAAA;AAC5B,EAAO,OAAA,OAAA;AACT;AAEa,IAAA,mBAAA,GAAsB,CAAC,MAAmB,KAAA;AACrD,EAAM,MAAA,OAAA,GAAU,MAAO,CAAA,WAAA,CAAY,MAAM,CAAA;AACzC,EAAO,MAAA,CAAA,OAAO,CAAE,CAAA,GAAA,CAAI,WAAY,EAAA;AAChC,EAAO,OAAA,OAAA;AACT;AAGO,IAAM,qBAAwB,GAAA,CACnC,MACA,EAAA,OAAA,GAAkC,EAC/B,KAAA;AACH,EAAM,MAAA,OAAA,GAAU,MAAO,CAAA,WAAA,CAAY,MAAM,CAAA;AAEzC,EAAI,IAAA,OAAA,CAAQ,aAAa,MAAW,EAAA;AAClC,IAAA,IAAI,QAAQ,QAAU,EAAA;AACpB,MAAO,MAAA,CAAA,OAAO,EAAE,YAAa,EAAA;AAAA,KACxB,MAAA;AACL,MAAO,MAAA,CAAA,OAAO,EAAE,WAAY,EAAA;AAAA;AAC9B;AAGF,EAAO,OAAA,OAAA;AACT", "file": "index.js", "sourcesContent": ["import '@testing-library/jest-dom/vitest'\nimport { cleanup } from '@testing-library/react'\nimport { afterEach, beforeAll, vi } from 'vitest'\n\n// Auto cleanup after each test\nafterEach(() => {\n  cleanup()\n})\n\n// Setup global mocks\nbeforeAll(() => {\n  // Mock window.matchMedia\n  Object.defineProperty(window, 'matchMedia', {\n    writable: true,\n    value: vi.fn().mockImplementation((query: string) => ({\n      matches: false,\n      media: query,\n      onchange: null,\n      addListener: vi.fn(), // deprecated\n      removeListener: vi.fn(), // deprecated\n      addEventListener: vi.fn(),\n      removeEventListener: vi.fn(),\n      dispatchEvent: vi.fn(),\n    })),\n  })\n\n  // Mock IntersectionObserver\n  global.IntersectionObserver = vi.fn().mockImplementation(() => ({\n    observe: vi.fn(),\n    unobserve: vi.fn(),\n    disconnect: vi.fn(),\n  }))\n\n  // Mock ResizeObserver\n  global.ResizeObserver = vi.fn().mockImplementation(() => ({\n    observe: vi.fn(),\n    unobserve: vi.fn(),\n    disconnect: vi.fn(),\n  }))\n\n  // Mock scrollTo\n  window.scrollTo = vi.fn()\n\n  // Mock fetch if not available\n  if (!global.fetch) {\n    global.fetch = vi.fn()\n  }\n\n  // Suppress console errors in tests by default\n  const originalError = console.error\n  console.error = (...args: any[]) => {\n    if (\n      typeof args[0] === 'string' &&\n      (args[0].includes('Warning: ReactDOM.render') ||\n        args[0].includes('Warning: An invalid form control') ||\n        args[0].includes('Not implemented: HTMLFormElement.prototype.submit'))\n    ) {\n      return\n    }\n    originalError.call(console, ...args)\n  }\n})\n\n// Common test utilities\nexport const mockLocalStorage = () => {\n  const storage: Record<string, string> = {}\n  \n  return {\n    getItem: vi.fn((key: string) => storage[key] || null),\n    setItem: vi.fn((key: string, value: string) => {\n      storage[key] = value\n    }),\n    removeItem: vi.fn((key: string) => {\n      delete storage[key]\n    }),\n    clear: vi.fn(() => {\n      Object.keys(storage).forEach(key => delete storage[key])\n    }),\n    get length() {\n      return Object.keys(storage).length\n    },\n    key: vi.fn((index: number) => {\n      const keys = Object.keys(storage)\n      return keys[index] || null\n    }),\n  }\n}\n\n// Mock common browser APIs\nexport const setupBrowserMocks = () => {\n  const localStorage = mockLocalStorage()\n  const sessionStorage = mockLocalStorage()\n\n  Object.defineProperty(window, 'localStorage', {\n    value: localStorage,\n    writable: true,\n  })\n\n  Object.defineProperty(window, 'sessionStorage', {\n    value: sessionStorage,\n    writable: true,\n  })\n\n  // Mock URL\n  global.URL = URL\n  global.URLSearchParams = URLSearchParams\n\n  // Mock crypto\n  Object.defineProperty(window, 'crypto', {\n    value: {\n      randomUUID: () => '12345678-1234-1234-1234-123456789012',\n      getRandomValues: (arr: any) => {\n        for (let i = 0; i < arr.length; i++) {\n          arr[i] = Math.floor(Math.random() * 256)\n        }\n        return arr\n      },\n    },\n  })\n}", "import React, { ReactElement, ReactNode } from 'react'\nimport { render as rtlR<PERSON>, RenderOptions, RenderResult } from '@testing-library/react'\nimport userEvent from '@testing-library/user-event'\n\n// Conditional import for react-router-dom to handle DTS build issues\nlet MemoryRouter: any\n\ntry {\n  const routerModule = require('react-router-dom')\n  MemoryRouter = routerModule.MemoryRouter\n} catch (error) {\n  // Fallback when react-router-dom is not available\n  MemoryRouter = ({ children }: { children: ReactNode }) => <>{children}</>\n}\n\ninterface MemoryRouterPropsType {\n  initialEntries?: string[]\n  initialIndex?: number\n  [key: string]: any\n}\n\n// Types for custom render options\ninterface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {\n  // Router options\n  routerProps?: MemoryRouterPropsType\n  route?: string\n  // Provider options\n  providers?: Array<React.ComponentType<{ children: ReactNode }>>\n  providerProps?: Record<string, any>\n  // Other options\n  mockData?: Record<string, any>\n}\n\n// Create a custom wrapper with all providers\nconst createWrapper = (options: CustomRenderOptions = {}) => {\n  const { routerProps = {}, route = '/', providers = [], providerProps = {} } = options\n\n  return ({ children }: { children: ReactNode }) => {\n    // Set initial route if provided\n    if (route && routerProps.initialEntries === undefined) {\n      routerProps.initialEntries = [route]\n    }\n\n    // Wrap with all providers\n    let wrapped = children\n    \n    // Always wrap with MemoryRouter for testing\n    wrapped = <MemoryRouter {...routerProps}>{wrapped}</MemoryRouter>\n\n    // Wrap with additional providers in reverse order\n    providers.reverse().forEach((Provider) => {\n      const props = providerProps[Provider.displayName || Provider.name] || {}\n      wrapped = <Provider {...props}>{wrapped}</Provider>\n    })\n\n    return wrapped\n  }\n}\n\n// Custom render function\nfunction customRender(\n  ui: ReactElement,\n  options: CustomRenderOptions = {}\n): RenderResult & { user: ReturnType<typeof userEvent.setup> } {\n  const { mockData, ...renderOptions } = options\n\n  // Setup user event\n  const user = userEvent.setup()\n\n  // Apply mock data if provided\n  if (mockData) {\n    Object.entries(mockData).forEach(([key, value]) => {\n      (window as any)[key] = value\n    })\n  }\n\n  const wrapper = createWrapper(options)\n  \n  const renderResult = rtlRender(ui, { wrapper, ...renderOptions })\n\n  return {\n    ...renderResult,\n    user,\n  }\n}\n\n// Override the render function\nexport { customRender as render }\n\n// Additional render helpers\nexport const renderWithRouter = (\n  ui: ReactElement,\n  routerProps?: MemoryRouterPropsType,\n  renderOptions?: Omit<CustomRenderOptions, 'routerProps'>\n): RenderResult & { user: ReturnType<typeof userEvent.setup> } => {\n  const options: CustomRenderOptions = { ...renderOptions }\n  if (routerProps) {\n    options.routerProps = routerProps\n  }\n  return customRender(ui, options)\n}\n\nexport const renderWithProviders = (\n  ui: ReactElement,\n  providers: CustomRenderOptions['providers'] = [],\n  renderOptions?: Omit<CustomRenderOptions, 'providers'>\n): RenderResult & { user: ReturnType<typeof userEvent.setup> } => {\n  return customRender(ui, { ...renderOptions, providers })\n}\n\n// Render hook with providers\nexport const renderHookWithProviders = <TProps, TResult>(\n  hook: (props: TProps) => TResult,\n  options?: CustomRenderOptions\n): (RenderResult & { user: ReturnType<typeof userEvent.setup> }) & { result: { current: TResult } } => {\n  const TestComponent = ({ hookProps }: { hookProps: TProps }) => {\n    const result = hook(hookProps)\n    return <div data-testid=\"hook-result\">{JSON.stringify(result)}</div>\n  }\n\n  return {\n    ...customRender(<TestComponent hookProps={{} as TProps} />, options),\n    result: {\n      current: hook({} as TProps),\n    },\n  }\n}\n\n// Helper to render and get common elements\nexport const renderAndGetElements = (\n  ui: ReactElement,\n  options?: CustomRenderOptions\n): RenderResult & { user: ReturnType<typeof userEvent.setup> } => {\n  const renderResult = customRender(ui, options)\n  \n  return {\n    ...renderResult,\n  }\n}", "import React, { ReactNode } from 'react'\n\ninterface ProviderOptions {\n  providers?: Array<React.ComponentType<{ children: ReactNode }>>\n  providerProps?: Record<string, any>\n}\n\nexport const createWrapper = (options: ProviderOptions = {}) => {\n  const { providers = [], providerProps = {} } = options\n\n  return ({ children }: { children: ReactNode }) => {\n    let wrapped = children\n\n    // Wrap with providers in reverse order\n    providers.reverse().forEach((Provider) => {\n      const props = providerProps[Provider.displayName || Provider.name] || {}\n      wrapped = <Provider {...props}>{wrapped}</Provider>\n    })\n\n    return <>{wrapped}</>\n  }\n}\n\n// Common test providers\nexport const TestProviders: React.FC<{ children: ReactNode }> = ({ children }) => {\n  return <>{children}</>\n}\n\n// Create a provider wrapper for specific contexts\nexport const createContextWrapper = <T,>(\n  Context: React.Context<T>,\n  value: T\n) => {\n  return ({ children }: { children: ReactNode }) => (\n    <Context.Provider value={value}>{children}</Context.Provider>\n  )\n}\n\n// Mock provider factory\nexport const createMockProvider = <T extends Record<string, any>>(\n  name: string,\n  defaultValue: T\n) => {\n  const Context = React.createContext<T>(defaultValue)\n  \n  const Provider: React.FC<{ children: ReactNode; value?: Partial<T> }> = ({\n    children,\n    value = {},\n  }) => {\n    const mergedValue = { ...defaultValue, ...value }\n    return <Context.Provider value={mergedValue}>{children}</Context.Provider>\n  }\n  \n  Provider.displayName = name\n  \n  return { Context, Provider }\n}\n\n// Combined provider wrapper\nexport const CombinedProviders: React.FC<{\n  children: ReactNode\n  providers?: ProviderOptions['providers']\n  providerProps?: ProviderOptions['providerProps']\n}> = ({ children, providers = [], providerProps = {} }) => {\n  const Wrapper = createWrapper({ providers, providerProps })\n  return <Wrapper>{children}</Wrapper>\n}", "import { renderHook, RenderHookOptions, RenderHookResult } from '@testing-library/react'\nimport { ReactNode } from 'react'\nimport { createWrapper } from './providers'\n\ninterface CustomRenderHookOptions<TProps> extends Omit<RenderHookOptions<TProps>, 'wrapper'> {\n  providers?: Array<React.ComponentType<{ children: ReactNode }>>\n  providerProps?: Record<string, any>\n}\n\nexport function renderHookWithContext<TProps, TResult>(\n  hook: (props: TProps) => TResult,\n  options: CustomRenderHookOptions<TProps> = {}\n): RenderHookResult<TResult, TProps> {\n  const { providers = [], providerProps = {}, ...renderOptions } = options\n  \n  const wrapper = createWrapper({ providers, providerProps })\n  \n  return renderHook(hook, {\n    wrapper,\n    ...renderOptions,\n  })\n}\n\n// Hook testing utilities\nexport const waitForHookUpdate = async (\n  _result: { current: any },\n  condition: () => boolean,\n  options = { timeout: 1000, interval: 50 }\n) => {\n  const { timeout, interval } = options\n  const startTime = Date.now()\n  \n  while (!condition() && Date.now() - startTime < timeout) {\n    await new Promise(resolve => setTimeout(resolve, interval))\n  }\n  \n  if (!condition()) {\n    throw new Error(`Hook update condition not met within ${timeout}ms`)\n  }\n}\n\n// Common hook test patterns\nexport const testHookWithAsyncState = async <T,>(\n  hook: () => { data: T | null; loading: boolean; error: Error | null },\n  expectedData: T\n) => {\n  const { result, rerender } = renderHook(hook)\n  \n  // Initial state\n  expect(result.current.loading).toBe(true)\n  expect(result.current.data).toBe(null)\n  expect(result.current.error).toBe(null)\n  \n  // Wait for data\n  await waitForHookUpdate(result, () => !result.current.loading)\n  \n  // Final state\n  expect(result.current.loading).toBe(false)\n  expect(result.current.data).toEqual(expectedData)\n  expect(result.current.error).toBe(null)\n  \n  return { result, rerender }\n}\n\nexport const testHookErrorState = async <T,>(\n  hook: () => { data: T | null; loading: boolean; error: Error | null },\n  expectedError: string\n) => {\n  const { result } = renderHook(hook)\n  \n  // Wait for error\n  await waitForHookUpdate(result, () => result.current.error !== null)\n  \n  expect(result.current.loading).toBe(false)\n  expect(result.current.data).toBe(null)\n  expect(result.current.error?.message).toBe(expectedError)\n}\n\n// Hook lifecycle helpers\nexport const trackHookRenders = <T,>(hook: () => T) => {\n  let renderCount = 0\n  const values: T[] = []\n  \n  const TrackedHook = () => {\n    const result = hook()\n    renderCount++\n    values.push(result)\n    return result\n  }\n  \n  const { result, rerender } = renderHook(TrackedHook)\n  \n  return {\n    result,\n    rerender,\n    getRenderCount: () => renderCount,\n    getValues: () => values,\n    getLastValue: () => values[values.length - 1],\n  }\n}", "import { screen, within, waitFor } from '@testing-library/react'\n\n// Enhanced query utilities\nexport const queries = {\n  // Get element by data-testid with better error messages\n  getElement: (testId: string, container?: HTMLElement) => {\n    const withinContext = container ? within(container) : screen\n    try {\n      return withinContext.getByTestId(testId)\n    } catch (error) {\n      throw new Error(`Element with testId=\"${testId}\" not found`)\n    }\n  },\n\n  // Find element with retry\n  findElement: async (testId: string, options: { timeout?: number } = { timeout: 3000 }) => {\n    return await screen.findByTestId(testId, {}, { timeout: options.timeout || 3000 })\n  },\n\n  // Query element (returns null if not found)\n  queryElement: (testId: string, container?: HTMLElement) => {\n    const withinContext = container ? within(container) : screen\n    return withinContext.queryByTestId(testId)\n  },\n\n  // Get all elements by testId\n  getAllElements: (testId: string, container?: HTMLElement) => {\n    const withinContext = container ? within(container) : screen\n    return withinContext.getAllByTestId(testId)\n  },\n\n  // Text queries with normalization\n  getByTextContent: (text: string | RegExp, container?: HTMLElement) => {\n    const withinContext = container ? within(container) : screen\n    return withinContext.getByText(text, {\n      normalizer: (str) => str.trim().replace(/\\s+/g, ' '),\n    })\n  },\n\n  // Role queries with accessible name\n  getByRoleWithName: (role: string, name: string | RegExp) => {\n    return screen.getByRole(role, { name })\n  },\n\n  // Complex queries\n  getButtonByText: (text: string | RegExp) => {\n    return screen.getByRole('button', { name: text })\n  },\n\n  getInputByLabel: (label: string | RegExp) => {\n    return screen.getByLabelText(label)\n  },\n\n  getLinkByText: (text: string | RegExp) => {\n    return screen.getByRole('link', { name: text })\n  },\n\n  // Form-specific queries\n  getFormField: (name: string) => {\n    return screen.getByRole('textbox', { name }) ||\n           screen.getByRole('combobox', { name }) ||\n           screen.getByRole('spinbutton', { name })\n  },\n\n  // Table queries\n  getTableCell: (rowIndex: number, colIndex: number) => {\n    const rows = screen.getAllByRole('row')\n    const cells = within(rows[rowIndex]).getAllByRole('cell')\n    return cells[colIndex]\n  },\n\n  // List queries\n  getListItems: (listTestId: string) => {\n    const list = screen.getByTestId(listTestId)\n    return within(list).getAllByRole('listitem')\n  },\n}\n\n// Wait for element utilities\nexport const waitForElement = async (\n  testId: string,\n  options = { timeout: 3000 }\n) => {\n  return await waitFor(\n    () => {\n      const element = screen.getByTestId(testId)\n      expect(element).toBeInTheDocument()\n      return element\n    },\n    options\n  )\n}\n\nexport const waitForElementToBeRemoved = async (\n  testId: string,\n  options = { timeout: 3000 }\n) => {\n  return await waitFor(\n    () => {\n      expect(screen.queryByTestId(testId)).not.toBeInTheDocument()\n    },\n    options\n  )\n}\n\nexport const waitForText = async (\n  text: string | RegExp,\n  options = { timeout: 3000 }\n) => {\n  return await waitFor(\n    () => {\n      const element = screen.getByText(text)\n      expect(element).toBeInTheDocument()\n      return element\n    },\n    options\n  )\n}\n\n// Assertion helpers\nexport const assertElementExists = (testId: string) => {\n  const element = screen.getByTestId(testId)\n  expect(element).toBeInTheDocument()\n  return element\n}\n\nexport const assertElementNotExists = (testId: string) => {\n  const element = screen.queryByTestId(testId)\n  expect(element).not.toBeInTheDocument()\n}\n\nexport const assertElementVisible = (testId: string) => {\n  const element = screen.getByTestId(testId)\n  expect(element).toBeVisible()\n  return element\n}\n\nexport const assertElementHidden = (testId: string) => {\n  const element = screen.getByTestId(testId)\n  expect(element).not.toBeVisible()\n  return element\n}\n\n// Interaction helpers\nexport const getInteractiveElement = (\n  testId: string,\n  options: { disabled?: boolean } = {}\n) => {\n  const element = screen.getByTestId(testId)\n  \n  if (options.disabled !== undefined) {\n    if (options.disabled) {\n      expect(element).toBeDisabled()\n    } else {\n      expect(element).toBeEnabled()\n    }\n  }\n  \n  return element\n}"]}