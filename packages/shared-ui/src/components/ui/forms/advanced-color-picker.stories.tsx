import React, { useState } from 'react'
import type { <PERSON>a, StoryObj } from '@storybook/react'
import { AdvancedColorPicker } from './advanced-color-picker'
import { Palette, Copy, Download, Sparkles } from 'lucide-react'

const meta = {
  title: 'Forms/Advanced Color Picker',
  component: AdvancedColorPicker,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A professional color picker with multiple format support, Tailwind color palette integration, and copy functionality. Perfect for design systems and theme builders.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    value: {
      control: 'color',
      description: 'Current color value in hex format',
    },
    onChange: {
      action: 'color changed',
      description: 'Callback when color value changes',
    },
    label: {
      control: 'text',
      description: 'Label for the color picker',
    },
    showFormats: {
      control: 'boolean',
      description: 'Show different color format options',
      defaultValue: true,
    },
    showPresets: {
      control: 'boolean',
      description: 'Show preset color options',
      defaultValue: true,
    },
    disabled: {
      control: 'boolean',
      description: 'Disable the color picker',
      defaultValue: false,
    },
  },
  decorators: [
    (Story) => (
      <div style={{ 
        minHeight: '600px',
        padding: '40px',
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}>
        <div style={{ width: '100%', maxWidth: '400px' }}>
          <Story />
        </div>
      </div>
    ),
  ],
} satisfies Meta<typeof AdvancedColorPicker>

export default meta
type Story = StoryObj<typeof meta>

// Color Palette Demo Component
const ColorPaletteDemo = () => {
  const [primaryColor, setPrimaryColor] = useState('#3b82f6')
  const [secondaryColor, setSecondaryColor] = useState('#64748b')
  const [accentColor, setAccentColor] = useState('#f59e0b')
  const [successColor, setSuccessColor] = useState('#10b981')
  const [warningColor, setWarningColor] = useState('#f59e0b')
  const [errorColor, setErrorColor] = useState('#ef4444')

  const colorScheme = [
    { label: 'Primary', value: primaryColor, onChange: setPrimaryColor },
    { label: 'Secondary', value: secondaryColor, onChange: setSecondaryColor },
    { label: 'Accent', value: accentColor, onChange: setAccentColor },
    { label: 'Success', value: successColor, onChange: setSuccessColor },
    { label: 'Warning', value: warningColor, onChange: setWarningColor },
    { label: 'Error', value: errorColor, onChange: setErrorColor },
  ]

  const generateCSS = () => {
    const cssVars = colorScheme.map(color => 
      `  --color-${color.label.toLowerCase()}: ${color.value};`
    ).join('\n')
    
    return `:root {\n${cssVars}\n}`
  }

  const copyCSS = () => {
    navigator.clipboard.writeText(generateCSS())
  }

  return (
    <div className="space-y-6 w-full max-w-2xl">
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-gray-800 mb-2">Color Palette Builder</h3>
        <p className="text-gray-600">Build and export your custom color scheme</p>
      </div>

      {/* Color Pickers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {colorScheme.map((color, index) => (
          <AdvancedColorPicker
            key={index}
            label={color.label}
            value={color.value}
            onChange={color.onChange}
            showFormats={false}
            showPresets={true}
          />
        ))}
      </div>

      {/* Color Preview */}
      <div className="bg-white rounded-xl shadow-lg p-6 border">
        <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
          <Palette className="w-5 h-5" />
          Color Preview
        </h4>
        
        <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
          {colorScheme.map((color, index) => (
            <div key={index} className="text-center">
              <div 
                className="w-full h-16 rounded-lg border-2 border-gray-200 mb-2 shadow-sm"
                style={{ backgroundColor: color.value }}
              />
              <div className="text-xs font-medium text-gray-700">{color.label}</div>
              <div className="text-xs text-gray-500 font-mono">{color.value}</div>
            </div>
          ))}
        </div>
      </div>

      {/* CSS Export */}
      <div className="bg-white rounded-xl shadow-lg p-6 border">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
            <Download className="w-5 h-5" />
            CSS Variables
          </h4>
          <button
            onClick={copyCSS}
            className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
          >
            <Copy className="w-4 h-4" />
            Copy CSS
          </button>
        </div>
        
        <pre className="bg-gray-50 p-4 rounded-lg text-sm font-mono text-gray-800 overflow-x-auto">
          {generateCSS()}
        </pre>
      </div>
    </div>
  )
}

// Theme Builder Demo
const ThemeBuilderDemo = () => {
  const [theme, setTheme] = useState({
    background: '#ffffff',
    foreground: '#000000',
    primary: '#3b82f6',
    secondary: '#64748b',
    accent: '#f59e0b',
    muted: '#f1f5f9',
  })

  const updateThemeColor = (key: string, value: string) => {
    setTheme(prev => ({ ...prev, [key]: value }))
  }

  return (
    <div className="space-y-6 w-full max-w-4xl">
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-gray-800 mb-2">Theme Builder</h3>
        <p className="text-gray-600">Create and preview your custom theme in real-time</p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Color Controls */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-800">Theme Colors</h4>
          
          {Object.entries(theme).map(([key, value]) => (
            <AdvancedColorPicker
              key={key}
              label={key.charAt(0).toUpperCase() + key.slice(1)}
              value={value}
              onChange={(newValue) => updateThemeColor(key, newValue)}
              showFormats={true}
              showPresets={false}
            />
          ))}
        </div>

        {/* Live Preview */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-800">Live Preview</h4>
          
          <div 
            className="rounded-xl shadow-lg p-6 border"
            style={{ 
              backgroundColor: theme.background,
              color: theme.foreground,
              borderColor: theme.muted
            }}
          >
            <div className="space-y-4">
              <h3 
                className="text-xl font-bold"
                style={{ color: theme.primary }}
              >
                Your Theme Preview
              </h3>
              
              <p style={{ color: theme.secondary }}>
                This is how your theme looks with different text elements and components.
              </p>
              
              <div className="flex gap-2">
                <button 
                  className="px-4 py-2 rounded-lg font-medium text-white"
                  style={{ backgroundColor: theme.primary }}
                >
                  Primary Button
                </button>
                
                <button 
                  className="px-4 py-2 rounded-lg font-medium text-white"
                  style={{ backgroundColor: theme.secondary }}
                >
                  Secondary Button
                </button>
                
                <button 
                  className="px-4 py-2 rounded-lg font-medium text-white"
                  style={{ backgroundColor: theme.accent }}
                >
                  Accent Button
                </button>
              </div>
              
              <div 
                className="p-4 rounded-lg"
                style={{ backgroundColor: theme.muted }}
              >
                <p style={{ color: theme.foreground }}>
                  This is a muted background section with your custom colors.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export const Default: Story = {
  name: 'Default Color Picker',
  args: {
    label: 'Choose Color',
    value: '#3b82f6',
    onChange: (value: string) => console.log('Color changed:', value),
    showFormats: true,
    showPresets: true,
  },
}

export const WithoutFormats: Story = {
  name: 'Without Format Options',
  args: {
    label: 'Simple Color Picker',
    value: '#ef4444',
    onChange: (value: string) => console.log('Color changed:', value),
    showFormats: false,
    showPresets: true,
  },
}

export const WithoutPresets: Story = {
  name: 'Without Preset Colors',
  args: {
    label: 'Custom Color Only',
    value: '#10b981',
    onChange: (value: string) => console.log('Color changed:', value),
    showFormats: true,
    showPresets: false,
  },
}

export const Disabled: Story = {
  name: 'Disabled State',
  args: {
    label: 'Disabled Color Picker',
    value: '#6b7280',
    onChange: (value: string) => console.log('Color changed:', value),
    disabled: true,
  },
}

export const ColorPalette: Story = {
  name: 'Color Palette Builder',
  args: {
    value: '#3b82f6',
    onChange: () => {},
  },
  render: () => <ColorPaletteDemo />,
  parameters: {
    docs: {
      description: {
        story: 'A complete color palette builder with preview and CSS export functionality.',
      },
    },
  },
  decorators: [
    (Story) => (
      <div style={{ 
        minHeight: '800px',
        padding: '40px',
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}>
        <Story />
      </div>
    ),
  ],
}

export const ThemeBuilder: Story = {
  name: 'Theme Builder',
  args: {
    value: '#3b82f6',
    onChange: () => {},
  },
  render: () => <ThemeBuilderDemo />,
  parameters: {
    docs: {
      description: {
        story: 'An advanced theme builder with real-time preview of your color choices.',
      },
    },
  },
  decorators: [
    (Story) => (
      <div style={{ 
        minHeight: '800px',
        padding: '40px',
        background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}>
        <Story />
      </div>
    ),
  ],
}

export const TailwindIntegration: Story = {
  name: 'Tailwind Color Integration',
  args: {
    value: '#3b82f6',
    onChange: () => {},
  },
  render: () => {
    const [selectedColors, setSelectedColors] = useState<string[]>([])
    
    const handleColorChange = (value: string) => {
      if (!selectedColors.includes(value)) {
        setSelectedColors(prev => [...prev, value])
      }
    }

    const removeColor = (colorToRemove: string) => {
      setSelectedColors(prev => prev.filter(color => color !== colorToRemove))
    }

    return (
      <div className="space-y-6 w-full max-w-2xl">
        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold text-gray-800 mb-2 flex items-center justify-center gap-2">
            <Sparkles className="w-6 h-6 text-purple-600" />
            Tailwind Color Explorer
          </h3>
          <p className="text-gray-600">Explore and select colors from the complete Tailwind palette</p>
        </div>

        <AdvancedColorPicker
          label="Select Tailwind Colors"
          value="#3b82f6"
          onChange={handleColorChange}
          showFormats={true}
          showPresets={true}
        />

        {selectedColors.length > 0 && (
          <div className="bg-white rounded-xl shadow-lg p-6 border">
            <h4 className="text-lg font-semibold text-gray-800 mb-4">Selected Colors</h4>
            
            <div className="grid grid-cols-4 md:grid-cols-6 gap-3 mb-4">
              {selectedColors.map((color, index) => (
                <div key={index} className="relative group">
                  <div 
                    className="w-full h-12 rounded-lg border-2 border-gray-200 cursor-pointer shadow-sm group-hover:scale-105 transition-transform"
                    style={{ backgroundColor: color }}
                    onClick={() => removeColor(color)}
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <span className="text-white text-xs">Remove</span>
                  </div>
                  <div className="text-xs text-center mt-1 font-mono text-gray-600">{color}</div>
                </div>
              ))}
            </div>

            <div className="text-sm text-gray-600">
              <p>💡 Click any color from the Tailwind palette to add it to your selection</p>
              <p>🗑️ Click a selected color to remove it</p>
            </div>
          </div>
        )}
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates the Tailwind color palette integration with selection functionality.',
      },
    },
  },
}