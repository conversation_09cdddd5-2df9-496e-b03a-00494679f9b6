import React, { ReactElement, ReactNode } from 'react'
import { render as rtlR<PERSON>, RenderOptions, RenderResult } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

// Conditional import for react-router-dom to handle DTS build issues
let MemoryRouter: any

try {
  const routerModule = require('react-router-dom')
  MemoryRouter = routerModule.MemoryRouter
} catch (error) {
  // Fallback when react-router-dom is not available
  MemoryRouter = ({ children }: { children: ReactNode }) => <>{children}</>
}

interface MemoryRouterPropsType {
  initialEntries?: string[]
  initialIndex?: number
  [key: string]: any
}

// Types for custom render options
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  // Router options
  routerProps?: MemoryRouterPropsType
  route?: string
  // Provider options
  providers?: Array<React.ComponentType<{ children: ReactNode }>>
  providerProps?: Record<string, any>
  // Other options
  mockData?: Record<string, any>
}

// Create a custom wrapper with all providers
const createWrapper = (options: CustomRenderOptions = {}) => {
  const { routerProps = {}, route = '/', providers = [], providerProps = {} } = options

  return ({ children }: { children: ReactNode }) => {
    // Set initial route if provided
    if (route && routerProps.initialEntries === undefined) {
      routerProps.initialEntries = [route]
    }

    // Wrap with all providers
    let wrapped = children
    
    // Always wrap with MemoryRouter for testing
    wrapped = <MemoryRouter {...routerProps}>{wrapped}</MemoryRouter>

    // Wrap with additional providers in reverse order
    providers.reverse().forEach((Provider) => {
      const props = providerProps[Provider.displayName || Provider.name] || {}
      wrapped = <Provider {...props}>{wrapped}</Provider>
    })

    return wrapped
  }
}

// Custom render function
function customRender(
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult & { user: ReturnType<typeof userEvent.setup> } {
  const { mockData, ...renderOptions } = options

  // Setup user event
  const user = userEvent.setup()

  // Apply mock data if provided
  if (mockData) {
    Object.entries(mockData).forEach(([key, value]) => {
      (window as any)[key] = value
    })
  }

  const wrapper = createWrapper(options)
  
  const renderResult = rtlRender(ui, { wrapper, ...renderOptions })

  return {
    ...renderResult,
    user,
  }
}

// Override the render function
export { customRender as render }

// Additional render helpers
export const renderWithRouter = (
  ui: ReactElement,
  routerProps?: MemoryRouterPropsType,
  renderOptions?: Omit<CustomRenderOptions, 'routerProps'>
): RenderResult & { user: ReturnType<typeof userEvent.setup> } => {
  const options: CustomRenderOptions = { ...renderOptions }
  if (routerProps) {
    options.routerProps = routerProps
  }
  return customRender(ui, options)
}

export const renderWithProviders = (
  ui: ReactElement,
  providers: CustomRenderOptions['providers'] = [],
  renderOptions?: Omit<CustomRenderOptions, 'providers'>
): RenderResult & { user: ReturnType<typeof userEvent.setup> } => {
  return customRender(ui, { ...renderOptions, providers })
}

// Render hook with providers
export const renderHookWithProviders = <TProps, TResult>(
  hook: (props: TProps) => TResult,
  options?: CustomRenderOptions
): (RenderResult & { user: ReturnType<typeof userEvent.setup> }) & { result: { current: TResult } } => {
  const TestComponent = ({ hookProps }: { hookProps: TProps }) => {
    const result = hook(hookProps)
    return <div data-testid="hook-result">{JSON.stringify(result)}</div>
  }

  return {
    ...customRender(<TestComponent hookProps={{} as TProps} />, options),
    result: {
      current: hook({} as TProps),
    },
  }
}

// Helper to render and get common elements
export const renderAndGetElements = (
  ui: ReactElement,
  options?: CustomRenderOptions
): RenderResult & { user: ReturnType<typeof userEvent.setup> } => {
  const renderResult = customRender(ui, options)
  
  return {
    ...renderResult,
  }
}