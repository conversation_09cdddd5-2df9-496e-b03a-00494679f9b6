{"version": 3, "sources": ["../../src/mocks/api.ts", "../../src/fixtures/index.ts", "../../src/mocks/handlers.ts", "../../src/mocks/server.ts", "../../src/mocks/browser.ts"], "names": ["http", "HttpResponse"], "mappings": ";;;;;;AAGO,IAAM,UAAN,MAAc;AAAA,EACX,OAAA;AAAA,EACA,WAAkB,EAAC;AAAA,EAE3B,WAAA,CAAY,UAAU,2BAA6B,EAAA;AACjD,IAAA,IAAA,CAAK,OAAU,GAAA,OAAA;AAAA;AACjB;AAAA,EAGA,GAAI,CAAA,IAAA,EAAc,QAAe,EAAA,OAAA,GAA+C,EAAI,EAAA;AAClF,IAAM,MAAA,OAAA,GAAU,KAAK,GAAI,CAAA,CAAA,EAAG,KAAK,OAAO,CAAA,EAAG,IAAI,CAAA,CAAA,EAAI,YAAY;AAC7D,MAAA,IAAI,QAAQ,KAAO,EAAA;AACjB,QAAA,MAAM,IAAI,OAAQ,CAAA,CAAA,OAAA,KAAW,WAAW,OAAS,EAAA,OAAA,CAAQ,KAAK,CAAC,CAAA;AAAA;AAEjE,MAAO,OAAA,YAAA,CAAa,KAAK,QAAU,EAAA,EAAE,QAAQ,OAAQ,CAAA,MAAA,IAAU,KAAK,CAAA;AAAA,KACrE,CAAA;AACD,IAAK,IAAA,CAAA,QAAA,CAAS,KAAK,OAAO,CAAA;AAC1B,IAAO,OAAA,IAAA;AAAA;AACT;AAAA,EAGA,IAAK,CAAA,IAAA,EAAc,OAA6B,EAAA,OAAA,GAA8B,EAAI,EAAA;AAChF,IAAA,MAAM,WAAc,GAAA,IAAA,CAAK,IAAK,CAAA,CAAA,EAAG,IAAK,CAAA,OAAO,CAAG,EAAA,IAAI,CAAI,CAAA,EAAA,OAAO,EAAE,OAAA,EAAc,KAAA;AAC7E,MAAM,MAAA,IAAA,GAAO,MAAM,OAAA,CAAQ,IAAK,EAAA;AAChC,MAAA,IAAI,QAAQ,KAAO,EAAA;AACjB,QAAA,MAAM,IAAI,OAAQ,CAAA,CAAA,OAAA,KAAW,WAAW,OAAS,EAAA,OAAA,CAAQ,KAAK,CAAC,CAAA;AAAA;AAEjE,MAAM,MAAA,QAAA,GAAW,MAAM,OAAA,CAAQ,IAAI,CAAA;AACnC,MAAO,OAAA,YAAA,CAAa,KAAK,QAAQ,CAAA;AAAA,KAClC,CAAA;AACD,IAAK,IAAA,CAAA,QAAA,CAAS,KAAK,WAAW,CAAA;AAC9B,IAAO,OAAA,IAAA;AAAA;AACT;AAAA,EAGA,GAAI,CAAA,IAAA,EAAc,OAA0C,EAAA,OAAA,GAA8B,EAAI,EAAA;AAC5F,IAAA,MAAM,WAAc,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,EAAG,IAAK,CAAA,OAAO,CAAG,EAAA,IAAI,CAAI,CAAA,EAAA,OAAO,EAAE,MAAA,EAAQ,SAAc,KAAA;AACpF,MAAM,MAAA,IAAA,GAAO,MAAM,OAAA,CAAQ,IAAK,EAAA;AAChC,MAAA,IAAI,QAAQ,KAAO,EAAA;AACjB,QAAA,MAAM,IAAI,OAAQ,CAAA,CAAA,OAAA,KAAW,WAAW,OAAS,EAAA,OAAA,CAAQ,KAAK,CAAC,CAAA;AAAA;AAEjE,MAAA,MAAM,QAAW,GAAA,MAAM,OAAQ,CAAA,MAAA,EAAQ,IAAI,CAAA;AAC3C,MAAO,OAAA,YAAA,CAAa,KAAK,QAAQ,CAAA;AAAA,KAClC,CAAA;AACD,IAAK,IAAA,CAAA,QAAA,CAAS,KAAK,WAAW,CAAA;AAC9B,IAAO,OAAA,IAAA;AAAA;AACT;AAAA,EAGA,MAAA,CAAO,MAAc,QAAgB,GAAA,EAAE,SAAS,IAAK,EAAA,EAAG,OAA8B,GAAA,EAAI,EAAA;AACxF,IAAM,MAAA,OAAA,GAAU,KAAK,MAAO,CAAA,CAAA,EAAG,KAAK,OAAO,CAAA,EAAG,IAAI,CAAA,CAAA,EAAI,YAAY;AAChE,MAAA,IAAI,QAAQ,KAAO,EAAA;AACjB,QAAA,MAAM,IAAI,OAAQ,CAAA,CAAA,OAAA,KAAW,WAAW,OAAS,EAAA,OAAA,CAAQ,KAAK,CAAC,CAAA;AAAA;AAEjE,MAAO,OAAA,YAAA,CAAa,KAAK,QAAQ,CAAA;AAAA,KAClC,CAAA;AACD,IAAK,IAAA,CAAA,QAAA,CAAS,KAAK,OAAO,CAAA;AAC1B,IAAO,OAAA,IAAA;AAAA;AACT;AAAA,EAGA,KAAM,CAAA,IAAA,EAAc,KAA2C,EAAA,MAAA,GAAS,GAAK,EAAA;AAC3E,IAAM,MAAA,OAAA,GAAU,KAAK,GAAI,CAAA,CAAA,EAAG,KAAK,OAAO,CAAA,EAAG,IAAI,CAAA,CAAA,EAAI,MAAM;AACvD,MAAA,OAAO,aAAa,IAAK,CAAA,EAAE,OAAS,EAAA,EAAE,QAAQ,CAAA;AAAA,KAC/C,CAAA;AACD,IAAK,IAAA,CAAA,QAAA,CAAS,KAAK,OAAO,CAAA;AAC1B,IAAO,OAAA,IAAA;AAAA;AACT;AAAA,EAGA,WAAc,GAAA;AACZ,IAAA,OAAO,IAAK,CAAA,QAAA;AAAA;AACd;AAAA,EAGA,SAAU,CAAA,IAAA,EAAc,aAA0B,EAAA,UAAA,GAAa,GAAK,EAAA;AAClE,IAAA,MAAM,OAAU,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,EAAG,IAAK,CAAA,OAAO,CAAG,EAAA,IAAI,CAAI,CAAA,EAAA,CAAC,EAAE,OAAA,EAAc,KAAA;AAClE,MAAA,MAAM,GAAM,GAAA,IAAI,GAAI,CAAA,OAAA,CAAQ,GAAG,CAAA;AAC/B,MAAA,MAAM,OAAO,QAAS,CAAA,GAAA,CAAI,aAAa,GAAI,CAAA,MAAM,KAAK,GAAG,CAAA;AACzD,MAAA,MAAM,QAAQ,QAAS,CAAA,GAAA,CAAI,aAAa,GAAI,CAAA,OAAO,KAAK,IAAI,CAAA;AAC5D,MAAM,MAAA,MAAA,GAAA,CAAU,OAAO,CAAK,IAAA,KAAA;AAE5B,MAAA,MAAM,KAAQ,GAAA,KAAA,CAAM,IAAK,CAAA,EAAE,MAAQ,EAAA,IAAA,CAAK,GAAI,CAAA,KAAA,EAAO,UAAa,GAAA,MAAM,CAAE,EAAA,EAAG,aAAa,CAAA;AAExF,MAAA,OAAO,aAAa,IAAK,CAAA;AAAA,QACvB,IAAM,EAAA,KAAA;AAAA,QACN,UAAY,EAAA;AAAA,UACV,IAAA;AAAA,UACA,KAAA;AAAA,UACA,KAAO,EAAA,UAAA;AAAA,UACP,UAAY,EAAA,IAAA,CAAK,IAAK,CAAA,UAAA,GAAa,KAAK,CAAA;AAAA,UACxC,OAAS,EAAA,IAAA,GAAO,IAAK,CAAA,IAAA,CAAK,aAAa,KAAK,CAAA;AAAA,UAC5C,SAAS,IAAO,GAAA;AAAA;AAClB,OACD,CAAA;AAAA,KACF,CAAA;AACD,IAAK,IAAA,CAAA,QAAA,CAAS,KAAK,OAAO,CAAA;AAC1B,IAAO,OAAA,IAAA;AAAA;AACT;AAAA,EAGA,IAAA,CAAK,UAAkB,SAAsB,EAAA;AAC3C,IAAM,MAAA,KAAA,uBAAY,GAAiB,EAAA;AAGnC,IAAA,IAAA,CAAK,GAAI,CAAA,CAAA,CAAA,EAAI,QAAQ,CAAA,CAAA,EAAI,MAAM;AAC7B,MAAA,OAAO,KAAM,CAAA,IAAA,CAAK,KAAM,CAAA,MAAA,EAAQ,CAAA;AAAA,KACjC,CAAA;AAGD,IAAA,IAAA,CAAK,QAAS,CAAA,IAAA;AAAA,MACZ,IAAA,CAAK,GAAI,CAAA,CAAA,EAAG,IAAK,CAAA,OAAO,CAAI,CAAA,EAAA,QAAQ,CAAQ,IAAA,CAAA,EAAA,CAAC,EAAE,MAAA,EAAa,KAAA;AAC1D,QAAA,MAAM,IAAO,GAAA,KAAA,CAAM,GAAI,CAAA,MAAA,CAAO,EAAY,CAAA;AAC1C,QAAA,IAAI,CAAC,IAAM,EAAA;AACT,UAAO,OAAA,YAAA,CAAa,KAAK,EAAE,KAAA,EAAO,aAAe,EAAA,EAAE,MAAQ,EAAA,GAAA,EAAK,CAAA;AAAA;AAElE,QAAO,OAAA,YAAA,CAAa,KAAK,IAAI,CAAA;AAAA,OAC9B;AAAA,KACH;AAGA,IAAA,IAAA,CAAK,IAAK,CAAA,CAAA,CAAA,EAAI,QAAQ,CAAA,CAAA,EAAI,CAAC,IAAS,KAAA;AAClC,MAAA,MAAM,EAAK,GAAA,IAAA,CAAK,GAAI,EAAA,CAAE,QAAS,EAAA;AAC/B,MAAA,MAAM,OAAO,EAAE,GAAG,WAAa,EAAA,GAAG,MAAM,EAAG,EAAA;AAC3C,MAAM,KAAA,CAAA,GAAA,CAAI,IAAI,IAAI,CAAA;AAClB,MAAO,OAAA,IAAA;AAAA,KACR,CAAA;AAGD,IAAA,IAAA,CAAK,QAAS,CAAA,IAAA;AAAA,MACZ,IAAK,CAAA,GAAA,CAAI,CAAG,EAAA,IAAA,CAAK,OAAO,CAAA,CAAA,EAAI,QAAQ,CAAA,IAAA,CAAA,EAAQ,OAAO,EAAE,MAAQ,EAAA,OAAA,EAAc,KAAA;AACzE,QAAM,MAAA,IAAA,GAAO,MAAM,OAAA,CAAQ,IAAK,EAAA;AAChC,QAAA,MAAM,KAAK,MAAO,CAAA,EAAA;AAClB,QAAM,MAAA,QAAA,GAAW,KAAM,CAAA,GAAA,CAAI,EAAE,CAAA;AAC7B,QAAA,IAAI,CAAC,QAAU,EAAA;AACb,UAAO,OAAA,YAAA,CAAa,KAAK,EAAE,KAAA,EAAO,aAAe,EAAA,EAAE,MAAQ,EAAA,GAAA,EAAK,CAAA;AAAA;AAElE,QAAA,MAAM,OAAU,GAAA,EAAE,GAAG,QAAA,EAAU,GAAG,IAAK,EAAA;AACvC,QAAM,KAAA,CAAA,GAAA,CAAI,IAAI,OAAO,CAAA;AACrB,QAAO,OAAA,YAAA,CAAa,KAAK,OAAO,CAAA;AAAA,OACjC;AAAA,KACH;AAGA,IAAA,IAAA,CAAK,QAAS,CAAA,IAAA;AAAA,MACZ,IAAA,CAAK,MAAO,CAAA,CAAA,EAAG,IAAK,CAAA,OAAO,CAAI,CAAA,EAAA,QAAQ,CAAQ,IAAA,CAAA,EAAA,CAAC,EAAE,MAAA,EAAa,KAAA;AAC7D,QAAA,MAAM,KAAK,MAAO,CAAA,EAAA;AAClB,QAAA,IAAI,CAAC,KAAA,CAAM,GAAI,CAAA,EAAE,CAAG,EAAA;AAClB,UAAO,OAAA,YAAA,CAAa,KAAK,EAAE,KAAA,EAAO,aAAe,EAAA,EAAE,MAAQ,EAAA,GAAA,EAAK,CAAA;AAAA;AAElE,QAAA,KAAA,CAAM,OAAO,EAAE,CAAA;AACf,QAAA,OAAO,IAAI,YAAa,CAAA,IAAA,EAAM,EAAE,MAAA,EAAQ,KAAK,CAAA;AAAA,OAC9C;AAAA,KACH;AAEA,IAAO,OAAA,IAAA;AAAA;AACT;AAAA,EAGA,UAAA,CAAW,MAAc,OAA+B,EAAA;AACtD,IAAA,MAAM,aAAgB,GAAA,IAAA,CAAK,IAAK,CAAA,CAAA,EAAG,IAAK,CAAA,OAAO,CAAG,EAAA,IAAI,CAAI,CAAA,EAAA,OAAO,EAAE,OAAA,EAAc,KAAA;AAC/E,MAAM,MAAA,QAAA,GAAW,MAAM,OAAA,CAAQ,QAAS,EAAA;AACxC,MAAM,MAAA,IAAA,GAAO,QAAS,CAAA,GAAA,CAAI,MAAM,CAAA;AAEhC,MAAA,IAAI,CAAC,IAAM,EAAA;AACT,QAAO,OAAA,YAAA,CAAa,KAAK,EAAE,KAAA,EAAO,oBAAsB,EAAA,EAAE,MAAQ,EAAA,GAAA,EAAK,CAAA;AAAA;AAGzE,MAAA,MAAM,QAAW,GAAA,OAAA,GAAU,MAAM,OAAA,CAAQ,IAAI,CAAI,GAAA;AAAA,QAC/C,EAAI,EAAA,IAAA,CAAK,GAAI,EAAA,CAAE,QAAS,EAAA;AAAA,QACxB,UAAU,IAAK,CAAA,IAAA;AAAA,QACf,MAAM,IAAK,CAAA,IAAA;AAAA,QACX,MAAM,IAAK,CAAA,IAAA;AAAA,QACX,GAAA,EAAK,CAAY,SAAA,EAAA,IAAA,CAAK,IAAI,CAAA;AAAA,OAC5B;AAEA,MAAO,OAAA,YAAA,CAAa,KAAK,QAAQ,CAAA;AAAA,KAClC,CAAA;AACD,IAAK,IAAA,CAAA,QAAA,CAAS,KAAK,aAAa,CAAA;AAChC,IAAO,OAAA,IAAA;AAAA;AAEX;AAGO,IAAM,oBAAoB,MAAM;AACrC,EAAO,OAAA,IAAI,SACR,CAAA,IAAA,CAAK,eAAe,CAAC,EAAE,KAAO,EAAA,QAAA,EAAe,KAAA;AAC5C,IAAI,IAAA,KAAA,KAAU,kBAAsB,IAAA,QAAA,KAAa,UAAY,EAAA;AAC3D,MAAO,OAAA;AAAA,QACL,MAAM,EAAE,EAAA,EAAI,GAAK,EAAA,KAAA,EAAO,MAAM,WAAY,EAAA;AAAA,QAC1C,KAAO,EAAA;AAAA,OACT;AAAA;AAEF,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA;AAAA,GACtC,CACA,CAAA,IAAA,CAAK,gBAAgB,OAAO,EAAE,SAAS,IAAK,EAAA,CAAE,EAC9C,GAAI,CAAA,UAAA,EAAY,EAAE,EAAI,EAAA,GAAA,EAAK,OAAO,kBAAoB,EAAA,IAAA,EAAM,aAAa,CAAA;AAC9E;AAEO,IAAM,oBAAoB,MAAM;AACrC,EAAA,OAAO,IAAI,OAAA,EAAU,CAAA,IAAA,CAAK,SAAS,OAAO;AAAA,IACxC,IAAM,EAAA,UAAA;AAAA,IACN,KAAO,EAAA,kBAAA;AAAA,IACP,IAAM,EAAA;AAAA,GACN,CAAA,CAAA;AACJ;AC7MO,IAAM,cAAiB,GAAA,CAAC,SAA0B,GAAA,EAAQ,MAAA;AAAA,EAC/D,EAAA,EAAI,KAAM,CAAA,MAAA,CAAO,IAAK,EAAA;AAAA,EACtB,KAAA,EAAO,KAAM,CAAA,QAAA,CAAS,KAAM,EAAA;AAAA,EAC5B,IAAA,EAAM,KAAM,CAAA,MAAA,CAAO,QAAS,EAAA;AAAA,EAC5B,MAAA,EAAQ,KAAM,CAAA,KAAA,CAAM,MAAO,EAAA;AAAA,EAC3B,IAAM,EAAA,MAAA;AAAA,EACN,SAAW,EAAA,KAAA,CAAM,IAAK,CAAA,IAAA,GAAO,WAAY,EAAA;AAAA,EACzC,SAAW,EAAA,KAAA,CAAM,IAAK,CAAA,MAAA,GAAS,WAAY,EAAA;AAAA,EAC3C,GAAG;AACL,CAAA,CAAA;AAsBO,IAAM,qBAAwB,GAAA,CAAU,IAAS,EAAA,SAAA,GAA0B,EAAQ,MAAA;AAAA,EACxF,IAAA;AAAA,EACA,MAAQ,EAAA,SAAA;AAAA,EACR,OAAS,EAAA,sBAAA;AAAA,EACT,SAAW,EAAA,iBAAA,IAAI,IAAK,EAAA,EAAE,WAAY,EAAA;AAAA,EAClC,GAAG;AACL,CAAA,CAAA;AAgFO,IAAM,iBAAoB,GAAA,CAAC,SAA0B,GAAA,EAAQ,MAAA;AAAA,EAClE,EAAA,EAAI,KAAM,CAAA,MAAA,CAAO,IAAK,EAAA;AAAA,EACtB,IAAA,EAAM,KAAM,CAAA,QAAA,CAAS,WAAY,EAAA;AAAA,EACjC,WAAA,EAAa,KAAM,CAAA,QAAA,CAAS,kBAAmB,EAAA;AAAA,EAC/C,KAAO,EAAA,UAAA,CAAW,KAAM,CAAA,QAAA,CAAS,OAAO,CAAA;AAAA,EACxC,KAAA,EAAO,KAAM,CAAA,KAAA,CAAM,GAAI,EAAA;AAAA,EACvB,QAAA,EAAU,KAAM,CAAA,QAAA,CAAS,UAAW,EAAA;AAAA,EACpC,OAAA,EAAS,KAAM,CAAA,QAAA,CAAS,OAAQ,EAAA;AAAA,EAChC,MAAA,EAAQ,KAAM,CAAA,MAAA,CAAO,KAAM,CAAA,EAAE,GAAK,EAAA,CAAA,EAAG,GAAK,EAAA,CAAA,EAAG,cAAgB,EAAA,CAAA,EAAG,CAAA;AAAA,EAChE,GAAG;AACL,CAAA,CAAA;AAEO,IAAM,kBAAqB,GAAA,CAAC,KAAe,EAAA,SAAA,GAA0B,EAAO,KAAA;AACjF,EAAO,OAAA,KAAA,CAAM,KAAK,EAAE,MAAA,EAAQ,OAAS,EAAA,MAAM,iBAAkB,CAAA,SAAS,CAAC,CAAA;AACzE,CAAA;;;AClIA,IAAM,YAAA,GAAe,OAAQ,CAAA,GAAA,CAAI,YAAgB,IAAA,2BAAA;AAG1C,IAAM,eAAkB,GAAA;AAAA;AAAA,EAE7BA,IAAAA,CAAK,KAAK,CAAG,EAAA,YAAY,eAAe,OAAO,EAAE,SAAc,KAAA;AAC7D,IAAM,MAAA,IAAA,GAAO,MAAM,OAAA,CAAQ,IAAK,EAAA;AAChC,IAAA,MAAM,MAAM,GAAG,CAAA;AAEf,IAAA,IAAI,IAAK,CAAA,KAAA,KAAU,kBAAsB,IAAA,IAAA,CAAK,aAAa,UAAY,EAAA;AACrE,MAAA,OAAOC,YAAa,CAAA,IAAA;AAAA,QAClB,qBAAsB,CAAA;AAAA,UACpB,MAAM,cAAe,CAAA,EAAE,KAAO,EAAA,IAAA,CAAK,OAAO,CAAA;AAAA,UAC1C,KAAO,EAAA;AAAA,SACR;AAAA,OACH;AAAA;AAGF,IAAA,OAAOA,YAAa,CAAA,IAAA;AAAA,MAClB,EAAE,OAAO,qBAAsB,EAAA;AAAA,MAC/B,EAAE,QAAQ,GAAI;AAAA,KAChB;AAAA,GACD,CAAA;AAAA,EAEDD,IAAK,CAAA,IAAA,CAAK,CAAG,EAAA,YAAY,gBAAgB,YAAY;AACnD,IAAA,MAAM,MAAM,GAAG,CAAA;AACf,IAAA,OAAOC,YAAa,CAAA,IAAA,CAAK,EAAE,OAAA,EAAS,MAAM,CAAA;AAAA,GAC3C,CAAA;AAAA,EAEDD,IAAK,CAAA,GAAA,CAAI,CAAG,EAAA,YAAY,YAAY,YAAY;AAC9C,IAAA,MAAM,MAAM,GAAG,CAAA;AACf,IAAA,OAAOC,YAAa,CAAA,IAAA;AAAA,MAClB,qBAAA,CAAsB,gBAAgB;AAAA,KACxC;AAAA,GACD,CAAA;AAAA;AAAA,EAGDD,IAAAA,CAAK,IAAI,CAAG,EAAA,YAAY,UAAU,OAAO,EAAE,SAAc,KAAA;AACvD,IAAA,MAAM,GAAM,GAAA,IAAI,GAAI,CAAA,OAAA,CAAQ,GAAG,CAAA;AAC/B,IAAA,MAAM,OAAO,QAAS,CAAA,GAAA,CAAI,aAAa,GAAI,CAAA,MAAM,KAAK,GAAG,CAAA;AACzD,IAAA,MAAM,QAAQ,QAAS,CAAA,GAAA,CAAI,aAAa,GAAI,CAAA,OAAO,KAAK,IAAI,CAAA;AAE5D,IAAA,MAAM,MAAM,GAAG,CAAA;AAEf,IAAM,MAAA,KAAA,GAAQ,MAAM,IAAK,CAAA,EAAE,QAAQ,KAAM,EAAA,EAAG,MAAM,cAAA,EAAgB,CAAA;AAElE,IAAA,OAAOC,YAAa,CAAA,IAAA;AAAA,MAClB,qBAAsB,CAAA;AAAA,QACpB,IAAM,EAAA,KAAA;AAAA,QACN,UAAY,EAAA;AAAA,UACV,IAAA;AAAA,UACA,KAAA;AAAA,UACA,KAAO,EAAA,GAAA;AAAA,UACP,UAAY,EAAA;AAAA;AACd,OACD;AAAA,KACH;AAAA,GACD,CAAA;AAAA,EAEDD,IAAAA,CAAK,IAAI,CAAG,EAAA,YAAY,cAAc,OAAO,EAAE,QAAa,KAAA;AAC1D,IAAA,MAAM,MAAM,GAAG,CAAA;AACf,IAAA,OAAOC,YAAa,CAAA,IAAA;AAAA,MAClB,sBAAsB,cAAe,CAAA,EAAE,IAAI,MAAO,CAAA,EAAA,EAAc,CAAC;AAAA,KACnE;AAAA,GACD,CAAA;AAAA,EAEDD,IAAAA,CAAK,KAAK,CAAG,EAAA,YAAY,UAAU,OAAO,EAAE,SAAc,KAAA;AACxD,IAAM,MAAA,IAAA,GAAO,MAAM,OAAA,CAAQ,IAAK,EAAA;AAChC,IAAA,MAAM,MAAM,GAAG,CAAA;AAEf,IAAA,OAAOC,YAAa,CAAA,IAAA;AAAA,MAClB,qBAAA,CAAsB,cAAe,CAAA,IAAI,CAAC,CAAA;AAAA,MAC1C,EAAE,QAAQ,GAAI;AAAA,KAChB;AAAA,GACD,CAAA;AAAA,EAEDD,IAAAA,CAAK,IAAI,CAAG,EAAA,YAAY,cAAc,OAAO,EAAE,OAAS,EAAA,MAAA,EAAa,KAAA;AACnE,IAAM,MAAA,IAAA,GAAO,MAAM,OAAA,CAAQ,IAAK,EAAA;AAChC,IAAA,MAAM,MAAM,GAAG,CAAA;AAEf,IAAA,OAAOC,YAAa,CAAA,IAAA;AAAA,MAClB,qBAAA,CAAsB,eAAe,EAAE,GAAG,MAAM,EAAI,EAAA,MAAA,CAAO,EAAa,EAAC,CAAC;AAAA,KAC5E;AAAA,GACD,CAAA;AAAA,EAEDD,IAAK,CAAA,MAAA,CAAO,CAAG,EAAA,YAAY,cAAc,YAAY;AACnD,IAAA,MAAM,MAAM,GAAG,CAAA;AACf,IAAA,OAAOC,YAAa,CAAA,IAAA,CAAK,EAAE,OAAA,EAAS,MAAM,CAAA;AAAA,GAC3C,CAAA;AAAA;AAAA,EAGDD,IAAK,CAAA,GAAA,CAAI,CAAG,EAAA,YAAY,aAAa,YAAY;AAC/C,IAAA,MAAM,MAAM,GAAG,CAAA;AACf,IAAA,OAAOC,YAAa,CAAA,IAAA;AAAA,MAClB,qBAAA,CAAsB,kBAAmB,CAAA,EAAE,CAAC;AAAA,KAC9C;AAAA,GACD,CAAA;AAAA;AAAA,EAGDD,IAAK,CAAA,GAAA,CAAI,CAAG,EAAA,YAAY,UAAU,MAAM;AACtC,IAAA,OAAOC,YAAa,CAAA,IAAA;AAAA,MAClB,EAAE,OAAO,uBAAwB,EAAA;AAAA,MACjC,EAAE,QAAQ,GAAI;AAAA,KAChB;AAAA,GACD;AACH;AAGa,IAAA,mBAAA,GAAsB,CAAC,SAAwB,KAAA;AAC1D,EAAA,OAAO,SAAU,CAAA,GAAA;AAAA,IAAI,CAAA,QAAA,KACnBD,KAAK,GAAI,CAAA,CAAA,EAAG,YAAY,CAAG,EAAA,QAAQ,IAAI,MAAM;AAC3C,MAAA,OAAOC,YAAa,CAAA,IAAA;AAAA,QAClB,EAAE,OAAO,YAAa,EAAA;AAAA,QACtB,EAAE,QAAQ,GAAI;AAAA,OAChB;AAAA,KACD;AAAA,GACH;AACF;AAEa,IAAA,qBAAA,GAAwB,CAAC,MAAmB,KAAA;AACvD,EAAO,OAAA,eAAA,CAAgB,IAAI,CAAW,OAAA,KAAA;AAEpC,IAAO,OAAA,OAAA;AAAA,GACR,CAAA;AACH;AAGO,IAAM,kBAAqB,GAAA,CAChC,QACA,EAAA,SAAA,EACA,UAAU,YACP,KAAA;AACH,EAAA,MAAM,WAAc,GAAA,CAAA,EAAG,OAAO,CAAA,CAAA,EAAI,QAAQ,CAAA,CAAA;AAE1C,EAAO,OAAA;AAAA;AAAA,IAELD,IAAAA,CAAK,GAAI,CAAA,WAAA,EAAa,YAAY;AAChC,MAAA,MAAM,MAAM,GAAG,CAAA;AACf,MAAA,MAAM,QAAQ,KAAM,CAAA,IAAA,CAAK,EAAE,MAAQ,EAAA,EAAA,IAAM,SAAS,CAAA;AAClD,MAAA,OAAOC,YAAa,CAAA,IAAA,CAAK,qBAAsB,CAAA,KAAK,CAAC,CAAA;AAAA,KACtD,CAAA;AAAA;AAAA,IAGDD,IAAAA,CAAK,IAAI,CAAG,EAAA,WAAW,QAAQ,OAAO,EAAE,QAAa,KAAA;AACnD,MAAA,MAAM,MAAM,GAAG,CAAA;AACf,MAAA,OAAOC,YAAa,CAAA,IAAA;AAAA,QAClB,qBAAA,CAAsB,EAAE,GAAG,SAAA,IAAa,EAAI,EAAA,MAAA,CAAO,IAAI;AAAA,OACzD;AAAA,KACD,CAAA;AAAA;AAAA,IAGDD,KAAK,IAAK,CAAA,WAAA,EAAa,OAAO,EAAE,SAAc,KAAA;AAC5C,MAAM,MAAA,IAAA,GAAO,MAAM,OAAA,CAAQ,IAAK,EAAA;AAChC,MAAA,MAAM,MAAM,GAAG,CAAA;AACf,MAAA,OAAOC,YAAa,CAAA,IAAA;AAAA,QAClB,sBAAsB,EAAE,GAAG,WAAa,EAAA,GAAG,MAAM,CAAA;AAAA,QACjD,EAAE,QAAQ,GAAI;AAAA,OAChB;AAAA,KACD,CAAA;AAAA;AAAA,IAGDD,IAAAA,CAAK,IAAI,CAAG,EAAA,WAAW,QAAQ,OAAO,EAAE,OAAS,EAAA,MAAA,EAAa,KAAA;AAC5D,MAAM,MAAA,IAAA,GAAO,MAAM,OAAA,CAAQ,IAAK,EAAA;AAChC,MAAA,MAAM,MAAM,GAAG,CAAA;AACf,MAAA,OAAOC,YAAa,CAAA,IAAA;AAAA,QAClB,qBAAA,CAAsB,EAAE,GAAG,SAAU,EAAA,EAAG,GAAG,IAAM,EAAA,EAAA,EAAI,MAAO,CAAA,EAAA,EAAc;AAAA,OAC5E;AAAA,KACD,CAAA;AAAA;AAAA,IAGDD,IAAK,CAAA,MAAA,CAAO,CAAG,EAAA,WAAW,QAAQ,YAAY;AAC5C,MAAA,MAAM,MAAM,GAAG,CAAA;AACf,MAAA,OAAO,IAAIC,YAAa,CAAA,IAAA,EAAM,EAAE,MAAA,EAAQ,KAAK,CAAA;AAAA,KAC9C;AAAA,GACH;AACF;AAGO,IAAM,0BAA0B,MAAM;AAI3C,EAAA,OAAO,EAAC;AACV;AAGO,IAAM,qBAAwB,GAAA,CAAC,QAAW,GAAA,CAAA,EAAG,YAAY,CAAe,QAAA,CAAA,KAAA;AAC7E,EAAO,OAAA;AAAA,IACLD,KAAK,IAAK,CAAA,QAAA,EAAU,OAAO,EAAE,SAAc,KAAA;AACzC,MAAM,MAAA,IAAA,GAAO,MAAM,OAAA,CAAQ,IAAK,EAAA;AAChC,MAAM,MAAA,EAAE,KAAO,EAAA,SAAA,EAAc,GAAA,IAAA;AAG7B,MAAI,IAAA,KAAA,CAAM,QAAS,CAAA,SAAS,CAAG,EAAA;AAC7B,QAAA,OAAOC,aAAa,IAAK,CAAA;AAAA,UACvB,IAAM,EAAA;AAAA,YACJ,IAAA,EAAM,cAAe,CAAA,SAAA,EAAW,EAAK,GAAA,EAAE,IAAI,SAAU,CAAA,EAAA,EAAO,GAAA,EAAE;AAAA;AAChE,SACD,CAAA;AAAA;AAGH,MAAA,OAAOA,aAAa,IAAK,CAAA;AAAA,QACvB,MAAQ,EAAA,CAAC,EAAE,OAAA,EAAS,oBAAoB;AAAA,OACzC,CAAA;AAAA,KACF;AAAA,GACH;AACF;AC7Ma,IAAA,MAAA,GAAS,WAAY,CAAA,GAAG,eAAe;AAG7C,IAAM,kBAAkB,MAAM;AACnC,EAAA,MAAA,CAAO,MAAO,CAAA;AAAA,IACZ,kBAAoB,EAAA;AAAA;AAAA,GACrB,CAAA;AACH;AAEO,IAAM,iBAAiB,MAAM;AAClC,EAAA,MAAA,CAAO,KAAM,EAAA;AACf;AAEO,IAAM,kBAAkB,MAAM;AACnC,EAAA,MAAA,CAAO,aAAc,EAAA;AACvB;AAGa,IAAA,cAAA,GAAiB,IAAI,QAAoB,KAAA;AACpD,EAAO,MAAA,CAAA,GAAA,CAAI,GAAG,QAAQ,CAAA;AACxB;AAGa,IAAA,mBAAA,GAAsB,CAAC,OAG9B,KAAA;AACJ,EAAA,MAAA,CAAO,OAAO,OAAO,CAAA;AACvB;AAGA,IAAI,cAAmC,EAAC;AAE3B,IAAA,cAAA,GAAiB,CAAC,GAAA,EAAa,KAAe,KAAA;AACzD,EAAA,WAAA,CAAY,GAAG,CAAI,GAAA,KAAA;AACrB;AAEa,IAAA,cAAA,GAAiB,CAAC,GAAgB,KAAA;AAC7C,EAAA,OAAO,YAAY,GAAG,CAAA;AACxB;AAEO,IAAM,mBAAmB,MAAM;AACpC,EAAA,WAAA,GAAc,EAAC;AACjB;AAGO,IAAM,aAAgB,GAAA;AAAA;AAAA,EAE3B,cAAc,MAAM;AAClB,IAAO,MAAA,CAAA,GAAA;AAAA,MACLD,IAAAA,CAAK,GAAI,CAAA,GAAA,EAAK,MAAM;AAClB,QAAA,OAAOC,aAAa,KAAM,EAAA;AAAA,OAC3B;AAAA,KACH;AAAA,GACF;AAAA;AAAA,EAGA,WAAA,EAAa,CAAC,QAAA,GAAW,GAAS,KAAA;AAChC,IAAO,MAAA,CAAA,GAAA;AAAA,MACL,GAAG,eAAA,CAAgB,GAAI,CAAA,CAAC,OAAY,KAAA;AAElC,QAAO,OAAA,OAAA;AAAA,OACR;AAAA,KACH;AAAA,GACF;AAAA;AAAA,EAGA,cAAc,MAAM;AAClB,IAAO,MAAA,CAAA,GAAA;AAAA,MACLD,IAAAA,CAAK,GAAI,CAAA,GAAA,EAAK,MAAM;AAClB,QAAA,OAAOC,YAAa,CAAA,IAAA;AAAA,UAClB,EAAE,OAAO,uBAAwB,EAAA;AAAA,UACjC,EAAE,QAAQ,GAAI;AAAA,SAChB;AAAA,OACD;AAAA,KACH;AAAA,GACF;AAAA;AAAA,EAGA,OAAO,MAAM;AACX,IAAO,MAAA,CAAA,aAAA,CAAc,GAAG,eAAe,CAAA;AAAA;AAE3C;ACnFa,IAAA,MAAA,GAAS,WAAY,CAAA,GAAG,eAAe;AAG7C,IAAM,kBAAkB,YAAY;AACzC,EAAI,IAAA,OAAO,WAAW,WAAa,EAAA;AACjC,IAAA,OAAO,OAAO,KAAM,CAAA;AAAA,MAClB,kBAAoB,EAAA,QAAA;AAAA,MACpB,aAAe,EAAA;AAAA,QACb,GAAK,EAAA;AAAA;AACP,KACD,CAAA;AAAA;AAEH,EAAO,OAAA,MAAA;AACT;AAEO,IAAM,iBAAiB,MAAM;AAClC,EAAI,IAAA,OAAO,WAAW,WAAa,EAAA;AACjC,IAAA,MAAA,CAAO,IAAK,EAAA;AAAA;AAEhB;AAGO,IAAM,6BAA6B,MAAM;AAC9C,EAAA,IAAI,QAAQ,GAAI,CAAA,QAAA,KAAa,iBAAiB,OAAQ,CAAA,GAAA,CAAI,wBAAwB,MAAQ,EAAA;AACxF,IAAgB,eAAA,EAAA,CAAE,KAAK,MAAM;AAC3B,MAAA,OAAA,CAAQ,IAAI,uBAAuB,CAAA;AAAA,KACpC,CAAA;AAAA;AAEL;AAGO,IAAM,4BAA4B,MAAM;AAC7C,EAAI,IAAA,OAAO,WAAW,WAAe,IAAA,MAAA,CAAO,SAAS,QAAS,CAAA,QAAA,CAAS,aAAa,CAAG,EAAA;AACrF,IAAgB,eAAA,EAAA;AAAA;AAEpB", "file": "index.js", "sourcesContent": ["import { http, HttpResponse } from 'msw'\n\n// API mock utilities\nexport class MockAPI {\n  private baseUrl: string\n  private handlers: any[] = []\n\n  constructor(baseUrl = 'http://localhost:3000/api') {\n    this.baseUrl = baseUrl\n  }\n\n  // Add a GET endpoint\n  get(path: string, response: any, options: { delay?: number; status?: number } = {}) {\n    const handler = http.get(`${this.baseUrl}${path}`, async () => {\n      if (options.delay) {\n        await new Promise(resolve => setTimeout(resolve, options.delay))\n      }\n      return HttpResponse.json(response, { status: options.status || 200 })\n    })\n    this.handlers.push(handler)\n    return this\n  }\n\n  // Add a POST endpoint\n  post(path: string, handler: (body: any) => any, options: { delay?: number } = {}) {\n    const mockHandler = http.post(`${this.baseUrl}${path}`, async ({ request }) => {\n      const body = await request.json()\n      if (options.delay) {\n        await new Promise(resolve => setTimeout(resolve, options.delay))\n      }\n      const response = await handler(body)\n      return HttpResponse.json(response)\n    })\n    this.handlers.push(mockHandler)\n    return this\n  }\n\n  // Add a PUT endpoint\n  put(path: string, handler: (params: any, body: any) => any, options: { delay?: number } = {}) {\n    const mockHandler = http.put(`${this.baseUrl}${path}`, async ({ params, request }) => {\n      const body = await request.json()\n      if (options.delay) {\n        await new Promise(resolve => setTimeout(resolve, options.delay))\n      }\n      const response = await handler(params, body)\n      return HttpResponse.json(response)\n    })\n    this.handlers.push(mockHandler)\n    return this\n  }\n\n  // Add a DELETE endpoint\n  delete(path: string, response: any = { success: true }, options: { delay?: number } = {}) {\n    const handler = http.delete(`${this.baseUrl}${path}`, async () => {\n      if (options.delay) {\n        await new Promise(resolve => setTimeout(resolve, options.delay))\n      }\n      return HttpResponse.json(response)\n    })\n    this.handlers.push(handler)\n    return this\n  }\n\n  // Add error response\n  error(path: string, error: { message: string; code?: string }, status = 500) {\n    const handler = http.get(`${this.baseUrl}${path}`, () => {\n      return HttpResponse.json({ error }, { status })\n    })\n    this.handlers.push(handler)\n    return this\n  }\n\n  // Get all handlers\n  getHandlers() {\n    return this.handlers\n  }\n\n  // Create paginated response\n  paginated(path: string, itemGenerator: () => any, totalItems = 100) {\n    const handler = http.get(`${this.baseUrl}${path}`, ({ request }) => {\n      const url = new URL(request.url)\n      const page = parseInt(url.searchParams.get('page') || '1')\n      const limit = parseInt(url.searchParams.get('limit') || '10')\n      const offset = (page - 1) * limit\n\n      const items = Array.from({ length: Math.min(limit, totalItems - offset) }, itemGenerator)\n\n      return HttpResponse.json({\n        data: items,\n        pagination: {\n          page,\n          limit,\n          total: totalItems,\n          totalPages: Math.ceil(totalItems / limit),\n          hasNext: page < Math.ceil(totalItems / limit),\n          hasPrev: page > 1,\n        },\n      })\n    })\n    this.handlers.push(handler)\n    return this\n  }\n\n  // Create CRUD endpoints for a resource\n  crud(resource: string, generator: () => any) {\n    const items = new Map<string, any>()\n\n    // List\n    this.get(`/${resource}`, () => {\n      return Array.from(items.values())\n    })\n\n    // Get by ID\n    this.handlers.push(\n      http.get(`${this.baseUrl}/${resource}/:id`, ({ params }) => {\n        const item = items.get(params.id as string)\n        if (!item) {\n          return HttpResponse.json({ error: 'Not found' }, { status: 404 })\n        }\n        return HttpResponse.json(item)\n      })\n    )\n\n    // Create\n    this.post(`/${resource}`, (body) => {\n      const id = Date.now().toString()\n      const item = { ...generator(), ...body, id }\n      items.set(id, item)\n      return item\n    })\n\n    // Update\n    this.handlers.push(\n      http.put(`${this.baseUrl}/${resource}/:id`, async ({ params, request }) => {\n        const body = await request.json() as Record<string, any>\n        const id = params.id as string\n        const existing = items.get(id)\n        if (!existing) {\n          return HttpResponse.json({ error: 'Not found' }, { status: 404 })\n        }\n        const updated = { ...existing, ...body }\n        items.set(id, updated)\n        return HttpResponse.json(updated)\n      })\n    )\n\n    // Delete\n    this.handlers.push(\n      http.delete(`${this.baseUrl}/${resource}/:id`, ({ params }) => {\n        const id = params.id as string\n        if (!items.has(id)) {\n          return HttpResponse.json({ error: 'Not found' }, { status: 404 })\n        }\n        items.delete(id)\n        return new HttpResponse(null, { status: 204 })\n      })\n    )\n\n    return this\n  }\n\n  // Create file upload endpoint\n  fileUpload(path: string, handler?: (file: File) => any) {\n    const uploadHandler = http.post(`${this.baseUrl}${path}`, async ({ request }) => {\n      const formData = await request.formData()\n      const file = formData.get('file') as File\n\n      if (!file) {\n        return HttpResponse.json({ error: 'No file provided' }, { status: 400 })\n      }\n\n      const response = handler ? await handler(file) : {\n        id: Date.now().toString(),\n        filename: file.name,\n        size: file.size,\n        type: file.type,\n        url: `/uploads/${file.name}`,\n      }\n\n      return HttpResponse.json(response)\n    })\n    this.handlers.push(uploadHandler)\n    return this\n  }\n}\n\n// Pre-configured mock APIs\nexport const createMockAuthAPI = () => {\n  return new MockAPI()\n    .post('/auth/login', ({ email, password }) => {\n      if (email === '<EMAIL>' && password === 'password') {\n        return {\n          user: { id: '1', email, name: 'Test User' },\n          token: 'mock-jwt-token',\n        }\n      }\n      throw new Error('Invalid credentials')\n    })\n    .post('/auth/logout', () => ({ success: true }))\n    .get('/auth/me', { id: '1', email: '<EMAIL>', name: 'Test User' })\n}\n\nexport const createMockUserAPI = () => {\n  return new MockAPI().crud('users', () => ({\n    name: 'John Doe',\n    email: '<EMAIL>',\n    role: 'user',\n  }))\n}", "import { faker } from '@faker-js/faker'\n\n// User fixtures\nexport const createMockUser = (overrides: Partial<any> = {}) => ({\n  id: faker.string.uuid(),\n  email: faker.internet.email(),\n  name: faker.person.fullName(),\n  avatar: faker.image.avatar(),\n  role: 'user',\n  createdAt: faker.date.past().toISOString(),\n  updatedAt: faker.date.recent().toISOString(),\n  ...overrides,\n})\n\nexport const createMockUsers = (count: number, overrides: Partial<any> = {}) => {\n  return Array.from({ length: count }, () => createMockUser(overrides))\n}\n\n// Auth fixtures\nexport const createMockAuthToken = () => ({\n  access_token: faker.string.alphanumeric(32),\n  refresh_token: faker.string.alphanumeric(32),\n  expires_in: 3600,\n  token_type: 'Bearer',\n})\n\nexport const createMockSession = (overrides: Partial<any> = {}) => ({\n  user: createMockUser(),\n  token: createMockAuthToken(),\n  expiresAt: faker.date.future().toISOString(),\n  ...overrides,\n})\n\n// API response fixtures\nexport const createMockApiResponse = <T = any>(data: T, overrides: Partial<any> = {}) => ({\n  data,\n  status: 'success',\n  message: 'Operation successful',\n  timestamp: new Date().toISOString(),\n  ...overrides,\n})\n\nexport const createMockApiError = (overrides: Partial<any> = {}) => ({\n  status: 'error',\n  message: faker.lorem.sentence(),\n  code: faker.string.alphanumeric(6).toUpperCase(),\n  timestamp: new Date().toISOString(),\n  ...overrides,\n})\n\nexport const createMockPaginatedResponse = <T = any>(\n  items: T[],\n  overrides: Partial<any> = {}\n) => ({\n  data: items,\n  pagination: {\n    page: 1,\n    pageSize: 10,\n    total: items.length,\n    totalPages: Math.ceil(items.length / 10),\n  },\n  ...overrides,\n})\n\n// Form data fixtures\nexport const createMockFormData = () => ({\n  name: faker.person.fullName(),\n  email: faker.internet.email(),\n  phone: faker.phone.number(),\n  message: faker.lorem.paragraph(),\n  company: faker.company.name(),\n  website: faker.internet.url(),\n})\n\n// File upload fixtures\nexport const createMockFile = (overrides: Partial<File> = {}): File => {\n  const content = faker.lorem.paragraphs()\n  const blob = new Blob([content], { type: 'text/plain' })\n  return new File([blob], faker.system.fileName(), {\n    type: 'text/plain',\n    lastModified: Date.now(),\n    ...overrides,\n  })\n}\n\nexport const createMockImageFile = (overrides: Partial<File> = {}): File => {\n  const canvas = document.createElement('canvas')\n  canvas.width = 100\n  canvas.height = 100\n  const ctx = canvas.getContext('2d')!\n  ctx.fillStyle = faker.color.rgb()\n  ctx.fillRect(0, 0, 100, 100)\n  \n  return new Promise<File>((resolve) => {\n    canvas.toBlob((blob) => {\n      const file = new File([blob!], faker.system.fileName() + '.png', {\n        type: 'image/png',\n        lastModified: Date.now(),\n        ...overrides,\n      })\n      resolve(file)\n    })\n  }) as any // Type assertion for simplicity\n}\n\n// Date fixtures\nexport const createMockDateRange = () => ({\n  start: faker.date.past(),\n  end: faker.date.future(),\n})\n\nexport const createMockTimeSlots = (count: number) => {\n  return Array.from({ length: count }, (_, i) => ({\n    id: faker.string.uuid(),\n    time: `${9 + i}:00`,\n    available: faker.datatype.boolean(),\n  }))\n}\n\n// Product/Item fixtures\nexport const createMockProduct = (overrides: Partial<any> = {}) => ({\n  id: faker.string.uuid(),\n  name: faker.commerce.productName(),\n  description: faker.commerce.productDescription(),\n  price: parseFloat(faker.commerce.price()),\n  image: faker.image.url(),\n  category: faker.commerce.department(),\n  inStock: faker.datatype.boolean(),\n  rating: faker.number.float({ min: 1, max: 5, fractionDigits: 1 }),\n  ...overrides,\n})\n\nexport const createMockProducts = (count: number, overrides: Partial<any> = {}) => {\n  return Array.from({ length: count }, () => createMockProduct(overrides))\n}\n\n// Notification fixtures\nexport const createMockNotification = (overrides: Partial<any> = {}) => ({\n  id: faker.string.uuid(),\n  type: faker.helpers.arrayElement(['info', 'success', 'warning', 'error']),\n  title: faker.lorem.sentence(),\n  message: faker.lorem.paragraph(),\n  read: faker.datatype.boolean(),\n  createdAt: faker.date.recent().toISOString(),\n  ...overrides,\n})\n\nexport const createMockNotifications = (count: number, overrides: Partial<any> = {}) => {\n  return Array.from({ length: count }, () => createMockNotification(overrides))\n}\n\n// Settings fixtures\nexport const createMockSettings = (overrides: Partial<any> = {}) => ({\n  theme: faker.helpers.arrayElement(['light', 'dark', 'auto']),\n  language: faker.helpers.arrayElement(['en', 'es', 'fr', 'de']),\n  notifications: {\n    email: faker.datatype.boolean(),\n    push: faker.datatype.boolean(),\n    sms: faker.datatype.boolean(),\n  },\n  privacy: {\n    profileVisible: faker.datatype.boolean(),\n    showEmail: faker.datatype.boolean(),\n    showPhone: faker.datatype.boolean(),\n  },\n  ...overrides,\n})\n\n// Chart data fixtures\nexport const createMockChartData = (points: number = 7) => ({\n  labels: Array.from({ length: points }, (_, i) => \n    faker.date.recent({ days: points - i }).toLocaleDateString()\n  ),\n  datasets: [\n    {\n      label: faker.lorem.word(),\n      data: Array.from({ length: points }, () => \n        faker.number.int({ min: 10, max: 100 })\n      ),\n    },\n  ],\n})\n\n// Table data fixtures\nexport const createMockTableData = (rows: number = 10, columns: string[] = ['id', 'name', 'email', 'status']) => {\n  return Array.from({ length: rows }, () => {\n    const row: Record<string, any> = {}\n    columns.forEach(col => {\n      switch (col) {\n        case 'id':\n          row[col] = faker.string.uuid()\n          break\n        case 'name':\n          row[col] = faker.person.fullName()\n          break\n        case 'email':\n          row[col] = faker.internet.email()\n          break\n        case 'status':\n          row[col] = faker.helpers.arrayElement(['active', 'inactive', 'pending'])\n          break\n        default:\n          row[col] = faker.lorem.word()\n      }\n    })\n    return row\n  })\n}\n\n// Fixture factory\nexport const createFixture = <T>(\n  generator: () => T,\n  count?: number\n): T | T[] => {\n  if (count === undefined) {\n    return generator()\n  }\n  return Array.from({ length: count }, generator)\n}", "import { http, HttpResponse, delay } from 'msw'\nimport { createMockUser, createMockApiResponse, createMockProducts } from '../fixtures'\n\n// Base API URL - can be configured via environment variable\nconst API_BASE_URL = process.env.VITE_API_URL || 'http://localhost:3000/api'\n\n// Default handlers for common API endpoints\nexport const defaultHandlers = [\n  // Auth endpoints\n  http.post(`${API_BASE_URL}/auth/login`, async ({ request }) => {\n    const body = await request.json() as any\n    await delay(300) // Simulate network delay\n    \n    if (body.email === '<EMAIL>' && body.password === 'password') {\n      return HttpResponse.json(\n        createMockApiResponse({\n          user: createMockUser({ email: body.email }),\n          token: 'mock-jwt-token',\n        })\n      )\n    }\n    \n    return HttpResponse.json(\n      { error: 'Invalid credentials' },\n      { status: 401 }\n    )\n  }),\n\n  http.post(`${API_BASE_URL}/auth/logout`, async () => {\n    await delay(100)\n    return HttpResponse.json({ success: true })\n  }),\n\n  http.get(`${API_BASE_URL}/auth/me`, async () => {\n    await delay(200)\n    return HttpResponse.json(\n      createMockApiResponse(createMockUser())\n    )\n  }),\n\n  // User endpoints\n  http.get(`${API_BASE_URL}/users`, async ({ request }) => {\n    const url = new URL(request.url)\n    const page = parseInt(url.searchParams.get('page') || '1')\n    const limit = parseInt(url.searchParams.get('limit') || '10')\n    \n    await delay(300)\n    \n    const users = Array.from({ length: limit }, () => createMockUser())\n    \n    return HttpResponse.json(\n      createMockApiResponse({\n        data: users,\n        pagination: {\n          page,\n          limit,\n          total: 100,\n          totalPages: 10,\n        },\n      })\n    )\n  }),\n\n  http.get(`${API_BASE_URL}/users/:id`, async ({ params }) => {\n    await delay(200)\n    return HttpResponse.json(\n      createMockApiResponse(createMockUser({ id: params.id as string }))\n    )\n  }),\n\n  http.post(`${API_BASE_URL}/users`, async ({ request }) => {\n    const body = await request.json() as any\n    await delay(300)\n    \n    return HttpResponse.json(\n      createMockApiResponse(createMockUser(body)),\n      { status: 201 }\n    )\n  }),\n\n  http.put(`${API_BASE_URL}/users/:id`, async ({ request, params }) => {\n    const body = await request.json() as any\n    await delay(300)\n    \n    return HttpResponse.json(\n      createMockApiResponse(createMockUser({ ...body, id: params.id as string }))\n    )\n  }),\n\n  http.delete(`${API_BASE_URL}/users/:id`, async () => {\n    await delay(200)\n    return HttpResponse.json({ success: true })\n  }),\n\n  // Products endpoints\n  http.get(`${API_BASE_URL}/products`, async () => {\n    await delay(400)\n    return HttpResponse.json(\n      createMockApiResponse(createMockProducts(20))\n    )\n  }),\n\n  // Generic error handler\n  http.get(`${API_BASE_URL}/error`, () => {\n    return HttpResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }),\n]\n\n// Create custom handlers for specific test scenarios\nexport const createErrorHandlers = (endpoints: string[]) => {\n  return endpoints.map(endpoint => \n    http.get(`${API_BASE_URL}${endpoint}`, () => {\n      return HttpResponse.json(\n        { error: 'Test error' },\n        { status: 500 }\n      )\n    })\n  )\n}\n\nexport const createDelayedHandlers = (_delay: number) => {\n  return defaultHandlers.map(handler => {\n    // Clone handler with custom delay\n    return handler\n  })\n}\n\n// Handler utilities\nexport const createCrudHandlers = <T>(\n  resource: string,\n  generator: () => T,\n  baseUrl = API_BASE_URL\n) => {\n  const resourceUrl = `${baseUrl}/${resource}`\n  \n  return [\n    // List\n    http.get(resourceUrl, async () => {\n      await delay(200)\n      const items = Array.from({ length: 10 }, generator)\n      return HttpResponse.json(createMockApiResponse(items))\n    }),\n    \n    // Get by ID\n    http.get(`${resourceUrl}/:id`, async ({ params }) => {\n      await delay(150)\n      return HttpResponse.json(\n        createMockApiResponse({ ...generator(), id: params.id })\n      )\n    }),\n    \n    // Create\n    http.post(resourceUrl, async ({ request }) => {\n      const body = await request.json() as any\n      await delay(250)\n      return HttpResponse.json(\n        createMockApiResponse({ ...generator(), ...body }),\n        { status: 201 }\n      )\n    }),\n    \n    // Update\n    http.put(`${resourceUrl}/:id`, async ({ request, params }) => {\n      const body = await request.json() as any\n      await delay(200)\n      return HttpResponse.json(\n        createMockApiResponse({ ...generator(), ...body, id: params.id as string })\n      )\n    }),\n    \n    // Delete\n    http.delete(`${resourceUrl}/:id`, async () => {\n      await delay(150)\n      return new HttpResponse(null, { status: 204 })\n    }),\n  ]\n}\n\n// WebSocket mock handlers\nexport const createWebSocketHandlers = () => {\n  // WebSocket mocking would be implemented here\n  // MSW doesn't support WebSocket mocking directly,\n  // but we can create mock implementations\n  return []\n}\n\n// GraphQL handlers\nexport const createGraphQLHandlers = (endpoint = `${API_BASE_URL}/graphql`) => {\n  return [\n    http.post(endpoint, async ({ request }) => {\n      const body = await request.json() as any\n      const { query, variables } = body\n      \n      // Simple query matching\n      if (query.includes('GetUser')) {\n        return HttpResponse.json({\n          data: {\n            user: createMockUser(variables?.id ? { id: variables.id } : {}),\n          },\n        })\n      }\n      \n      return HttpResponse.json({\n        errors: [{ message: 'Query not mocked' }],\n      })\n    }),\n  ]\n}", "import { setupServer } from 'msw/node'\nimport { http, HttpResponse } from 'msw'\nimport { defaultHandlers } from './handlers'\n\n// Setup MSW server for Node.js (tests)\nexport const server = setupServer(...defaultHandlers)\n\n// Server lifecycle management\nexport const startMockServer = () => {\n  server.listen({\n    onUnhandledRequest: 'bypass', // or 'warn' or 'error'\n  })\n}\n\nexport const stopMockServer = () => {\n  server.close()\n}\n\nexport const resetMockServer = () => {\n  server.resetHandlers()\n}\n\n// Add handlers dynamically\nexport const addMockHandler = (...handlers: any[]) => {\n  server.use(...handlers)\n}\n\n// Test-specific server configuration\nexport const configureMockServer = (options: {\n  onUnhandledRequest?: 'bypass' | 'warn' | 'error'\n  quiet?: boolean\n}) => {\n  server.listen(options)\n}\n\n// Server state management\nlet serverState: Record<string, any> = {}\n\nexport const setServerState = (key: string, value: any) => {\n  serverState[key] = value\n}\n\nexport const getServerState = (key: string) => {\n  return serverState[key]\n}\n\nexport const clearServerState = () => {\n  serverState = {}\n}\n\n// Mock server scenarios\nexport const mockScenarios = {\n  // Simulate network failure\n  networkError: () => {\n    server.use(\n      http.all('*', () => {\n        return HttpResponse.error()\n      })\n    )\n  },\n\n  // Simulate slow network\n  slowNetwork: (_delayMs = 3000) => {\n    server.use(\n      ...defaultHandlers.map((handler) => {\n        // Add delay to handler\n        return handler\n      })\n    )\n  },\n\n  // Simulate server errors\n  serverErrors: () => {\n    server.use(\n      http.all('*', () => {\n        return HttpResponse.json(\n          { error: 'Internal Server Error' },\n          { status: 500 }\n        )\n      })\n    )\n  },\n\n  // Reset to default\n  reset: () => {\n    server.resetHandlers(...defaultHandlers)\n  },\n}", "import { setupWorker } from 'msw/browser'\nimport { defaultHandlers } from './handlers'\n\n// Setup MSW worker for browser (development/Storybook)\nexport const worker = setupWorker(...defaultHandlers)\n\n// Browser-specific configuration\nexport const startMockWorker = async () => {\n  if (typeof window !== 'undefined') {\n    return worker.start({\n      onUnhandledRequest: 'bypass',\n      serviceWorker: {\n        url: '/mockServiceWorker.js',\n      },\n    })\n  }\n  return undefined\n}\n\nexport const stopMockWorker = () => {\n  if (typeof window !== 'undefined') {\n    worker.stop()\n  }\n}\n\n// Development helpers\nexport const enableMockingInDevelopment = () => {\n  if (process.env.NODE_ENV === 'development' && process.env.VITE_ENABLE_MOCKING === 'true') {\n    startMockWorker().then(() => {\n      console.log('[MSW] Mocking enabled')\n    })\n  }\n}\n\n// Storybook integration\nexport const enableMockingForStorybook = () => {\n  if (typeof window !== 'undefined' && window.location.pathname.includes('iframe.html')) {\n    startMockWorker()\n  }\n}"]}