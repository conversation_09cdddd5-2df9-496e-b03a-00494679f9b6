/**
 * Luminar API Client - Unified HTTP client with interceptors, retry logic, and error handling
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import {
  LuminarAPIClient,
  LuminarAPIClientConfig,
  ApiResponse,
  ApiError,
  RequestConfig,
  RequestInterceptor,
  ResponseInterceptor,
  ErrorType,
  ErrorContext,
  RetryConfig
} from './types';
import { ApiErrorHandler } from './error-handler';

export class LuminarAPIClientImpl implements LuminarAPIClient {
  private axiosInstance: AxiosInstance;
  private config: LuminarAPIClientConfig;
  private requestInterceptors: Map<number, RequestInterceptor> = new Map();
  private responseInterceptors: Map<number, ResponseInterceptor> = new Map();
  private interceptorIdCounter = 0;
  private errorHandler: ApiErrorHandler;

  constructor(config: LuminarAPIClientConfig) {
    this.config = { ...config };
    this.errorHandler = new ApiErrorHandler();
    
    this.axiosInstance = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 10000,
      headers: {
        'Content-Type': 'application/json',
        ...config.defaultHeaders
      }
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        // Apply all registered request interceptors
        let modifiedConfig = { ...config };
        
        for (const interceptor of this.requestInterceptors.values()) {
          if (interceptor.onRequest) {
            const requestData: RequestConfig & { url: string; method: string; data?: any } = {
              url: modifiedConfig.url || '',
              method: modifiedConfig.method || 'get',
              data: modifiedConfig.data,
              headers: (modifiedConfig.headers as Record<string, string>) || {},
              timeout: modifiedConfig.timeout,
              retries: (modifiedConfig as any).retries,
              retryDelay: (modifiedConfig as any).retryDelay,
              signal: modifiedConfig.signal as AbortSignal | undefined
            };
            
            const result = await interceptor.onRequest(requestData);
            modifiedConfig = {
              ...modifiedConfig,
              url: result.url,
              method: result.method as any,
              data: result.data,
              headers: result.headers as any,
              timeout: result.timeout,
              signal: result.signal as any
            };
            (modifiedConfig as any).retries = result.retries;
            (modifiedConfig as any).retryDelay = result.retryDelay;
          }
        }

        if (this.config.enableLogging) {
          console.log(`[API Request] ${modifiedConfig.method?.toUpperCase()} ${modifiedConfig.url}`);
        }

        return modifiedConfig;
      },
      async (error) => {
        // Apply request error interceptors
        let modifiedError = error;
        
        for (const interceptor of this.requestInterceptors.values()) {
          if (interceptor.onRequestError) {
            modifiedError = await interceptor.onRequestError(modifiedError);
          }
        }

        return Promise.reject(modifiedError);
      }
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      async (response) => {
        const apiResponse = this.transformResponse(response);
        
        // Apply all registered response interceptors
        let modifiedResponse = apiResponse;
        
        for (const interceptor of this.responseInterceptors.values()) {
          if (interceptor.onResponse) {
            modifiedResponse = await interceptor.onResponse(modifiedResponse);
          }
        }

        if (this.config.enableLogging) {
          console.log(`[API Response] ${response.status} ${response.config.url}`);
        }

        return response;
      },
      async (error) => {
        const apiError = this.errorHandler.handleAxiosError(error);
        
        // Apply response error interceptors
        let modifiedError: ApiError | Error = apiError;
        
        for (const interceptor of this.responseInterceptors.values()) {
          if (interceptor.onResponseError) {
            modifiedError = await interceptor.onResponseError(modifiedError);
          }
        }

        if (this.config.enableLogging) {
          console.error(`[API Error] ${error.response?.status || 'Network'} ${error.config?.url}`, modifiedError);
        }

        return Promise.reject(modifiedError);
      }
    );
  }

  private transformResponse<T>(response: AxiosResponse): ApiResponse<T> {
    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      message: response.data?.message,
      errors: response.data?.errors,
      metadata: response.data?.metadata
    };
  }

  private async executeWithRetry<T>(
    requestFn: () => Promise<AxiosResponse>,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    const retryConfig: RetryConfig = {
      maxAttempts: config?.retries || this.config.retries || 3,
      backoffStrategy: 'exponential',
      baseDelay: config?.retryDelay || this.config.retryDelay || 1000,
      maxDelay: 10000,
      retryCondition: (error: Error) => {
        if (error instanceof AxiosError) {
          // Retry on network errors or 5xx server errors
          return !error.response || (error.response.status >= 500 && error.response.status < 600);
        }
        return false;
      }
    };

    let lastError: Error;
    
    for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
      try {
        const response = await requestFn();
        return this.transformResponse<T>(response);
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === retryConfig.maxAttempts || !retryConfig.retryCondition?.(lastError)) {
          throw lastError;
        }

        // Calculate delay for next retry
        const delay = retryConfig.backoffStrategy === 'exponential'
          ? Math.min(retryConfig.baseDelay * Math.pow(2, attempt - 1), retryConfig.maxDelay)
          : retryConfig.baseDelay;

        if (this.config.enableLogging) {
          console.log(`[API Retry] Attempt ${attempt + 1}/${retryConfig.maxAttempts} in ${delay}ms`);
        }

        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  // HTTP Methods
  async get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.executeWithRetry<T>(
      () => this.axiosInstance.get(url, this.mergeConfig(config)),
      config
    );
  }

  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.executeWithRetry<T>(
      () => this.axiosInstance.post(url, data, this.mergeConfig(config)),
      config
    );
  }

  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.executeWithRetry<T>(
      () => this.axiosInstance.put(url, data, this.mergeConfig(config)),
      config
    );
  }

  async patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.executeWithRetry<T>(
      () => this.axiosInstance.patch(url, data, this.mergeConfig(config)),
      config
    );
  }

  async delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.executeWithRetry<T>(
      () => this.axiosInstance.delete(url, this.mergeConfig(config)),
      config
    );
  }

  // Interceptor Management
  addRequestInterceptor(interceptor: RequestInterceptor): number {
    const id = ++this.interceptorIdCounter;
    this.requestInterceptors.set(id, interceptor);
    return id;
  }

  addResponseInterceptor(interceptor: ResponseInterceptor): number {
    const id = ++this.interceptorIdCounter;
    this.responseInterceptors.set(id, interceptor);
    return id;
  }

  removeRequestInterceptor(id: number): void {
    this.requestInterceptors.delete(id);
  }

  removeResponseInterceptor(id: number): void {
    this.responseInterceptors.delete(id);
  }

  // Configuration Management
  setBaseURL(url: string): void {
    this.config.baseURL = url;
    this.axiosInstance.defaults.baseURL = url;
  }

  setDefaultHeaders(headers: Record<string, string>): void {
    this.config.defaultHeaders = { ...this.config.defaultHeaders, ...headers };
    Object.assign(this.axiosInstance.defaults.headers, headers);
  }

  getConfig(): LuminarAPIClientConfig {
    return { ...this.config };
  }

  private mergeConfig(config?: RequestConfig): AxiosRequestConfig {
    if (!config) return {};
    
    return {
      headers: config.headers,
      timeout: config.timeout,
      signal: config.signal
    };
  }
}

// Factory function to create API client instance
export function createLuminarAPIClient(config: LuminarAPIClientConfig): LuminarAPIClient {
  return new LuminarAPIClientImpl(config);
}

// Default client instance (can be configured later)
let defaultClient: LuminarAPIClient | null = null;

export function getDefaultAPIClient(): LuminarAPIClient {
  if (!defaultClient) {
    throw new Error('Default API client not initialized. Call initializeDefaultAPIClient() first.');
  }
  return defaultClient;
}

export function initializeDefaultAPIClient(config: LuminarAPIClientConfig): void {
  defaultClient = createLuminarAPIClient(config);
}

export function setDefaultAPIClient(client: LuminarAPIClient): void {
  defaultClient = client;
}