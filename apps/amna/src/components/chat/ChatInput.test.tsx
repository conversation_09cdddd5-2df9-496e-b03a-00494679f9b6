import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import { render } from '../../test/test-utils'
import { ChatInput } from './ChatInput'

describe('ChatInput', () => {
  const defaultProps = {
    onSendMessage: vi.fn(),
    onFileUpload: vi.fn(),
    onVoiceRecord: vi.fn(),
    placeholder: 'Type your message...',
    disabled: false,
    maxLength: 1000,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders input with placeholder', () => {
    render(<ChatInput {...defaultProps} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    expect(input).toBeInTheDocument()
  })

  it('sends message on Enter key', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    await user.type(input, 'Hello AMNA!')
    await user.keyboard('{Enter}')

    expect(defaultProps.onSendMessage).toHaveBeenCalledWith('Hello AMNA!')
    expect(input).toHaveValue('') // Input should be cleared
  })

  it('sends message on send button click', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    await user.type(input, 'Hello AMNA!')
    
    const sendButton = screen.getByRole('button', { name: /send/i })
    await user.click(sendButton)

    expect(defaultProps.onSendMessage).toHaveBeenCalledWith('Hello AMNA!')
    expect(input).toHaveValue('')
  })

  it('does not send empty messages', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    await user.keyboard('{Enter}')

    expect(defaultProps.onSendMessage).not.toHaveBeenCalled()
  })

  it('does not send whitespace-only messages', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    await user.type(input, '   ')
    await user.keyboard('{Enter}')

    expect(defaultProps.onSendMessage).not.toHaveBeenCalled()
  })

  it('allows multiline input with Shift+Enter', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    await user.type(input, 'Line 1')
    await user.keyboard('{Shift>}{Enter}{/Shift}')
    await user.type(input, 'Line 2')

    expect(input).toHaveValue('Line 1\nLine 2')
    expect(defaultProps.onSendMessage).not.toHaveBeenCalled()
  })

  it('disables input when disabled prop is true', () => {
    render(<ChatInput {...defaultProps} disabled={true} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    expect(input).toBeDisabled()
    
    const sendButton = screen.getByRole('button', { name: /send/i })
    expect(sendButton).toBeDisabled()
  })

  it('shows character count when approaching limit', async () => {
    const { user } = render(<ChatInput {...defaultProps} maxLength={100} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    const longMessage = 'a'.repeat(85) // 85% of limit
    await user.type(input, longMessage)

    expect(screen.getByText('85/100')).toBeInTheDocument()
  })

  it('prevents typing beyond maxLength', async () => {
    const { user } = render(<ChatInput {...defaultProps} maxLength={10} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    await user.type(input, 'This is a very long message')

    expect(input).toHaveValue('This is a ') // Only 10 characters
  })

  it('handles file upload button click', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const uploadButton = screen.getByRole('button', { name: /upload file/i })
    await user.click(uploadButton)

    // Should trigger file input click
    expect(screen.getByLabelText(/choose file/i)).toBeInTheDocument()
  })

  it('handles file selection', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const file = new File(['test content'], 'test.txt', { type: 'text/plain' })
    const fileInput = screen.getByLabelText(/choose file/i)
    
    await user.upload(fileInput, file)

    await waitFor(() => {
      expect(defaultProps.onFileUpload).toHaveBeenCalledWith(file)
    })
  })

  it('validates file type', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const file = new File(['test'], 'test.exe', { type: 'application/x-msdownload' })
    const fileInput = screen.getByLabelText(/choose file/i)
    
    await user.upload(fileInput, file)

    expect(screen.getByText(/file type not allowed/i)).toBeInTheDocument()
    expect(defaultProps.onFileUpload).not.toHaveBeenCalled()
  })

  it('validates file size', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    // Create a large file (over 10MB)
    const largeContent = new Uint8Array(11 * 1024 * 1024)
    const file = new File([largeContent], 'large.pdf', { type: 'application/pdf' })
    const fileInput = screen.getByLabelText(/choose file/i)
    
    await user.upload(fileInput, file)

    expect(screen.getByText(/file size exceeds 10MB limit/i)).toBeInTheDocument()
    expect(defaultProps.onFileUpload).not.toHaveBeenCalled()
  })

  it('shows voice recording button', () => {
    render(<ChatInput {...defaultProps} />)
    
    const voiceButton = screen.getByRole('button', { name: /voice record/i })
    expect(voiceButton).toBeInTheDocument()
  })

  it('toggles voice recording on button click', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const voiceButton = screen.getByRole('button', { name: /voice record/i })
    await user.click(voiceButton)

    expect(screen.getByText(/recording/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /stop recording/i })).toBeInTheDocument()
  })

  it('stops recording and sends voice message', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const voiceButton = screen.getByRole('button', { name: /voice record/i })
    await user.click(voiceButton)
    
    // Wait a bit to simulate recording
    await new Promise(resolve => setTimeout(resolve, 100))
    
    const stopButton = screen.getByRole('button', { name: /stop recording/i })
    await user.click(stopButton)

    await waitFor(() => {
      expect(defaultProps.onVoiceRecord).toHaveBeenCalledWith(expect.any(Blob))
    })
  })

  it('shows emoji picker button', () => {
    render(<ChatInput {...defaultProps} />)
    
    const emojiButton = screen.getByRole('button', { name: /emoji/i })
    expect(emojiButton).toBeInTheDocument()
  })

  it('toggles emoji picker on button click', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const emojiButton = screen.getByRole('button', { name: /emoji/i })
    await user.click(emojiButton)

    expect(screen.getByRole('dialog', { name: /emoji picker/i })).toBeInTheDocument()
  })

  it('inserts emoji into message', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    await user.type(input, 'Hello ')
    
    const emojiButton = screen.getByRole('button', { name: /emoji/i })
    await user.click(emojiButton)
    
    const smileEmoji = screen.getByRole('button', { name: /😊/i })
    await user.click(smileEmoji)

    expect(input).toHaveValue('Hello 😊')
  })

  it('shows typing indicator when typing', async () => {
    const { user } = render(<ChatInput {...defaultProps} showTypingIndicator={true} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    await user.type(input, 'Typing...')

    expect(screen.getByTestId('typing-indicator')).toBeInTheDocument()
  })

  it('handles paste event with text', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    const clipboardData = new DataTransfer()
    clipboardData.setData('text/plain', 'Pasted text')
    
    await user.click(input)
    await user.paste(clipboardData)

    expect(input).toHaveValue('Pasted text')
  })

  it('handles paste event with image', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    const imageFile = new File(['image'], 'image.png', { type: 'image/png' })
    const clipboardData = new DataTransfer()
    clipboardData.items.add(imageFile)
    
    await user.click(input)
    await user.paste(clipboardData)

    await waitFor(() => {
      expect(defaultProps.onFileUpload).toHaveBeenCalledWith(imageFile)
    })
  })

  it('shows quick actions menu', () => {
    render(<ChatInput {...defaultProps} showQuickActions={true} />)
    
    expect(screen.getByRole('button', { name: /quick actions/i })).toBeInTheDocument()
  })

  it('handles quick action selection', async () => {
    const { user } = render(<ChatInput {...defaultProps} showQuickActions={true} />)
    
    const quickActionsButton = screen.getByRole('button', { name: /quick actions/i })
    await user.click(quickActionsButton)
    
    const helpAction = screen.getByRole('menuitem', { name: /help/i })
    await user.click(helpAction)

    expect(defaultProps.onSendMessage).toHaveBeenCalledWith('/help')
  })

  it('auto-focuses input on mount', () => {
    render(<ChatInput {...defaultProps} autoFocus={true} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    expect(document.activeElement).toBe(input)
  })

  it('maintains focus after sending message', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    await user.type(input, 'Test message')
    await user.keyboard('{Enter}')

    expect(document.activeElement).toBe(input)
  })

  it('shows loading state while sending', async () => {
    const slowSend = vi.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    )
    
    const { user } = render(<ChatInput {...defaultProps} onSendMessage={slowSend} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    await user.type(input, 'Test message')
    await user.keyboard('{Enter}')

    expect(screen.getByText(/sending/i)).toBeInTheDocument()
    expect(input).toBeDisabled()

    await waitFor(() => {
      expect(screen.queryByText(/sending/i)).not.toBeInTheDocument()
      expect(input).not.toBeDisabled()
    })
  })

  it('handles keyboard shortcuts', async () => {
    const { user } = render(<ChatInput {...defaultProps} />)
    
    // Test Cmd/Ctrl + Enter to send
    const input = screen.getByPlaceholderText('Type your message...')
    await user.type(input, 'Test message')
    await user.keyboard('{Control>}{Enter}{/Control}')

    expect(defaultProps.onSendMessage).toHaveBeenCalledWith('Test message')
  })

  it('preserves message draft in local storage', async () => {
    const { user, unmount } = render(<ChatInput {...defaultProps} />)
    
    const input = screen.getByPlaceholderText('Type your message...')
    await user.type(input, 'Draft message')
    
    unmount()
    
    // Remount component
    render(<ChatInput {...defaultProps} />)
    
    const newInput = screen.getByPlaceholderText('Type your message...')
    expect(newInput).toHaveValue('Draft message')
  })
})
