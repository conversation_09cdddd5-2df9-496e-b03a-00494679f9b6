{"version": 3, "sources": ["../src/luminar-apps.config.ts"], "names": [], "mappings": ";;;;;AAcO,IAAM,YAA6B,GAAA;AAAA,EACxC;AAAA,IACE,EAAI,EAAA,MAAA;AAAA,IACJ,IAAM,EAAA,qBAAA;AAAA,IACN,SAAW,EAAA,MAAA;AAAA,IACX,WAAa,EAAA,uCAAA;AAAA,IACb,GAAA,EAAK,OAAQ,CAAA,GAAA,CAAI,QAAY,IAAA,uBAAA;AAAA,IAC7B,IAAM,EAAA,WAAA;AAAA,IACN,KAAO,EAAA,SAAA;AAAA,IACP,KAAA,EAAO,CAAC,MAAM,CAAA;AAAA,IACd,aAAa,EAAC;AAAA,IACd,QAAU,EAAA,MAAA;AAAA,IACV,MAAQ,EAAA;AAAA,GACV;AAAA,EACA;AAAA,IACE,EAAI,EAAA,oBAAA;AAAA,IACJ,IAAM,EAAA,oBAAA;AAAA,IACN,SAAW,EAAA,UAAA;AAAA,IACX,WAAa,EAAA,0CAAA;AAAA,IACb,GAAA,EAAK,OAAQ,CAAA,GAAA,CAAI,YAAgB,IAAA,uBAAA;AAAA,IACjC,IAAM,EAAA,WAAA;AAAA,IACN,KAAO,EAAA,SAAA;AAAA,IACP,KAAA,EAAO,CAAC,MAAM,CAAA;AAAA,IACd,WAAA,EAAa,CAAC,eAAe,CAAA;AAAA,IAC7B,QAAU,EAAA,UAAA;AAAA,IACV,MAAQ,EAAA;AAAA,GACV;AAAA,EACA;AAAA,IACE,EAAI,EAAA,mBAAA;AAAA,IACJ,IAAM,EAAA,mBAAA;AAAA,IACN,SAAW,EAAA,SAAA;AAAA,IACX,WAAa,EAAA,uCAAA;AAAA,IACb,GAAA,EAAK,OAAQ,CAAA,GAAA,CAAI,UAAc,IAAA,uBAAA;AAAA,IAC/B,IAAM,EAAA,WAAA;AAAA,IACN,KAAO,EAAA,SAAA;AAAA,IACP,KAAA,EAAO,CAAC,SAAA,EAAW,OAAO,CAAA;AAAA,IAC1B,WAAA,EAAa,CAAC,cAAc,CAAA;AAAA,IAC5B,QAAU,EAAA,YAAA;AAAA,IACV,MAAQ,EAAA;AAAA,GACV;AAAA,EACA;AAAA,IACE,EAAI,EAAA,cAAA;AAAA,IACJ,IAAM,EAAA,kBAAA;AAAA,IACN,SAAW,EAAA,MAAA;AAAA,IACX,WAAa,EAAA,uCAAA;AAAA,IACb,GAAA,EAAK,OAAQ,CAAA,GAAA,CAAI,QAAY,IAAA,uBAAA;AAAA,IAC7B,IAAM,EAAA,WAAA;AAAA,IACN,KAAO,EAAA,SAAA;AAAA,IACP,KAAA,EAAO,CAAC,MAAM,CAAA;AAAA,IACd,WAAA,EAAa,CAAC,aAAa,CAAA;AAAA,IAC3B,QAAU,EAAA,MAAA;AAAA,IACV,MAAQ,EAAA;AAAA,GACV;AAAA,EACA;AAAA,IACE,EAAI,EAAA,gBAAA;AAAA,IACJ,IAAM,EAAA,wBAAA;AAAA,IACN,SAAW,EAAA,KAAA;AAAA,IACX,WAAa,EAAA,uCAAA;AAAA,IACb,GAAA,EAAK,OAAQ,CAAA,GAAA,CAAI,OAAW,IAAA,uBAAA;AAAA,IAC5B,IAAM,EAAA,WAAA;AAAA,IACN,KAAO,EAAA,SAAA;AAAA,IACP,KAAA,EAAO,CAAC,MAAM,CAAA;AAAA,IACd,WAAA,EAAa,CAAC,eAAe,CAAA;AAAA,IAC7B,QAAU,EAAA,UAAA;AAAA,IACV,MAAQ,EAAA;AAAA,GACV;AAAA,EACA;AAAA,IACE,EAAI,EAAA,YAAA;AAAA,IACJ,IAAM,EAAA,oBAAA;AAAA,IACN,SAAW,EAAA,YAAA;AAAA,IACX,WAAa,EAAA,mCAAA;AAAA,IACb,GAAA,EAAK,OAAQ,CAAA,GAAA,CAAI,cAAkB,IAAA,uBAAA;AAAA,IACnC,IAAM,EAAA,WAAA;AAAA,IACN,KAAO,EAAA,SAAA;AAAA,IACP,KAAA,EAAO,CAAC,SAAA,EAAW,OAAO,CAAA;AAAA,IAC1B,WAAA,EAAa,CAAC,gBAAgB,CAAA;AAAA,IAC9B,QAAU,EAAA,WAAA;AAAA,IACV,MAAQ,EAAA;AAAA,GACV;AAAA,EACA;AAAA,IACE,EAAI,EAAA,WAAA;AAAA,IACJ,IAAM,EAAA,uBAAA;AAAA,IACN,SAAW,EAAA,WAAA;AAAA,IACX,WAAa,EAAA,wCAAA;AAAA,IACb,GAAA,EAAK,OAAQ,CAAA,GAAA,CAAI,YAAgB,IAAA,uBAAA;AAAA,IACjC,IAAM,EAAA,WAAA;AAAA,IACN,KAAO,EAAA,SAAA;AAAA,IACP,KAAA,EAAO,CAAC,OAAO,CAAA;AAAA,IACf,WAAA,EAAa,CAAC,eAAe,CAAA;AAAA,IAC7B,QAAU,EAAA,YAAA;AAAA,IACV,MAAQ,EAAA;AAAA,GACV;AAAA,EACA;AAAA,IACE,EAAI,EAAA,aAAA;AAAA,IACJ,IAAM,EAAA,aAAA;AAAA,IACN,SAAW,EAAA,OAAA;AAAA,IACX,WAAa,EAAA,yCAAA;AAAA,IACb,GAAA,EAAK,OAAQ,CAAA,GAAA,CAAI,SAAa,IAAA,uBAAA;AAAA,IAC9B,IAAM,EAAA,cAAA;AAAA,IACN,KAAO,EAAA,SAAA;AAAA,IACP,KAAA,EAAO,CAAC,OAAA,EAAS,aAAa,CAAA;AAAA,IAC9B,WAAA,EAAa,CAAC,eAAe,CAAA;AAAA,IAC7B,QAAU,EAAA,YAAA;AAAA,IACV,MAAQ,EAAA;AAAA;AAEZ;AAGO,SAAS,WAAW,EAAoC,EAAA;AAC7D,EAAA,OAAO,YAAa,CAAA,IAAA,CAAK,CAAO,GAAA,KAAA,GAAA,CAAI,OAAO,EAAE,CAAA;AAC/C;AAEO,SAAS,kBAAkB,QAAgD,EAAA;AAChF,EAAA,OAAO,YAAa,CAAA,MAAA,CAAO,CAAO,GAAA,KAAA,GAAA,CAAI,aAAa,QAAQ,CAAA;AAC7D;AAEO,SAAS,aAA8B,GAAA;AAC5C,EAAA,OAAO,YAAa,CAAA,MAAA,CAAO,CAAO,GAAA,KAAA,GAAA,CAAI,WAAW,QAAQ,CAAA;AAC3D;AAEO,SAAS,cAAA,CAAe,OAAiB,WAAqC,EAAA;AACnF,EAAO,OAAA,YAAA,CAAa,OAAO,CAAO,GAAA,KAAA;AAEhC,IAAA,MAAM,OAAU,GAAA,GAAA,CAAI,KAAM,CAAA,MAAA,KAAW,CACnC,IAAA,GAAA,CAAI,KAAM,CAAA,IAAA,CAAK,CAAQ,IAAA,KAAA,KAAA,CAAM,QAAS,CAAA,IAAI,CAAC,CAAA;AAG7C,IAAA,MAAM,aAAgB,GAAA,GAAA,CAAI,WAAY,CAAA,MAAA,KAAW,CAC/C,IAAA,GAAA,CAAI,WAAY,CAAA,IAAA,CAAK,CAAc,UAAA,KAAA,WAAA,CAAY,QAAS,CAAA,UAAU,CAAC,CAAA;AAErE,IAAO,OAAA,OAAA,IAAW,aAAiB,IAAA,GAAA,CAAI,MAAW,KAAA,aAAA;AAAA,GACnD,CAAA;AACH;AAGO,IAAM,gBAAmB,GAAA;AAAA,EAC9B,WAAa,EAAA;AAAA,IACX,MAAQ,EAAA,8BAAA;AAAA,IACR,KAAO,EAAA,qBAAA;AAAA,IACP,MAAQ,EAAA;AAAA,GACV;AAAA,EACA,OAAS,EAAA;AAAA,IACP,MAAQ,EAAA,iCAAA;AAAA,IACR,KAAO,EAAA,+BAAA;AAAA,IACP,MAAQ,EAAA;AAAA,GACV;AAAA,EACA,UAAY,EAAA;AAAA,IACV,MAAQ,EAAA,yBAAA;AAAA,IACR,KAAO,EAAA,uBAAA;AAAA,IACP,MAAQ,EAAA;AAAA;AAEZ;AAIO,SAAS,oBAAA,CAAqB,MAAmB,aAAe,EAAA;AACrE,EAAA,OAAO,iBAAiB,GAAG,CAAA;AAC7B;AAGO,IAAM,aAAgB,GAAA;AAAA,EAC3B,WAAa,EAAA,SAAA;AAAA,EACb,YAAc,EAAA,oBAAA;AAAA,EACd,OAAS,EAAA,0BAAA;AAAA,EACT,UAAY,EAAA,6BAAA;AAAA,EACZ,QAAU,EAAA,2BAAA;AAAA,EACV,IAAM,EAAA,0BAAA;AAAA,EACN,OAAS,EAAA,cAAA;AAAA,EACT,KAAO,EAAA;AAAA,IACL,YAAc,EAAA,SAAA;AAAA,IACd,cAAgB,EAAA,SAAA;AAAA,IAChB,WAAa,EAAA,SAAA;AAAA,IACb,eAAiB,EAAA,SAAA;AAAA,IACjB,SAAW,EAAA;AAAA,GACb;AAAA,EACA,IAAM,EAAA;AAAA,IACJ,cAAA,EAAgB,KAAK,EAAK,GAAA,GAAA;AAAA;AAAA,IAC1B,eAAA,EAAiB,KAAK,EAAK,GAAA,GAAA;AAAA;AAAA,IAC3B,cAAgB,EAAA;AAAA,MACd,SAAW,EAAA,CAAA;AAAA,MACX,gBAAkB,EAAA,IAAA;AAAA,MAClB,gBAAkB,EAAA,IAAA;AAAA,MAClB,cAAgB,EAAA,IAAA;AAAA,MAChB,mBAAqB,EAAA;AAAA;AACvB,GACF;AAAA,EACA,QAAU,EAAA;AAAA,IACR,SAAW,EAAA,IAAA;AAAA,IACX,SAAW,EAAA,IAAA;AAAA,IACX,iBAAmB,EAAA,IAAA;AAAA,IACnB,uBAAyB,EAAA,IAAA;AAAA,IACzB,eAAiB,EAAA;AAAA;AAErB", "file": "index.js", "sourcesContent": ["export interface LuminarApp {\n  id: string\n  name: string\n  shortName: string\n  description: string\n  url: string\n  icon: string\n  color: string\n  roles: string[]\n  permissions: string[]\n  category: 'core' | 'training' | 'management' | 'analytics'\n  status: 'active' | 'beta' | 'coming-soon'\n}\n\nexport const LUMINAR_APPS: LuminarApp[] = [\n  {\n    id: 'amna',\n    name: 'AMNA - AI Assistant',\n    shortName: 'AMNA',\n    description: 'AI-powered assistant for modern needs',\n    url: process.env.AMNA_URL || 'http://localhost:5173',\n    icon: '🤖',\n    color: '#3b82f6',\n    roles: ['user'],\n    permissions: [],\n    category: 'core',\n    status: 'active'\n  },\n  {\n    id: 'training-dashboard',\n    name: 'Training Dashboard',\n    shortName: 'Training',\n    description: 'Manage training programs and enrollments',\n    url: process.env.TRAINING_URL || 'http://localhost:5174',\n    icon: '📚',\n    color: '#10b981',\n    roles: ['user'],\n    permissions: ['training:view'],\n    category: 'training',\n    status: 'active'\n  },\n  {\n    id: 'vendor-management',\n    name: 'Vendor Management',\n    shortName: 'Vendors',\n    description: 'Manage training vendors and contracts',\n    url: process.env.VENDOR_URL || 'http://localhost:5175',\n    icon: '🏢',\n    color: '#8b5cf6',\n    roles: ['manager', 'admin'],\n    permissions: ['vendors:view'],\n    category: 'management',\n    status: 'active'\n  },\n  {\n    id: 'wins-of-week',\n    name: 'Wins of the Week',\n    shortName: 'Wins',\n    description: 'Submit and review weekly achievements',\n    url: process.env.WINS_URL || 'http://localhost:5176',\n    icon: '🏆',\n    color: '#f59e0b',\n    roles: ['user'],\n    permissions: ['wins:submit'],\n    category: 'core',\n    status: 'active'\n  },\n  {\n    id: 'training-needs',\n    name: 'Training Need Analysis',\n    shortName: 'TNA',\n    description: 'Analyze skill gaps and training needs',\n    url: process.env.TNA_URL || 'http://localhost:5177',\n    icon: '📊',\n    color: '#ef4444',\n    roles: ['user'],\n    permissions: ['training:view'],\n    category: 'training',\n    status: 'active'\n  },\n  {\n    id: 'lighthouse',\n    name: 'Lighthouse Reports',\n    shortName: 'Lighthouse',\n    description: 'Performance metrics and analytics',\n    url: process.env.LIGHTHOUSE_URL || 'http://localhost:5178',\n    icon: '🏠',\n    color: '#06b6d4',\n    roles: ['manager', 'admin'],\n    permissions: ['analytics:view'],\n    category: 'analytics',\n    status: 'active'\n  },\n  {\n    id: 'e-connect',\n    name: 'e-Connect Integration',\n    shortName: 'e-Connect',\n    description: 'Connect with external training systems',\n    url: process.env.ECONNECT_URL || 'http://localhost:5179',\n    icon: '🔌',\n    color: '#64748b',\n    roles: ['admin'],\n    permissions: ['system:config'],\n    category: 'management',\n    status: 'beta'\n  },\n  {\n    id: 'admin-panel',\n    name: 'Admin Panel',\n    shortName: 'Admin',\n    description: 'System administration and configuration',\n    url: process.env.ADMIN_URL || 'http://localhost:5180',\n    icon: '⚙️',\n    color: '#1f2937',\n    roles: ['admin', 'super_admin'],\n    permissions: ['system:config'],\n    category: 'management',\n    status: 'active'\n  }\n]\n\n// Helper functions\nexport function getAppById(id: string): LuminarApp | undefined {\n  return LUMINAR_APPS.find(app => app.id === id)\n}\n\nexport function getAppsByCategory(category: LuminarApp['category']): LuminarApp[] {\n  return LUMINAR_APPS.filter(app => app.category === category)\n}\n\nexport function getActiveApps(): LuminarApp[] {\n  return LUMINAR_APPS.filter(app => app.status === 'active')\n}\n\nexport function getAppsForUser(roles: string[], permissions: string[]): LuminarApp[] {\n  return LUMINAR_APPS.filter(app => {\n    // Check if user has required role\n    const hasRole = app.roles.length === 0 || \n      app.roles.some(role => roles.includes(role))\n    \n    // Check if user has required permissions\n    const hasPermission = app.permissions.length === 0 || \n      app.permissions.some(permission => permissions.includes(permission))\n    \n    return hasRole && hasPermission && app.status !== 'coming-soon'\n  })\n}\n\n// Environment-specific configurations\nexport const APP_ENVIRONMENTS = {\n  development: {\n    apiUrl: 'http://localhost:3000/api/v1',\n    wsUrl: 'ws://localhost:3000',\n    domain: 'localhost'\n  },\n  staging: {\n    apiUrl: 'https://api-staging.luminar.com',\n    wsUrl: 'wss://api-staging.luminar.com',\n    domain: '.luminar-staging.com'\n  },\n  production: {\n    apiUrl: 'https://api.luminar.com',\n    wsUrl: 'wss://api.luminar.com',\n    domain: '.luminar.com'\n  }\n} as const\n\nexport type Environment = keyof typeof APP_ENVIRONMENTS\n\nexport function getEnvironmentConfig(env: Environment = 'development') {\n  return APP_ENVIRONMENTS[env]\n}\n\n// Shared configuration for all apps\nexport const SHARED_CONFIG = {\n  companyName: 'Luminar',\n  supportEmail: '<EMAIL>',\n  helpUrl: 'https://help.luminar.com',\n  privacyUrl: 'https://luminar.com/privacy',\n  termsUrl: 'https://luminar.com/terms',\n  logo: '/assets/luminar-logo.svg',\n  favicon: '/favicon.ico',\n  theme: {\n    primaryColor: '#3b82f6',\n    secondaryColor: '#10b981',\n    accentColor: '#8b5cf6',\n    backgroundColor: '#f3f4f6',\n    textColor: '#1f2937'\n  },\n  auth: {\n    sessionTimeout: 30 * 60 * 1000, // 30 minutes\n    refreshInterval: 10 * 60 * 1000, // 10 minutes\n    passwordPolicy: {\n      minLength: 8,\n      requireUppercase: true,\n      requireLowercase: true,\n      requireNumbers: true,\n      requireSpecialChars: true\n    }\n  },\n  features: {\n    enableSSO: true,\n    enableMFA: true,\n    enableOfflineMode: true,\n    enablePushNotifications: true,\n    enableAnalytics: true\n  }\n}"]}