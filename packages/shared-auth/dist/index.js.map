{"version": 3, "sources": ["../src/index.ts", "../src/auth-client.ts", "../src/types.ts", "../src/auth-provider.tsx", "../src/auth-context.tsx", "../src/utils.ts", "../src/hooks.ts", "../src/components/ProtectedRoute.tsx", "../src/components/PermissionGate.tsx", "../src/components/UnifiedLoginPage.tsx", "../src/components/CrossAppNavigation.tsx", "../src/components/OAuthButtons.tsx", "../src/oauth-client.ts", "../src/cross-app-client.ts", "../src/cors-utils.ts"], "sourcesContent": ["export * from './auth-client'\nexport * from './auth-provider'\nexport * from './auth-context'\nexport * from './types'\nexport * from './utils'\nexport * from './hooks'\nexport * from './components'\nexport * from './cross-app-client'\nexport * from './cors-utils'\nexport * from './oauth-client'", "import axios, { AxiosInstance, AxiosError } from 'axios'\nimport { jwtDecode } from 'jwt-decode'\nimport Cookies from 'js-cookie'\nimport { \n  AuthConfig, \n  AuthTokens, \n  LoginCredentials, \n  RegisterData, \n  User, \n  AuthError \n} from './types'\n\nexport class AuthClient {\n  private api: AxiosInstance\n  private config: AuthConfig\n  private refreshPromise: Promise<AuthTokens> | null = null\n\n  constructor(config: AuthConfig) {\n    this.config = {\n      authEndpoint: '/auth',\n      tokenKey: 'access_token',\n      refreshTokenKey: 'refresh_token',\n      userKey: 'user',\n      enableAutoRefresh: true,\n      refreshInterval: 10 * 60 * 1000, // 10 minutes\n      storage: 'localStorage',\n      ...config\n    }\n\n    this.api = axios.create({\n      baseURL: config.apiBaseUrl,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    })\n\n    this.setupInterceptors()\n  }\n\n  private setupInterceptors() {\n    // Request interceptor to add auth token\n    this.api.interceptors.request.use(\n      (config) => {\n        const token = this.getAccessToken()\n        if (token && !config.headers['skipAuth']) {\n          config.headers['Authorization'] = `Bearer ${token}`\n        }\n        return config\n      },\n      (error) => Promise.reject(error)\n    )\n\n    // Response interceptor to handle auth errors\n    this.api.interceptors.response.use(\n      (response) => response,\n      async (error: AxiosError) => {\n        const originalRequest = error.config as any\n\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true\n\n          try {\n            await this.refreshTokens()\n            const token = this.getAccessToken()\n            if (token) {\n              originalRequest.headers['Authorization'] = `Bearer ${token}`\n              return this.api(originalRequest)\n            }\n          } catch (refreshError) {\n            this.config.onAuthError?.(new AuthError(\n              'Session expired. Please login again.',\n              'SESSION_EXPIRED',\n              401\n            ))\n            this.clearAuth()\n            throw refreshError\n          }\n        }\n\n        throw this.handleApiError(error)\n      }\n    )\n  }\n\n  private handleApiError(error: AxiosError): AuthError {\n    if (!error.response) {\n      return new AuthError('Network error', 'NETWORK_ERROR')\n    }\n\n    const { status, data } = error.response as any\n    const message = data?.message || 'An error occurred'\n    const code = data?.code || 'UNKNOWN_ERROR'\n\n    return new AuthError(message, code, status, data)\n  }\n\n  // Storage methods\n  private getStorage() {\n    switch (this.config.storage) {\n      case 'sessionStorage':\n        return sessionStorage\n      case 'cookie':\n        return {\n          getItem: (key: string) => Cookies.get(key) || null,\n          setItem: (key: string, value: string) => {\n            Cookies.set(key, value, this.config.cookieOptions)\n          },\n          removeItem: (key: string) => Cookies.remove(key)\n        }\n      default:\n        return localStorage\n    }\n  }\n\n  private setTokens(tokens: AuthTokens) {\n    const storage = this.getStorage()\n    storage.setItem(this.config.tokenKey!, tokens.accessToken)\n    storage.setItem(this.config.refreshTokenKey!, tokens.refreshToken)\n  }\n\n  private getAccessToken(): string | null {\n    return this.getStorage().getItem(this.config.tokenKey!)\n  }\n\n  private getRefreshToken(): string | null {\n    return this.getStorage().getItem(this.config.refreshTokenKey!)\n  }\n\n  private setUser(user: User) {\n    const storage = this.getStorage()\n    storage.setItem(this.config.userKey!, JSON.stringify(user))\n  }\n\n  private getUser(): User | null {\n    const storage = this.getStorage()\n    const userStr = storage.getItem(this.config.userKey!)\n    if (userStr) {\n      try {\n        return JSON.parse(userStr)\n      } catch {\n        return null\n      }\n    }\n    return null\n  }\n\n  private clearAuth() {\n    const storage = this.getStorage()\n    storage.removeItem(this.config.tokenKey!)\n    storage.removeItem(this.config.refreshTokenKey!)\n    storage.removeItem(this.config.userKey!)\n  }\n\n  // Auth methods\n  async login(credentials: LoginCredentials): Promise<{ user: User; tokens: AuthTokens }> {\n    const response = await this.api.post(`${this.config.authEndpoint}/login`, credentials, {\n      headers: { skipAuth: true }\n    })\n\n    const { user, accessToken, refreshToken, expiresIn } = response.data\n\n    const tokens: AuthTokens = {\n      accessToken,\n      refreshToken,\n      expiresIn,\n      tokenType: 'Bearer'\n    }\n\n    this.setTokens(tokens)\n    this.setUser(user)\n\n    return { user, tokens }\n  }\n\n  async register(data: RegisterData): Promise<{ user: User; tokens: AuthTokens }> {\n    const response = await this.api.post(`${this.config.authEndpoint}/register`, data, {\n      headers: { skipAuth: true }\n    })\n\n    const { user, accessToken, refreshToken, expiresIn } = response.data\n\n    const tokens: AuthTokens = {\n      accessToken,\n      refreshToken,\n      expiresIn,\n      tokenType: 'Bearer'\n    }\n\n    this.setTokens(tokens)\n    this.setUser(user)\n\n    return { user, tokens }\n  }\n\n  async logout(): Promise<void> {\n    try {\n      await this.api.post(`${this.config.authEndpoint}/logout`)\n    } catch (error) {\n      // Even if logout fails, clear local auth\n      console.error('Logout API failed:', error)\n    } finally {\n      this.clearAuth()\n    }\n  }\n\n  async refreshTokens(): Promise<AuthTokens> {\n    // Prevent multiple simultaneous refresh attempts\n    if (this.refreshPromise) {\n      return this.refreshPromise\n    }\n\n    const refreshToken = this.getRefreshToken()\n    if (!refreshToken) {\n      throw new AuthError('No refresh token available', 'NO_REFRESH_TOKEN')\n    }\n\n    this.refreshPromise = this.api\n      .post(\n        `${this.config.authEndpoint}/refresh`,\n        { refreshToken },\n        { headers: { skipAuth: true } }\n      )\n      .then((response) => {\n        const { accessToken, refreshToken: newRefreshToken, expiresIn } = response.data\n        \n        const tokens: AuthTokens = {\n          accessToken,\n          refreshToken: newRefreshToken,\n          expiresIn,\n          tokenType: 'Bearer'\n        }\n\n        this.setTokens(tokens)\n        this.config.onTokenRefresh?.(tokens)\n        \n        return tokens\n      })\n      .finally(() => {\n        this.refreshPromise = null\n      })\n\n    return this.refreshPromise\n  }\n\n  async getCurrentUser(): Promise<User> {\n    const response = await this.api.get(`${this.config.authEndpoint}/me`)\n    const user = response.data\n    this.setUser(user)\n    return user\n  }\n\n  async updateProfile(data: Partial<User>): Promise<User> {\n    const response = await this.api.patch(`${this.config.authEndpoint}/profile`, data)\n    const user = response.data\n    this.setUser(user)\n    return user\n  }\n\n  async changePassword(currentPassword: string, newPassword: string): Promise<void> {\n    await this.api.post(`${this.config.authEndpoint}/change-password`, {\n      currentPassword,\n      newPassword\n    })\n  }\n\n  async requestPasswordReset(email: string): Promise<void> {\n    await this.api.post(`${this.config.authEndpoint}/forgot-password`, { email }, {\n      headers: { skipAuth: true }\n    })\n  }\n\n  async resetPassword(token: string, newPassword: string): Promise<void> {\n    await this.api.post(`${this.config.authEndpoint}/reset-password`, {\n      token,\n      newPassword\n    }, {\n      headers: { skipAuth: true }\n    })\n  }\n\n  // Token validation\n  isTokenExpired(token?: string): boolean {\n    const tokenToCheck = token || this.getAccessToken()\n    if (!tokenToCheck) return true\n\n    try {\n      const decoded = jwtDecode<{ exp: number }>(tokenToCheck)\n      return decoded.exp * 1000 < Date.now()\n    } catch {\n      return true\n    }\n  }\n\n  // Session management\n  async validateSession(): Promise<boolean> {\n    try {\n      const token = this.getAccessToken()\n      if (!token || this.isTokenExpired(token)) {\n        // Try to refresh\n        await this.refreshTokens()\n      }\n      \n      // Verify by getting current user\n      await this.getCurrentUser()\n      return true\n    } catch {\n      return false\n    }\n  }\n\n  // Utility methods\n  getStoredUser(): User | null {\n    return this.getUser()\n  }\n\n  getStoredTokens(): AuthTokens | null {\n    const accessToken = this.getAccessToken()\n    const refreshToken = this.getRefreshToken()\n    \n    if (!accessToken || !refreshToken) {\n      return null\n    }\n\n    return {\n      accessToken,\n      refreshToken,\n      tokenType: 'Bearer'\n    }\n  }\n\n  clearSession(): void {\n    this.clearAuth()\n  }\n}", "export interface User {\n  id: string\n  email: string\n  firstName: string\n  lastName: string\n  roles: string[]\n  permissions: string[]\n  department?: string\n  organizationId?: string\n  avatar?: string\n  metadata?: Record<string, any>\n}\n\nexport interface AuthTokens {\n  accessToken: string\n  refreshToken: string\n  expiresIn?: number\n  tokenType?: string\n}\n\nexport interface LoginCredentials {\n  email: string\n  password: string\n  rememberMe?: boolean\n}\n\nexport interface RegisterData {\n  email: string\n  password: string\n  firstName: string\n  lastName: string\n  organizationId?: string\n  department?: string\n}\n\nexport interface AuthConfig {\n  apiBaseUrl: string\n  authEndpoint?: string\n  tokenKey?: string\n  refreshTokenKey?: string\n  userKey?: string\n  enableAutoRefresh?: boolean\n  refreshInterval?: number\n  onAuthError?: (error: AuthError) => void\n  onTokenRefresh?: (tokens: AuthTokens) => void\n  storage?: 'localStorage' | 'sessionStorage' | 'cookie'\n  cookieOptions?: {\n    domain?: string\n    secure?: boolean\n    sameSite?: 'strict' | 'lax' | 'none'\n    httpOnly?: boolean\n  }\n}\n\nexport interface AuthState {\n  user: User | null\n  tokens: AuthTokens | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  error: AuthError | null\n}\n\nexport class AuthError extends Error {\n  constructor(\n    message: string,\n    public code: string,\n    public statusCode?: number,\n    public details?: any\n  ) {\n    super(message)\n    this.name = 'AuthError'\n  }\n}\n\nexport interface AuthContextValue {\n  user: User | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  error: AuthError | null\n  login: (credentials: LoginCredentials) => Promise<User>\n  logout: () => Promise<void>\n  register: (data: RegisterData) => Promise<User>\n  refreshAuth: () => Promise<void>\n  updateUser: (data: Partial<User>) => Promise<User>\n  hasRole: (role: string) => boolean\n  hasPermission: (permission: string) => boolean\n  hasAnyRole: (roles: string[]) => boolean\n  hasAllRoles: (roles: string[]) => boolean\n  hasAnyPermission: (permissions: string[]) => boolean\n  hasAllPermissions: (permissions: string[]) => boolean\n}\n\nexport interface AuthProviderProps {\n  children: React.ReactNode\n  config: AuthConfig\n  initialState?: Partial<AuthState>\n  onAuthStateChange?: (state: AuthState) => void\n}", "import React, { useState, useEffect, useCallback, useRef } from 'react'\nimport { AuthContext } from './auth-context'\nimport { AuthClient } from './auth-client'\nimport {\n  AuthConfig,\n  AuthState,\n  AuthProviderProps,\n  User,\n  LoginCredentials,\n  RegisterData,\n  AuthError,\n  AuthContextValue\n} from './types'\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({\n  children,\n  config,\n  initialState,\n  onAuthStateChange\n}) => {\n  const authClient = useRef<AuthClient>(new AuthClient(config))\n  \n  const [state, setState] = useState<AuthState>({\n    user: null,\n    tokens: null,\n    isAuthenticated: false,\n    isLoading: true,\n    error: null,\n    ...initialState\n  })\n\n  // Notify state changes\n  useEffect(() => {\n    onAuthStateChange?.(state)\n  }, [state, onAuthStateChange])\n\n  // Initialize auth state from storage\n  useEffect(() => {\n    const initAuth = async () => {\n      try {\n        const storedUser = authClient.current.getStoredUser()\n        const storedTokens = authClient.current.getStoredTokens()\n\n        if (storedUser && storedTokens) {\n          // Validate session\n          const isValid = await authClient.current.validateSession()\n          \n          if (isValid) {\n            setState({\n              user: authClient.current.getStoredUser(),\n              tokens: authClient.current.getStoredTokens(),\n              isAuthenticated: true,\n              isLoading: false,\n              error: null\n            })\n          } else {\n            authClient.current.clearSession()\n            setState({\n              user: null,\n              tokens: null,\n              isAuthenticated: false,\n              isLoading: false,\n              error: null\n            })\n          }\n        } else {\n          setState(prev => ({ ...prev, isLoading: false }))\n        }\n      } catch (error) {\n        setState({\n          user: null,\n          tokens: null,\n          isAuthenticated: false,\n          isLoading: false,\n          error: error as AuthError\n        })\n      }\n    }\n\n    initAuth()\n  }, [])\n\n  // Set up auto token refresh\n  useEffect(() => {\n    if (!config.enableAutoRefresh || !state.isAuthenticated) {\n      return\n    }\n\n    const interval = setInterval(async () => {\n      try {\n        const tokens = await authClient.current.refreshTokens()\n        setState(prev => ({ ...prev, tokens }))\n      } catch (error) {\n        console.error('Auto refresh failed:', error)\n      }\n    }, config.refreshInterval || 10 * 60 * 1000)\n\n    return () => clearInterval(interval)\n  }, [state.isAuthenticated, config.enableAutoRefresh, config.refreshInterval])\n\n  const login = useCallback(async (credentials: LoginCredentials): Promise<User> => {\n    setState(prev => ({ ...prev, isLoading: true, error: null }))\n    \n    try {\n      const { user, tokens } = await authClient.current.login(credentials)\n      \n      setState({\n        user,\n        tokens,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      })\n      \n      return user\n    } catch (error) {\n      const authError = error as AuthError\n      setState({\n        user: null,\n        tokens: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: authError\n      })\n      throw authError\n    }\n  }, [])\n\n  const register = useCallback(async (data: RegisterData): Promise<User> => {\n    setState(prev => ({ ...prev, isLoading: true, error: null }))\n    \n    try {\n      const { user, tokens } = await authClient.current.register(data)\n      \n      setState({\n        user,\n        tokens,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      })\n      \n      return user\n    } catch (error) {\n      const authError = error as AuthError\n      setState({\n        user: null,\n        tokens: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: authError\n      })\n      throw authError\n    }\n  }, [])\n\n  const logout = useCallback(async (): Promise<void> => {\n    setState(prev => ({ ...prev, isLoading: true }))\n    \n    try {\n      await authClient.current.logout()\n    } catch (error) {\n      console.error('Logout error:', error)\n    } finally {\n      setState({\n        user: null,\n        tokens: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      })\n    }\n  }, [])\n\n  const refreshAuth = useCallback(async (): Promise<void> => {\n    try {\n      const tokens = await authClient.current.refreshTokens()\n      const user = await authClient.current.getCurrentUser()\n      \n      setState({\n        user,\n        tokens,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      })\n    } catch (error) {\n      const authError = error as AuthError\n      setState({\n        user: null,\n        tokens: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: authError\n      })\n      throw authError\n    }\n  }, [])\n\n  const updateUser = useCallback(async (data: Partial<User>): Promise<User> => {\n    if (!state.user) {\n      throw new AuthError('No authenticated user', 'NO_USER')\n    }\n\n    try {\n      const updatedUser = await authClient.current.updateProfile(data)\n      setState(prev => ({ ...prev, user: updatedUser }))\n      return updatedUser\n    } catch (error) {\n      throw error as AuthError\n    }\n  }, [state.user])\n\n  // Permission helpers\n  const hasRole = useCallback((role: string): boolean => {\n    return state.user?.roles.includes(role) || false\n  }, [state.user])\n\n  const hasPermission = useCallback((permission: string): boolean => {\n    return state.user?.permissions.includes(permission) || false\n  }, [state.user])\n\n  const hasAnyRole = useCallback((roles: string[]): boolean => {\n    return roles.some(role => hasRole(role))\n  }, [hasRole])\n\n  const hasAllRoles = useCallback((roles: string[]): boolean => {\n    return roles.every(role => hasRole(role))\n  }, [hasRole])\n\n  const hasAnyPermission = useCallback((permissions: string[]): boolean => {\n    return permissions.some(permission => hasPermission(permission))\n  }, [hasPermission])\n\n  const hasAllPermissions = useCallback((permissions: string[]): boolean => {\n    return permissions.every(permission => hasPermission(permission))\n  }, [hasPermission])\n\n  const contextValue: AuthContextValue = {\n    user: state.user,\n    isAuthenticated: state.isAuthenticated,\n    isLoading: state.isLoading,\n    error: state.error,\n    login,\n    logout,\n    register,\n    refreshAuth,\n    updateUser,\n    hasRole,\n    hasPermission,\n    hasAnyRole,\n    hasAllRoles,\n    hasAnyPermission,\n    hasAllPermissions\n  }\n\n  return (\n    <AuthContext.Provider value={contextValue}>\n      {children}\n    </AuthContext.Provider>\n  )\n}", "import React, { createContext, useContext } from 'react'\nimport { AuthContextValue } from './types'\n\nconst AuthContext = createContext<AuthContextValue | undefined>(undefined)\n\nexport const useAuthContext = (): AuthContextValue => {\n  const context = useContext(AuthContext)\n  if (!context) {\n    throw new Error('useAuthContext must be used within an AuthProvider')\n  }\n  return context\n}\n\nexport { AuthContext }", "import { jwtDecode } from 'jwt-decode'\n\nexport interface TokenPayload {\n  sub: string\n  email: string\n  roles: string[]\n  permissions: string[]\n  exp: number\n  iat: number\n  [key: string]: any\n}\n\nexport function decodeToken(token: string): TokenPayload | null {\n  try {\n    return jwtDecode<TokenPayload>(token)\n  } catch {\n    return null\n  }\n}\n\nexport function isTokenExpired(token: string): boolean {\n  const decoded = decodeToken(token)\n  if (!decoded) return true\n  \n  return decoded.exp * 1000 < Date.now()\n}\n\nexport function getTokenExpirationTime(token: string): Date | null {\n  const decoded = decodeToken(token)\n  if (!decoded) return null\n  \n  return new Date(decoded.exp * 1000)\n}\n\nexport function getTokenRemainingTime(token: string): number {\n  const expirationTime = getTokenExpirationTime(token)\n  if (!expirationTime) return 0\n  \n  const remaining = expirationTime.getTime() - Date.now()\n  return Math.max(0, remaining)\n}\n\n// Role hierarchy helper\nconst ROLE_HIERARCHY: Record<string, string[]> = {\n  'super_admin': ['admin', 'manager', 'user'],\n  'admin': ['manager', 'user'],\n  'manager': ['user'],\n  'user': []\n}\n\nexport function hasRoleWithHierarchy(userRoles: string[], requiredRole: string): boolean {\n  // Direct role check\n  if (userRoles.includes(requiredRole)) {\n    return true\n  }\n\n  // Check if user has a higher role\n  for (const userRole of userRoles) {\n    const inheritedRoles = ROLE_HIERARCHY[userRole] || []\n    if (inheritedRoles.includes(requiredRole)) {\n      return true\n    }\n  }\n\n  return false\n}\n\n// Permission pattern matching\nexport function hasPermissionPattern(\n  userPermissions: string[],\n  pattern: string\n): boolean {\n  // Convert pattern to regex (e.g., \"users:*\" -> \"users:.*\")\n  const regexPattern = pattern.replace(/\\*/g, '.*')\n  const regex = new RegExp(`^${regexPattern}$`)\n  \n  return userPermissions.some(permission => regex.test(permission))\n}\n\n// Storage helpers\nexport function getStorageItem(key: string, storage: Storage = localStorage): string | null {\n  try {\n    return storage.getItem(key)\n  } catch {\n    return null\n  }\n}\n\nexport function setStorageItem(\n  key: string,\n  value: string,\n  storage: Storage = localStorage\n): boolean {\n  try {\n    storage.setItem(key, value)\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport function removeStorageItem(key: string, storage: Storage = localStorage): boolean {\n  try {\n    storage.removeItem(key)\n    return true\n  } catch {\n    return false\n  }\n}\n\n// Session timeout helpers\nexport class SessionTimer {\n  private timer: NodeJS.Timeout | null = null\n  private warningTimer: NodeJS.Timeout | null = null\n\n  start(\n    expirationTime: Date,\n    onExpire: () => void,\n    onWarning?: () => void,\n    warningMinutes = 5\n  ) {\n    this.stop()\n\n    const now = Date.now()\n    const expirationMs = expirationTime.getTime()\n    const timeUntilExpiration = expirationMs - now\n    const timeUntilWarning = timeUntilExpiration - (warningMinutes * 60 * 1000)\n\n    if (timeUntilExpiration > 0) {\n      this.timer = setTimeout(onExpire, timeUntilExpiration)\n\n      if (onWarning && timeUntilWarning > 0) {\n        this.warningTimer = setTimeout(onWarning, timeUntilWarning)\n      }\n    }\n  }\n\n  stop() {\n    if (this.timer) {\n      clearTimeout(this.timer)\n      this.timer = null\n    }\n    if (this.warningTimer) {\n      clearTimeout(this.warningTimer)\n      this.warningTimer = null\n    }\n  }\n\n  getRemainingTime(expirationTime: Date): number {\n    const remaining = expirationTime.getTime() - Date.now()\n    return Math.max(0, remaining)\n  }\n}\n\n// URL helpers for auth redirects\nexport function getAuthRedirectUrl(\n  defaultUrl = '/dashboard',\n  locationState?: any\n): string {\n  // Check if there's a redirect URL in location state\n  if (locationState?.from) {\n    return locationState.from\n  }\n\n  // Check URL params\n  const params = new URLSearchParams(window.location.search)\n  const redirectParam = params.get('redirect') || params.get('returnUrl')\n  \n  if (redirectParam) {\n    // Validate it's a safe redirect (same origin)\n    try {\n      const url = new URL(redirectParam, window.location.origin)\n      if (url.origin === window.location.origin) {\n        return url.pathname + url.search + url.hash\n      }\n    } catch {\n      // Invalid URL, ignore\n    }\n  }\n\n  return defaultUrl\n}\n\n// Cross-tab communication for auth sync\nexport class AuthSyncManager {\n  private channel: BroadcastChannel | null = null\n  private storageKey = 'auth_sync'\n\n  constructor(private onAuthChange: (event: 'login' | 'logout' | 'refresh') => void) {\n    if (typeof window !== 'undefined') {\n      if ('BroadcastChannel' in window) {\n        this.channel = new BroadcastChannel('auth_sync')\n        this.channel.onmessage = (event) => {\n          this.onAuthChange(event.data.type)\n        }\n      } else {\n        // Fallback to storage events\n        (window as any).addEventListener('storage', this.handleStorageChange)\n      }\n    }\n  }\n\n  private handleStorageChange = (event: StorageEvent) => {\n    if (event.key === this.storageKey && event.newValue) {\n      const data = JSON.parse(event.newValue)\n      this.onAuthChange(data.type)\n    }\n  }\n\n  broadcast(type: 'login' | 'logout' | 'refresh') {\n    if (this.channel) {\n      this.channel.postMessage({ type })\n    } else {\n      // Fallback to storage\n      localStorage.setItem(\n        this.storageKey,\n        JSON.stringify({ type, timestamp: Date.now() })\n      )\n    }\n  }\n\n  destroy() {\n    if (this.channel) {\n      this.channel.close()\n    } else {\n      window.removeEventListener('storage', this.handleStorageChange)\n    }\n  }\n}", "import { useEffect } from 'react'\nimport { useNavigate, useLocation } from '@tanstack/react-router'\nimport { useAuthContext } from './auth-context'\n\nexport { useAuthContext as useAuth } from './auth-context'\n\n// Hook to protect routes\nexport function useRequireAuth(options?: {\n  redirectTo?: string\n  roles?: string[]\n  permissions?: string[]\n}) {\n  const { \n    isAuthenticated, \n    isLoading, \n    user,\n    hasAllRoles,\n    hasAllPermissions \n  } = useAuthContext()\n  const navigate = useNavigate()\n  const location = useLocation()\n\n  useEffect(() => {\n    if (isLoading) return\n\n    const redirectTo = options?.redirectTo || '/login'\n    \n    if (!isAuthenticated) {\n      navigate({ to: redirectTo })\n      return\n    }\n\n    // Check role requirements\n    if (options?.roles && !hasAllRoles(options.roles)) {\n      navigate({ to: '/unauthorized' })\n      return\n    }\n\n    // Check permission requirements\n    if (options?.permissions && !hasAllPermissions(options.permissions)) {\n      navigate({ to: '/unauthorized' })\n      return\n    }\n  }, [\n    isAuthenticated,\n    isLoading,\n    user,\n    navigate,\n    location,\n    options,\n    hasAllRoles,\n    hasAllPermissions\n  ])\n\n  return {\n    isAuthenticated,\n    isLoading,\n    user,\n    isAuthorized: true\n  }\n}\n\n// Hook to redirect if already authenticated\nexport function useRedirectIfAuthenticated(redirectTo = '/dashboard') {\n  const { isAuthenticated, isLoading } = useAuthContext()\n  const navigate = useNavigate()\n  const location = useLocation()\n\n  useEffect(() => {\n    if (!isLoading && isAuthenticated) {\n      const from = (location.state as any)?.from || redirectTo\n      navigate({ to: from })\n    }\n  }, [isAuthenticated, isLoading, navigate, location, redirectTo])\n}\n\n// Hook to check permissions\nexport function usePermissions() {\n  const {\n    user,\n    hasRole,\n    hasPermission,\n    hasAnyRole,\n    hasAllRoles,\n    hasAnyPermission,\n    hasAllPermissions\n  } = useAuthContext()\n\n  return {\n    user,\n    roles: user?.roles || [],\n    permissions: user?.permissions || [],\n    hasRole,\n    hasPermission,\n    hasAnyRole,\n    hasAllRoles,\n    hasAnyPermission,\n    hasAllPermissions,\n    isAdmin: hasRole('admin'),\n    isManager: hasAnyRole(['admin', 'manager']),\n    isUser: hasRole('user')\n  }\n}\n\n// Hook for conditional rendering based on auth\nexport function useAuthGuard(\n  condition: 'authenticated' | 'unauthenticated' | 'loading'\n): boolean {\n  const { isAuthenticated, isLoading } = useAuthContext()\n\n  switch (condition) {\n    case 'authenticated':\n      return isAuthenticated && !isLoading\n    case 'unauthenticated':\n      return !isAuthenticated && !isLoading\n    case 'loading':\n      return isLoading\n    default:\n      return false\n  }\n}\n\n// Hook to handle auth errors\nexport function useAuthError(handler?: (error: Error) => void) {\n  const { error } = useAuthContext()\n\n  useEffect(() => {\n    if (error && handler) {\n      handler(error)\n    }\n  }, [error, handler])\n\n  return error\n}", "import React from 'react'\nimport { Navigate, useLocation } from '@tanstack/react-router'\nimport { useAuth } from '../hooks'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  roles?: string[]\n  permissions?: string[]\n  redirectTo?: string\n  fallback?: React.ReactNode\n}\n\nexport const ProtectedRoute: React.FC<ProtectedRouteProps> = ({\n  children,\n  roles,\n  permissions,\n  redirectTo = '/login',\n  fallback = <div>Loading...</div>\n}) => {\n  const { isAuthenticated, isLoading, hasAllRoles, hasAllPermissions } = useAuth()\n  const location = useLocation()\n\n  if (isLoading) {\n    return <>{fallback}</>\n  }\n\n  if (!isAuthenticated) {\n    return <Navigate to={redirectTo} replace />\n  }\n\n  if (roles && !hasAllRoles(roles)) {\n    return <Navigate to=\"/unauthorized\" replace />\n  }\n\n  if (permissions && !hasAllPermissions(permissions)) {\n    return <Navigate to=\"/unauthorized\" replace />\n  }\n\n  return <>{children}</>\n}", "import React from 'react'\nimport { usePermissions } from '../hooks'\n\ninterface PermissionGateProps {\n  children: React.ReactNode\n  roles?: string[]\n  permissions?: string[]\n  requireAll?: boolean\n  fallback?: React.ReactNode\n}\n\nexport const PermissionGate: React.FC<PermissionGateProps> = ({\n  children,\n  roles,\n  permissions,\n  requireAll = true,\n  fallback = null\n}) => {\n  const { hasAllRoles, hasAnyRole, hasAllPermissions, hasAnyPermission } = usePermissions()\n\n  let hasAccess = true\n\n  if (roles) {\n    hasAccess = requireAll ? hasAllRoles(roles) : hasAnyRole(roles)\n  }\n\n  if (hasAccess && permissions) {\n    const hasPerms = requireAll ? hasAllPermissions(permissions) : hasAnyPermission(permissions)\n    hasAccess = hasAccess && hasPerms\n  }\n\n  return hasAccess ? <>{children}</> : <>{fallback}</>\n}", "import React, { useState, useEffect } from 'react'\nimport { useAuth, useRedirectIfAuthenticated } from '../hooks'\nimport { getAuthRedirectUrl } from '../utils'\n\ninterface UnifiedLoginPageProps {\n  appName: string\n  appLogo?: string\n  appDescription?: string\n  showRegisterLink?: boolean\n  registerUrl?: string\n  forgotPasswordUrl?: string\n  customStyles?: {\n    primaryColor?: string\n    backgroundColor?: string\n    cardBackground?: string\n  }\n  onLoginSuccess?: () => void\n}\n\nexport const UnifiedLoginPage: React.FC<UnifiedLoginPageProps> = ({\n  appName,\n  appLogo,\n  appDescription,\n  showRegisterLink = true,\n  registerUrl = '/register',\n  forgotPasswordUrl = '/forgot-password',\n  customStyles = {},\n  onLoginSuccess\n}) => {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [rememberMe, setRememberMe] = useState(false)\n  const [showPassword, setShowPassword] = useState(false)\n  \n  const { login, isLoading, error } = useAuth()\n  \n  // Redirect if already authenticated\n  useRedirectIfAuthenticated(getAuthRedirectUrl())\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    try {\n      await login({ email, password, rememberMe })\n      onLoginSuccess?.()\n    } catch (err) {\n      // Error is handled by the auth context\n    }\n  }\n\n  const styles = {\n    primaryColor: customStyles.primaryColor || '#3b82f6',\n    backgroundColor: customStyles.backgroundColor || '#f3f4f6',\n    cardBackground: customStyles.cardBackground || '#ffffff'\n  }\n\n  return (\n    <div \n      className=\"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\"\n      style={{ backgroundColor: styles.backgroundColor }}\n    >\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          {appLogo && (\n            <img\n              className=\"mx-auto h-16 w-auto mb-4\"\n              src={appLogo}\n              alt={appName}\n            />\n          )}\n          <h2 className=\"text-3xl font-extrabold text-gray-900\">\n            Sign in to {appName}\n          </h2>\n          {appDescription && (\n            <p className=\"mt-2 text-sm text-gray-600\">\n              {appDescription}\n            </p>\n          )}\n        </div>\n\n        <div \n          className=\"bg-white shadow-xl rounded-lg p-8\"\n          style={{ backgroundColor: styles.cardBackground }}\n        >\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative\">\n                <span className=\"block sm:inline\">{error.message}</span>\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                required\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm\"\n                placeholder=\"Enter your email\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"current-password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm\"\n                    placeholder=\"Enter your password\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600\"\n                >\n                  {showPassword ? (\n                    <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                    </svg>\n                  ) : (\n                    <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21\" />\n                    </svg>\n                  )}\n                </button>\n              </div>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <input\n                  id=\"remember-me\"\n                  name=\"remember-me\"\n                  type=\"checkbox\"\n                  checked={rememberMe}\n                  onChange={(e) => setRememberMe(e.target.checked)}\n                  className=\"h-4 w-4 rounded border-gray-300\"\n                  style={{ color: styles.primaryColor }}\n                />\n                <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-900\">\n                  Remember me\n                </label>\n              </div>\n\n              <div className=\"text-sm\">\n                <a \n                  href={forgotPasswordUrl} \n                  className=\"font-medium hover:opacity-80\"\n                  style={{ color: styles.primaryColor }}\n                >\n                  Forgot your password?\n                </a>\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n                style={{ \n                  backgroundColor: styles.primaryColor\n                }}\n              >\n                {isLoading ? (\n                  <>\n                    <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    Signing in...\n                  </>\n                ) : (\n                  'Sign in'\n                )}\n              </button>\n            </div>\n          </form>\n\n          {showRegisterLink && (\n            <div className=\"mt-6\">\n              <div className=\"relative\">\n                <div className=\"absolute inset-0 flex items-center\">\n                  <div className=\"w-full border-t border-gray-300\" />\n                </div>\n                <div className=\"relative flex justify-center text-sm\">\n                  <span className=\"px-2 bg-white text-gray-500\">New to {appName}?</span>\n                </div>\n              </div>\n\n              <div className=\"mt-6 text-center\">\n                <a \n                  href={registerUrl}\n                  className=\"font-medium hover:opacity-80\"\n                  style={{ color: styles.primaryColor }}\n                >\n                  Create an account\n                </a>\n              </div>\n            </div>\n          )}\n\n          {/* SSO Options */}\n          <div className=\"mt-6 space-y-3\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">Or continue with</span>\n              </div>\n            </div>\n\n            <button\n              type=\"button\"\n              className=\"w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n              onClick={() => window.location.href = '/auth/sso/microsoft'}\n            >\n              <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n                <path fill=\"#f25022\" d=\"M0 0h11.377v11.372H0zm12.623 0H24v11.372H12.623z\"/>\n                <path fill=\"#00a4ef\" d=\"M0 12.623h11.377V24H0z\"/>\n                <path fill=\"#7fba00\" d=\"M12.623 12.623H24V24H12.623z\"/>\n              </svg>\n              Microsoft\n            </button>\n\n            <button\n              type=\"button\"\n              className=\"w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n              onClick={() => window.location.href = '/auth/sso/google'}\n            >\n              <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n                <path fill=\"#4285f4\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n                <path fill=\"#34a853\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n                <path fill=\"#fbbc05\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n                <path fill=\"#ea4335\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n              </svg>\n              Google\n            </button>\n          </div>\n        </div>\n\n        <p className=\"mt-4 text-center text-xs text-gray-600\">\n          By signing in, you agree to our{' '}\n          <a href=\"/terms\" className=\"underline hover:text-gray-900\">Terms of Service</a>\n          {' '}and{' '}\n          <a href=\"/privacy\" className=\"underline hover:text-gray-900\">Privacy Policy</a>\n        </p>\n      </div>\n    </div>\n  )\n}", "import React, { useState } from 'react'\nimport { useAuth } from '../hooks'\n\ninterface AppLink {\n  id: string\n  name: string\n  url: string\n  icon?: React.ReactNode\n  description?: string\n  roles?: string[]\n  permissions?: string[]\n}\n\ninterface CrossAppNavigationProps {\n  currentApp: string\n  apps: AppLink[]\n  position?: 'top' | 'left' | 'right'\n  theme?: 'light' | 'dark'\n  onAppSwitch?: (app: AppLink) => void\n}\n\nexport const CrossAppNavigation: React.FC<CrossAppNavigationProps> = ({\n  currentApp,\n  apps,\n  position = 'top',\n  theme = 'light',\n  onAppSwitch\n}) => {\n  const [isOpen, setIsOpen] = useState(false)\n  const { user, hasAnyRole, hasAnyPermission } = useAuth()\n\n  // Filter apps based on user permissions\n  const accessibleApps = apps.filter(app => {\n    if (app.roles && !hasAnyRole(app.roles)) {\n      return false\n    }\n    if (app.permissions && !hasAnyPermission(app.permissions)) {\n      return false\n    }\n    return true\n  })\n\n  const handleAppClick = (app: AppLink) => {\n    if (app.id === currentApp) {\n      setIsOpen(false)\n      return\n    }\n\n    onAppSwitch?.(app)\n    \n    // Navigate to the app\n    if (app.url.startsWith('http')) {\n      // External URL - open in same tab to maintain auth\n      window.location.href = app.url\n    } else {\n      // Relative URL\n      window.location.href = app.url\n    }\n  }\n\n  const themeClasses = {\n    light: {\n      background: 'bg-white',\n      text: 'text-gray-700',\n      hover: 'hover:bg-gray-100',\n      border: 'border-gray-200',\n      activeApp: 'bg-blue-50 text-blue-700'\n    },\n    dark: {\n      background: 'bg-gray-800',\n      text: 'text-gray-200',\n      hover: 'hover:bg-gray-700',\n      border: 'border-gray-600',\n      activeApp: 'bg-gray-700 text-white'\n    }\n  }[theme]\n\n  if (position === 'top') {\n    return (\n      <div className={`${themeClasses.background} border-b ${themeClasses.border}`}>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-12\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => setIsOpen(!isOpen)}\n                className={`flex items-center space-x-2 px-3 py-1 rounded-md ${themeClasses.hover} ${themeClasses.text}`}\n              >\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n                <span className=\"font-medium\">Apps</span>\n              </button>\n\n              <span className={`text-sm ${themeClasses.text}`}>\n                Current: <span className=\"font-medium\">{currentApp}</span>\n              </span>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              <span className={`text-sm ${themeClasses.text}`}>\n                {user?.firstName} {user?.lastName}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Dropdown */}\n        {isOpen && (\n          <div className=\"absolute z-50 mt-1 w-64 rounded-md shadow-lg\">\n            <div className={`rounded-md ${themeClasses.background} ring-1 ring-black ring-opacity-5`}>\n              <div className=\"py-1\">\n                {accessibleApps.map(app => (\n                  <button\n                    key={app.id}\n                    onClick={() => handleAppClick(app)}\n                    className={`\n                      w-full text-left px-4 py-2 text-sm\n                      ${app.id === currentApp ? themeClasses.activeApp : themeClasses.text}\n                      ${themeClasses.hover}\n                    `}\n                  >\n                    <div className=\"flex items-center\">\n                      {app.icon && <div className=\"mr-3\">{app.icon}</div>}\n                      <div>\n                        <div className=\"font-medium\">{app.name}</div>\n                        {app.description && (\n                          <div className=\"text-xs opacity-75\">{app.description}</div>\n                        )}\n                      </div>\n                    </div>\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    )\n  }\n\n  // Side navigation for 'left' or 'right' position\n  const sideClasses = position === 'left' ? 'left-0' : 'right-0'\n  \n  return (\n    <>\n      {/* Toggle button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className={`\n          fixed ${position}-4 top-4 z-50\n          ${themeClasses.background} ${themeClasses.text}\n          p-2 rounded-md shadow-lg ${themeClasses.hover}\n        `}\n      >\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16m-7 6h7\" />\n        </svg>\n      </button>\n\n      {/* Side panel */}\n      {isOpen && (\n        <>\n          {/* Backdrop */}\n          <div \n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40\"\n            onClick={() => setIsOpen(false)}\n          />\n\n          {/* Panel */}\n          <div className={`\n            fixed ${sideClasses} top-0 h-full w-64 z-50\n            ${themeClasses.background} shadow-xl\n            transform transition-transform duration-300\n            ${isOpen ? 'translate-x-0' : position === 'left' ? '-translate-x-full' : 'translate-x-full'}\n          `}>\n            <div className=\"p-4\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className={`text-lg font-semibold ${themeClasses.text}`}>\n                  Luminar Apps\n                </h2>\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className={`${themeClasses.text} ${themeClasses.hover} p-1 rounded`}\n                >\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n\n              <nav className=\"space-y-1\">\n                {accessibleApps.map(app => (\n                  <button\n                    key={app.id}\n                    onClick={() => handleAppClick(app)}\n                    className={`\n                      w-full text-left px-3 py-2 rounded-md text-sm\n                      ${app.id === currentApp ? themeClasses.activeApp : themeClasses.text}\n                      ${themeClasses.hover}\n                    `}\n                  >\n                    <div className=\"flex items-center\">\n                      {app.icon && <div className=\"mr-3\">{app.icon}</div>}\n                      <div>\n                        <div className=\"font-medium\">{app.name}</div>\n                        {app.description && (\n                          <div className=\"text-xs opacity-75\">{app.description}</div>\n                        )}\n                      </div>\n                    </div>\n                  </button>\n                ))}\n              </nav>\n\n              <div className={`mt-8 pt-8 border-t ${themeClasses.border}`}>\n                <div className={`text-sm ${themeClasses.text}`}>\n                  <div className=\"font-medium\">{user?.firstName} {user?.lastName}</div>\n                  <div className=\"text-xs opacity-75\">{user?.email}</div>\n                  <div className=\"mt-2\">\n                    <span className=\"text-xs px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded\">\n                      {user?.roles[0]}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </>\n      )}\n    </>\n  )\n}", "import React from 'react';\nimport { OAuthClient, defaultOAuthProviders } from '../oauth-client';\nimport { AuthConfig } from '../types';\n\ninterface OAuthButtonsProps {\n  providers?: ('google' | 'github' | 'microsoft')[];\n  authConfig: AuthConfig;\n  className?: string;\n  buttonClassName?: string;\n  showDivider?: boolean;\n  dividerText?: string;\n}\n\nconst providerIcons = {\n  google: (\n    <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n      <path fill=\"#4285f4\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n      <path fill=\"#34a853\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n      <path fill=\"#fbbc05\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n      <path fill=\"#ea4335\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n    </svg>\n  ),\n  github: (\n    <svg className=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n    </svg>\n  ),\n  microsoft: (\n    <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n      <path fill=\"#f25022\" d=\"M0 0h11.377v11.372H0zm12.623 0H24v11.372H12.623z\"/>\n      <path fill=\"#00a4ef\" d=\"M0 12.623h11.377V24H0z\"/>\n      <path fill=\"#7fba00\" d=\"M12.623 12.623H24V24H12.623z\"/>\n    </svg>\n  )\n};\n\nconst providerNames = {\n  google: 'Google',\n  github: 'GitHub',\n  microsoft: 'Microsoft'\n};\n\nexport const OAuthButtons: React.FC<OAuthButtonsProps> = ({\n  providers = ['google', 'github', 'microsoft'],\n  authConfig,\n  className = '',\n  buttonClassName = '',\n  showDivider = true,\n  dividerText = 'Or continue with'\n}) => {\n  const handleOAuthLogin = async (provider: string) => {\n    try {\n      const oauthClient = new OAuthClient(authConfig, {\n        providers: defaultOAuthProviders\n      });\n      \n      await oauthClient.initiateOAuth(provider);\n    } catch (error) {\n      console.error(`OAuth login failed for ${provider}:`, error);\n    }\n  };\n\n  return (\n    <div className={className}>\n      {showDivider && (\n        <div className=\"relative mb-6\">\n          <div className=\"absolute inset-0 flex items-center\">\n            <div className=\"w-full border-t border-gray-300\" />\n          </div>\n          <div className=\"relative flex justify-center text-sm\">\n            <span className=\"px-2 bg-white text-gray-500\">{dividerText}</span>\n          </div>\n        </div>\n      )}\n\n      <div className=\"space-y-3\">\n        {providers.map((provider) => (\n          <button\n            key={provider}\n            type=\"button\"\n            onClick={() => handleOAuthLogin(provider)}\n            className={`w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary ${buttonClassName}`}\n          >\n            {providerIcons[provider]}\n            Sign in with {providerNames[provider]}\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n};", "import axios, { AxiosInstance } from 'axios';\nimport { AuthTokens, User, AuthError, AuthConfig } from './types';\n\nexport interface OAuthProvider {\n  name: string;\n  authUrl: string;\n  tokenUrl: string;\n  userInfoUrl: string;\n  clientId: string;\n  clientSecret?: string;\n  scope: string;\n  redirectUri: string;\n}\n\nexport interface OAuthConfig {\n  providers: {\n    google?: OAuthProvider;\n    github?: OAuthProvider;\n    microsoft?: OAuthProvider;\n    [key: string]: OAuthProvider | undefined;\n  };\n  onSuccess?: (user: User, tokens: AuthTokens) => void;\n  onError?: (error: AuthError) => void;\n}\n\nexport interface OAuthState {\n  state: string;\n  codeVerifier?: string;\n  provider: string;\n  timestamp: number;\n}\n\nexport class OAuthClient {\n  private api: AxiosInstance;\n  private config: OAuthConfig;\n  private authConfig: AuthConfig;\n  private stateKey = 'oauth_state';\n\n  constructor(authConfig: AuthConfig, oauthConfig: OAuthConfig) {\n    this.authConfig = authConfig;\n    this.config = oauthConfig;\n    this.api = axios.create({\n      baseURL: authConfig.apiBaseUrl,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n  }\n\n  /**\n   * Initiate OAuth flow\n   */\n  async initiateOAuth(provider: string): Promise<void> {\n    const providerConfig = this.config.providers[provider];\n    if (!providerConfig) {\n      throw new AuthError(`OAuth provider ${provider} not configured`, 'PROVIDER_NOT_CONFIGURED');\n    }\n\n    // Generate state for CSRF protection\n    const state = this.generateState();\n    \n    // Generate PKCE code verifier and challenge for supported providers\n    const codeVerifier = this.generateCodeVerifier();\n    const codeChallenge = await this.generateCodeChallenge(codeVerifier);\n\n    // Store state for verification\n    const oauthState: OAuthState = {\n      state,\n      codeVerifier,\n      provider,\n      timestamp: Date.now()\n    };\n    \n    localStorage.setItem(this.stateKey, JSON.stringify(oauthState));\n\n    // Build authorization URL\n    const params = new URLSearchParams({\n      client_id: providerConfig.clientId,\n      redirect_uri: providerConfig.redirectUri,\n      response_type: 'code',\n      scope: providerConfig.scope,\n      state,\n      access_type: 'offline', // For refresh tokens\n      prompt: 'consent'\n    });\n\n    // Add PKCE parameters if supported\n    if (provider === 'google' || provider === 'github') {\n      params.append('code_challenge', codeChallenge);\n      params.append('code_challenge_method', 'S256');\n    }\n\n    // Redirect to provider\n    window.location.href = `${providerConfig.authUrl}?${params.toString()}`;\n  }\n\n  /**\n   * Handle OAuth callback\n   */\n  async handleCallback(code: string, state: string): Promise<{ user: User; tokens: AuthTokens }> {\n    // Verify state\n    const storedStateStr = localStorage.getItem(this.stateKey);\n    if (!storedStateStr) {\n      throw new AuthError('No OAuth state found', 'INVALID_STATE');\n    }\n\n    const storedState: OAuthState = JSON.parse(storedStateStr);\n    \n    // Clean up state\n    localStorage.removeItem(this.stateKey);\n\n    // Verify state matches and not expired (5 minutes)\n    if (storedState.state !== state) {\n      throw new AuthError('Invalid OAuth state', 'INVALID_STATE');\n    }\n\n    if (Date.now() - storedState.timestamp > 5 * 60 * 1000) {\n      throw new AuthError('OAuth state expired', 'STATE_EXPIRED');\n    }\n\n    const providerConfig = this.config.providers[storedState.provider];\n    if (!providerConfig) {\n      throw new AuthError('OAuth provider not configured', 'PROVIDER_NOT_CONFIGURED');\n    }\n\n    try {\n      // Exchange code for tokens\n      const tokenResponse = await this.exchangeCodeForTokens(\n        code,\n        storedState.provider,\n        providerConfig,\n        storedState.codeVerifier\n      );\n\n      // Get user info from provider\n      const userInfo = await this.getUserInfo(\n        tokenResponse.access_token,\n        providerConfig\n      );\n\n      // Send to backend to create/update user\n      const response = await this.api.post(`${this.authConfig.authEndpoint}/oauth/callback`, {\n        provider: storedState.provider,\n        accessToken: tokenResponse.access_token,\n        refreshToken: tokenResponse.refresh_token,\n        idToken: tokenResponse.id_token,\n        userInfo,\n        expiresIn: tokenResponse.expires_in\n      });\n\n      const { user, accessToken, refreshToken, expiresIn } = response.data;\n\n      const tokens: AuthTokens = {\n        accessToken,\n        refreshToken,\n        expiresIn,\n        tokenType: 'Bearer'\n      };\n\n      // Call success callback\n      this.config.onSuccess?.(user, tokens);\n\n      return { user, tokens };\n    } catch (error: any) {\n      const authError = new AuthError(\n        error.response?.data?.message || 'OAuth authentication failed',\n        error.response?.data?.code || 'OAUTH_ERROR',\n        error.response?.status\n      );\n      \n      this.config.onError?.(authError);\n      throw authError;\n    }\n  }\n\n  /**\n   * Exchange authorization code for tokens\n   */\n  private async exchangeCodeForTokens(\n    code: string,\n    provider: string,\n    providerConfig: OAuthProvider,\n    codeVerifier?: string\n  ): Promise<any> {\n    const params: any = {\n      grant_type: 'authorization_code',\n      code,\n      client_id: providerConfig.clientId,\n      redirect_uri: providerConfig.redirectUri\n    };\n\n    // Add client secret if configured (for confidential clients)\n    if (providerConfig.clientSecret) {\n      params.client_secret = providerConfig.clientSecret;\n    }\n\n    // Add PKCE verifier if used\n    if (codeVerifier && (provider === 'google' || provider === 'github')) {\n      params.code_verifier = codeVerifier;\n    }\n\n    const response = await axios.post(providerConfig.tokenUrl, params, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      }\n    });\n\n    return response.data;\n  }\n\n  /**\n   * Get user info from OAuth provider\n   */\n  private async getUserInfo(accessToken: string, providerConfig: OAuthProvider): Promise<any> {\n    const response = await axios.get(providerConfig.userInfoUrl, {\n      headers: {\n        'Authorization': `Bearer ${accessToken}`\n      }\n    });\n\n    return response.data;\n  }\n\n  /**\n   * Link OAuth account to existing user\n   */\n  async linkAccount(provider: string, userId: string): Promise<void> {\n    // Store linking state\n    const linkingState = {\n      userId,\n      action: 'link',\n      timestamp: Date.now()\n    };\n    \n    localStorage.setItem('oauth_linking', JSON.stringify(linkingState));\n    \n    // Initiate OAuth flow\n    await this.initiateOAuth(provider);\n  }\n\n  /**\n   * Unlink OAuth account\n   */\n  async unlinkAccount(provider: string, userId: string): Promise<void> {\n    await this.api.post(`${this.authConfig.authEndpoint}/oauth/unlink`, {\n      provider,\n      userId\n    });\n  }\n\n  /**\n   * Get linked accounts for user\n   */\n  async getLinkedAccounts(userId: string): Promise<string[]> {\n    const response = await this.api.get(`${this.authConfig.authEndpoint}/oauth/linked/${userId}`);\n    return response.data.providers;\n  }\n\n  /**\n   * Refresh OAuth tokens\n   */\n  async refreshOAuthTokens(provider: string, refreshToken: string): Promise<AuthTokens> {\n    const providerConfig = this.config.providers[provider];\n    if (!providerConfig) {\n      throw new AuthError('OAuth provider not configured', 'PROVIDER_NOT_CONFIGURED');\n    }\n\n    const params: any = {\n      grant_type: 'refresh_token',\n      refresh_token: refreshToken,\n      client_id: providerConfig.clientId\n    };\n\n    if (providerConfig.clientSecret) {\n      params.client_secret = providerConfig.clientSecret;\n    }\n\n    const response = await axios.post(providerConfig.tokenUrl, params, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      }\n    });\n\n    return {\n      accessToken: response.data.access_token,\n      refreshToken: response.data.refresh_token || refreshToken,\n      expiresIn: response.data.expires_in,\n      tokenType: 'Bearer'\n    };\n  }\n\n  /**\n   * Generate random state for CSRF protection\n   */\n  private generateState(): string {\n    const array = new Uint8Array(32);\n    crypto.getRandomValues(array);\n    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');\n  }\n\n  /**\n   * Generate PKCE code verifier\n   */\n  private generateCodeVerifier(): string {\n    const array = new Uint8Array(32);\n    crypto.getRandomValues(array);\n    return this.base64UrlEncode(array);\n  }\n\n  /**\n   * Generate PKCE code challenge\n   */\n  private async generateCodeChallenge(verifier: string): Promise<string> {\n    const encoder = new TextEncoder();\n    const data = encoder.encode(verifier);\n    const digest = await crypto.subtle.digest('SHA-256', data);\n    return this.base64UrlEncode(new Uint8Array(digest));\n  }\n\n  /**\n   * Base64 URL encode\n   */\n  private base64UrlEncode(buffer: Uint8Array): string {\n    const base64 = btoa(String.fromCharCode(...buffer));\n    return base64\n      .replace(/\\+/g, '-')\n      .replace(/\\//g, '_')\n      .replace(/=/g, '');\n  }\n}\n\n// OAuth provider configurations\nexport const defaultOAuthProviders: Partial<OAuthConfig['providers']> = {\n  google: {\n    name: 'Google',\n    authUrl: 'https://accounts.google.com/o/oauth2/v2/auth',\n    tokenUrl: 'https://oauth2.googleapis.com/token',\n    userInfoUrl: 'https://www.googleapis.com/oauth2/v2/userinfo',\n    clientId: process.env.VITE_GOOGLE_CLIENT_ID || '',\n    clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n    scope: 'openid email profile',\n    redirectUri: `${window.location.origin}/auth/callback/google`\n  },\n  github: {\n    name: 'GitHub',\n    authUrl: 'https://github.com/login/oauth/authorize',\n    tokenUrl: 'https://github.com/login/oauth/access_token',\n    userInfoUrl: 'https://api.github.com/user',\n    clientId: process.env.VITE_GITHUB_CLIENT_ID || '',\n    clientSecret: process.env.GITHUB_CLIENT_SECRET,\n    scope: 'read:user user:email',\n    redirectUri: `${window.location.origin}/auth/callback/github`\n  },\n  microsoft: {\n    name: 'Microsoft',\n    authUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',\n    tokenUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',\n    userInfoUrl: 'https://graph.microsoft.com/v1.0/me',\n    clientId: process.env.VITE_MICROSOFT_CLIENT_ID || '',\n    clientSecret: process.env.MICROSOFT_CLIENT_SECRET,\n    scope: 'openid email profile User.Read',\n    redirectUri: `${window.location.origin}/auth/callback/microsoft`\n  }\n};", "import axios, { AxiosInstance } from 'axios'\nimport { AuthClient } from './auth-client'\nimport { AuthConfig, AuthTokens, User, AuthError } from './types'\n\nexport interface CrossAppTokens extends AuthTokens {\n  targetApp: string\n  permissions: string[]\n}\n\nexport interface CrossAppConfig {\n  appId: string\n  apiBaseUrl: string\n  allowedOrigins: string[]\n  tokenExpiryMinutes: number\n}\n\nexport interface CrossAppRequest {\n  targetAppId: string\n  permissions?: string[]\n  sessionData?: Record<string, any>\n}\n\nexport class CrossAppAuthClient {\n  private authClient: AuthClient\n  private api: AxiosInstance\n  private crossAppTokens: Map<string, CrossAppTokens> = new Map()\n\n  constructor(authClient: AuthClient) {\n    this.authClient = authClient\n    this.api = axios.create({\n      baseURL: authClient['config'].apiBaseUrl,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    })\n\n    this.setupInterceptors()\n  }\n\n  private setupInterceptors() {\n    this.api.interceptors.request.use(\n      (config) => {\n        const token = this.authClient.getStoredTokens()?.accessToken\n        if (token && !config.headers['skipAuth']) {\n          config.headers['Authorization'] = `Bearer ${token}`\n        }\n        return config\n      },\n      (error) => Promise.reject(error)\n    )\n  }\n\n  /**\n   * Generate a cross-app token for accessing another Luminar app\n   */\n  async generateCrossAppToken(request: CrossAppRequest): Promise<CrossAppTokens> {\n    try {\n      const response = await this.api.post('/auth/cross-app/token/generate', request)\n      \n      const { token, targetApp, permissions, expiresIn } = response.data.data\n      \n      const crossAppTokens: CrossAppTokens = {\n        accessToken: token,\n        refreshToken: '', // Cross-app tokens don't have refresh tokens\n        targetApp,\n        permissions,\n        expiresIn,\n        tokenType: 'Bearer'\n      }\n\n      // Cache the token\n      this.crossAppTokens.set(targetApp, crossAppTokens)\n      \n      return crossAppTokens\n    } catch (error: any) {\n      throw new AuthError(\n        error.response?.data?.message || 'Failed to generate cross-app token',\n        'CROSS_APP_TOKEN_FAILED',\n        error.response?.status\n      )\n    }\n  }\n\n  /**\n   * Get cached cross-app token or generate new one\n   */\n  async getCrossAppToken(appId: string, permissions?: string[]): Promise<string> {\n    const cached = this.crossAppTokens.get(appId)\n    \n    // Check if cached token is still valid\n    if (cached && !this.authClient.isTokenExpired(cached.accessToken)) {\n      return cached.accessToken\n    }\n\n    // Generate new token\n    const newTokens = await this.generateCrossAppToken({\n      targetAppId: appId,\n      permissions\n    })\n\n    return newTokens.accessToken\n  }\n\n  /**\n   * Verify a cross-app token\n   */\n  async verifyToken(token: string, appId: string): Promise<{\n    valid: boolean\n    payload?: {\n      userId: string\n      email: string\n      roles: string[]\n      permissions: string[]\n      appId: string\n      sessionId: string\n    }\n    error?: string\n  }> {\n    try {\n      const response = await this.api.post('/auth/cross-app/token/verify', {\n        token,\n        appId\n      }, {\n        headers: { skipAuth: true }\n      })\n\n      return response.data.data\n    } catch (error: any) {\n      return {\n        valid: false,\n        error: error.response?.data?.message || 'Token verification failed'\n      }\n    }\n  }\n\n  /**\n   * Refresh a cross-app token\n   */\n  async refreshCrossAppToken(appId: string): Promise<CrossAppTokens> {\n    try {\n      const cached = this.crossAppTokens.get(appId)\n      if (!cached) {\n        throw new Error('No cached token to refresh')\n      }\n\n      const response = await this.api.post('/auth/cross-app/token/refresh', {\n        token: cached.accessToken,\n        appId\n      })\n\n      const { token } = response.data.data\n      \n      const refreshedTokens: CrossAppTokens = {\n        ...cached,\n        accessToken: token\n      }\n\n      this.crossAppTokens.set(appId, refreshedTokens)\n      \n      return refreshedTokens\n    } catch (error: any) {\n      // Remove invalid token from cache\n      this.crossAppTokens.delete(appId)\n      throw new AuthError(\n        'Failed to refresh cross-app token',\n        'CROSS_APP_REFRESH_FAILED',\n        error.response?.status\n      )\n    }\n  }\n\n  /**\n   * Get available apps configuration\n   */\n  async getAvailableApps(): Promise<CrossAppConfig[]> {\n    try {\n      const response = await this.api.get('/auth/cross-app/apps/config', {\n        headers: { skipAuth: true }\n      })\n      \n      return response.data.data\n    } catch (error: any) {\n      throw new AuthError(\n        'Failed to get available apps',\n        'APPS_CONFIG_FAILED',\n        error.response?.status\n      )\n    }\n  }\n\n  /**\n   * Get user permissions for specific app\n   */\n  async getUserPermissionsForApp(appId: string): Promise<string[]> {\n    try {\n      const response = await this.api.get(`/auth/cross-app/permissions/${appId}`)\n      \n      return response.data.data.permissions\n    } catch (error: any) {\n      throw new AuthError(\n        'Failed to get user permissions',\n        'PERMISSIONS_FAILED',\n        error.response?.status\n      )\n    }\n  }\n\n  /**\n   * Logout from all apps\n   */\n  async logoutFromAllApps(): Promise<void> {\n    try {\n      // Generate logout tokens for all apps\n      const response = await this.api.post('/auth/cross-app/logout/all')\n      const { logoutTokens } = response.data.data\n\n      // Notify all apps about logout\n      const logoutPromises = Object.entries(logoutTokens).map(async ([appId, token]) => {\n        try {\n          const appConfig = await this.getAppConfig(appId)\n          if (appConfig) {\n            await this.notifyAppLogout(appConfig, token as string)\n          }\n        } catch (error) {\n          console.warn(`Failed to notify ${appId} about logout:`, error)\n        }\n      })\n\n      await Promise.allSettled(logoutPromises)\n      \n      // Clear local cross-app tokens\n      this.crossAppTokens.clear()\n      \n      // Logout from main app\n      await this.authClient.logout()\n    } catch (error: any) {\n      throw new AuthError(\n        'Failed to logout from all apps',\n        'LOGOUT_ALL_FAILED',\n        error.response?.status\n      )\n    }\n  }\n\n  /**\n   * Navigate to another app with SSO\n   */\n  async navigateToApp(\n    appId: string, \n    redirectPath: string = '/',\n    permissions?: string[]\n  ): Promise<string> {\n    try {\n      // Generate cross-app token\n      const token = await this.getCrossAppToken(appId, permissions)\n      \n      // Get app configuration\n      const appConfig = await this.getAppConfig(appId)\n      if (!appConfig) {\n        throw new Error(`App ${appId} not found`)\n      }\n\n      // Construct SSO URL\n      const baseUrl = appConfig.allowedOrigins[0] // Use first allowed origin\n      const ssoUrl = new URL('/auth/sso', baseUrl)\n      ssoUrl.searchParams.set('token', token)\n      ssoUrl.searchParams.set('redirect', redirectPath)\n      \n      return ssoUrl.toString()\n    } catch (error: any) {\n      throw new AuthError(\n        'Failed to generate SSO URL',\n        'SSO_URL_FAILED',\n        error.response?.status\n      )\n    }\n  }\n\n  /**\n   * Validate app origin for CORS\n   */\n  isOriginAllowed(appId: string, origin: string): boolean {\n    const cached = this.crossAppTokens.get(appId)\n    // This would typically be validated against server configuration\n    return true // Simplified for demo\n  }\n\n  /**\n   * Clear cached tokens for specific app\n   */\n  clearAppToken(appId: string): void {\n    this.crossAppTokens.delete(appId)\n  }\n\n  /**\n   * Clear all cached cross-app tokens\n   */\n  clearAllAppTokens(): void {\n    this.crossAppTokens.clear()\n  }\n\n  /**\n   * Get cached token info\n   */\n  getCachedTokenInfo(appId: string): CrossAppTokens | null {\n    return this.crossAppTokens.get(appId) || null\n  }\n\n  /**\n   * Check if user has permission for specific app action\n   */\n  async hasAppPermission(appId: string, permission: string): Promise<boolean> {\n    try {\n      const permissions = await this.getUserPermissionsForApp(appId)\n      return permissions.includes(permission)\n    } catch {\n      return false\n    }\n  }\n\n  private async getAppConfig(appId: string): Promise<CrossAppConfig | null> {\n    try {\n      const apps = await this.getAvailableApps()\n      return apps.find(app => app.appId === appId) || null\n    } catch {\n      return null\n    }\n  }\n\n  private async notifyAppLogout(appConfig: CrossAppConfig, logoutToken: string): Promise<void> {\n    try {\n      const logoutUrl = new URL('/auth/logout/notify', appConfig.allowedOrigins[0])\n      \n      await axios.post(logoutUrl.toString(), {\n        token: logoutToken,\n        appId: appConfig.appId\n      }, {\n        timeout: 5000, // 5 second timeout\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      })\n    } catch (error) {\n      // Silent fail for logout notifications\n      console.warn(`Failed to notify ${appConfig.appId} about logout:`, error)\n    }\n  }\n\n  /**\n   * Auto-refresh cross-app tokens before they expire\n   */\n  startAutoRefresh(intervalMinutes: number = 5): void {\n    setInterval(async () => {\n      for (const [appId, tokens] of this.crossAppTokens) {\n        try {\n          // Check if token expires in next 5 minutes\n          const expiresIn = tokens.expiresIn || 0\n          const expiresAt = Date.now() + (expiresIn * 1000)\n          const fiveMinutesFromNow = Date.now() + (5 * 60 * 1000)\n\n          if (expiresAt <= fiveMinutesFromNow) {\n            await this.refreshCrossAppToken(appId)\n          }\n        } catch (error) {\n          console.warn(`Failed to auto-refresh token for ${appId}:`, error)\n          this.crossAppTokens.delete(appId)\n        }\n      }\n    }, intervalMinutes * 60 * 1000)\n  }\n}\n\n// Export utility functions for SSO integration\nexport const createSSOUrl = (appBaseUrl: string, token: string, redirectPath: string = '/'): string => {\n  const url = new URL('/auth/sso', appBaseUrl)\n  url.searchParams.set('token', token)\n  url.searchParams.set('redirect', redirectPath)\n  return url.toString()\n}\n\nexport const extractSSOToken = (): string | null => {\n  if (typeof window === 'undefined') return null\n  \n  const params = new URLSearchParams(window.location.search)\n  return params.get('token')\n}\n\nexport const extractSSORedirect = (): string => {\n  if (typeof window === 'undefined') return '/'\n  \n  const params = new URLSearchParams(window.location.search)\n  return params.get('redirect') || '/'\n}", "import { AuthConfig } from './types'\n\nexport interface CorsHeaders {\n  'Access-Control-Allow-Origin'?: string\n  'Access-Control-Allow-Methods'?: string\n  'Access-Control-Allow-Headers'?: string\n  'Access-Control-Expose-Headers'?: string\n  'Access-Control-Allow-Credentials'?: string\n  'Access-Control-Max-Age'?: string\n}\n\nexport interface CorsValidationResult {\n  allowed: boolean\n  headers?: CorsHeaders\n  reason?: string\n}\n\nexport class CorsHelper {\n  private config: AuthConfig\n  private appId: string\n\n  constructor(config: AuthConfig, appId: string) {\n    this.config = config\n    this.appId = appId\n  }\n\n  /**\n   * Validate if current origin is allowed for cross-app requests\n   */\n  validateCurrentOrigin(): boolean {\n    if (typeof window === 'undefined') return true // Server-side\n\n    const currentOrigin = window.location.origin\n    return this.isOriginAllowed(currentOrigin)\n  }\n\n  /**\n   * Check if an origin is allowed for this app\n   */\n  isOriginAllowed(origin: string): boolean {\n    // Get allowed origins from environment or config\n    const allowedOrigins = this.getAllowedOrigins()\n    \n    // Check exact match\n    if (allowedOrigins.includes(origin)) {\n      return true\n    }\n\n    // Check localhost patterns in development\n    if (this.isDevelopment()) {\n      const localhostPattern = /^https?:\\/\\/(localhost|127\\.0\\.0\\.1)(:\\d+)?$/\n      if (localhostPattern.test(origin)) {\n        return allowedOrigins.some(allowed => \n          allowed.includes('localhost') || allowed.includes('127.0.0.1')\n        )\n      }\n    }\n\n    return false\n  }\n\n  /**\n   * Get CORS headers for cross-app requests\n   */\n  getCorsHeaders(targetOrigin?: string): CorsHeaders {\n    const origin = targetOrigin || this.getCurrentOrigin()\n    \n    if (!this.isOriginAllowed(origin)) {\n      return {}\n    }\n\n    return {\n      'Access-Control-Allow-Origin': origin,\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': this.getAllowedHeaders().join(', '),\n      'Access-Control-Expose-Headers': this.getExposedHeaders().join(', '),\n      'Access-Control-Allow-Credentials': 'true',\n      'Access-Control-Max-Age': '86400'\n    }\n  }\n\n  /**\n   * Validate CORS request against backend\n   */\n  async validateCorsRequest(\n    origin: string,\n    method: string,\n    headers: string[]\n  ): Promise<CorsValidationResult> {\n    try {\n      const response = await fetch(`${this.config.apiBaseUrl}/auth/cors/validate`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          origin,\n          method,\n          headers,\n          appId: this.appId\n        })\n      })\n\n      const data = await response.json()\n\n      if (data.success) {\n        return {\n          allowed: data.data.allowed,\n          headers: data.data.headers,\n          reason: data.data.reason\n        }\n      }\n\n      return {\n        allowed: false,\n        reason: data.message || 'CORS validation failed'\n      }\n    } catch (error) {\n      return {\n        allowed: false,\n        reason: `CORS validation error: ${error}`\n      }\n    }\n  }\n\n  /**\n   * Create preflight request for complex CORS requests\n   */\n  async sendPreflightRequest(\n    targetUrl: string,\n    method: string,\n    headers: string[]\n  ): Promise<CorsValidationResult> {\n    try {\n      const response = await fetch(targetUrl, {\n        method: 'OPTIONS',\n        headers: {\n          'Origin': this.getCurrentOrigin(),\n          'Access-Control-Request-Method': method,\n          'Access-Control-Request-Headers': headers.join(', ')\n        }\n      })\n\n      if (response.ok) {\n        const corsHeaders: CorsHeaders = {}\n        \n        // Extract CORS headers from response\n        const allowOrigin = response.headers.get('Access-Control-Allow-Origin')\n        const allowMethods = response.headers.get('Access-Control-Allow-Methods')\n        const allowHeaders = response.headers.get('Access-Control-Allow-Headers')\n        const exposeHeaders = response.headers.get('Access-Control-Expose-Headers')\n        const allowCredentials = response.headers.get('Access-Control-Allow-Credentials')\n        const maxAge = response.headers.get('Access-Control-Max-Age')\n\n        if (allowOrigin) corsHeaders['Access-Control-Allow-Origin'] = allowOrigin\n        if (allowMethods) corsHeaders['Access-Control-Allow-Methods'] = allowMethods\n        if (allowHeaders) corsHeaders['Access-Control-Allow-Headers'] = allowHeaders\n        if (exposeHeaders) corsHeaders['Access-Control-Expose-Headers'] = exposeHeaders\n        if (allowCredentials) corsHeaders['Access-Control-Allow-Credentials'] = allowCredentials\n        if (maxAge) corsHeaders['Access-Control-Max-Age'] = maxAge\n\n        return {\n          allowed: true,\n          headers: corsHeaders\n        }\n      }\n\n      return {\n        allowed: false,\n        reason: `Preflight failed with status ${response.status}`\n      }\n    } catch (error) {\n      return {\n        allowed: false,\n        reason: `Preflight error: ${error}`\n      }\n    }\n  }\n\n  /**\n   * Setup CORS for cross-app communication\n   */\n  setupCrossAppCors(): void {\n    if (typeof window === 'undefined') return\n\n    // Add event listener for cross-origin messages\n    window.addEventListener('message', (event) => {\n      if (!this.isOriginAllowed(event.origin)) {\n        console.warn(`Blocked message from unauthorized origin: ${event.origin}`)\n        return\n      }\n\n      // Handle authorized cross-app messages\n      this.handleCrossAppMessage(event)\n    })\n  }\n\n  /**\n   * Send message to another L&D app\n   */\n  sendCrossAppMessage(\n    targetOrigin: string,\n    message: any,\n    targetWindow?: Window\n  ): boolean {\n    if (!this.isOriginAllowed(targetOrigin)) {\n      console.error(`Cannot send message to unauthorized origin: ${targetOrigin}`)\n      return false\n    }\n\n    const target = targetWindow || window.parent\n    target.postMessage(message, targetOrigin)\n    return true\n  }\n\n  /**\n   * Create CORS-compliant fetch wrapper\n   */\n  createCorsAwareFetch() {\n    const originalFetch = fetch\n    const corsHelper = this\n\n    return async function corsAwareFetch(\n      input: RequestInfo | URL,\n      init?: RequestInit\n    ): Promise<Response> {\n      const url = typeof input === 'string' ? input : input.toString()\n      const urlObj = new URL(url)\n      const targetOrigin = urlObj.origin\n\n      // Check if cross-origin request\n      if (targetOrigin !== corsHelper.getCurrentOrigin()) {\n        // Validate CORS before making request\n        const validation = await corsHelper.validateCorsRequest(\n          corsHelper.getCurrentOrigin(),\n          init?.method || 'GET',\n          Object.keys(init?.headers || {})\n        )\n\n        if (!validation.allowed) {\n          throw new Error(`CORS blocked: ${validation.reason}`)\n        }\n\n        // Add CORS headers to request\n        const corsHeaders = corsHelper.getCorsHeaders(targetOrigin)\n        init = {\n          ...init,\n          headers: {\n            ...init?.headers,\n            ...corsHeaders\n          }\n        }\n      }\n\n      return originalFetch(input, init)\n    }\n  }\n\n  private getAllowedOrigins(): string[] {\n    // Default origins for L&D apps\n    const defaultOrigins = [\n      'http://localhost:3000', // Command Center\n      'http://localhost:3001', // Learning Dashboard\n      'http://localhost:3002', // Skills Assessment\n      'http://localhost:3003', // Talent Analytics\n      'http://localhost:3004', // Admin Portal\n      'https://command-center.luminar.app',\n      'https://learn.luminar.app',\n      'https://assess.luminar.app',\n      'https://analytics.luminar.app',\n      'https://admin.luminar.app'\n    ]\n\n    // Add any custom origins from config\n    const customOrigins = process.env.CORS_ORIGINS?.split(',') || []\n    \n    return [...defaultOrigins, ...customOrigins.filter(Boolean)]\n  }\n\n  private getAllowedHeaders(): string[] {\n    return [\n      'Origin',\n      'X-Requested-With',\n      'Content-Type',\n      'Accept',\n      'Authorization',\n      'X-Cross-App-Token',\n      'X-User-Context',\n      'X-Learning-Context',\n      'X-Assessment-Context',\n      'X-Analytics-Context',\n      'X-Admin-Context',\n      'X-Session-ID',\n      'X-Device-ID',\n      'X-App-ID'\n    ]\n  }\n\n  private getExposedHeaders(): string[] {\n    return [\n      'X-Total-Count',\n      'X-Request-ID',\n      'X-Rate-Limit-Limit',\n      'X-Rate-Limit-Remaining',\n      'X-Learning-Progress',\n      'X-Assessment-Status',\n      'X-Analytics-Version'\n    ]\n  }\n\n  private getCurrentOrigin(): string {\n    if (typeof window === 'undefined') return ''\n    return window.location.origin\n  }\n\n  private isDevelopment(): boolean {\n    return process.env.NODE_ENV === 'development' ||\n           this.getCurrentOrigin().includes('localhost') ||\n           this.getCurrentOrigin().includes('127.0.0.1')\n  }\n\n  private handleCrossAppMessage(event: MessageEvent): void {\n    // Handle different types of cross-app messages\n    const { type, data } = event.data\n\n    switch (type) {\n      case 'AUTH_STATE_CHANGE':\n        this.handleAuthStateChange(data)\n        break\n      case 'USER_LOGOUT':\n        this.handleUserLogout(data)\n        break\n      case 'PERMISSION_UPDATE':\n        this.handlePermissionUpdate(data)\n        break\n      default:\n        console.log('Unknown cross-app message type:', type)\n    }\n  }\n\n  private handleAuthStateChange(data: any): void {\n    // Emit custom event for auth state changes\n    if (typeof window !== 'undefined') {\n      window.dispatchEvent(new CustomEvent('luminar:auth:change', {\n        detail: data\n      }))\n    }\n  }\n\n  private handleUserLogout(data: any): void {\n    // Emit custom event for logout\n    if (typeof window !== 'undefined') {\n      window.dispatchEvent(new CustomEvent('luminar:auth:logout', {\n        detail: data\n      }))\n    }\n  }\n\n  private handlePermissionUpdate(data: any): void {\n    // Emit custom event for permission updates\n    if (typeof window !== 'undefined') {\n      window.dispatchEvent(new CustomEvent('luminar:auth:permissions', {\n        detail: data\n      }))\n    }\n  }\n}\n\n// Utility functions\nexport const createCorsHelper = (config: AuthConfig, appId: string): CorsHelper => {\n  return new CorsHelper(config, appId)\n}\n\nexport const isOriginAllowed = (origin: string, allowedOrigins: string[]): boolean => {\n  return allowedOrigins.includes(origin) ||\n         allowedOrigins.includes('*') ||\n         (origin.includes('localhost') && allowedOrigins.some(o => o.includes('localhost')))\n}\n\nexport const extractOriginFromUrl = (url: string): string => {\n  try {\n    return new URL(url).origin\n  } catch {\n    return ''\n  }\n}\n\nexport const isSameOrigin = (url1: string, url2: string): boolean => {\n  return extractOriginFromUrl(url1) === extractOriginFromUrl(url2)\n}\n\nexport const buildCorsUrl = (baseUrl: string, path: string, params?: Record<string, string>): string => {\n  const url = new URL(path, baseUrl)\n  \n  if (params) {\n    Object.entries(params).forEach(([key, value]) => {\n      url.searchParams.set(key, value)\n    })\n  }\n  \n  return url.toString()\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,mBAAiD;AACjD,wBAA0B;AAC1B,uBAAoB;;;AC4Db,IAAM,YAAN,cAAwB,MAAM;AAAA,EACnC,YACE,SACO,MACA,YACA,SACP;AACA,UAAM,OAAO;AAJN;AACA;AACA;AAGP,SAAK,OAAO;AAAA,EACd;AACF;;;AD5DO,IAAM,aAAN,MAAiB;AAAA,EAKtB,YAAY,QAAoB;AAFhC,SAAQ,iBAA6C;AAGnD,SAAK,SAAS;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,mBAAmB;AAAA,MACnB,iBAAiB,KAAK,KAAK;AAAA;AAAA,MAC3B,SAAS;AAAA,MACT,GAAG;AAAA,IACL;AAEA,SAAK,MAAM,aAAAA,QAAM,OAAO;AAAA,MACtB,SAAS,OAAO;AAAA,MAChB,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAED,SAAK,kBAAkB;AAAA,EACzB;AAAA,EAEQ,oBAAoB;AAE1B,SAAK,IAAI,aAAa,QAAQ;AAAA,MAC5B,CAAC,WAAW;AACV,cAAM,QAAQ,KAAK,eAAe;AAClC,YAAI,SAAS,CAAC,OAAO,QAAQ,UAAU,GAAG;AACxC,iBAAO,QAAQ,eAAe,IAAI,UAAU,KAAK;AAAA,QACnD;AACA,eAAO;AAAA,MACT;AAAA,MACA,CAAC,UAAU,QAAQ,OAAO,KAAK;AAAA,IACjC;AAGA,SAAK,IAAI,aAAa,SAAS;AAAA,MAC7B,CAAC,aAAa;AAAA,MACd,OAAO,UAAsB;AAC3B,cAAM,kBAAkB,MAAM;AAE9B,YAAI,MAAM,UAAU,WAAW,OAAO,CAAC,gBAAgB,QAAQ;AAC7D,0BAAgB,SAAS;AAEzB,cAAI;AACF,kBAAM,KAAK,cAAc;AACzB,kBAAM,QAAQ,KAAK,eAAe;AAClC,gBAAI,OAAO;AACT,8BAAgB,QAAQ,eAAe,IAAI,UAAU,KAAK;AAC1D,qBAAO,KAAK,IAAI,eAAe;AAAA,YACjC;AAAA,UACF,SAAS,cAAc;AACrB,iBAAK,OAAO,cAAc,IAAI;AAAA,cAC5B;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AACD,iBAAK,UAAU;AACf,kBAAM;AAAA,UACR;AAAA,QACF;AAEA,cAAM,KAAK,eAAe,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,eAAe,OAA8B;AACnD,QAAI,CAAC,MAAM,UAAU;AACnB,aAAO,IAAI,UAAU,iBAAiB,eAAe;AAAA,IACvD;AAEA,UAAM,EAAE,QAAQ,KAAK,IAAI,MAAM;AAC/B,UAAM,UAAU,MAAM,WAAW;AACjC,UAAM,OAAO,MAAM,QAAQ;AAE3B,WAAO,IAAI,UAAU,SAAS,MAAM,QAAQ,IAAI;AAAA,EAClD;AAAA;AAAA,EAGQ,aAAa;AACnB,YAAQ,KAAK,OAAO,SAAS;AAAA,MAC3B,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,UACL,SAAS,CAAC,QAAgB,iBAAAC,QAAQ,IAAI,GAAG,KAAK;AAAA,UAC9C,SAAS,CAAC,KAAa,UAAkB;AACvC,6BAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK,OAAO,aAAa;AAAA,UACnD;AAAA,UACA,YAAY,CAAC,QAAgB,iBAAAA,QAAQ,OAAO,GAAG;AAAA,QACjD;AAAA,MACF;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EAEQ,UAAU,QAAoB;AACpC,UAAM,UAAU,KAAK,WAAW;AAChC,YAAQ,QAAQ,KAAK,OAAO,UAAW,OAAO,WAAW;AACzD,YAAQ,QAAQ,KAAK,OAAO,iBAAkB,OAAO,YAAY;AAAA,EACnE;AAAA,EAEQ,iBAAgC;AACtC,WAAO,KAAK,WAAW,EAAE,QAAQ,KAAK,OAAO,QAAS;AAAA,EACxD;AAAA,EAEQ,kBAAiC;AACvC,WAAO,KAAK,WAAW,EAAE,QAAQ,KAAK,OAAO,eAAgB;AAAA,EAC/D;AAAA,EAEQ,QAAQ,MAAY;AAC1B,UAAM,UAAU,KAAK,WAAW;AAChC,YAAQ,QAAQ,KAAK,OAAO,SAAU,KAAK,UAAU,IAAI,CAAC;AAAA,EAC5D;AAAA,EAEQ,UAAuB;AAC7B,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM,UAAU,QAAQ,QAAQ,KAAK,OAAO,OAAQ;AACpD,QAAI,SAAS;AACX,UAAI;AACF,eAAO,KAAK,MAAM,OAAO;AAAA,MAC3B,QAAQ;AACN,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEQ,YAAY;AAClB,UAAM,UAAU,KAAK,WAAW;AAChC,YAAQ,WAAW,KAAK,OAAO,QAAS;AACxC,YAAQ,WAAW,KAAK,OAAO,eAAgB;AAC/C,YAAQ,WAAW,KAAK,OAAO,OAAQ;AAAA,EACzC;AAAA;AAAA,EAGA,MAAM,MAAM,aAA4E;AACtF,UAAM,WAAW,MAAM,KAAK,IAAI,KAAK,GAAG,KAAK,OAAO,YAAY,UAAU,aAAa;AAAA,MACrF,SAAS,EAAE,UAAU,KAAK;AAAA,IAC5B,CAAC;AAED,UAAM,EAAE,MAAM,aAAa,cAAc,UAAU,IAAI,SAAS;AAEhE,UAAM,SAAqB;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb;AAEA,SAAK,UAAU,MAAM;AACrB,SAAK,QAAQ,IAAI;AAEjB,WAAO,EAAE,MAAM,OAAO;AAAA,EACxB;AAAA,EAEA,MAAM,SAAS,MAAiE;AAC9E,UAAM,WAAW,MAAM,KAAK,IAAI,KAAK,GAAG,KAAK,OAAO,YAAY,aAAa,MAAM;AAAA,MACjF,SAAS,EAAE,UAAU,KAAK;AAAA,IAC5B,CAAC;AAED,UAAM,EAAE,MAAM,aAAa,cAAc,UAAU,IAAI,SAAS;AAEhE,UAAM,SAAqB;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb;AAEA,SAAK,UAAU,MAAM;AACrB,SAAK,QAAQ,IAAI;AAEjB,WAAO,EAAE,MAAM,OAAO;AAAA,EACxB;AAAA,EAEA,MAAM,SAAwB;AAC5B,QAAI;AACF,YAAM,KAAK,IAAI,KAAK,GAAG,KAAK,OAAO,YAAY,SAAS;AAAA,IAC1D,SAAS,OAAO;AAEd,cAAQ,MAAM,sBAAsB,KAAK;AAAA,IAC3C,UAAE;AACA,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EAEA,MAAM,gBAAqC;AAEzC,QAAI,KAAK,gBAAgB;AACvB,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,CAAC,cAAc;AACjB,YAAM,IAAI,UAAU,8BAA8B,kBAAkB;AAAA,IACtE;AAEA,SAAK,iBAAiB,KAAK,IACxB;AAAA,MACC,GAAG,KAAK,OAAO,YAAY;AAAA,MAC3B,EAAE,aAAa;AAAA,MACf,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE;AAAA,IAChC,EACC,KAAK,CAAC,aAAa;AAClB,YAAM,EAAE,aAAa,cAAc,iBAAiB,UAAU,IAAI,SAAS;AAE3E,YAAM,SAAqB;AAAA,QACzB;AAAA,QACA,cAAc;AAAA,QACd;AAAA,QACA,WAAW;AAAA,MACb;AAEA,WAAK,UAAU,MAAM;AACrB,WAAK,OAAO,iBAAiB,MAAM;AAEnC,aAAO;AAAA,IACT,CAAC,EACA,QAAQ,MAAM;AACb,WAAK,iBAAiB;AAAA,IACxB,CAAC;AAEH,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAM,iBAAgC;AACpC,UAAM,WAAW,MAAM,KAAK,IAAI,IAAI,GAAG,KAAK,OAAO,YAAY,KAAK;AACpE,UAAM,OAAO,SAAS;AACtB,SAAK,QAAQ,IAAI;AACjB,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,cAAc,MAAoC;AACtD,UAAM,WAAW,MAAM,KAAK,IAAI,MAAM,GAAG,KAAK,OAAO,YAAY,YAAY,IAAI;AACjF,UAAM,OAAO,SAAS;AACtB,SAAK,QAAQ,IAAI;AACjB,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,eAAe,iBAAyB,aAAoC;AAChF,UAAM,KAAK,IAAI,KAAK,GAAG,KAAK,OAAO,YAAY,oBAAoB;AAAA,MACjE;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,qBAAqB,OAA8B;AACvD,UAAM,KAAK,IAAI,KAAK,GAAG,KAAK,OAAO,YAAY,oBAAoB,EAAE,MAAM,GAAG;AAAA,MAC5E,SAAS,EAAE,UAAU,KAAK;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,cAAc,OAAe,aAAoC;AACrE,UAAM,KAAK,IAAI,KAAK,GAAG,KAAK,OAAO,YAAY,mBAAmB;AAAA,MAChE;AAAA,MACA;AAAA,IACF,GAAG;AAAA,MACD,SAAS,EAAE,UAAU,KAAK;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,eAAe,OAAyB;AACtC,UAAM,eAAe,SAAS,KAAK,eAAe;AAClD,QAAI,CAAC,aAAc,QAAO;AAE1B,QAAI;AACF,YAAM,cAAU,6BAA2B,YAAY;AACvD,aAAO,QAAQ,MAAM,MAAO,KAAK,IAAI;AAAA,IACvC,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,kBAAoC;AACxC,QAAI;AACF,YAAM,QAAQ,KAAK,eAAe;AAClC,UAAI,CAAC,SAAS,KAAK,eAAe,KAAK,GAAG;AAExC,cAAM,KAAK,cAAc;AAAA,MAC3B;AAGA,YAAM,KAAK,eAAe;AAC1B,aAAO;AAAA,IACT,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGA,gBAA6B;AAC3B,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,kBAAqC;AACnC,UAAM,cAAc,KAAK,eAAe;AACxC,UAAM,eAAe,KAAK,gBAAgB;AAE1C,QAAI,CAAC,eAAe,CAAC,cAAc;AACjC,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EAEA,eAAqB;AACnB,SAAK,UAAU;AAAA,EACjB;AACF;;;AE7UA,IAAAC,gBAAgE;;;ACAhE,mBAAiD;AAGjD,IAAM,kBAAc,4BAA4C,MAAS;AAElE,IAAM,iBAAiB,MAAwB;AACpD,QAAM,cAAU,yBAAW,WAAW;AACtC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACtE;AACA,SAAO;AACT;;;ADGO,IAAM,eAA4C,CAAC;AAAA,EACxD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,iBAAa,sBAAmB,IAAI,WAAW,MAAM,CAAC;AAE5D,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAoB;AAAA,IAC5C,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,OAAO;AAAA,IACP,GAAG;AAAA,EACL,CAAC;AAGD,+BAAU,MAAM;AACd,wBAAoB,KAAK;AAAA,EAC3B,GAAG,CAAC,OAAO,iBAAiB,CAAC;AAG7B,+BAAU,MAAM;AACd,UAAM,WAAW,YAAY;AAC3B,UAAI;AACF,cAAM,aAAa,WAAW,QAAQ,cAAc;AACpD,cAAM,eAAe,WAAW,QAAQ,gBAAgB;AAExD,YAAI,cAAc,cAAc;AAE9B,gBAAM,UAAU,MAAM,WAAW,QAAQ,gBAAgB;AAEzD,cAAI,SAAS;AACX,qBAAS;AAAA,cACP,MAAM,WAAW,QAAQ,cAAc;AAAA,cACvC,QAAQ,WAAW,QAAQ,gBAAgB;AAAA,cAC3C,iBAAiB;AAAA,cACjB,WAAW;AAAA,cACX,OAAO;AAAA,YACT,CAAC;AAAA,UACH,OAAO;AACL,uBAAW,QAAQ,aAAa;AAChC,qBAAS;AAAA,cACP,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,iBAAiB;AAAA,cACjB,WAAW;AAAA,cACX,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AACL,mBAAS,WAAS,EAAE,GAAG,MAAM,WAAW,MAAM,EAAE;AAAA,QAClD;AAAA,MACF,SAAS,OAAO;AACd,iBAAS;AAAA,UACP,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,iBAAiB;AAAA,UACjB,WAAW;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,aAAS;AAAA,EACX,GAAG,CAAC,CAAC;AAGL,+BAAU,MAAM;AACd,QAAI,CAAC,OAAO,qBAAqB,CAAC,MAAM,iBAAiB;AACvD;AAAA,IACF;AAEA,UAAM,WAAW,YAAY,YAAY;AACvC,UAAI;AACF,cAAM,SAAS,MAAM,WAAW,QAAQ,cAAc;AACtD,iBAAS,WAAS,EAAE,GAAG,MAAM,OAAO,EAAE;AAAA,MACxC,SAAS,OAAO;AACd,gBAAQ,MAAM,wBAAwB,KAAK;AAAA,MAC7C;AAAA,IACF,GAAG,OAAO,mBAAmB,KAAK,KAAK,GAAI;AAE3C,WAAO,MAAM,cAAc,QAAQ;AAAA,EACrC,GAAG,CAAC,MAAM,iBAAiB,OAAO,mBAAmB,OAAO,eAAe,CAAC;AAE5E,QAAM,YAAQ,2BAAY,OAAO,gBAAiD;AAChF,aAAS,WAAS,EAAE,GAAG,MAAM,WAAW,MAAM,OAAO,KAAK,EAAE;AAE5D,QAAI;AACF,YAAM,EAAE,MAAM,OAAO,IAAI,MAAM,WAAW,QAAQ,MAAM,WAAW;AAEnE,eAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AAED,aAAO;AAAA,IACT,SAAS,OAAO;AACd,YAAM,YAAY;AAClB,eAAS;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AACD,YAAM;AAAA,IACR;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,eAAW,2BAAY,OAAO,SAAsC;AACxE,aAAS,WAAS,EAAE,GAAG,MAAM,WAAW,MAAM,OAAO,KAAK,EAAE;AAE5D,QAAI;AACF,YAAM,EAAE,MAAM,OAAO,IAAI,MAAM,WAAW,QAAQ,SAAS,IAAI;AAE/D,eAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AAED,aAAO;AAAA,IACT,SAAS,OAAO;AACd,YAAM,YAAY;AAClB,eAAS;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AACD,YAAM;AAAA,IACR;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,aAAS,2BAAY,YAA2B;AACpD,aAAS,WAAS,EAAE,GAAG,MAAM,WAAW,KAAK,EAAE;AAE/C,QAAI;AACF,YAAM,WAAW,QAAQ,OAAO;AAAA,IAClC,SAAS,OAAO;AACd,cAAQ,MAAM,iBAAiB,KAAK;AAAA,IACtC,UAAE;AACA,eAAS;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,kBAAc,2BAAY,YAA2B;AACzD,QAAI;AACF,YAAM,SAAS,MAAM,WAAW,QAAQ,cAAc;AACtD,YAAM,OAAO,MAAM,WAAW,QAAQ,eAAe;AAErD,eAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AAAA,IACH,SAAS,OAAO;AACd,YAAM,YAAY;AAClB,eAAS;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AACD,YAAM;AAAA,IACR;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,iBAAa,2BAAY,OAAO,SAAuC;AAC3E,QAAI,CAAC,MAAM,MAAM;AACf,YAAM,IAAI,UAAU,yBAAyB,SAAS;AAAA,IACxD;AAEA,QAAI;AACF,YAAM,cAAc,MAAM,WAAW,QAAQ,cAAc,IAAI;AAC/D,eAAS,WAAS,EAAE,GAAG,MAAM,MAAM,YAAY,EAAE;AACjD,aAAO;AAAA,IACT,SAAS,OAAO;AACd,YAAM;AAAA,IACR;AAAA,EACF,GAAG,CAAC,MAAM,IAAI,CAAC;AAGf,QAAM,cAAU,2BAAY,CAAC,SAA0B;AACrD,WAAO,MAAM,MAAM,MAAM,SAAS,IAAI,KAAK;AAAA,EAC7C,GAAG,CAAC,MAAM,IAAI,CAAC;AAEf,QAAM,oBAAgB,2BAAY,CAAC,eAAgC;AACjE,WAAO,MAAM,MAAM,YAAY,SAAS,UAAU,KAAK;AAAA,EACzD,GAAG,CAAC,MAAM,IAAI,CAAC;AAEf,QAAM,iBAAa,2BAAY,CAAC,UAA6B;AAC3D,WAAO,MAAM,KAAK,UAAQ,QAAQ,IAAI,CAAC;AAAA,EACzC,GAAG,CAAC,OAAO,CAAC;AAEZ,QAAM,kBAAc,2BAAY,CAAC,UAA6B;AAC5D,WAAO,MAAM,MAAM,UAAQ,QAAQ,IAAI,CAAC;AAAA,EAC1C,GAAG,CAAC,OAAO,CAAC;AAEZ,QAAM,uBAAmB,2BAAY,CAAC,gBAAmC;AACvE,WAAO,YAAY,KAAK,gBAAc,cAAc,UAAU,CAAC;AAAA,EACjE,GAAG,CAAC,aAAa,CAAC;AAElB,QAAM,wBAAoB,2BAAY,CAAC,gBAAmC;AACxE,WAAO,YAAY,MAAM,gBAAc,cAAc,UAAU,CAAC;AAAA,EAClE,GAAG,CAAC,aAAa,CAAC;AAElB,QAAM,eAAiC;AAAA,IACrC,MAAM,MAAM;AAAA,IACZ,iBAAiB,MAAM;AAAA,IACvB,WAAW,MAAM;AAAA,IACjB,OAAO,MAAM;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SACE,8BAAAC,QAAA,cAAC,YAAY,UAAZ,EAAqB,OAAO,gBAC1B,QACH;AAEJ;;;AErQA,IAAAC,qBAA0B;AAYnB,SAAS,YAAY,OAAoC;AAC9D,MAAI;AACF,eAAO,8BAAwB,KAAK;AAAA,EACtC,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEO,SAAS,eAAe,OAAwB;AACrD,QAAM,UAAU,YAAY,KAAK;AACjC,MAAI,CAAC,QAAS,QAAO;AAErB,SAAO,QAAQ,MAAM,MAAO,KAAK,IAAI;AACvC;AAEO,SAAS,uBAAuB,OAA4B;AACjE,QAAM,UAAU,YAAY,KAAK;AACjC,MAAI,CAAC,QAAS,QAAO;AAErB,SAAO,IAAI,KAAK,QAAQ,MAAM,GAAI;AACpC;AAEO,SAAS,sBAAsB,OAAuB;AAC3D,QAAM,iBAAiB,uBAAuB,KAAK;AACnD,MAAI,CAAC,eAAgB,QAAO;AAE5B,QAAM,YAAY,eAAe,QAAQ,IAAI,KAAK,IAAI;AACtD,SAAO,KAAK,IAAI,GAAG,SAAS;AAC9B;AAGA,IAAM,iBAA2C;AAAA,EAC/C,eAAe,CAAC,SAAS,WAAW,MAAM;AAAA,EAC1C,SAAS,CAAC,WAAW,MAAM;AAAA,EAC3B,WAAW,CAAC,MAAM;AAAA,EAClB,QAAQ,CAAC;AACX;AAEO,SAAS,qBAAqB,WAAqB,cAA+B;AAEvF,MAAI,UAAU,SAAS,YAAY,GAAG;AACpC,WAAO;AAAA,EACT;AAGA,aAAW,YAAY,WAAW;AAChC,UAAM,iBAAiB,eAAe,QAAQ,KAAK,CAAC;AACpD,QAAI,eAAe,SAAS,YAAY,GAAG;AACzC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAGO,SAAS,qBACd,iBACA,SACS;AAET,QAAM,eAAe,QAAQ,QAAQ,OAAO,IAAI;AAChD,QAAM,QAAQ,IAAI,OAAO,IAAI,YAAY,GAAG;AAE5C,SAAO,gBAAgB,KAAK,gBAAc,MAAM,KAAK,UAAU,CAAC;AAClE;AAGO,SAAS,eAAe,KAAa,UAAmB,cAA6B;AAC1F,MAAI;AACF,WAAO,QAAQ,QAAQ,GAAG;AAAA,EAC5B,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEO,SAAS,eACd,KACA,OACA,UAAmB,cACV;AACT,MAAI;AACF,YAAQ,QAAQ,KAAK,KAAK;AAC1B,WAAO;AAAA,EACT,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEO,SAAS,kBAAkB,KAAa,UAAmB,cAAuB;AACvF,MAAI;AACF,YAAQ,WAAW,GAAG;AACtB,WAAO;AAAA,EACT,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAGO,IAAM,eAAN,MAAmB;AAAA,EAAnB;AACL,SAAQ,QAA+B;AACvC,SAAQ,eAAsC;AAAA;AAAA,EAE9C,MACE,gBACA,UACA,WACA,iBAAiB,GACjB;AACA,SAAK,KAAK;AAEV,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,eAAe,eAAe,QAAQ;AAC5C,UAAM,sBAAsB,eAAe;AAC3C,UAAM,mBAAmB,sBAAuB,iBAAiB,KAAK;AAEtE,QAAI,sBAAsB,GAAG;AAC3B,WAAK,QAAQ,WAAW,UAAU,mBAAmB;AAErD,UAAI,aAAa,mBAAmB,GAAG;AACrC,aAAK,eAAe,WAAW,WAAW,gBAAgB;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAO;AACL,QAAI,KAAK,OAAO;AACd,mBAAa,KAAK,KAAK;AACvB,WAAK,QAAQ;AAAA,IACf;AACA,QAAI,KAAK,cAAc;AACrB,mBAAa,KAAK,YAAY;AAC9B,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EAEA,iBAAiB,gBAA8B;AAC7C,UAAM,YAAY,eAAe,QAAQ,IAAI,KAAK,IAAI;AACtD,WAAO,KAAK,IAAI,GAAG,SAAS;AAAA,EAC9B;AACF;AAGO,SAAS,mBACd,aAAa,cACb,eACQ;AAER,MAAI,eAAe,MAAM;AACvB,WAAO,cAAc;AAAA,EACvB;AAGA,QAAM,SAAS,IAAI,gBAAgB,OAAO,SAAS,MAAM;AACzD,QAAM,gBAAgB,OAAO,IAAI,UAAU,KAAK,OAAO,IAAI,WAAW;AAEtE,MAAI,eAAe;AAEjB,QAAI;AACF,YAAM,MAAM,IAAI,IAAI,eAAe,OAAO,SAAS,MAAM;AACzD,UAAI,IAAI,WAAW,OAAO,SAAS,QAAQ;AACzC,eAAO,IAAI,WAAW,IAAI,SAAS,IAAI;AAAA,MACzC;AAAA,IACF,QAAQ;AAAA,IAER;AAAA,EACF;AAEA,SAAO;AACT;AAGO,IAAM,kBAAN,MAAsB;AAAA,EAI3B,YAAoB,cAA+D;AAA/D;AAHpB,SAAQ,UAAmC;AAC3C,SAAQ,aAAa;AAgBrB,SAAQ,sBAAsB,CAAC,UAAwB;AACrD,UAAI,MAAM,QAAQ,KAAK,cAAc,MAAM,UAAU;AACnD,cAAM,OAAO,KAAK,MAAM,MAAM,QAAQ;AACtC,aAAK,aAAa,KAAK,IAAI;AAAA,MAC7B;AAAA,IACF;AAlBE,QAAI,OAAO,WAAW,aAAa;AACjC,UAAI,sBAAsB,QAAQ;AAChC,aAAK,UAAU,IAAI,iBAAiB,WAAW;AAC/C,aAAK,QAAQ,YAAY,CAAC,UAAU;AAClC,eAAK,aAAa,MAAM,KAAK,IAAI;AAAA,QACnC;AAAA,MACF,OAAO;AAEL,QAAC,OAAe,iBAAiB,WAAW,KAAK,mBAAmB;AAAA,MACtE;AAAA,IACF;AAAA,EACF;AAAA,EASA,UAAU,MAAsC;AAC9C,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,YAAY,EAAE,KAAK,CAAC;AAAA,IACnC,OAAO;AAEL,mBAAa;AAAA,QACX,KAAK;AAAA,QACL,KAAK,UAAU,EAAE,MAAM,WAAW,KAAK,IAAI,EAAE,CAAC;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,MAAM;AAAA,IACrB,OAAO;AACL,aAAO,oBAAoB,WAAW,KAAK,mBAAmB;AAAA,IAChE;AAAA,EACF;AACF;;;ACpOA,IAAAC,gBAA0B;AAC1B,0BAAyC;AAMlC,SAAS,eAAe,SAI5B;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AACnB,QAAM,eAAW,iCAAY;AAC7B,QAAM,eAAW,iCAAY;AAE7B,+BAAU,MAAM;AACd,QAAI,UAAW;AAEf,UAAM,aAAa,SAAS,cAAc;AAE1C,QAAI,CAAC,iBAAiB;AACpB,eAAS,EAAE,IAAI,WAAW,CAAC;AAC3B;AAAA,IACF;AAGA,QAAI,SAAS,SAAS,CAAC,YAAY,QAAQ,KAAK,GAAG;AACjD,eAAS,EAAE,IAAI,gBAAgB,CAAC;AAChC;AAAA,IACF;AAGA,QAAI,SAAS,eAAe,CAAC,kBAAkB,QAAQ,WAAW,GAAG;AACnE,eAAS,EAAE,IAAI,gBAAgB,CAAC;AAChC;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,EAChB;AACF;AAGO,SAAS,2BAA2B,aAAa,cAAc;AACpE,QAAM,EAAE,iBAAiB,UAAU,IAAI,eAAe;AACtD,QAAM,eAAW,iCAAY;AAC7B,QAAM,eAAW,iCAAY;AAE7B,+BAAU,MAAM;AACd,QAAI,CAAC,aAAa,iBAAiB;AACjC,YAAM,OAAQ,SAAS,OAAe,QAAQ;AAC9C,eAAS,EAAE,IAAI,KAAK,CAAC;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,iBAAiB,WAAW,UAAU,UAAU,UAAU,CAAC;AACjE;AAGO,SAAS,iBAAiB;AAC/B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AAEnB,SAAO;AAAA,IACL;AAAA,IACA,OAAO,MAAM,SAAS,CAAC;AAAA,IACvB,aAAa,MAAM,eAAe,CAAC;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,QAAQ,OAAO;AAAA,IACxB,WAAW,WAAW,CAAC,SAAS,SAAS,CAAC;AAAA,IAC1C,QAAQ,QAAQ,MAAM;AAAA,EACxB;AACF;AAGO,SAAS,aACd,WACS;AACT,QAAM,EAAE,iBAAiB,UAAU,IAAI,eAAe;AAEtD,UAAQ,WAAW;AAAA,IACjB,KAAK;AACH,aAAO,mBAAmB,CAAC;AAAA,IAC7B,KAAK;AACH,aAAO,CAAC,mBAAmB,CAAC;AAAA,IAC9B,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAGO,SAAS,aAAa,SAAkC;AAC7D,QAAM,EAAE,MAAM,IAAI,eAAe;AAEjC,+BAAU,MAAM;AACd,QAAI,SAAS,SAAS;AACpB,cAAQ,KAAK;AAAA,IACf;AAAA,EACF,GAAG,CAAC,OAAO,OAAO,CAAC;AAEnB,SAAO;AACT;;;ACrIA,IAAAC,gBAAkB;AAClB,IAAAC,uBAAsC;AAW/B,IAAM,iBAAgD,CAAC;AAAA,EAC5D;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,WAAW,8BAAAC,QAAA,cAAC,aAAI,YAAU;AAC5B,MAAM;AACJ,QAAM,EAAE,iBAAiB,WAAW,aAAa,kBAAkB,IAAI,eAAQ;AAC/E,QAAM,eAAW,kCAAY;AAE7B,MAAI,WAAW;AACb,WAAO,8BAAAA,QAAA,4BAAAA,QAAA,gBAAG,QAAS;AAAA,EACrB;AAEA,MAAI,CAAC,iBAAiB;AACpB,WAAO,8BAAAA,QAAA,cAAC,iCAAS,IAAI,YAAY,SAAO,MAAC;AAAA,EAC3C;AAEA,MAAI,SAAS,CAAC,YAAY,KAAK,GAAG;AAChC,WAAO,8BAAAA,QAAA,cAAC,iCAAS,IAAG,iBAAgB,SAAO,MAAC;AAAA,EAC9C;AAEA,MAAI,eAAe,CAAC,kBAAkB,WAAW,GAAG;AAClD,WAAO,8BAAAA,QAAA,cAAC,iCAAS,IAAG,iBAAgB,SAAO,MAAC;AAAA,EAC9C;AAEA,SAAO,8BAAAA,QAAA,4BAAAA,QAAA,gBAAG,QAAS;AACrB;;;ACvCA,IAAAC,gBAAkB;AAWX,IAAM,iBAAgD,CAAC;AAAA,EAC5D;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,WAAW;AACb,MAAM;AACJ,QAAM,EAAE,aAAa,YAAY,mBAAmB,iBAAiB,IAAI,eAAe;AAExF,MAAI,YAAY;AAEhB,MAAI,OAAO;AACT,gBAAY,aAAa,YAAY,KAAK,IAAI,WAAW,KAAK;AAAA,EAChE;AAEA,MAAI,aAAa,aAAa;AAC5B,UAAM,WAAW,aAAa,kBAAkB,WAAW,IAAI,iBAAiB,WAAW;AAC3F,gBAAY,aAAa;AAAA,EAC3B;AAEA,SAAO,YAAY,8BAAAC,QAAA,4BAAAA,QAAA,gBAAG,QAAS,IAAM,8BAAAA,QAAA,4BAAAA,QAAA,gBAAG,QAAS;AACnD;;;AChCA,IAAAC,gBAA2C;AAmBpC,IAAM,mBAAoD,CAAC;AAAA,EAChE;AAAA,EACA;AAAA,EACA;AAAA,EACA,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,eAAe,CAAC;AAAA,EAChB;AACF,MAAM;AACJ,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAS,EAAE;AACrC,QAAM,CAAC,UAAU,WAAW,QAAI,wBAAS,EAAE;AAC3C,QAAM,CAAC,YAAY,aAAa,QAAI,wBAAS,KAAK;AAClD,QAAM,CAAC,cAAc,eAAe,QAAI,wBAAS,KAAK;AAEtD,QAAM,EAAE,OAAO,WAAW,MAAM,IAAI,eAAQ;AAG5C,6BAA2B,mBAAmB,CAAC;AAE/C,QAAM,eAAe,OAAO,MAAuB;AACjD,MAAE,eAAe;AAEjB,QAAI;AACF,YAAM,MAAM,EAAE,OAAO,UAAU,WAAW,CAAC;AAC3C,uBAAiB;AAAA,IACnB,SAAS,KAAK;AAAA,IAEd;AAAA,EACF;AAEA,QAAM,SAAS;AAAA,IACb,cAAc,aAAa,gBAAgB;AAAA,IAC3C,iBAAiB,aAAa,mBAAmB;AAAA,IACjD,gBAAgB,aAAa,kBAAkB;AAAA,EACjD;AAEA,SACE,8BAAAC,QAAA;AAAA,IAAC;AAAA;AAAA,MACC,WAAU;AAAA,MACV,OAAO,EAAE,iBAAiB,OAAO,gBAAgB;AAAA;AAAA,IAEjD,8BAAAA,QAAA,cAAC,SAAI,WAAU,+BACb,8BAAAA,QAAA,cAAC,SAAI,WAAU,iBACZ,WACC,8BAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACC,WAAU;AAAA,QACV,KAAK;AAAA,QACL,KAAK;AAAA;AAAA,IACP,GAEF,8BAAAA,QAAA,cAAC,QAAG,WAAU,2CAAwC,eACxC,OACd,GACC,kBACC,8BAAAA,QAAA,cAAC,OAAE,WAAU,gCACV,cACH,CAEJ,GAEA,8BAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACC,WAAU;AAAA,QACV,OAAO,EAAE,iBAAiB,OAAO,eAAe;AAAA;AAAA,MAEhD,8BAAAA,QAAA,cAAC,UAAK,WAAU,aAAY,UAAU,gBACnC,SACC,8BAAAA,QAAA,cAAC,SAAI,WAAU,6EACb,8BAAAA,QAAA,cAAC,UAAK,WAAU,qBAAmB,MAAM,OAAQ,CACnD,GAGF,8BAAAA,QAAA,cAAC,aACC,8BAAAA,QAAA,cAAC,WAAM,SAAQ,SAAQ,WAAU,6CAA0C,eAE3E,GACA,8BAAAA,QAAA;AAAA,QAAC;AAAA;AAAA,UACC,IAAG;AAAA,UACH,MAAK;AAAA,UACL,MAAK;AAAA,UACL,cAAa;AAAA,UACb,UAAQ;AAAA,UACR,OAAO;AAAA,UACP,UAAU,CAAC,MAAM,SAAS,EAAE,OAAO,KAAK;AAAA,UACxC,WAAU;AAAA,UACV,aAAY;AAAA;AAAA,MACd,CACF,GAEA,8BAAAA,QAAA,cAAC,aACC,8BAAAA,QAAA,cAAC,WAAM,SAAQ,YAAW,WAAU,6CAA0C,UAE9E,GACA,8BAAAA,QAAA,cAAC,SAAI,WAAU,mBACb,8BAAAA,QAAA;AAAA,QAAC;AAAA;AAAA,UACC,IAAG;AAAA,UACH,MAAK;AAAA,UACL,MAAM,eAAe,SAAS;AAAA,UAC9B,cAAa;AAAA,UACb,UAAQ;AAAA,UACR,OAAO;AAAA,UACP,UAAU,CAAC,MAAM,YAAY,EAAE,OAAO,KAAK;AAAA,UAC3C,WAAU;AAAA,UACR,aAAY;AAAA;AAAA,MAChB,GACA,8BAAAA,QAAA;AAAA,QAAC;AAAA;AAAA,UACC,MAAK;AAAA,UACL,SAAS,MAAM,gBAAgB,CAAC,YAAY;AAAA,UAC5C,WAAU;AAAA;AAAA,QAET,eACC,8BAAAA,QAAA,cAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,eACjE,8BAAAA,QAAA,cAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,oCAAmC,GACxG,8BAAAA,QAAA,cAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,2HAA0H,CACjM,IAEA,8BAAAA,QAAA,cAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,eACjE,8BAAAA,QAAA,cAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,4SAA2S,CAClX;AAAA,MAEJ,CACF,CACF,GAEA,8BAAAA,QAAA,cAAC,SAAI,WAAU,uCACb,8BAAAA,QAAA,cAAC,SAAI,WAAU,uBACb,8BAAAA,QAAA;AAAA,QAAC;AAAA;AAAA,UACC,IAAG;AAAA,UACH,MAAK;AAAA,UACL,MAAK;AAAA,UACL,SAAS;AAAA,UACT,UAAU,CAAC,MAAM,cAAc,EAAE,OAAO,OAAO;AAAA,UAC/C,WAAU;AAAA,UACV,OAAO,EAAE,OAAO,OAAO,aAAa;AAAA;AAAA,MACtC,GACA,8BAAAA,QAAA,cAAC,WAAM,SAAQ,eAAc,WAAU,sCAAmC,aAE1E,CACF,GAEA,8BAAAA,QAAA,cAAC,SAAI,WAAU,aACb,8BAAAA,QAAA;AAAA,QAAC;AAAA;AAAA,UACC,MAAM;AAAA,UACN,WAAU;AAAA,UACV,OAAO,EAAE,OAAO,OAAO,aAAa;AAAA;AAAA,QACrC;AAAA,MAED,CACF,CACF,GAEA,8BAAAA,QAAA,cAAC,aACC,8BAAAA,QAAA;AAAA,QAAC;AAAA;AAAA,UACC,MAAK;AAAA,UACL,UAAU;AAAA,UACV,WAAU;AAAA,UACV,OAAO;AAAA,YACL,iBAAiB,OAAO;AAAA,UAC1B;AAAA;AAAA,QAEC,YACC,8BAAAA,QAAA,4BAAAA,QAAA,gBACE,8BAAAA,QAAA,cAAC,SAAI,WAAU,8CAA6C,MAAK,QAAO,SAAQ,eAC9E,8BAAAA,QAAA,cAAC,YAAO,WAAU,cAAa,IAAG,MAAK,IAAG,MAAK,GAAE,MAAK,QAAO,gBAAe,aAAY,KAAI,GAC5F,8BAAAA,QAAA,cAAC,UAAK,WAAU,cAAa,MAAK,gBAAe,GAAE,mHAAkH,CACvK,GAAM,eAER,IAEA;AAAA,MAEJ,CACF,CACF;AAAA,MAEC,oBACC,8BAAAA,QAAA,cAAC,SAAI,WAAU,UACb,8BAAAA,QAAA,cAAC,SAAI,WAAU,cACb,8BAAAA,QAAA,cAAC,SAAI,WAAU,wCACb,8BAAAA,QAAA,cAAC,SAAI,WAAU,mCAAkC,CACnD,GACA,8BAAAA,QAAA,cAAC,SAAI,WAAU,0CACb,8BAAAA,QAAA,cAAC,UAAK,WAAU,iCAA8B,WAAQ,SAAQ,GAAC,CACjE,CACF,GAEA,8BAAAA,QAAA,cAAC,SAAI,WAAU,sBACb,8BAAAA,QAAA;AAAA,QAAC;AAAA;AAAA,UACC,MAAM;AAAA,UACN,WAAU;AAAA,UACV,OAAO,EAAE,OAAO,OAAO,aAAa;AAAA;AAAA,QACrC;AAAA,MAED,CACF,CACF;AAAA,MAIF,8BAAAA,QAAA,cAAC,SAAI,WAAU,oBACb,8BAAAA,QAAA,cAAC,SAAI,WAAU,cACb,8BAAAA,QAAA,cAAC,SAAI,WAAU,wCACb,8BAAAA,QAAA,cAAC,SAAI,WAAU,mCAAkC,CACnD,GACA,8BAAAA,QAAA,cAAC,SAAI,WAAU,0CACb,8BAAAA,QAAA,cAAC,UAAK,WAAU,iCAA8B,kBAAgB,CAChE,CACF,GAEA,8BAAAA,QAAA;AAAA,QAAC;AAAA;AAAA,UACC,MAAK;AAAA,UACL,WAAU;AAAA,UACV,SAAS,MAAM,OAAO,SAAS,OAAO;AAAA;AAAA,QAEtC,8BAAAA,QAAA,cAAC,SAAI,WAAU,gBAAe,SAAQ,eACpC,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,oDAAkD,GACzE,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,0BAAwB,GAC/C,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,gCAA8B,CACvD;AAAA,QAAM;AAAA,MAER,GAEA,8BAAAA,QAAA;AAAA,QAAC;AAAA;AAAA,UACC,MAAK;AAAA,UACL,WAAU;AAAA,UACV,SAAS,MAAM,OAAO,SAAS,OAAO;AAAA;AAAA,QAEtC,8BAAAA,QAAA,cAAC,SAAI,WAAU,gBAAe,SAAQ,eACpC,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,2HAAyH,GAChJ,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,yIAAuI,GAC9J,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,iIAA+H,GACtJ,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,uIAAqI,CAC9J;AAAA,QAAM;AAAA,MAER,CACF;AAAA,IACF,GAEA,8BAAAA,QAAA,cAAC,OAAE,WAAU,4CAAyC,mCACpB,KAChC,8BAAAA,QAAA,cAAC,OAAE,MAAK,UAAS,WAAU,mCAAgC,kBAAgB,GAC1E,KAAI,OAAI,KACT,8BAAAA,QAAA,cAAC,OAAE,MAAK,YAAW,WAAU,mCAAgC,gBAAc,CAC7E,CACF;AAAA,EACF;AAEJ;;;AC1QA,IAAAC,gBAAgC;AAqBzB,IAAM,qBAAwD,CAAC;AAAA,EACpE;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,QAAQ;AAAA,EACR;AACF,MAAM;AACJ,QAAM,CAAC,QAAQ,SAAS,QAAI,wBAAS,KAAK;AAC1C,QAAM,EAAE,MAAM,YAAY,iBAAiB,IAAI,eAAQ;AAGvD,QAAM,iBAAiB,KAAK,OAAO,SAAO;AACxC,QAAI,IAAI,SAAS,CAAC,WAAW,IAAI,KAAK,GAAG;AACvC,aAAO;AAAA,IACT;AACA,QAAI,IAAI,eAAe,CAAC,iBAAiB,IAAI,WAAW,GAAG;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,CAAC;AAED,QAAM,iBAAiB,CAAC,QAAiB;AACvC,QAAI,IAAI,OAAO,YAAY;AACzB,gBAAU,KAAK;AACf;AAAA,IACF;AAEA,kBAAc,GAAG;AAGjB,QAAI,IAAI,IAAI,WAAW,MAAM,GAAG;AAE9B,aAAO,SAAS,OAAO,IAAI;AAAA,IAC7B,OAAO;AAEL,aAAO,SAAS,OAAO,IAAI;AAAA,IAC7B;AAAA,EACF;AAEA,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,WAAW;AAAA,IACb;AAAA,IACA,MAAM;AAAA,MACJ,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,WAAW;AAAA,IACb;AAAA,EACF,EAAE,KAAK;AAEP,MAAI,aAAa,OAAO;AACtB,WACE,8BAAAC,QAAA,cAAC,SAAI,WAAW,GAAG,aAAa,UAAU,aAAa,aAAa,MAAM,MACxE,8BAAAA,QAAA,cAAC,SAAI,WAAU,4CACb,8BAAAA,QAAA,cAAC,SAAI,WAAU,4CACb,8BAAAA,QAAA,cAAC,SAAI,WAAU,iCACb,8BAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACC,SAAS,MAAM,UAAU,CAAC,MAAM;AAAA,QAChC,WAAW,oDAAoD,aAAa,KAAK,IAAI,aAAa,IAAI;AAAA;AAAA,MAEtG,8BAAAA,QAAA,cAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,eACjE,8BAAAA,QAAA,cAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,2BAA0B,CACjG;AAAA,MACA,8BAAAA,QAAA,cAAC,UAAK,WAAU,iBAAc,MAAI;AAAA,IACpC,GAEA,8BAAAA,QAAA,cAAC,UAAK,WAAW,WAAW,aAAa,IAAI,MAAI,aACtC,8BAAAA,QAAA,cAAC,UAAK,WAAU,iBAAe,UAAW,CACrD,CACF,GAEA,8BAAAA,QAAA,cAAC,SAAI,WAAU,iCACb,8BAAAA,QAAA,cAAC,UAAK,WAAW,WAAW,aAAa,IAAI,MAC1C,MAAM,WAAU,KAAE,MAAM,QAC3B,CACF,CACF,CACF,GAGC,UACC,8BAAAA,QAAA,cAAC,SAAI,WAAU,kDACb,8BAAAA,QAAA,cAAC,SAAI,WAAW,cAAc,aAAa,UAAU,uCACnD,8BAAAA,QAAA,cAAC,SAAI,WAAU,UACZ,eAAe,IAAI,SAClB,8BAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACC,KAAK,IAAI;AAAA,QACT,SAAS,MAAM,eAAe,GAAG;AAAA,QACjC,WAAW;AAAA;AAAA,wBAEP,IAAI,OAAO,aAAa,aAAa,YAAY,aAAa,IAAI;AAAA,wBAClE,aAAa,KAAK;AAAA;AAAA;AAAA,MAGtB,8BAAAA,QAAA,cAAC,SAAI,WAAU,uBACZ,IAAI,QAAQ,8BAAAA,QAAA,cAAC,SAAI,WAAU,UAAQ,IAAI,IAAK,GAC7C,8BAAAA,QAAA,cAAC,aACC,8BAAAA,QAAA,cAAC,SAAI,WAAU,iBAAe,IAAI,IAAK,GACtC,IAAI,eACH,8BAAAA,QAAA,cAAC,SAAI,WAAU,wBAAsB,IAAI,WAAY,CAEzD,CACF;AAAA,IACF,CACD,CACH,CACF,CACF,CAEJ;AAAA,EAEJ;AAGA,QAAM,cAAc,aAAa,SAAS,WAAW;AAErD,SACE,8BAAAA,QAAA,4BAAAA,QAAA,gBAEE,8BAAAA,QAAA;AAAA,IAAC;AAAA;AAAA,MACC,SAAS,MAAM,UAAU,CAAC,MAAM;AAAA,MAChC,WAAW;AAAA,kBACD,QAAQ;AAAA,YACd,aAAa,UAAU,IAAI,aAAa,IAAI;AAAA,qCACnB,aAAa,KAAK;AAAA;AAAA;AAAA,IAG/C,8BAAAA,QAAA,cAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,eACjE,8BAAAA,QAAA,cAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,0BAAyB,CAChG;AAAA,EACF,GAGC,UACC,8BAAAA,QAAA,4BAAAA,QAAA,gBAEE,8BAAAA,QAAA;AAAA,IAAC;AAAA;AAAA,MACC,WAAU;AAAA,MACV,SAAS,MAAM,UAAU,KAAK;AAAA;AAAA,EAChC,GAGA,8BAAAA,QAAA,cAAC,SAAI,WAAW;AAAA,oBACN,WAAW;AAAA,cACjB,aAAa,UAAU;AAAA;AAAA,cAEvB,SAAS,kBAAkB,aAAa,SAAS,sBAAsB,kBAAkB;AAAA,eAE3F,8BAAAA,QAAA,cAAC,SAAI,WAAU,SACb,8BAAAA,QAAA,cAAC,SAAI,WAAU,4CACb,8BAAAA,QAAA,cAAC,QAAG,WAAW,yBAAyB,aAAa,IAAI,MAAI,cAE7D,GACA,8BAAAA,QAAA;AAAA,IAAC;AAAA;AAAA,MACC,SAAS,MAAM,UAAU,KAAK;AAAA,MAC9B,WAAW,GAAG,aAAa,IAAI,IAAI,aAAa,KAAK;AAAA;AAAA,IAErD,8BAAAA,QAAA,cAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,eACjE,8BAAAA,QAAA,cAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,wBAAuB,CAC9F;AAAA,EACF,CACF,GAEA,8BAAAA,QAAA,cAAC,SAAI,WAAU,eACZ,eAAe,IAAI,SAClB,8BAAAA,QAAA;AAAA,IAAC;AAAA;AAAA,MACC,KAAK,IAAI;AAAA,MACT,SAAS,MAAM,eAAe,GAAG;AAAA,MACjC,WAAW;AAAA;AAAA,wBAEP,IAAI,OAAO,aAAa,aAAa,YAAY,aAAa,IAAI;AAAA,wBAClE,aAAa,KAAK;AAAA;AAAA;AAAA,IAGtB,8BAAAA,QAAA,cAAC,SAAI,WAAU,uBACZ,IAAI,QAAQ,8BAAAA,QAAA,cAAC,SAAI,WAAU,UAAQ,IAAI,IAAK,GAC7C,8BAAAA,QAAA,cAAC,aACC,8BAAAA,QAAA,cAAC,SAAI,WAAU,iBAAe,IAAI,IAAK,GACtC,IAAI,eACH,8BAAAA,QAAA,cAAC,SAAI,WAAU,wBAAsB,IAAI,WAAY,CAEzD,CACF;AAAA,EACF,CACD,CACH,GAEA,8BAAAA,QAAA,cAAC,SAAI,WAAW,sBAAsB,aAAa,MAAM,MACvD,8BAAAA,QAAA,cAAC,SAAI,WAAW,WAAW,aAAa,IAAI,MAC1C,8BAAAA,QAAA,cAAC,SAAI,WAAU,iBAAe,MAAM,WAAU,KAAE,MAAM,QAAS,GAC/D,8BAAAA,QAAA,cAAC,SAAI,WAAU,wBAAsB,MAAM,KAAM,GACjD,8BAAAA,QAAA,cAAC,SAAI,WAAU,UACb,8BAAAA,QAAA,cAAC,UAAK,WAAU,4DACb,MAAM,MAAM,CAAC,CAChB,CACF,CACF,CACF,CACF,CACF,CACF,CAEJ;AAEJ;;;ACvOA,IAAAC,gBAAkB;;;ACAlB,IAAAC,gBAAqC;AAgC9B,IAAM,cAAN,MAAkB;AAAA,EAMvB,YAAY,YAAwB,aAA0B;AAF9D,SAAQ,WAAW;AAGjB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,MAAM,cAAAC,QAAM,OAAO;AAAA,MACtB,SAAS,WAAW;AAAA,MACpB,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc,UAAiC;AACnD,UAAM,iBAAiB,KAAK,OAAO,UAAU,QAAQ;AACrD,QAAI,CAAC,gBAAgB;AACnB,YAAM,IAAI,UAAU,kBAAkB,QAAQ,mBAAmB,yBAAyB;AAAA,IAC5F;AAGA,UAAM,QAAQ,KAAK,cAAc;AAGjC,UAAM,eAAe,KAAK,qBAAqB;AAC/C,UAAM,gBAAgB,MAAM,KAAK,sBAAsB,YAAY;AAGnE,UAAM,aAAyB;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,KAAK,IAAI;AAAA,IACtB;AAEA,iBAAa,QAAQ,KAAK,UAAU,KAAK,UAAU,UAAU,CAAC;AAG9D,UAAM,SAAS,IAAI,gBAAgB;AAAA,MACjC,WAAW,eAAe;AAAA,MAC1B,cAAc,eAAe;AAAA,MAC7B,eAAe;AAAA,MACf,OAAO,eAAe;AAAA,MACtB;AAAA,MACA,aAAa;AAAA;AAAA,MACb,QAAQ;AAAA,IACV,CAAC;AAGD,QAAI,aAAa,YAAY,aAAa,UAAU;AAClD,aAAO,OAAO,kBAAkB,aAAa;AAC7C,aAAO,OAAO,yBAAyB,MAAM;AAAA,IAC/C;AAGA,WAAO,SAAS,OAAO,GAAG,eAAe,OAAO,IAAI,OAAO,SAAS,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,MAAc,OAA4D;AAE7F,UAAM,iBAAiB,aAAa,QAAQ,KAAK,QAAQ;AACzD,QAAI,CAAC,gBAAgB;AACnB,YAAM,IAAI,UAAU,wBAAwB,eAAe;AAAA,IAC7D;AAEA,UAAM,cAA0B,KAAK,MAAM,cAAc;AAGzD,iBAAa,WAAW,KAAK,QAAQ;AAGrC,QAAI,YAAY,UAAU,OAAO;AAC/B,YAAM,IAAI,UAAU,uBAAuB,eAAe;AAAA,IAC5D;AAEA,QAAI,KAAK,IAAI,IAAI,YAAY,YAAY,IAAI,KAAK,KAAM;AACtD,YAAM,IAAI,UAAU,uBAAuB,eAAe;AAAA,IAC5D;AAEA,UAAM,iBAAiB,KAAK,OAAO,UAAU,YAAY,QAAQ;AACjE,QAAI,CAAC,gBAAgB;AACnB,YAAM,IAAI,UAAU,iCAAiC,yBAAyB;AAAA,IAChF;AAEA,QAAI;AAEF,YAAM,gBAAgB,MAAM,KAAK;AAAA,QAC/B;AAAA,QACA,YAAY;AAAA,QACZ;AAAA,QACA,YAAY;AAAA,MACd;AAGA,YAAM,WAAW,MAAM,KAAK;AAAA,QAC1B,cAAc;AAAA,QACd;AAAA,MACF;AAGA,YAAM,WAAW,MAAM,KAAK,IAAI,KAAK,GAAG,KAAK,WAAW,YAAY,mBAAmB;AAAA,QACrF,UAAU,YAAY;AAAA,QACtB,aAAa,cAAc;AAAA,QAC3B,cAAc,cAAc;AAAA,QAC5B,SAAS,cAAc;AAAA,QACvB;AAAA,QACA,WAAW,cAAc;AAAA,MAC3B,CAAC;AAED,YAAM,EAAE,MAAM,aAAa,cAAc,UAAU,IAAI,SAAS;AAEhE,YAAM,SAAqB;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb;AAGA,WAAK,OAAO,YAAY,MAAM,MAAM;AAEpC,aAAO,EAAE,MAAM,OAAO;AAAA,IACxB,SAAS,OAAY;AACnB,YAAM,YAAY,IAAI;AAAA,QACpB,MAAM,UAAU,MAAM,WAAW;AAAA,QACjC,MAAM,UAAU,MAAM,QAAQ;AAAA,QAC9B,MAAM,UAAU;AAAA,MAClB;AAEA,WAAK,OAAO,UAAU,SAAS;AAC/B,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,sBACZ,MACA,UACA,gBACA,cACc;AACd,UAAM,SAAc;AAAA,MAClB,YAAY;AAAA,MACZ;AAAA,MACA,WAAW,eAAe;AAAA,MAC1B,cAAc,eAAe;AAAA,IAC/B;AAGA,QAAI,eAAe,cAAc;AAC/B,aAAO,gBAAgB,eAAe;AAAA,IACxC;AAGA,QAAI,iBAAiB,aAAa,YAAY,aAAa,WAAW;AACpE,aAAO,gBAAgB;AAAA,IACzB;AAEA,UAAM,WAAW,MAAM,cAAAA,QAAM,KAAK,eAAe,UAAU,QAAQ;AAAA,MACjE,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAED,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,YAAY,aAAqB,gBAA6C;AAC1F,UAAM,WAAW,MAAM,cAAAA,QAAM,IAAI,eAAe,aAAa;AAAA,MAC3D,SAAS;AAAA,QACP,iBAAiB,UAAU,WAAW;AAAA,MACxC;AAAA,IACF,CAAC;AAED,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,YAAY,UAAkB,QAA+B;AAEjE,UAAM,eAAe;AAAA,MACnB;AAAA,MACA,QAAQ;AAAA,MACR,WAAW,KAAK,IAAI;AAAA,IACtB;AAEA,iBAAa,QAAQ,iBAAiB,KAAK,UAAU,YAAY,CAAC;AAGlE,UAAM,KAAK,cAAc,QAAQ;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc,UAAkB,QAA+B;AACnE,UAAM,KAAK,IAAI,KAAK,GAAG,KAAK,WAAW,YAAY,iBAAiB;AAAA,MAClE;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,kBAAkB,QAAmC;AACzD,UAAM,WAAW,MAAM,KAAK,IAAI,IAAI,GAAG,KAAK,WAAW,YAAY,iBAAiB,MAAM,EAAE;AAC5F,WAAO,SAAS,KAAK;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,mBAAmB,UAAkB,cAA2C;AACpF,UAAM,iBAAiB,KAAK,OAAO,UAAU,QAAQ;AACrD,QAAI,CAAC,gBAAgB;AACnB,YAAM,IAAI,UAAU,iCAAiC,yBAAyB;AAAA,IAChF;AAEA,UAAM,SAAc;AAAA,MAClB,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,WAAW,eAAe;AAAA,IAC5B;AAEA,QAAI,eAAe,cAAc;AAC/B,aAAO,gBAAgB,eAAe;AAAA,IACxC;AAEA,UAAM,WAAW,MAAM,cAAAA,QAAM,KAAK,eAAe,UAAU,QAAQ;AAAA,MACjE,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAED,WAAO;AAAA,MACL,aAAa,SAAS,KAAK;AAAA,MAC3B,cAAc,SAAS,KAAK,iBAAiB;AAAA,MAC7C,WAAW,SAAS,KAAK;AAAA,MACzB,WAAW;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKQ,gBAAwB;AAC9B,UAAM,QAAQ,IAAI,WAAW,EAAE;AAC/B,WAAO,gBAAgB,KAAK;AAC5B,WAAO,MAAM,KAAK,OAAO,UAAQ,KAAK,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA,EAKQ,uBAA+B;AACrC,UAAM,QAAQ,IAAI,WAAW,EAAE;AAC/B,WAAO,gBAAgB,KAAK;AAC5B,WAAO,KAAK,gBAAgB,KAAK;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,sBAAsB,UAAmC;AACrE,UAAM,UAAU,IAAI,YAAY;AAChC,UAAM,OAAO,QAAQ,OAAO,QAAQ;AACpC,UAAM,SAAS,MAAM,OAAO,OAAO,OAAO,WAAW,IAAI;AACzD,WAAO,KAAK,gBAAgB,IAAI,WAAW,MAAM,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAKQ,gBAAgB,QAA4B;AAClD,UAAM,SAAS,KAAK,OAAO,aAAa,GAAG,MAAM,CAAC;AAClD,WAAO,OACJ,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,GAAG,EAClB,QAAQ,MAAM,EAAE;AAAA,EACrB;AACF;AAGO,IAAM,wBAA2D;AAAA,EACtE,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,UAAU,QAAQ,IAAI,yBAAyB;AAAA,IAC/C,cAAc,QAAQ,IAAI;AAAA,IAC1B,OAAO;AAAA,IACP,aAAa,GAAG,OAAO,SAAS,MAAM;AAAA,EACxC;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,UAAU,QAAQ,IAAI,yBAAyB;AAAA,IAC/C,cAAc,QAAQ,IAAI;AAAA,IAC1B,OAAO;AAAA,IACP,aAAa,GAAG,OAAO,SAAS,MAAM;AAAA,EACxC;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,UAAU,QAAQ,IAAI,4BAA4B;AAAA,IAClD,cAAc,QAAQ,IAAI;AAAA,IAC1B,OAAO;AAAA,IACP,aAAa,GAAG,OAAO,SAAS,MAAM;AAAA,EACxC;AACF;;;AD9VA,IAAM,gBAAgB;AAAA,EACpB,QACE,8BAAAC,QAAA,cAAC,SAAI,WAAU,gBAAe,SAAQ,eACpC,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,2HAAyH,GAChJ,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,yIAAuI,GAC9J,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,iIAA+H,GACtJ,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,uIAAqI,CAC9J;AAAA,EAEF,QACE,8BAAAA,QAAA,cAAC,SAAI,WAAU,gBAAe,MAAK,gBAAe,SAAQ,eACxD,8BAAAA,QAAA,cAAC,UAAK,GAAE,6sBAA2sB,CACrtB;AAAA,EAEF,WACE,8BAAAA,QAAA,cAAC,SAAI,WAAU,gBAAe,SAAQ,eACpC,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,oDAAkD,GACzE,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,0BAAwB,GAC/C,8BAAAA,QAAA,cAAC,UAAK,MAAK,WAAU,GAAE,gCAA8B,CACvD;AAEJ;AAEA,IAAM,gBAAgB;AAAA,EACpB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,WAAW;AACb;AAEO,IAAM,eAA4C,CAAC;AAAA,EACxD,YAAY,CAAC,UAAU,UAAU,WAAW;AAAA,EAC5C;AAAA,EACA,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAChB,MAAM;AACJ,QAAM,mBAAmB,OAAO,aAAqB;AACnD,QAAI;AACF,YAAM,cAAc,IAAI,YAAY,YAAY;AAAA,QAC9C,WAAW;AAAA,MACb,CAAC;AAED,YAAM,YAAY,cAAc,QAAQ;AAAA,IAC1C,SAAS,OAAO;AACd,cAAQ,MAAM,0BAA0B,QAAQ,KAAK,KAAK;AAAA,IAC5D;AAAA,EACF;AAEA,SACE,8BAAAA,QAAA,cAAC,SAAI,aACF,eACC,8BAAAA,QAAA,cAAC,SAAI,WAAU,mBACb,8BAAAA,QAAA,cAAC,SAAI,WAAU,wCACb,8BAAAA,QAAA,cAAC,SAAI,WAAU,mCAAkC,CACnD,GACA,8BAAAA,QAAA,cAAC,SAAI,WAAU,0CACb,8BAAAA,QAAA,cAAC,UAAK,WAAU,iCAA+B,WAAY,CAC7D,CACF,GAGF,8BAAAA,QAAA,cAAC,SAAI,WAAU,eACZ,UAAU,IAAI,CAAC,aACd,8BAAAA,QAAA;AAAA,IAAC;AAAA;AAAA,MACC,KAAK;AAAA,MACL,MAAK;AAAA,MACL,SAAS,MAAM,iBAAiB,QAAQ;AAAA,MACxC,WAAW,oOAAoO,eAAe;AAAA;AAAA,IAE7P,cAAc,QAAQ;AAAA,IAAE;AAAA,IACX,cAAc,QAAQ;AAAA,EACtC,CACD,CACH,CACF;AAEJ;;;AE1FA,IAAAC,gBAAqC;AAsB9B,IAAM,qBAAN,MAAyB;AAAA,EAK9B,YAAY,YAAwB;AAFpC,SAAQ,iBAA8C,oBAAI,IAAI;AAG5D,SAAK,aAAa;AAClB,SAAK,MAAM,cAAAC,QAAM,OAAO;AAAA,MACtB,SAAS,WAAW,QAAQ,EAAE;AAAA,MAC9B,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAED,SAAK,kBAAkB;AAAA,EACzB;AAAA,EAEQ,oBAAoB;AAC1B,SAAK,IAAI,aAAa,QAAQ;AAAA,MAC5B,CAAC,WAAW;AACV,cAAM,QAAQ,KAAK,WAAW,gBAAgB,GAAG;AACjD,YAAI,SAAS,CAAC,OAAO,QAAQ,UAAU,GAAG;AACxC,iBAAO,QAAQ,eAAe,IAAI,UAAU,KAAK;AAAA,QACnD;AACA,eAAO;AAAA,MACT;AAAA,MACA,CAAC,UAAU,QAAQ,OAAO,KAAK;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,sBAAsB,SAAmD;AAC7E,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,IAAI,KAAK,kCAAkC,OAAO;AAE9E,YAAM,EAAE,OAAO,WAAW,aAAa,UAAU,IAAI,SAAS,KAAK;AAEnE,YAAM,iBAAiC;AAAA,QACrC,aAAa;AAAA,QACb,cAAc;AAAA;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb;AAGA,WAAK,eAAe,IAAI,WAAW,cAAc;AAEjD,aAAO;AAAA,IACT,SAAS,OAAY;AACnB,YAAM,IAAI;AAAA,QACR,MAAM,UAAU,MAAM,WAAW;AAAA,QACjC;AAAA,QACA,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBAAiB,OAAe,aAAyC;AAC7E,UAAM,SAAS,KAAK,eAAe,IAAI,KAAK;AAG5C,QAAI,UAAU,CAAC,KAAK,WAAW,eAAe,OAAO,WAAW,GAAG;AACjE,aAAO,OAAO;AAAA,IAChB;AAGA,UAAM,YAAY,MAAM,KAAK,sBAAsB;AAAA,MACjD,aAAa;AAAA,MACb;AAAA,IACF,CAAC;AAED,WAAO,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,YAAY,OAAe,OAW9B;AACD,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,IAAI,KAAK,gCAAgC;AAAA,QACnE;AAAA,QACA;AAAA,MACF,GAAG;AAAA,QACD,SAAS,EAAE,UAAU,KAAK;AAAA,MAC5B,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB,SAAS,OAAY;AACnB,aAAO;AAAA,QACL,OAAO;AAAA,QACP,OAAO,MAAM,UAAU,MAAM,WAAW;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,qBAAqB,OAAwC;AACjE,QAAI;AACF,YAAM,SAAS,KAAK,eAAe,IAAI,KAAK;AAC5C,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,4BAA4B;AAAA,MAC9C;AAEA,YAAM,WAAW,MAAM,KAAK,IAAI,KAAK,iCAAiC;AAAA,QACpE,OAAO,OAAO;AAAA,QACd;AAAA,MACF,CAAC;AAED,YAAM,EAAE,MAAM,IAAI,SAAS,KAAK;AAEhC,YAAM,kBAAkC;AAAA,QACtC,GAAG;AAAA,QACH,aAAa;AAAA,MACf;AAEA,WAAK,eAAe,IAAI,OAAO,eAAe;AAE9C,aAAO;AAAA,IACT,SAAS,OAAY;AAEnB,WAAK,eAAe,OAAO,KAAK;AAChC,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,mBAA8C;AAClD,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,IAAI,IAAI,+BAA+B;AAAA,QACjE,SAAS,EAAE,UAAU,KAAK;AAAA,MAC5B,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB,SAAS,OAAY;AACnB,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,yBAAyB,OAAkC;AAC/D,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,IAAI,IAAI,+BAA+B,KAAK,EAAE;AAE1E,aAAO,SAAS,KAAK,KAAK;AAAA,IAC5B,SAAS,OAAY;AACnB,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,oBAAmC;AACvC,QAAI;AAEF,YAAM,WAAW,MAAM,KAAK,IAAI,KAAK,4BAA4B;AACjE,YAAM,EAAE,aAAa,IAAI,SAAS,KAAK;AAGvC,YAAM,iBAAiB,OAAO,QAAQ,YAAY,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,MAAM;AAChF,YAAI;AACF,gBAAM,YAAY,MAAM,KAAK,aAAa,KAAK;AAC/C,cAAI,WAAW;AACb,kBAAM,KAAK,gBAAgB,WAAW,KAAe;AAAA,UACvD;AAAA,QACF,SAAS,OAAO;AACd,kBAAQ,KAAK,oBAAoB,KAAK,kBAAkB,KAAK;AAAA,QAC/D;AAAA,MACF,CAAC;AAED,YAAM,QAAQ,WAAW,cAAc;AAGvC,WAAK,eAAe,MAAM;AAG1B,YAAM,KAAK,WAAW,OAAO;AAAA,IAC/B,SAAS,OAAY;AACnB,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cACJ,OACA,eAAuB,KACvB,aACiB;AACjB,QAAI;AAEF,YAAM,QAAQ,MAAM,KAAK,iBAAiB,OAAO,WAAW;AAG5D,YAAM,YAAY,MAAM,KAAK,aAAa,KAAK;AAC/C,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,OAAO,KAAK,YAAY;AAAA,MAC1C;AAGA,YAAM,UAAU,UAAU,eAAe,CAAC;AAC1C,YAAM,SAAS,IAAI,IAAI,aAAa,OAAO;AAC3C,aAAO,aAAa,IAAI,SAAS,KAAK;AACtC,aAAO,aAAa,IAAI,YAAY,YAAY;AAEhD,aAAO,OAAO,SAAS;AAAA,IACzB,SAAS,OAAY;AACnB,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,MAAM,UAAU;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,OAAe,QAAyB;AACtD,UAAM,SAAS,KAAK,eAAe,IAAI,KAAK;AAE5C,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,OAAqB;AACjC,SAAK,eAAe,OAAO,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,oBAA0B;AACxB,SAAK,eAAe,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,OAAsC;AACvD,WAAO,KAAK,eAAe,IAAI,KAAK,KAAK;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBAAiB,OAAe,YAAsC;AAC1E,QAAI;AACF,YAAM,cAAc,MAAM,KAAK,yBAAyB,KAAK;AAC7D,aAAO,YAAY,SAAS,UAAU;AAAA,IACxC,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,MAAc,aAAa,OAA+C;AACxE,QAAI;AACF,YAAM,OAAO,MAAM,KAAK,iBAAiB;AACzC,aAAO,KAAK,KAAK,SAAO,IAAI,UAAU,KAAK,KAAK;AAAA,IAClD,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,MAAc,gBAAgB,WAA2B,aAAoC;AAC3F,QAAI;AACF,YAAM,YAAY,IAAI,IAAI,uBAAuB,UAAU,eAAe,CAAC,CAAC;AAE5E,YAAM,cAAAA,QAAM,KAAK,UAAU,SAAS,GAAG;AAAA,QACrC,OAAO;AAAA,QACP,OAAO,UAAU;AAAA,MACnB,GAAG;AAAA,QACD,SAAS;AAAA;AAAA,QACT,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH,SAAS,OAAO;AAEd,cAAQ,KAAK,oBAAoB,UAAU,KAAK,kBAAkB,KAAK;AAAA,IACzE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,kBAA0B,GAAS;AAClD,gBAAY,YAAY;AACtB,iBAAW,CAAC,OAAO,MAAM,KAAK,KAAK,gBAAgB;AACjD,YAAI;AAEF,gBAAM,YAAY,OAAO,aAAa;AACtC,gBAAM,YAAY,KAAK,IAAI,IAAK,YAAY;AAC5C,gBAAM,qBAAqB,KAAK,IAAI,IAAK,IAAI,KAAK;AAElD,cAAI,aAAa,oBAAoB;AACnC,kBAAM,KAAK,qBAAqB,KAAK;AAAA,UACvC;AAAA,QACF,SAAS,OAAO;AACd,kBAAQ,KAAK,oCAAoC,KAAK,KAAK,KAAK;AAChE,eAAK,eAAe,OAAO,KAAK;AAAA,QAClC;AAAA,MACF;AAAA,IACF,GAAG,kBAAkB,KAAK,GAAI;AAAA,EAChC;AACF;AAGO,IAAM,eAAe,CAAC,YAAoB,OAAe,eAAuB,QAAgB;AACrG,QAAM,MAAM,IAAI,IAAI,aAAa,UAAU;AAC3C,MAAI,aAAa,IAAI,SAAS,KAAK;AACnC,MAAI,aAAa,IAAI,YAAY,YAAY;AAC7C,SAAO,IAAI,SAAS;AACtB;AAEO,IAAM,kBAAkB,MAAqB;AAClD,MAAI,OAAO,WAAW,YAAa,QAAO;AAE1C,QAAM,SAAS,IAAI,gBAAgB,OAAO,SAAS,MAAM;AACzD,SAAO,OAAO,IAAI,OAAO;AAC3B;AAEO,IAAM,qBAAqB,MAAc;AAC9C,MAAI,OAAO,WAAW,YAAa,QAAO;AAE1C,QAAM,SAAS,IAAI,gBAAgB,OAAO,SAAS,MAAM;AACzD,SAAO,OAAO,IAAI,UAAU,KAAK;AACnC;;;ACvXO,IAAM,aAAN,MAAiB;AAAA,EAItB,YAAY,QAAoB,OAAe;AAC7C,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAiC;AAC/B,QAAI,OAAO,WAAW,YAAa,QAAO;AAE1C,UAAM,gBAAgB,OAAO,SAAS;AACtC,WAAO,KAAK,gBAAgB,aAAa;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,QAAyB;AAEvC,UAAM,iBAAiB,KAAK,kBAAkB;AAG9C,QAAI,eAAe,SAAS,MAAM,GAAG;AACnC,aAAO;AAAA,IACT;AAGA,QAAI,KAAK,cAAc,GAAG;AACxB,YAAM,mBAAmB;AACzB,UAAI,iBAAiB,KAAK,MAAM,GAAG;AACjC,eAAO,eAAe;AAAA,UAAK,aACzB,QAAQ,SAAS,WAAW,KAAK,QAAQ,SAAS,WAAW;AAAA,QAC/D;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,cAAoC;AACjD,UAAM,SAAS,gBAAgB,KAAK,iBAAiB;AAErD,QAAI,CAAC,KAAK,gBAAgB,MAAM,GAAG;AACjC,aAAO,CAAC;AAAA,IACV;AAEA,WAAO;AAAA,MACL,+BAA+B;AAAA,MAC/B,gCAAgC;AAAA,MAChC,gCAAgC,KAAK,kBAAkB,EAAE,KAAK,IAAI;AAAA,MAClE,iCAAiC,KAAK,kBAAkB,EAAE,KAAK,IAAI;AAAA,MACnE,oCAAoC;AAAA,MACpC,0BAA0B;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,oBACJ,QACA,QACA,SAC+B;AAC/B,QAAI;AACF,YAAM,WAAW,MAAM,MAAM,GAAG,KAAK,OAAO,UAAU,uBAAuB;AAAA,QAC3E,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,UACA,OAAO,KAAK;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AAED,YAAM,OAAO,MAAM,SAAS,KAAK;AAEjC,UAAI,KAAK,SAAS;AAChB,eAAO;AAAA,UACL,SAAS,KAAK,KAAK;AAAA,UACnB,SAAS,KAAK,KAAK;AAAA,UACnB,QAAQ,KAAK,KAAK;AAAA,QACpB;AAAA,MACF;AAEA,aAAO;AAAA,QACL,SAAS;AAAA,QACT,QAAQ,KAAK,WAAW;AAAA,MAC1B;AAAA,IACF,SAAS,OAAO;AACd,aAAO;AAAA,QACL,SAAS;AAAA,QACT,QAAQ,0BAA0B,KAAK;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,qBACJ,WACA,QACA,SAC+B;AAC/B,QAAI;AACF,YAAM,WAAW,MAAM,MAAM,WAAW;AAAA,QACtC,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,UAAU,KAAK,iBAAiB;AAAA,UAChC,iCAAiC;AAAA,UACjC,kCAAkC,QAAQ,KAAK,IAAI;AAAA,QACrD;AAAA,MACF,CAAC;AAED,UAAI,SAAS,IAAI;AACf,cAAM,cAA2B,CAAC;AAGlC,cAAM,cAAc,SAAS,QAAQ,IAAI,6BAA6B;AACtE,cAAM,eAAe,SAAS,QAAQ,IAAI,8BAA8B;AACxE,cAAM,eAAe,SAAS,QAAQ,IAAI,8BAA8B;AACxE,cAAM,gBAAgB,SAAS,QAAQ,IAAI,+BAA+B;AAC1E,cAAM,mBAAmB,SAAS,QAAQ,IAAI,kCAAkC;AAChF,cAAM,SAAS,SAAS,QAAQ,IAAI,wBAAwB;AAE5D,YAAI,YAAa,aAAY,6BAA6B,IAAI;AAC9D,YAAI,aAAc,aAAY,8BAA8B,IAAI;AAChE,YAAI,aAAc,aAAY,8BAA8B,IAAI;AAChE,YAAI,cAAe,aAAY,+BAA+B,IAAI;AAClE,YAAI,iBAAkB,aAAY,kCAAkC,IAAI;AACxE,YAAI,OAAQ,aAAY,wBAAwB,IAAI;AAEpD,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,QACX;AAAA,MACF;AAEA,aAAO;AAAA,QACL,SAAS;AAAA,QACT,QAAQ,gCAAgC,SAAS,MAAM;AAAA,MACzD;AAAA,IACF,SAAS,OAAO;AACd,aAAO;AAAA,QACL,SAAS;AAAA,QACT,QAAQ,oBAAoB,KAAK;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,oBAA0B;AACxB,QAAI,OAAO,WAAW,YAAa;AAGnC,WAAO,iBAAiB,WAAW,CAAC,UAAU;AAC5C,UAAI,CAAC,KAAK,gBAAgB,MAAM,MAAM,GAAG;AACvC,gBAAQ,KAAK,6CAA6C,MAAM,MAAM,EAAE;AACxE;AAAA,MACF;AAGA,WAAK,sBAAsB,KAAK;AAAA,IAClC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,oBACE,cACA,SACA,cACS;AACT,QAAI,CAAC,KAAK,gBAAgB,YAAY,GAAG;AACvC,cAAQ,MAAM,+CAA+C,YAAY,EAAE;AAC3E,aAAO;AAAA,IACT;AAEA,UAAM,SAAS,gBAAgB,OAAO;AACtC,WAAO,YAAY,SAAS,YAAY;AACxC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACrB,UAAM,gBAAgB;AACtB,UAAM,aAAa;AAEnB,WAAO,eAAe,eACpB,OACA,MACmB;AACnB,YAAM,MAAM,OAAO,UAAU,WAAW,QAAQ,MAAM,SAAS;AAC/D,YAAM,SAAS,IAAI,IAAI,GAAG;AAC1B,YAAM,eAAe,OAAO;AAG5B,UAAI,iBAAiB,WAAW,iBAAiB,GAAG;AAElD,cAAM,aAAa,MAAM,WAAW;AAAA,UAClC,WAAW,iBAAiB;AAAA,UAC5B,MAAM,UAAU;AAAA,UAChB,OAAO,KAAK,MAAM,WAAW,CAAC,CAAC;AAAA,QACjC;AAEA,YAAI,CAAC,WAAW,SAAS;AACvB,gBAAM,IAAI,MAAM,iBAAiB,WAAW,MAAM,EAAE;AAAA,QACtD;AAGA,cAAM,cAAc,WAAW,eAAe,YAAY;AAC1D,eAAO;AAAA,UACL,GAAG;AAAA,UACH,SAAS;AAAA,YACP,GAAG,MAAM;AAAA,YACT,GAAG;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAEA,aAAO,cAAc,OAAO,IAAI;AAAA,IAClC;AAAA,EACF;AAAA,EAEQ,oBAA8B;AAEpC,UAAM,iBAAiB;AAAA,MACrB;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,UAAM,gBAAgB,QAAQ,IAAI,cAAc,MAAM,GAAG,KAAK,CAAC;AAE/D,WAAO,CAAC,GAAG,gBAAgB,GAAG,cAAc,OAAO,OAAO,CAAC;AAAA,EAC7D;AAAA,EAEQ,oBAA8B;AACpC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,oBAA8B;AACpC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,mBAA2B;AACjC,QAAI,OAAO,WAAW,YAAa,QAAO;AAC1C,WAAO,OAAO,SAAS;AAAA,EACzB;AAAA,EAEQ,gBAAyB;AAC/B,WAAO,QAAQ,IAAI,aAAa,iBACzB,KAAK,iBAAiB,EAAE,SAAS,WAAW,KAC5C,KAAK,iBAAiB,EAAE,SAAS,WAAW;AAAA,EACrD;AAAA,EAEQ,sBAAsB,OAA2B;AAEvD,UAAM,EAAE,MAAM,KAAK,IAAI,MAAM;AAE7B,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,aAAK,sBAAsB,IAAI;AAC/B;AAAA,MACF,KAAK;AACH,aAAK,iBAAiB,IAAI;AAC1B;AAAA,MACF,KAAK;AACH,aAAK,uBAAuB,IAAI;AAChC;AAAA,MACF;AACE,gBAAQ,IAAI,mCAAmC,IAAI;AAAA,IACvD;AAAA,EACF;AAAA,EAEQ,sBAAsB,MAAiB;AAE7C,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,cAAc,IAAI,YAAY,uBAAuB;AAAA,QAC1D,QAAQ;AAAA,MACV,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AAAA,EAEQ,iBAAiB,MAAiB;AAExC,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,cAAc,IAAI,YAAY,uBAAuB;AAAA,QAC1D,QAAQ;AAAA,MACV,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AAAA,EAEQ,uBAAuB,MAAiB;AAE9C,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,cAAc,IAAI,YAAY,4BAA4B;AAAA,QAC/D,QAAQ;AAAA,MACV,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACF;AAGO,IAAM,mBAAmB,CAAC,QAAoB,UAA8B;AACjF,SAAO,IAAI,WAAW,QAAQ,KAAK;AACrC;AAEO,IAAM,kBAAkB,CAAC,QAAgB,mBAAsC;AACpF,SAAO,eAAe,SAAS,MAAM,KAC9B,eAAe,SAAS,GAAG,KAC1B,OAAO,SAAS,WAAW,KAAK,eAAe,KAAK,OAAK,EAAE,SAAS,WAAW,CAAC;AAC1F;AAEO,IAAM,uBAAuB,CAAC,QAAwB;AAC3D,MAAI;AACF,WAAO,IAAI,IAAI,GAAG,EAAE;AAAA,EACtB,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEO,IAAM,eAAe,CAAC,MAAc,SAA0B;AACnE,SAAO,qBAAqB,IAAI,MAAM,qBAAqB,IAAI;AACjE;AAEO,IAAM,eAAe,CAAC,SAAiB,MAAc,WAA4C;AACtG,QAAM,MAAM,IAAI,IAAI,MAAM,OAAO;AAEjC,MAAI,QAAQ;AACV,WAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC/C,UAAI,aAAa,IAAI,KAAK,KAAK;AAAA,IACjC,CAAC;AAAA,EACH;AAEA,SAAO,IAAI,SAAS;AACtB;", "names": ["axios", "Cookies", "import_react", "React", "import_jwt_decode", "import_react", "import_react", "import_react_router", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "import_axios", "axios", "React", "import_axios", "axios"]}