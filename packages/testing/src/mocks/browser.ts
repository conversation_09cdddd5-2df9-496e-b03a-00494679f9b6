import { setupWorker } from 'msw/browser'
import { defaultHandlers } from './handlers'

// Setup MSW worker for browser (development/Storybook)
export const worker = setupWorker(...defaultHandlers)

// Browser-specific configuration
export const startMockWorker = async () => {
  if (typeof window !== 'undefined') {
    return worker.start({
      onUnhandledRequest: 'bypass',
      serviceWorker: {
        url: '/mockServiceWorker.js',
      },
    })
  }
  return undefined
}

export const stopMockWorker = () => {
  if (typeof window !== 'undefined') {
    worker.stop()
  }
}

// Development helpers
export const enableMockingInDevelopment = () => {
  if (process.env.NODE_ENV === 'development' && process.env.VITE_ENABLE_MOCKING === 'true') {
    startMockWorker().then(() => {
      console.log('[MSW] Mocking enabled')
    })
  }
}

// Storybook integration
export const enableMockingForStorybook = () => {
  if (typeof window !== 'undefined' && window.location.pathname.includes('iframe.html')) {
    startMockWorker()
  }
}