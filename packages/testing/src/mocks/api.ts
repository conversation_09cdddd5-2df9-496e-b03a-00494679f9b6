import { http, HttpResponse } from 'msw'

// API mock utilities
export class MockAPI {
  private baseUrl: string
  private handlers: any[] = []

  constructor(baseUrl = 'http://localhost:3000/api') {
    this.baseUrl = baseUrl
  }

  // Add a GET endpoint
  get(path: string, response: any, options: { delay?: number; status?: number } = {}) {
    const handler = http.get(`${this.baseUrl}${path}`, async () => {
      if (options.delay) {
        await new Promise(resolve => setTimeout(resolve, options.delay))
      }
      return HttpResponse.json(response, { status: options.status || 200 })
    })
    this.handlers.push(handler)
    return this
  }

  // Add a POST endpoint
  post(path: string, handler: (body: any) => any, options: { delay?: number } = {}) {
    const mockHandler = http.post(`${this.baseUrl}${path}`, async ({ request }) => {
      const body = await request.json()
      if (options.delay) {
        await new Promise(resolve => setTimeout(resolve, options.delay))
      }
      const response = await handler(body)
      return HttpResponse.json(response)
    })
    this.handlers.push(mockHandler)
    return this
  }

  // Add a PUT endpoint
  put(path: string, handler: (params: any, body: any) => any, options: { delay?: number } = {}) {
    const mockHandler = http.put(`${this.baseUrl}${path}`, async ({ params, request }) => {
      const body = await request.json()
      if (options.delay) {
        await new Promise(resolve => setTimeout(resolve, options.delay))
      }
      const response = await handler(params, body)
      return HttpResponse.json(response)
    })
    this.handlers.push(mockHandler)
    return this
  }

  // Add a DELETE endpoint
  delete(path: string, response: any = { success: true }, options: { delay?: number } = {}) {
    const handler = http.delete(`${this.baseUrl}${path}`, async () => {
      if (options.delay) {
        await new Promise(resolve => setTimeout(resolve, options.delay))
      }
      return HttpResponse.json(response)
    })
    this.handlers.push(handler)
    return this
  }

  // Add error response
  error(path: string, error: { message: string; code?: string }, status = 500) {
    const handler = http.get(`${this.baseUrl}${path}`, () => {
      return HttpResponse.json({ error }, { status })
    })
    this.handlers.push(handler)
    return this
  }

  // Get all handlers
  getHandlers() {
    return this.handlers
  }

  // Create paginated response
  paginated(path: string, itemGenerator: () => any, totalItems = 100) {
    const handler = http.get(`${this.baseUrl}${path}`, ({ request }) => {
      const url = new URL(request.url)
      const page = parseInt(url.searchParams.get('page') || '1')
      const limit = parseInt(url.searchParams.get('limit') || '10')
      const offset = (page - 1) * limit

      const items = Array.from({ length: Math.min(limit, totalItems - offset) }, itemGenerator)

      return HttpResponse.json({
        data: items,
        pagination: {
          page,
          limit,
          total: totalItems,
          totalPages: Math.ceil(totalItems / limit),
          hasNext: page < Math.ceil(totalItems / limit),
          hasPrev: page > 1,
        },
      })
    })
    this.handlers.push(handler)
    return this
  }

  // Create CRUD endpoints for a resource
  crud(resource: string, generator: () => any) {
    const items = new Map<string, any>()

    // List
    this.get(`/${resource}`, () => {
      return Array.from(items.values())
    })

    // Get by ID
    this.handlers.push(
      http.get(`${this.baseUrl}/${resource}/:id`, ({ params }) => {
        const item = items.get(params.id as string)
        if (!item) {
          return HttpResponse.json({ error: 'Not found' }, { status: 404 })
        }
        return HttpResponse.json(item)
      })
    )

    // Create
    this.post(`/${resource}`, (body) => {
      const id = Date.now().toString()
      const item = { ...generator(), ...body, id }
      items.set(id, item)
      return item
    })

    // Update
    this.handlers.push(
      http.put(`${this.baseUrl}/${resource}/:id`, async ({ params, request }) => {
        const body = await request.json() as Record<string, any>
        const id = params.id as string
        const existing = items.get(id)
        if (!existing) {
          return HttpResponse.json({ error: 'Not found' }, { status: 404 })
        }
        const updated = { ...existing, ...body }
        items.set(id, updated)
        return HttpResponse.json(updated)
      })
    )

    // Delete
    this.handlers.push(
      http.delete(`${this.baseUrl}/${resource}/:id`, ({ params }) => {
        const id = params.id as string
        if (!items.has(id)) {
          return HttpResponse.json({ error: 'Not found' }, { status: 404 })
        }
        items.delete(id)
        return new HttpResponse(null, { status: 204 })
      })
    )

    return this
  }

  // Create file upload endpoint
  fileUpload(path: string, handler?: (file: File) => any) {
    const uploadHandler = http.post(`${this.baseUrl}${path}`, async ({ request }) => {
      const formData = await request.formData()
      const file = formData.get('file') as File

      if (!file) {
        return HttpResponse.json({ error: 'No file provided' }, { status: 400 })
      }

      const response = handler ? await handler(file) : {
        id: Date.now().toString(),
        filename: file.name,
        size: file.size,
        type: file.type,
        url: `/uploads/${file.name}`,
      }

      return HttpResponse.json(response)
    })
    this.handlers.push(uploadHandler)
    return this
  }
}

// Pre-configured mock APIs
export const createMockAuthAPI = () => {
  return new MockAPI()
    .post('/auth/login', ({ email, password }) => {
      if (email === '<EMAIL>' && password === 'password') {
        return {
          user: { id: '1', email, name: 'Test User' },
          token: 'mock-jwt-token',
        }
      }
      throw new Error('Invalid credentials')
    })
    .post('/auth/logout', () => ({ success: true }))
    .get('/auth/me', { id: '1', email: '<EMAIL>', name: 'Test User' })
}

export const createMockUserAPI = () => {
  return new MockAPI().crud('users', () => ({
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'user',
  }))
}