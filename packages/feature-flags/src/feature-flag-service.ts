import axios from 'axios';
import Cookies from 'js-cookie';
import { v4 as uuidv4 } from 'uuid';
import type {
  FeatureFlagConfig,
  UserContext,
  TargetingRule,
  FeatureFlagEvent
} from './types';

export class FeatureFlagService {
  private static instance: FeatureFlagService;
  private flags: Map<string, FeatureFlagConfig> = new Map();
  private userContext: UserContext = {};
  private sessionId: string;
  private apiEndpoint: string;
  private analyticsBuffer: FeatureFlagEvent[] = [];
  private flushInterval: ReturnType<typeof setInterval> | null = null;

  private constructor(apiEndpoint: string) {
    this.apiEndpoint = apiEndpoint;
    this.sessionId = this.getOrCreateSessionId();
    this.startAnalyticsFlush();
  }

  static getInstance(apiEndpoint: string = '/api/feature-flags'): FeatureFlagService {
    if (!FeatureFlagService.instance) {
      FeatureFlagService.instance = new FeatureFlagService(apiEndpoint);
    }
    return FeatureFlagService.instance;
  }

  /**
   * Initialize the service with flags and user context
   */
  async initialize(userContext?: UserContext): Promise<void> {
    if (userContext) {
      this.setUserContext(userContext);
    }
    await this.fetchFlags();
  }

  /**
   * Set user context for targeting
   */
  setUserContext(context: UserContext): void {
    this.userContext = { ...this.userContext, ...context };
  }

  /**
   * Get user context
   */
  getUserContext(): UserContext {
    return this.userContext;
  }

  /**
   * Fetch feature flags from the server
   */
  private async fetchFlags(): Promise<void> {
    try {
      const response = await axios.get<FeatureFlagConfig[]>(
        `${this.apiEndpoint}`,
        {
          params: {
            userId: this.userContext.userId,
            context: JSON.stringify(this.userContext),
          },
        }
      );
      
      this.flags.clear();
      response.data.forEach(flag => {
        this.flags.set(flag.key, flag);
      });
    } catch (error) {
      console.error('Failed to fetch feature flags:', error);
    }
  }

  /**
   * Check if a feature flag is enabled
   */
  isEnabled(flagKey: string, defaultValue: boolean = false): boolean {
    const flag = this.flags.get(flagKey);
    if (!flag) return defaultValue;

    // Track evaluation
    this.trackEvent({
      featureFlag: flagKey,
      evaluated: true,
    });

    // Check if globally enabled
    if (!flag.enabled) return false;

    // Check rollout percentage
    if (flag.rolloutPercentage < 100) {
      const hash = this.hashString(`${this.userContext.userId || this.sessionId}:${flagKey}`);
      const percentage = Math.abs(hash % 100);
      if (percentage >= flag.rolloutPercentage) return false;
    }

    // Check targeting rules
    if (flag.targetingRules && flag.targetingRules.length > 0) {
      return this.evaluateTargetingRules(flag.targetingRules);
    }

    return true;
  }

  /**
   * Get variant for a feature flag
   */
  getVariant(flagKey: string, defaultVariant?: string): string | null {
    const flag = this.flags.get(flagKey);
    if (!flag || !this.isEnabled(flagKey)) return defaultVariant || null;

    if (!flag.variants || flag.variants.length === 0) {
      return flag.defaultVariant || defaultVariant || null;
    }

    // Use consistent hashing for variant assignment
    const hash = this.hashString(`${this.userContext.userId || this.sessionId}:${flagKey}:variant`);
    const percentage = Math.abs(hash % 100);

    let cumulativeWeight = 0;
    for (const variant of flag.variants) {
      cumulativeWeight += variant.weight;
      if (percentage < cumulativeWeight) {
        // Track variant selection
        this.trackEvent({
          featureFlag: flagKey,
          variant: variant.key,
          evaluated: true,
        });
        return variant.key;
      }
    }

    return flag.defaultVariant || defaultVariant || null;
  }

  /**
   * Get variant value
   */
  getVariantValue(flagKey: string, variantKey: string): any {
    const flag = this.flags.get(flagKey);
    if (!flag || !flag.variants) return null;

    const variant = flag.variants.find(v => v.key === variantKey);
    return variant?.value || null;
  }

  /**
   * Evaluate targeting rules
   */
  private evaluateTargetingRules(rules: TargetingRule[]): boolean {
    for (const rule of rules) {
      const result = this.evaluateRule(rule);
      if (!result) return false;
    }
    return true;
  }

  /**
   * Evaluate a single targeting rule
   */
  private evaluateRule(rule: TargetingRule): boolean {
    const value = this.getAttributeValue(rule.attribute);
    let result = false;

    switch (rule.operator) {
      case 'equals':
        result = value === rule.value;
        break;
      case 'not_equals':
        result = value !== rule.value;
        break;
      case 'contains':
        result = String(value).includes(String(rule.value));
        break;
      case 'not_contains':
        result = !String(value).includes(String(rule.value));
        break;
      case 'starts_with':
        result = String(value).startsWith(String(rule.value));
        break;
      case 'ends_with':
        result = String(value).endsWith(String(rule.value));
        break;
      case 'greater_than':
        result = Number(value) > Number(rule.value);
        break;
      case 'less_than':
        result = Number(value) < Number(rule.value);
        break;
      case 'in':
        result = Array.isArray(rule.value) && rule.value.includes(value);
        break;
      case 'not_in':
        result = Array.isArray(rule.value) && !rule.value.includes(value);
        break;
      case 'regex':
        result = new RegExp(String(rule.value)).test(String(value));
        break;
    }

    return rule.negate ? !result : result;
  }

  /**
   * Get attribute value from user context
   */
  private getAttributeValue(attribute: string): any {
    if (attribute.includes('.')) {
      const parts = attribute.split('.');
      let value: any = this.userContext;
      for (const part of parts) {
        value = value?.[part];
      }
      return value;
    }
    return this.userContext[attribute as keyof UserContext];
  }

  /**
   * Hash string to number
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash;
  }

  /**
   * Get or create session ID
   */
  private getOrCreateSessionId(): string {
    let sessionId = Cookies.get('luminar_session_id');
    if (!sessionId) {
      sessionId = uuidv4();
      Cookies.set('luminar_session_id', sessionId, { expires: 365 });
    }
    return sessionId;
  }

  /**
   * Track feature flag event
   */
  private trackEvent(event: Partial<FeatureFlagEvent>): void {
    this.analyticsBuffer.push({
      timestamp: new Date(),
      sessionId: this.sessionId,
      userId: this.userContext.userId,
      context: this.userContext,
      evaluated: false,
      ...event,
    } as FeatureFlagEvent);
  }

  /**
   * Start analytics flush interval
   */
  private startAnalyticsFlush(): void {
    this.flushInterval = setInterval(() => {
      this.flushAnalytics();
    }, 30000); // Flush every 30 seconds
  }

  /**
   * Flush analytics events
   */
  private async flushAnalytics(): Promise<void> {
    if (this.analyticsBuffer.length === 0) return;

    const events = [...this.analyticsBuffer];
    this.analyticsBuffer = [];

    try {
      await axios.post(`${this.apiEndpoint}/analytics`, { events });
    } catch (error) {
      console.error('Failed to flush feature flag analytics:', error);
      // Re-add events to buffer on failure
      this.analyticsBuffer.unshift(...events);
    }
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
    }
    this.flushAnalytics();
  }
}