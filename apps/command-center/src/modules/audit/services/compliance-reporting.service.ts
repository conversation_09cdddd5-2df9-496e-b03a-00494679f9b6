import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Cron } from '@nestjs/schedule';

import { ComplianceReport } from '../entities/compliance-report.entity';
import { AuditLog } from '../entities/audit-log.entity';

export interface ComplianceReportConfig {
  reportTypes: string[];
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  recipients: string[];
  format: 'pdf' | 'excel' | 'json';
}

@Injectable()
export class ComplianceReportingService {
  private readonly logger = new Logger(ComplianceReportingService.name);

  constructor(
    @InjectRepository(ComplianceReport)
    private readonly complianceReportRepository: Repository<ComplianceReport>,
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Generate compliance report
   */
  async generateReport(config: ComplianceReportConfig): Promise<ComplianceReport> {
    this.logger.log('Generating compliance report');

    const report = this.complianceReportRepository.create({
      reportType: config.reportTypes.join(','),
      frequency: config.frequency,
      format: config.format,
      status: 'generating',
      createdAt: new Date(),
    });

    const savedReport = await this.complianceReportRepository.save(report);

    // Process report generation asynchronously
    this.processReportGeneration(savedReport.id, config);

    return savedReport;
  }

  /**
   * Get compliance reports
   */
  async getReports(limit = 50): Promise<ComplianceReport[]> {
    return this.complianceReportRepository.find({
      take: limit,
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Get report by ID
   */
  async getReportById(id: string): Promise<ComplianceReport | null> {
    return this.complianceReportRepository.findOne({ where: { id } });
  }

  /**
   * Delete old reports
   */
  @Cron('0 0 * * 0') // Weekly on Sunday
  async cleanupOldReports(): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 90); // Keep reports for 90 days

    await this.complianceReportRepository
      .createQueryBuilder()
      .delete()
      .where('createdAt < :cutoffDate', { cutoffDate })
      .execute();

    this.logger.log('Cleaned up old compliance reports');
  }

  /**
   * Process report generation
   */
  private async processReportGeneration(reportId: string, config: ComplianceReportConfig): Promise<void> {
    try {
      const report = await this.complianceReportRepository.findOne({ where: { id: reportId } });
      if (!report) return;

      // Simulate report generation
      await new Promise(resolve => setTimeout(resolve, 2000));

      report.status = 'completed';
      report.data = {
        summary: 'Compliance report generated successfully',
        totalEvents: Math.floor(Math.random() * 1000),
        complianceScore: Math.floor(Math.random() * 100),
        generatedAt: new Date(),
      };

      await this.complianceReportRepository.save(report);
      this.logger.log(`Compliance report ${reportId} generated successfully`);
    } catch (error) {
      this.logger.error(`Failed to generate compliance report ${reportId}:`, error);
      
      const report = await this.complianceReportRepository.findOne({ where: { id: reportId } });
      if (report) {
        report.status = 'failed';
        await this.complianceReportRepository.save(report);
      }
    }
  }
}