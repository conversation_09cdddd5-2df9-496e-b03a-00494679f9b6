import {
  Controller,
  Get,
  Post,
  Query,
  Param,
  Body,
  UseGuards,
  UseInterceptors,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { IsString, IsArray, IsEnum, IsOptional } from 'class-validator';

import { ComplianceReportingService, ComplianceReportConfig } from '../services/compliance-reporting.service';
import { AuditLogInterceptor } from '../interceptors/audit-log.interceptor';

export class CreateComplianceReportDto {
  @IsArray()
  @IsString({ each: true })
  reportTypes: string[];

  @IsEnum(['daily', 'weekly', 'monthly', 'quarterly'])
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';

  @IsArray()
  @IsString({ each: true })
  recipients: string[];

  @IsEnum(['pdf', 'excel', 'json'])
  format: 'pdf' | 'excel' | 'json';
}

export class GetComplianceReportsDto {
  @IsOptional()
  @IsString()
  reportType?: string;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  limit?: number = 50;
}

@ApiTags('Compliance')
@ApiBearerAuth()
@Controller('compliance')
@UseInterceptors(AuditLogInterceptor)
export class ComplianceController {
  constructor(
    private readonly complianceReportingService: ComplianceReportingService,
  ) {}

  @Post('reports')
  @ApiOperation({ summary: 'Generate compliance report' })
  @ApiResponse({ status: 201, description: 'Compliance report generation started' })
  async generateComplianceReport(@Body(ValidationPipe) reportDto: CreateComplianceReportDto) {
    const report = await this.complianceReportingService.generateReport(reportDto);

    return {
      success: true,
      data: report,
      message: 'Compliance report generation started',
    };
  }

  @Get('reports')
  @ApiOperation({ summary: 'Get compliance reports' })
  @ApiResponse({ status: 200, description: 'Compliance reports retrieved successfully' })
  async getComplianceReports(@Query(ValidationPipe) query: GetComplianceReportsDto) {
    const reports = await this.complianceReportingService.getReports(query.limit);

    // Filter by reportType and status if provided
    let filteredReports = reports;
    if (query.reportType) {
      filteredReports = filteredReports.filter(report => 
        report.reportType.includes(query.reportType!)
      );
    }
    if (query.status) {
      filteredReports = filteredReports.filter(report => 
        report.status === query.status
      );
    }

    return {
      success: true,
      data: filteredReports,
      total: filteredReports.length,
    };
  }

  @Get('reports/:id')
  @ApiOperation({ summary: 'Get compliance report by ID' })
  @ApiResponse({ status: 200, description: 'Compliance report retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Compliance report not found' })
  async getComplianceReportById(@Param('id') id: string) {
    const report = await this.complianceReportingService.getReportById(id);

    if (!report) {
      return {
        success: false,
        message: 'Compliance report not found',
      };
    }

    return {
      success: true,
      data: report,
    };
  }

  @Get('dashboard')
  @ApiOperation({ summary: 'Get compliance dashboard data' })
  @ApiResponse({ status: 200, description: 'Compliance dashboard data retrieved successfully' })
  async getComplianceDashboard() {
    const reports = await this.complianceReportingService.getReports(100);

    const dashboardData = {
      totalReports: reports.length,
      recentReports: reports.slice(0, 10),
      reportsByStatus: reports.reduce((acc, report) => {
        acc[report.status] = (acc[report.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      reportsByType: reports.reduce((acc, report) => {
        const types = report.reportType.split(',');
        types.forEach(type => {
          acc[type.trim()] = (acc[type.trim()] || 0) + 1;
        });
        return acc;
      }, {} as Record<string, number>),
      reportsByFrequency: reports.reduce((acc, report) => {
        acc[report.frequency] = (acc[report.frequency] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      completionRate: reports.length > 0 
        ? Math.round((reports.filter(r => r.status === 'completed').length / reports.length) * 100)
        : 0,
    };

    return {
      success: true,
      data: dashboardData,
    };
  }

  @Get('health')
  @ApiOperation({ summary: 'Get compliance system health' })
  @ApiResponse({ status: 200, description: 'Compliance system health retrieved successfully' })
  async getComplianceHealth() {
    const reports = await this.complianceReportingService.getReports(50);

    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const recentReports = reports.filter(report => report.createdAt >= oneDayAgo);
    const weeklyReports = reports.filter(report => report.createdAt >= oneWeekAgo);

    const failedReports = reports.filter(report => report.status === 'failed');
    const processingReports = reports.filter(report => report.status === 'generating');

    const health = {
      status: failedReports.length > recentReports.length * 0.1 ? 'unhealthy' : 'healthy',
      metrics: {
        reportsLast24h: recentReports.length,
        reportsLast7d: weeklyReports.length,
        failedReports: failedReports.length,
        processingReports: processingReports.length,
        averageProcessingTime: '2-5 minutes', // Placeholder
      },
      issues: failedReports.length > 0 ? [
        `${failedReports.length} failed reports detected`,
      ] : [],
      recommendations: processingReports.length > 10 ? [
        'High number of reports in processing state - consider scaling resources',
      ] : [],
    };

    return {
      success: true,
      data: health,
    };
  }

  @Get('templates')
  @ApiOperation({ summary: 'Get compliance report templates' })
  @ApiResponse({ status: 200, description: 'Compliance report templates retrieved successfully' })
  async getComplianceTemplates() {
    const templates = [
      {
        id: 'gdpr-compliance',
        name: 'GDPR Compliance Report',
        description: 'Data protection and privacy compliance report',
        reportTypes: ['data_access', 'data_deletion', 'consent_management'],
        requiredFields: ['user_data_summary', 'consent_records', 'data_breaches'],
      },
      {
        id: 'sox-compliance',
        name: 'SOX Compliance Report',
        description: 'Sarbanes-Oxley financial compliance report',
        reportTypes: ['financial_controls', 'access_reviews', 'change_management'],
        requiredFields: ['financial_transactions', 'user_access_changes', 'control_testing'],
      },
      {
        id: 'hipaa-compliance',
        name: 'HIPAA Compliance Report',
        description: 'Healthcare data protection compliance report',
        reportTypes: ['phi_access', 'security_incidents', 'risk_assessments'],
        requiredFields: ['phi_access_logs', 'security_events', 'risk_mitigation'],
      },
      {
        id: 'pci-compliance',
        name: 'PCI DSS Compliance Report',
        description: 'Payment card industry security standards report',
        reportTypes: ['cardholder_data', 'security_controls', 'vulnerability_scans'],
        requiredFields: ['payment_transactions', 'security_measures', 'vulnerability_results'],
      },
    ];

    return {
      success: true,
      data: templates,
    };
  }
}