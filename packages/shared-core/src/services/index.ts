/**
 * Services Module - Domain-specific API services
 */

import type { LuminarAPIClient } from '../api/types';
import { createUsersService } from './users.service';
import { createTrainingService } from './training.service';
import { createVendorsService } from './vendors.service';
import { createEmailService } from './email.service';
import { createWinsService } from './wins.service';

// Service implementations
export {
  UsersServiceImpl,
  createUsersService
} from './users.service';

export {
  TrainingServiceImpl,
  createTrainingService
} from './training.service';

export {
  VendorsServiceImpl,
  createVendorsService
} from './vendors.service';

export {
  EmailServiceImpl,
  createEmailService
} from './email.service';

export {
  WinsServiceImpl,
  createWinsService
} from './wins.service';

// Service interfaces and types
export type {
  UsersService,
  TrainingService,
  VendorsService,
  EmailService,
  WinsService,
  User,
  Role,
  Permission,
  Training,
  TrainingCategory,
  Vendor,
  Email,
  Win,
  CreateUserDto,
  UpdateUserDto,
  UserQueryParams,
  CreateTrainingDto,
  UpdateTrainingDto,
  TrainingQueryParams,
  CreateVendorDto,
  UpdateVendorDto,
  VendorQueryParams,
  SendEmailDto,
  EmailQueryParams,
  CreateWinDto,
  UpdateWinDto,
  WinQueryParams,
  BaseQueryParams,
  Address,
  ContactPerson,
  VendorService,
  EmailAttachment,
  WinMetric,
  WinAttachment
} from './types';

export {
  TrainingDifficulty,
  TrainingStatus,
  EmailStatus,
  WinCategory,
  WinImpact
} from './types';

// Utility function to create all services with a single API client
export function createAllServices(apiClient: LuminarAPIClient) {
  return {
    users: createUsersService(apiClient),
    training: createTrainingService(apiClient),
    vendors: createVendorsService(apiClient),
    email: createEmailService(apiClient),
    wins: createWinsService(apiClient)
  };
}

// Service factory with configuration
export interface ServiceConfig {
  apiClient: LuminarAPIClient;
  enableLogging?: boolean;
  enableCaching?: boolean;
}

export function createConfiguredServices(config: ServiceConfig) {
  const services = createAllServices(config.apiClient);

  // Add logging wrapper if enabled
  if (config.enableLogging) {
    Object.keys(services).forEach(serviceName => {
      const service = services[serviceName as keyof typeof services];
      wrapServiceWithLogging(service, serviceName);
    });
  }

  return services;
}

// Logging wrapper for services
function wrapServiceWithLogging(service: any, serviceName: string) {
  const originalMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(service))
    .filter(name => name !== 'constructor' && typeof service[name] === 'function');

  originalMethods.forEach(methodName => {
    const originalMethod = service[methodName];
    service[methodName] = async function(...args: any[]) {
      console.log(`[${serviceName}Service] ${methodName}`, args);
      try {
        const result = await originalMethod.apply(this, args);
        console.log(`[${serviceName}Service] ${methodName} - Success`);
        return result;
      } catch (error) {
        console.error(`[${serviceName}Service] ${methodName} - Error:`, error);
        throw error;
      }
    };
  });
}