import { useEffect, useState } from 'react';
import { useModuleFederation } from './ModuleFederationProvider';
import { loadRemoteModule, isRemoteAvailable } from '../utils';

/**
 * Hook to dynamically load a remote module
 */
export function useRemoteModule<T = any>(
  remoteName: string,
  modulePath: string = './App'
) {
  const { runtime } = useModuleFederation();
  const [module, setModule] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let mounted = true;

    const load = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const loadedModule = await loadRemoteModule<T>(remoteName, modulePath);
        
        if (mounted) {
          setModule(loadedModule);
        }
      } catch (err) {
        if (mounted) {
          setError(err as Error);
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    load();

    return () => {
      mounted = false;
    };
  }, [remoteName, modulePath, runtime]);

  return { module, isLoading, error, retry: () => window.location.reload() };
}

/**
 * Hook to check if a remote is available
 */
export function useRemoteAvailability(remoteName: string) {
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    let mounted = true;

    const check = async () => {
      try {
        setIsChecking(true);
        const available = await isRemoteAvailable(remoteName);
        if (mounted) {
          setIsAvailable(available);
        }
      } catch (error) {
        if (mounted) {
          setIsAvailable(false);
        }
      } finally {
        if (mounted) {
          setIsChecking(false);
        }
      }
    };

    check();
    
    // Recheck periodically
    const interval = setInterval(check, 30000); // 30 seconds

    return () => {
      mounted = false;
      clearInterval(interval);
    };
  }, [remoteName]);

  return { isAvailable, isChecking };
}

/**
 * Hook to preload remote modules
 */
export function usePreloadRemotes(remotes: Array<{ name: string; module?: string }>) {
  const [preloadStatus, setPreloadStatus] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const preload = async () => {
      const status: Record<string, boolean> = {};

      await Promise.all(
        remotes.map(async ({ name, module }) => {
          try {
            await loadRemoteModule(name, module, 1, 0);
            status[name] = true;
          } catch (error) {
            console.warn(`Failed to preload ${name}:`, error);
            status[name] = false;
          }
        })
      );

      setPreloadStatus(status);
    };

    preload();
  }, [remotes]);

  return preloadStatus;
}

/**
 * Hook to manage remote URLs dynamically
 */
export function useRemoteUrl(remoteName: string) {
  const { runtime, config } = useModuleFederation();
  const [url, setUrl] = useState(config.remotes[remoteName] || '');

  const updateUrl = async (newUrl: string) => {
    runtime.setRemoteUrl(remoteName, newUrl);
    setUrl(newUrl);
    await runtime.reloadRemote(remoteName);
  };

  return { url, updateUrl };
}