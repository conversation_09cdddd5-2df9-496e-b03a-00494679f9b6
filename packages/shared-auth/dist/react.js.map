{"version": 3, "sources": ["../src/react/index.ts", "../src/auth-provider.tsx", "../src/auth-context.tsx", "../src/auth-client.ts", "../src/types.ts", "../src/hooks.ts"], "sourcesContent": ["// React-specific exports\nexport { AuthProvider } from '../auth-provider'\nexport { useAuth } from '../hooks'\n\n// Re-export types from main types file\nexport type { AuthContextValue, AuthProviderProps } from '../types'\n", "import React, { useState, useEffect, useCallback, useRef } from 'react'\nimport { AuthContext } from './auth-context'\nimport { AuthClient } from './auth-client'\nimport {\n  AuthConfig,\n  AuthState,\n  AuthProviderProps,\n  User,\n  LoginCredentials,\n  RegisterData,\n  AuthError,\n  AuthContextValue\n} from './types'\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({\n  children,\n  config,\n  initialState,\n  onAuthStateChange\n}) => {\n  const authClient = useRef<AuthClient>(new AuthClient(config))\n  \n  const [state, setState] = useState<AuthState>({\n    user: null,\n    tokens: null,\n    isAuthenticated: false,\n    isLoading: true,\n    error: null,\n    ...initialState\n  })\n\n  // Notify state changes\n  useEffect(() => {\n    onAuthStateChange?.(state)\n  }, [state, onAuthStateChange])\n\n  // Initialize auth state from storage\n  useEffect(() => {\n    const initAuth = async () => {\n      try {\n        const storedUser = authClient.current.getStoredUser()\n        const storedTokens = authClient.current.getStoredTokens()\n\n        if (storedUser && storedTokens) {\n          // Validate session\n          const isValid = await authClient.current.validateSession()\n          \n          if (isValid) {\n            setState({\n              user: authClient.current.getStoredUser(),\n              tokens: authClient.current.getStoredTokens(),\n              isAuthenticated: true,\n              isLoading: false,\n              error: null\n            })\n          } else {\n            authClient.current.clearSession()\n            setState({\n              user: null,\n              tokens: null,\n              isAuthenticated: false,\n              isLoading: false,\n              error: null\n            })\n          }\n        } else {\n          setState(prev => ({ ...prev, isLoading: false }))\n        }\n      } catch (error) {\n        setState({\n          user: null,\n          tokens: null,\n          isAuthenticated: false,\n          isLoading: false,\n          error: error as AuthError\n        })\n      }\n    }\n\n    initAuth()\n  }, [])\n\n  // Set up auto token refresh\n  useEffect(() => {\n    if (!config.enableAutoRefresh || !state.isAuthenticated) {\n      return\n    }\n\n    const interval = setInterval(async () => {\n      try {\n        const tokens = await authClient.current.refreshTokens()\n        setState(prev => ({ ...prev, tokens }))\n      } catch (error) {\n        console.error('Auto refresh failed:', error)\n      }\n    }, config.refreshInterval || 10 * 60 * 1000)\n\n    return () => clearInterval(interval)\n  }, [state.isAuthenticated, config.enableAutoRefresh, config.refreshInterval])\n\n  const login = useCallback(async (credentials: LoginCredentials): Promise<User> => {\n    setState(prev => ({ ...prev, isLoading: true, error: null }))\n    \n    try {\n      const { user, tokens } = await authClient.current.login(credentials)\n      \n      setState({\n        user,\n        tokens,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      })\n      \n      return user\n    } catch (error) {\n      const authError = error as AuthError\n      setState({\n        user: null,\n        tokens: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: authError\n      })\n      throw authError\n    }\n  }, [])\n\n  const register = useCallback(async (data: RegisterData): Promise<User> => {\n    setState(prev => ({ ...prev, isLoading: true, error: null }))\n    \n    try {\n      const { user, tokens } = await authClient.current.register(data)\n      \n      setState({\n        user,\n        tokens,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      })\n      \n      return user\n    } catch (error) {\n      const authError = error as AuthError\n      setState({\n        user: null,\n        tokens: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: authError\n      })\n      throw authError\n    }\n  }, [])\n\n  const logout = useCallback(async (): Promise<void> => {\n    setState(prev => ({ ...prev, isLoading: true }))\n    \n    try {\n      await authClient.current.logout()\n    } catch (error) {\n      console.error('Logout error:', error)\n    } finally {\n      setState({\n        user: null,\n        tokens: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      })\n    }\n  }, [])\n\n  const refreshAuth = useCallback(async (): Promise<void> => {\n    try {\n      const tokens = await authClient.current.refreshTokens()\n      const user = await authClient.current.getCurrentUser()\n      \n      setState({\n        user,\n        tokens,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      })\n    } catch (error) {\n      const authError = error as AuthError\n      setState({\n        user: null,\n        tokens: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: authError\n      })\n      throw authError\n    }\n  }, [])\n\n  const updateUser = useCallback(async (data: Partial<User>): Promise<User> => {\n    if (!state.user) {\n      throw new AuthError('No authenticated user', 'NO_USER')\n    }\n\n    try {\n      const updatedUser = await authClient.current.updateProfile(data)\n      setState(prev => ({ ...prev, user: updatedUser }))\n      return updatedUser\n    } catch (error) {\n      throw error as AuthError\n    }\n  }, [state.user])\n\n  // Permission helpers\n  const hasRole = useCallback((role: string): boolean => {\n    return state.user?.roles.includes(role) || false\n  }, [state.user])\n\n  const hasPermission = useCallback((permission: string): boolean => {\n    return state.user?.permissions.includes(permission) || false\n  }, [state.user])\n\n  const hasAnyRole = useCallback((roles: string[]): boolean => {\n    return roles.some(role => hasRole(role))\n  }, [hasRole])\n\n  const hasAllRoles = useCallback((roles: string[]): boolean => {\n    return roles.every(role => hasRole(role))\n  }, [hasRole])\n\n  const hasAnyPermission = useCallback((permissions: string[]): boolean => {\n    return permissions.some(permission => hasPermission(permission))\n  }, [hasPermission])\n\n  const hasAllPermissions = useCallback((permissions: string[]): boolean => {\n    return permissions.every(permission => hasPermission(permission))\n  }, [hasPermission])\n\n  const contextValue: AuthContextValue = {\n    user: state.user,\n    isAuthenticated: state.isAuthenticated,\n    isLoading: state.isLoading,\n    error: state.error,\n    login,\n    logout,\n    register,\n    refreshAuth,\n    updateUser,\n    hasRole,\n    hasPermission,\n    hasAnyRole,\n    hasAllRoles,\n    hasAnyPermission,\n    hasAllPermissions\n  }\n\n  return (\n    <AuthContext.Provider value={contextValue}>\n      {children}\n    </AuthContext.Provider>\n  )\n}", "import React, { createContext, useContext } from 'react'\nimport { AuthContextValue } from './types'\n\nconst AuthContext = createContext<AuthContextValue | undefined>(undefined)\n\nexport const useAuthContext = (): AuthContextValue => {\n  const context = useContext(AuthContext)\n  if (!context) {\n    throw new Error('useAuthContext must be used within an AuthProvider')\n  }\n  return context\n}\n\nexport { AuthContext }", "import axios, { AxiosInstance, AxiosError } from 'axios'\nimport { jwtDecode } from 'jwt-decode'\nimport Cookies from 'js-cookie'\nimport { \n  AuthConfig, \n  AuthTokens, \n  LoginCredentials, \n  RegisterData, \n  User, \n  AuthError \n} from './types'\n\nexport class AuthClient {\n  private api: AxiosInstance\n  private config: AuthConfig\n  private refreshPromise: Promise<AuthTokens> | null = null\n\n  constructor(config: AuthConfig) {\n    this.config = {\n      authEndpoint: '/auth',\n      tokenKey: 'access_token',\n      refreshTokenKey: 'refresh_token',\n      userKey: 'user',\n      enableAutoRefresh: true,\n      refreshInterval: 10 * 60 * 1000, // 10 minutes\n      storage: 'localStorage',\n      ...config\n    }\n\n    this.api = axios.create({\n      baseURL: config.apiBaseUrl,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    })\n\n    this.setupInterceptors()\n  }\n\n  private setupInterceptors() {\n    // Request interceptor to add auth token\n    this.api.interceptors.request.use(\n      (config) => {\n        const token = this.getAccessToken()\n        if (token && !config.headers['skipAuth']) {\n          config.headers['Authorization'] = `Bearer ${token}`\n        }\n        return config\n      },\n      (error) => Promise.reject(error)\n    )\n\n    // Response interceptor to handle auth errors\n    this.api.interceptors.response.use(\n      (response) => response,\n      async (error: AxiosError) => {\n        const originalRequest = error.config as any\n\n        if (error.response?.status === 401 && !originalRequest._retry) {\n          originalRequest._retry = true\n\n          try {\n            await this.refreshTokens()\n            const token = this.getAccessToken()\n            if (token) {\n              originalRequest.headers['Authorization'] = `Bearer ${token}`\n              return this.api(originalRequest)\n            }\n          } catch (refreshError) {\n            this.config.onAuthError?.(new AuthError(\n              'Session expired. Please login again.',\n              'SESSION_EXPIRED',\n              401\n            ))\n            this.clearAuth()\n            throw refreshError\n          }\n        }\n\n        throw this.handleApiError(error)\n      }\n    )\n  }\n\n  private handleApiError(error: AxiosError): AuthError {\n    if (!error.response) {\n      return new AuthError('Network error', 'NETWORK_ERROR')\n    }\n\n    const { status, data } = error.response as any\n    const message = data?.message || 'An error occurred'\n    const code = data?.code || 'UNKNOWN_ERROR'\n\n    return new AuthError(message, code, status, data)\n  }\n\n  // Storage methods\n  private getStorage() {\n    switch (this.config.storage) {\n      case 'sessionStorage':\n        return sessionStorage\n      case 'cookie':\n        return {\n          getItem: (key: string) => Cookies.get(key) || null,\n          setItem: (key: string, value: string) => {\n            Cookies.set(key, value, this.config.cookieOptions)\n          },\n          removeItem: (key: string) => Cookies.remove(key)\n        }\n      default:\n        return localStorage\n    }\n  }\n\n  private setTokens(tokens: AuthTokens) {\n    const storage = this.getStorage()\n    storage.setItem(this.config.tokenKey!, tokens.accessToken)\n    storage.setItem(this.config.refreshTokenKey!, tokens.refreshToken)\n  }\n\n  private getAccessToken(): string | null {\n    return this.getStorage().getItem(this.config.tokenKey!)\n  }\n\n  private getRefreshToken(): string | null {\n    return this.getStorage().getItem(this.config.refreshTokenKey!)\n  }\n\n  private setUser(user: User) {\n    const storage = this.getStorage()\n    storage.setItem(this.config.userKey!, JSON.stringify(user))\n  }\n\n  private getUser(): User | null {\n    const storage = this.getStorage()\n    const userStr = storage.getItem(this.config.userKey!)\n    if (userStr) {\n      try {\n        return JSON.parse(userStr)\n      } catch {\n        return null\n      }\n    }\n    return null\n  }\n\n  private clearAuth() {\n    const storage = this.getStorage()\n    storage.removeItem(this.config.tokenKey!)\n    storage.removeItem(this.config.refreshTokenKey!)\n    storage.removeItem(this.config.userKey!)\n  }\n\n  // Auth methods\n  async login(credentials: LoginCredentials): Promise<{ user: User; tokens: AuthTokens }> {\n    const response = await this.api.post(`${this.config.authEndpoint}/login`, credentials, {\n      headers: { skipAuth: true }\n    })\n\n    const { user, accessToken, refreshToken, expiresIn } = response.data\n\n    const tokens: AuthTokens = {\n      accessToken,\n      refreshToken,\n      expiresIn,\n      tokenType: 'Bearer'\n    }\n\n    this.setTokens(tokens)\n    this.setUser(user)\n\n    return { user, tokens }\n  }\n\n  async register(data: RegisterData): Promise<{ user: User; tokens: AuthTokens }> {\n    const response = await this.api.post(`${this.config.authEndpoint}/register`, data, {\n      headers: { skipAuth: true }\n    })\n\n    const { user, accessToken, refreshToken, expiresIn } = response.data\n\n    const tokens: AuthTokens = {\n      accessToken,\n      refreshToken,\n      expiresIn,\n      tokenType: 'Bearer'\n    }\n\n    this.setTokens(tokens)\n    this.setUser(user)\n\n    return { user, tokens }\n  }\n\n  async logout(): Promise<void> {\n    try {\n      await this.api.post(`${this.config.authEndpoint}/logout`)\n    } catch (error) {\n      // Even if logout fails, clear local auth\n      console.error('Logout API failed:', error)\n    } finally {\n      this.clearAuth()\n    }\n  }\n\n  async refreshTokens(): Promise<AuthTokens> {\n    // Prevent multiple simultaneous refresh attempts\n    if (this.refreshPromise) {\n      return this.refreshPromise\n    }\n\n    const refreshToken = this.getRefreshToken()\n    if (!refreshToken) {\n      throw new AuthError('No refresh token available', 'NO_REFRESH_TOKEN')\n    }\n\n    this.refreshPromise = this.api\n      .post(\n        `${this.config.authEndpoint}/refresh`,\n        { refreshToken },\n        { headers: { skipAuth: true } }\n      )\n      .then((response) => {\n        const { accessToken, refreshToken: newRefreshToken, expiresIn } = response.data\n        \n        const tokens: AuthTokens = {\n          accessToken,\n          refreshToken: newRefreshToken,\n          expiresIn,\n          tokenType: 'Bearer'\n        }\n\n        this.setTokens(tokens)\n        this.config.onTokenRefresh?.(tokens)\n        \n        return tokens\n      })\n      .finally(() => {\n        this.refreshPromise = null\n      })\n\n    return this.refreshPromise\n  }\n\n  async getCurrentUser(): Promise<User> {\n    const response = await this.api.get(`${this.config.authEndpoint}/me`)\n    const user = response.data\n    this.setUser(user)\n    return user\n  }\n\n  async updateProfile(data: Partial<User>): Promise<User> {\n    const response = await this.api.patch(`${this.config.authEndpoint}/profile`, data)\n    const user = response.data\n    this.setUser(user)\n    return user\n  }\n\n  async changePassword(currentPassword: string, newPassword: string): Promise<void> {\n    await this.api.post(`${this.config.authEndpoint}/change-password`, {\n      currentPassword,\n      newPassword\n    })\n  }\n\n  async requestPasswordReset(email: string): Promise<void> {\n    await this.api.post(`${this.config.authEndpoint}/forgot-password`, { email }, {\n      headers: { skipAuth: true }\n    })\n  }\n\n  async resetPassword(token: string, newPassword: string): Promise<void> {\n    await this.api.post(`${this.config.authEndpoint}/reset-password`, {\n      token,\n      newPassword\n    }, {\n      headers: { skipAuth: true }\n    })\n  }\n\n  // Token validation\n  isTokenExpired(token?: string): boolean {\n    const tokenToCheck = token || this.getAccessToken()\n    if (!tokenToCheck) return true\n\n    try {\n      const decoded = jwtDecode<{ exp: number }>(tokenToCheck)\n      return decoded.exp * 1000 < Date.now()\n    } catch {\n      return true\n    }\n  }\n\n  // Session management\n  async validateSession(): Promise<boolean> {\n    try {\n      const token = this.getAccessToken()\n      if (!token || this.isTokenExpired(token)) {\n        // Try to refresh\n        await this.refreshTokens()\n      }\n      \n      // Verify by getting current user\n      await this.getCurrentUser()\n      return true\n    } catch {\n      return false\n    }\n  }\n\n  // Utility methods\n  getStoredUser(): User | null {\n    return this.getUser()\n  }\n\n  getStoredTokens(): AuthTokens | null {\n    const accessToken = this.getAccessToken()\n    const refreshToken = this.getRefreshToken()\n    \n    if (!accessToken || !refreshToken) {\n      return null\n    }\n\n    return {\n      accessToken,\n      refreshToken,\n      tokenType: 'Bearer'\n    }\n  }\n\n  clearSession(): void {\n    this.clearAuth()\n  }\n}", "export interface User {\n  id: string\n  email: string\n  firstName: string\n  lastName: string\n  roles: string[]\n  permissions: string[]\n  department?: string\n  organizationId?: string\n  avatar?: string\n  metadata?: Record<string, any>\n}\n\nexport interface AuthTokens {\n  accessToken: string\n  refreshToken: string\n  expiresIn?: number\n  tokenType?: string\n}\n\nexport interface LoginCredentials {\n  email: string\n  password: string\n  rememberMe?: boolean\n}\n\nexport interface RegisterData {\n  email: string\n  password: string\n  firstName: string\n  lastName: string\n  organizationId?: string\n  department?: string\n}\n\nexport interface AuthConfig {\n  apiBaseUrl: string\n  authEndpoint?: string\n  tokenKey?: string\n  refreshTokenKey?: string\n  userKey?: string\n  enableAutoRefresh?: boolean\n  refreshInterval?: number\n  onAuthError?: (error: AuthError) => void\n  onTokenRefresh?: (tokens: AuthTokens) => void\n  storage?: 'localStorage' | 'sessionStorage' | 'cookie'\n  cookieOptions?: {\n    domain?: string\n    secure?: boolean\n    sameSite?: 'strict' | 'lax' | 'none'\n    httpOnly?: boolean\n  }\n}\n\nexport interface AuthState {\n  user: User | null\n  tokens: AuthTokens | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  error: AuthError | null\n}\n\nexport class AuthError extends Error {\n  constructor(\n    message: string,\n    public code: string,\n    public statusCode?: number,\n    public details?: any\n  ) {\n    super(message)\n    this.name = 'AuthError'\n  }\n}\n\nexport interface AuthContextValue {\n  user: User | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  error: AuthError | null\n  login: (credentials: LoginCredentials) => Promise<User>\n  logout: () => Promise<void>\n  register: (data: RegisterData) => Promise<User>\n  refreshAuth: () => Promise<void>\n  updateUser: (data: Partial<User>) => Promise<User>\n  hasRole: (role: string) => boolean\n  hasPermission: (permission: string) => boolean\n  hasAnyRole: (roles: string[]) => boolean\n  hasAllRoles: (roles: string[]) => boolean\n  hasAnyPermission: (permissions: string[]) => boolean\n  hasAllPermissions: (permissions: string[]) => boolean\n}\n\nexport interface AuthProviderProps {\n  children: React.ReactNode\n  config: AuthConfig\n  initialState?: Partial<AuthState>\n  onAuthStateChange?: (state: AuthState) => void\n}", "import { useEffect } from 'react'\nimport { useNavigate, useLocation } from '@tanstack/react-router'\nimport { useAuthContext } from './auth-context'\n\nexport { useAuthContext as useAuth } from './auth-context'\n\n// Hook to protect routes\nexport function useRequireAuth(options?: {\n  redirectTo?: string\n  roles?: string[]\n  permissions?: string[]\n}) {\n  const { \n    isAuthenticated, \n    isLoading, \n    user,\n    hasAllRoles,\n    hasAllPermissions \n  } = useAuthContext()\n  const navigate = useNavigate()\n  const location = useLocation()\n\n  useEffect(() => {\n    if (isLoading) return\n\n    const redirectTo = options?.redirectTo || '/login'\n    \n    if (!isAuthenticated) {\n      navigate({ to: redirectTo })\n      return\n    }\n\n    // Check role requirements\n    if (options?.roles && !hasAllRoles(options.roles)) {\n      navigate({ to: '/unauthorized' })\n      return\n    }\n\n    // Check permission requirements\n    if (options?.permissions && !hasAllPermissions(options.permissions)) {\n      navigate({ to: '/unauthorized' })\n      return\n    }\n  }, [\n    isAuthenticated,\n    isLoading,\n    user,\n    navigate,\n    location,\n    options,\n    hasAllRoles,\n    hasAllPermissions\n  ])\n\n  return {\n    isAuthenticated,\n    isLoading,\n    user,\n    isAuthorized: true\n  }\n}\n\n// Hook to redirect if already authenticated\nexport function useRedirectIfAuthenticated(redirectTo = '/dashboard') {\n  const { isAuthenticated, isLoading } = useAuthContext()\n  const navigate = useNavigate()\n  const location = useLocation()\n\n  useEffect(() => {\n    if (!isLoading && isAuthenticated) {\n      const from = (location.state as any)?.from || redirectTo\n      navigate({ to: from })\n    }\n  }, [isAuthenticated, isLoading, navigate, location, redirectTo])\n}\n\n// Hook to check permissions\nexport function usePermissions() {\n  const {\n    user,\n    hasRole,\n    hasPermission,\n    hasAnyRole,\n    hasAllRoles,\n    hasAnyPermission,\n    hasAllPermissions\n  } = useAuthContext()\n\n  return {\n    user,\n    roles: user?.roles || [],\n    permissions: user?.permissions || [],\n    hasRole,\n    hasPermission,\n    hasAnyRole,\n    hasAllRoles,\n    hasAnyPermission,\n    hasAllPermissions,\n    isAdmin: hasRole('admin'),\n    isManager: hasAnyRole(['admin', 'manager']),\n    isUser: hasRole('user')\n  }\n}\n\n// Hook for conditional rendering based on auth\nexport function useAuthGuard(\n  condition: 'authenticated' | 'unauthenticated' | 'loading'\n): boolean {\n  const { isAuthenticated, isLoading } = useAuthContext()\n\n  switch (condition) {\n    case 'authenticated':\n      return isAuthenticated && !isLoading\n    case 'unauthenticated':\n      return !isAuthenticated && !isLoading\n    case 'loading':\n      return isLoading\n    default:\n      return false\n  }\n}\n\n// Hook to handle auth errors\nexport function useAuthError(handler?: (error: Error) => void) {\n  const { error } = useAuthContext()\n\n  useEffect(() => {\n    if (error && handler) {\n      handler(error)\n    }\n  }, [error, handler])\n\n  return error\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAA,gBAAgE;;;ACAhE,mBAAiD;AAGjD,IAAM,kBAAc,4BAA4C,MAAS;AAElE,IAAM,iBAAiB,MAAwB;AACpD,QAAM,cAAU,yBAAW,WAAW;AACtC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACtE;AACA,SAAO;AACT;;;ACXA,mBAAiD;AACjD,wBAA0B;AAC1B,uBAAoB;;;AC4Db,IAAM,YAAN,cAAwB,MAAM;AAAA,EACnC,YACE,SACO,MACA,YACA,SACP;AACA,UAAM,OAAO;AAJN;AACA;AACA;AAGP,SAAK,OAAO;AAAA,EACd;AACF;;;AD5DO,IAAM,aAAN,MAAiB;AAAA,EAKtB,YAAY,QAAoB;AAFhC,SAAQ,iBAA6C;AAGnD,SAAK,SAAS;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,mBAAmB;AAAA,MACnB,iBAAiB,KAAK,KAAK;AAAA;AAAA,MAC3B,SAAS;AAAA,MACT,GAAG;AAAA,IACL;AAEA,SAAK,MAAM,aAAAC,QAAM,OAAO;AAAA,MACtB,SAAS,OAAO;AAAA,MAChB,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAED,SAAK,kBAAkB;AAAA,EACzB;AAAA,EAEQ,oBAAoB;AAE1B,SAAK,IAAI,aAAa,QAAQ;AAAA,MAC5B,CAAC,WAAW;AACV,cAAM,QAAQ,KAAK,eAAe;AAClC,YAAI,SAAS,CAAC,OAAO,QAAQ,UAAU,GAAG;AACxC,iBAAO,QAAQ,eAAe,IAAI,UAAU,KAAK;AAAA,QACnD;AACA,eAAO;AAAA,MACT;AAAA,MACA,CAAC,UAAU,QAAQ,OAAO,KAAK;AAAA,IACjC;AAGA,SAAK,IAAI,aAAa,SAAS;AAAA,MAC7B,CAAC,aAAa;AAAA,MACd,OAAO,UAAsB;AAC3B,cAAM,kBAAkB,MAAM;AAE9B,YAAI,MAAM,UAAU,WAAW,OAAO,CAAC,gBAAgB,QAAQ;AAC7D,0BAAgB,SAAS;AAEzB,cAAI;AACF,kBAAM,KAAK,cAAc;AACzB,kBAAM,QAAQ,KAAK,eAAe;AAClC,gBAAI,OAAO;AACT,8BAAgB,QAAQ,eAAe,IAAI,UAAU,KAAK;AAC1D,qBAAO,KAAK,IAAI,eAAe;AAAA,YACjC;AAAA,UACF,SAAS,cAAc;AACrB,iBAAK,OAAO,cAAc,IAAI;AAAA,cAC5B;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AACD,iBAAK,UAAU;AACf,kBAAM;AAAA,UACR;AAAA,QACF;AAEA,cAAM,KAAK,eAAe,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,eAAe,OAA8B;AACnD,QAAI,CAAC,MAAM,UAAU;AACnB,aAAO,IAAI,UAAU,iBAAiB,eAAe;AAAA,IACvD;AAEA,UAAM,EAAE,QAAQ,KAAK,IAAI,MAAM;AAC/B,UAAM,UAAU,MAAM,WAAW;AACjC,UAAM,OAAO,MAAM,QAAQ;AAE3B,WAAO,IAAI,UAAU,SAAS,MAAM,QAAQ,IAAI;AAAA,EAClD;AAAA;AAAA,EAGQ,aAAa;AACnB,YAAQ,KAAK,OAAO,SAAS;AAAA,MAC3B,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,UACL,SAAS,CAAC,QAAgB,iBAAAC,QAAQ,IAAI,GAAG,KAAK;AAAA,UAC9C,SAAS,CAAC,KAAa,UAAkB;AACvC,6BAAAA,QAAQ,IAAI,KAAK,OAAO,KAAK,OAAO,aAAa;AAAA,UACnD;AAAA,UACA,YAAY,CAAC,QAAgB,iBAAAA,QAAQ,OAAO,GAAG;AAAA,QACjD;AAAA,MACF;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EAEQ,UAAU,QAAoB;AACpC,UAAM,UAAU,KAAK,WAAW;AAChC,YAAQ,QAAQ,KAAK,OAAO,UAAW,OAAO,WAAW;AACzD,YAAQ,QAAQ,KAAK,OAAO,iBAAkB,OAAO,YAAY;AAAA,EACnE;AAAA,EAEQ,iBAAgC;AACtC,WAAO,KAAK,WAAW,EAAE,QAAQ,KAAK,OAAO,QAAS;AAAA,EACxD;AAAA,EAEQ,kBAAiC;AACvC,WAAO,KAAK,WAAW,EAAE,QAAQ,KAAK,OAAO,eAAgB;AAAA,EAC/D;AAAA,EAEQ,QAAQ,MAAY;AAC1B,UAAM,UAAU,KAAK,WAAW;AAChC,YAAQ,QAAQ,KAAK,OAAO,SAAU,KAAK,UAAU,IAAI,CAAC;AAAA,EAC5D;AAAA,EAEQ,UAAuB;AAC7B,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM,UAAU,QAAQ,QAAQ,KAAK,OAAO,OAAQ;AACpD,QAAI,SAAS;AACX,UAAI;AACF,eAAO,KAAK,MAAM,OAAO;AAAA,MAC3B,QAAQ;AACN,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEQ,YAAY;AAClB,UAAM,UAAU,KAAK,WAAW;AAChC,YAAQ,WAAW,KAAK,OAAO,QAAS;AACxC,YAAQ,WAAW,KAAK,OAAO,eAAgB;AAC/C,YAAQ,WAAW,KAAK,OAAO,OAAQ;AAAA,EACzC;AAAA;AAAA,EAGA,MAAM,MAAM,aAA4E;AACtF,UAAM,WAAW,MAAM,KAAK,IAAI,KAAK,GAAG,KAAK,OAAO,YAAY,UAAU,aAAa;AAAA,MACrF,SAAS,EAAE,UAAU,KAAK;AAAA,IAC5B,CAAC;AAED,UAAM,EAAE,MAAM,aAAa,cAAc,UAAU,IAAI,SAAS;AAEhE,UAAM,SAAqB;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb;AAEA,SAAK,UAAU,MAAM;AACrB,SAAK,QAAQ,IAAI;AAEjB,WAAO,EAAE,MAAM,OAAO;AAAA,EACxB;AAAA,EAEA,MAAM,SAAS,MAAiE;AAC9E,UAAM,WAAW,MAAM,KAAK,IAAI,KAAK,GAAG,KAAK,OAAO,YAAY,aAAa,MAAM;AAAA,MACjF,SAAS,EAAE,UAAU,KAAK;AAAA,IAC5B,CAAC;AAED,UAAM,EAAE,MAAM,aAAa,cAAc,UAAU,IAAI,SAAS;AAEhE,UAAM,SAAqB;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb;AAEA,SAAK,UAAU,MAAM;AACrB,SAAK,QAAQ,IAAI;AAEjB,WAAO,EAAE,MAAM,OAAO;AAAA,EACxB;AAAA,EAEA,MAAM,SAAwB;AAC5B,QAAI;AACF,YAAM,KAAK,IAAI,KAAK,GAAG,KAAK,OAAO,YAAY,SAAS;AAAA,IAC1D,SAAS,OAAO;AAEd,cAAQ,MAAM,sBAAsB,KAAK;AAAA,IAC3C,UAAE;AACA,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EAEA,MAAM,gBAAqC;AAEzC,QAAI,KAAK,gBAAgB;AACvB,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,CAAC,cAAc;AACjB,YAAM,IAAI,UAAU,8BAA8B,kBAAkB;AAAA,IACtE;AAEA,SAAK,iBAAiB,KAAK,IACxB;AAAA,MACC,GAAG,KAAK,OAAO,YAAY;AAAA,MAC3B,EAAE,aAAa;AAAA,MACf,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE;AAAA,IAChC,EACC,KAAK,CAAC,aAAa;AAClB,YAAM,EAAE,aAAa,cAAc,iBAAiB,UAAU,IAAI,SAAS;AAE3E,YAAM,SAAqB;AAAA,QACzB;AAAA,QACA,cAAc;AAAA,QACd;AAAA,QACA,WAAW;AAAA,MACb;AAEA,WAAK,UAAU,MAAM;AACrB,WAAK,OAAO,iBAAiB,MAAM;AAEnC,aAAO;AAAA,IACT,CAAC,EACA,QAAQ,MAAM;AACb,WAAK,iBAAiB;AAAA,IACxB,CAAC;AAEH,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAM,iBAAgC;AACpC,UAAM,WAAW,MAAM,KAAK,IAAI,IAAI,GAAG,KAAK,OAAO,YAAY,KAAK;AACpE,UAAM,OAAO,SAAS;AACtB,SAAK,QAAQ,IAAI;AACjB,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,cAAc,MAAoC;AACtD,UAAM,WAAW,MAAM,KAAK,IAAI,MAAM,GAAG,KAAK,OAAO,YAAY,YAAY,IAAI;AACjF,UAAM,OAAO,SAAS;AACtB,SAAK,QAAQ,IAAI;AACjB,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,eAAe,iBAAyB,aAAoC;AAChF,UAAM,KAAK,IAAI,KAAK,GAAG,KAAK,OAAO,YAAY,oBAAoB;AAAA,MACjE;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,qBAAqB,OAA8B;AACvD,UAAM,KAAK,IAAI,KAAK,GAAG,KAAK,OAAO,YAAY,oBAAoB,EAAE,MAAM,GAAG;AAAA,MAC5E,SAAS,EAAE,UAAU,KAAK;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,cAAc,OAAe,aAAoC;AACrE,UAAM,KAAK,IAAI,KAAK,GAAG,KAAK,OAAO,YAAY,mBAAmB;AAAA,MAChE;AAAA,MACA;AAAA,IACF,GAAG;AAAA,MACD,SAAS,EAAE,UAAU,KAAK;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,eAAe,OAAyB;AACtC,UAAM,eAAe,SAAS,KAAK,eAAe;AAClD,QAAI,CAAC,aAAc,QAAO;AAE1B,QAAI;AACF,YAAM,cAAU,6BAA2B,YAAY;AACvD,aAAO,QAAQ,MAAM,MAAO,KAAK,IAAI;AAAA,IACvC,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,kBAAoC;AACxC,QAAI;AACF,YAAM,QAAQ,KAAK,eAAe;AAClC,UAAI,CAAC,SAAS,KAAK,eAAe,KAAK,GAAG;AAExC,cAAM,KAAK,cAAc;AAAA,MAC3B;AAGA,YAAM,KAAK,eAAe;AAC1B,aAAO;AAAA,IACT,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA,EAGA,gBAA6B;AAC3B,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,kBAAqC;AACnC,UAAM,cAAc,KAAK,eAAe;AACxC,UAAM,eAAe,KAAK,gBAAgB;AAE1C,QAAI,CAAC,eAAe,CAAC,cAAc;AACjC,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EAEA,eAAqB;AACnB,SAAK,UAAU;AAAA,EACjB;AACF;;;AF/TO,IAAM,eAA4C,CAAC;AAAA,EACxD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,iBAAa,sBAAmB,IAAI,WAAW,MAAM,CAAC;AAE5D,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAoB;AAAA,IAC5C,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,OAAO;AAAA,IACP,GAAG;AAAA,EACL,CAAC;AAGD,+BAAU,MAAM;AACd,wBAAoB,KAAK;AAAA,EAC3B,GAAG,CAAC,OAAO,iBAAiB,CAAC;AAG7B,+BAAU,MAAM;AACd,UAAM,WAAW,YAAY;AAC3B,UAAI;AACF,cAAM,aAAa,WAAW,QAAQ,cAAc;AACpD,cAAM,eAAe,WAAW,QAAQ,gBAAgB;AAExD,YAAI,cAAc,cAAc;AAE9B,gBAAM,UAAU,MAAM,WAAW,QAAQ,gBAAgB;AAEzD,cAAI,SAAS;AACX,qBAAS;AAAA,cACP,MAAM,WAAW,QAAQ,cAAc;AAAA,cACvC,QAAQ,WAAW,QAAQ,gBAAgB;AAAA,cAC3C,iBAAiB;AAAA,cACjB,WAAW;AAAA,cACX,OAAO;AAAA,YACT,CAAC;AAAA,UACH,OAAO;AACL,uBAAW,QAAQ,aAAa;AAChC,qBAAS;AAAA,cACP,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,iBAAiB;AAAA,cACjB,WAAW;AAAA,cACX,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AACL,mBAAS,WAAS,EAAE,GAAG,MAAM,WAAW,MAAM,EAAE;AAAA,QAClD;AAAA,MACF,SAAS,OAAO;AACd,iBAAS;AAAA,UACP,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,iBAAiB;AAAA,UACjB,WAAW;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,aAAS;AAAA,EACX,GAAG,CAAC,CAAC;AAGL,+BAAU,MAAM;AACd,QAAI,CAAC,OAAO,qBAAqB,CAAC,MAAM,iBAAiB;AACvD;AAAA,IACF;AAEA,UAAM,WAAW,YAAY,YAAY;AACvC,UAAI;AACF,cAAM,SAAS,MAAM,WAAW,QAAQ,cAAc;AACtD,iBAAS,WAAS,EAAE,GAAG,MAAM,OAAO,EAAE;AAAA,MACxC,SAAS,OAAO;AACd,gBAAQ,MAAM,wBAAwB,KAAK;AAAA,MAC7C;AAAA,IACF,GAAG,OAAO,mBAAmB,KAAK,KAAK,GAAI;AAE3C,WAAO,MAAM,cAAc,QAAQ;AAAA,EACrC,GAAG,CAAC,MAAM,iBAAiB,OAAO,mBAAmB,OAAO,eAAe,CAAC;AAE5E,QAAM,YAAQ,2BAAY,OAAO,gBAAiD;AAChF,aAAS,WAAS,EAAE,GAAG,MAAM,WAAW,MAAM,OAAO,KAAK,EAAE;AAE5D,QAAI;AACF,YAAM,EAAE,MAAM,OAAO,IAAI,MAAM,WAAW,QAAQ,MAAM,WAAW;AAEnE,eAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AAED,aAAO;AAAA,IACT,SAAS,OAAO;AACd,YAAM,YAAY;AAClB,eAAS;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AACD,YAAM;AAAA,IACR;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,eAAW,2BAAY,OAAO,SAAsC;AACxE,aAAS,WAAS,EAAE,GAAG,MAAM,WAAW,MAAM,OAAO,KAAK,EAAE;AAE5D,QAAI;AACF,YAAM,EAAE,MAAM,OAAO,IAAI,MAAM,WAAW,QAAQ,SAAS,IAAI;AAE/D,eAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AAED,aAAO;AAAA,IACT,SAAS,OAAO;AACd,YAAM,YAAY;AAClB,eAAS;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AACD,YAAM;AAAA,IACR;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,aAAS,2BAAY,YAA2B;AACpD,aAAS,WAAS,EAAE,GAAG,MAAM,WAAW,KAAK,EAAE;AAE/C,QAAI;AACF,YAAM,WAAW,QAAQ,OAAO;AAAA,IAClC,SAAS,OAAO;AACd,cAAQ,MAAM,iBAAiB,KAAK;AAAA,IACtC,UAAE;AACA,eAAS;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,kBAAc,2BAAY,YAA2B;AACzD,QAAI;AACF,YAAM,SAAS,MAAM,WAAW,QAAQ,cAAc;AACtD,YAAM,OAAO,MAAM,WAAW,QAAQ,eAAe;AAErD,eAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AAAA,IACH,SAAS,OAAO;AACd,YAAM,YAAY;AAClB,eAAS;AAAA,QACP,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AACD,YAAM;AAAA,IACR;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,QAAM,iBAAa,2BAAY,OAAO,SAAuC;AAC3E,QAAI,CAAC,MAAM,MAAM;AACf,YAAM,IAAI,UAAU,yBAAyB,SAAS;AAAA,IACxD;AAEA,QAAI;AACF,YAAM,cAAc,MAAM,WAAW,QAAQ,cAAc,IAAI;AAC/D,eAAS,WAAS,EAAE,GAAG,MAAM,MAAM,YAAY,EAAE;AACjD,aAAO;AAAA,IACT,SAAS,OAAO;AACd,YAAM;AAAA,IACR;AAAA,EACF,GAAG,CAAC,MAAM,IAAI,CAAC;AAGf,QAAM,cAAU,2BAAY,CAAC,SAA0B;AACrD,WAAO,MAAM,MAAM,MAAM,SAAS,IAAI,KAAK;AAAA,EAC7C,GAAG,CAAC,MAAM,IAAI,CAAC;AAEf,QAAM,oBAAgB,2BAAY,CAAC,eAAgC;AACjE,WAAO,MAAM,MAAM,YAAY,SAAS,UAAU,KAAK;AAAA,EACzD,GAAG,CAAC,MAAM,IAAI,CAAC;AAEf,QAAM,iBAAa,2BAAY,CAAC,UAA6B;AAC3D,WAAO,MAAM,KAAK,UAAQ,QAAQ,IAAI,CAAC;AAAA,EACzC,GAAG,CAAC,OAAO,CAAC;AAEZ,QAAM,kBAAc,2BAAY,CAAC,UAA6B;AAC5D,WAAO,MAAM,MAAM,UAAQ,QAAQ,IAAI,CAAC;AAAA,EAC1C,GAAG,CAAC,OAAO,CAAC;AAEZ,QAAM,uBAAmB,2BAAY,CAAC,gBAAmC;AACvE,WAAO,YAAY,KAAK,gBAAc,cAAc,UAAU,CAAC;AAAA,EACjE,GAAG,CAAC,aAAa,CAAC;AAElB,QAAM,wBAAoB,2BAAY,CAAC,gBAAmC;AACxE,WAAO,YAAY,MAAM,gBAAc,cAAc,UAAU,CAAC;AAAA,EAClE,GAAG,CAAC,aAAa,CAAC;AAElB,QAAM,eAAiC;AAAA,IACrC,MAAM,MAAM;AAAA,IACZ,iBAAiB,MAAM;AAAA,IACvB,WAAW,MAAM;AAAA,IACjB,OAAO,MAAM;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SACE,8BAAAC,QAAA,cAAC,YAAY,UAAZ,EAAqB,OAAO,gBAC1B,QACH;AAEJ;;;AIrQA,IAAAC,gBAA0B;AAC1B,0BAAyC;", "names": ["import_react", "axios", "Cookies", "React", "import_react"]}