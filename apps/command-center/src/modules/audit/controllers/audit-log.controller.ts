import {
  Controller,
  Get,
  Post,
  Query,
  Param,
  Body,
  UseGuards,
  UseInterceptors,
  ValidationPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsDateString } from 'class-validator';

import { AuditLogService } from '../services/audit-log.service';
import { ActivityTrackerService } from '../services/activity-tracker.service';
import { AuditLogInterceptor } from '../interceptors/audit-log.interceptor';

export class GetAuditLogsDto {
  @IsOptional()
  @IsString()
  userId?: string;

  @IsOptional()
  @IsString()
  action?: string;

  @IsOptional()
  @IsString()
  severity?: string;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  @IsOptional()
  @IsNumber()
  limit?: number = 100;

  @IsOptional()
  @IsNumber()
  offset?: number = 0;
}

export class TrackActivityDto {
  @IsString()
  userId: string;

  @IsString()
  action: string;

  @IsString()
  resource: string;

  @IsOptional()
  metadata?: Record<string, any>;

  @IsOptional()
  @IsString()
  ipAddress?: string;

  @IsOptional()
  @IsString()
  userAgent?: string;
}

@ApiTags('Audit Logs')
@ApiBearerAuth()
@Controller('audit')
@UseInterceptors(AuditLogInterceptor)
export class AuditLogController {
  constructor(
    private readonly auditLogService: AuditLogService,
    private readonly activityTrackerService: ActivityTrackerService,
  ) {}

  @Get('logs')
  @ApiOperation({ summary: 'Get audit logs' })
  @ApiResponse({ status: 200, description: 'Audit logs retrieved successfully' })
  async getAuditLogs(@Query(ValidationPipe) query: GetAuditLogsDto) {
    const { userId, action, severity, startDate, endDate, limit, offset } = query;

    const filters: any = {};
    if (userId) filters.userId = userId;
    if (action) filters.action = action;
    if (severity) filters.severity = severity;

    const dateRange = startDate && endDate ? {
      start: new Date(startDate),
      end: new Date(endDate),
    } : undefined;

    const logs = await this.auditLogService.getAuditLogs({
      filters,
      dateRange,
      limit,
      offset,
    });

    return {
      success: true,
      data: logs,
      total: logs.length,
    };
  }

  @Get('logs/:id')
  @ApiOperation({ summary: 'Get audit log by ID' })
  @ApiResponse({ status: 200, description: 'Audit log retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Audit log not found' })
  async getAuditLogById(@Param('id') id: string) {
    const log = await this.auditLogService.getAuditLogById(id);
    
    if (!log) {
      return {
        success: false,
        message: 'Audit log not found',
      };
    }

    return {
      success: true,
      data: log,
    };
  }

  @Post('activity')
  @ApiOperation({ summary: 'Track user activity' })
  @ApiResponse({ status: 201, description: 'Activity tracked successfully' })
  async trackActivity(@Body(ValidationPipe) activityDto: TrackActivityDto) {
    const activity = await this.activityTrackerService.trackUserActivity(activityDto);

    return {
      success: true,
      data: activity,
      message: 'Activity tracked successfully',
    };
  }

  @Get('activities/user/:userId')
  @ApiOperation({ summary: 'Get user activities' })
  @ApiResponse({ status: 200, description: 'User activities retrieved successfully' })
  async getUserActivities(
    @Param('userId') userId: string,
    @Query('limit') limit?: number,
  ) {
    const activities = await this.activityTrackerService.getUserActivities(
      userId,
      limit || 100,
    );

    return {
      success: true,
      data: activities,
      total: activities.length,
    };
  }

  @Get('activities/resource/:resource')
  @ApiOperation({ summary: 'Get resource activities' })
  @ApiResponse({ status: 200, description: 'Resource activities retrieved successfully' })
  async getResourceActivities(
    @Param('resource') resource: string,
    @Query('limit') limit?: number,
  ) {
    const activities = await this.activityTrackerService.getResourceActivities(
      resource,
      limit || 100,
    );

    return {
      success: true,
      data: activities,
      total: activities.length,
    };
  }

  @Get('summary')
  @ApiOperation({ summary: 'Get audit summary' })
  @ApiResponse({ status: 200, description: 'Audit summary retrieved successfully' })
  async getAuditSummary(@Query('userId') userId?: string) {
    const summary = await this.activityTrackerService.generateActivitySummary(userId);

    return {
      success: true,
      data: summary,
    };
  }

  @Get('statistics')
  @ApiOperation({ summary: 'Get audit statistics' })
  @ApiResponse({ status: 200, description: 'Audit statistics retrieved successfully' })
  async getAuditStatistics(@Query('days') days?: number) {
    const daysToAnalyze = days || 30;
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - daysToAnalyze);

    const activities = await this.activityTrackerService.getActivitiesInRange(startDate, endDate);
    
    const statistics = {
      totalActivities: activities.length,
      dailyAverage: Math.round(activities.length / daysToAnalyze),
      periodStart: startDate,
      periodEnd: endDate,
      activityDistribution: activities.reduce((acc, activity) => {
        const date = activity.timestamp.toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };

    return {
      success: true,
      data: statistics,
    };
  }
}