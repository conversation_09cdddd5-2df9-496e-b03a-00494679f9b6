import React, { createContext, useContext, useEffect, useState } from 'react';
import type { ModuleFederationRuntime, RuntimeConfig, FeatureFlag } from '../types';
import { loadRemoteScript, isRemoteAvailable } from '../utils';

interface ModuleFederationContextValue {
  runtime: ModuleFederationRuntime;
  config: RuntimeConfig;
  featureFlags: Record<string, FeatureFlag>;
  isLoading: boolean;
  error: Error | null;
}

const ModuleFederationContext = createContext<ModuleFederationContextValue | null>(null);

/**
 * Provider for Module Federation runtime configuration
 */
export function ModuleFederationProvider({ 
  children,
  config: initialConfig,
}: { 
  children: React.ReactNode;
  config?: Partial<RuntimeConfig>;
}) {
  const [config, setConfig] = useState<RuntimeConfig>({
    remotes: {},
    featureFlags: {},
    environment: 'development',
    ...initialConfig,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Module Federation Runtime implementation
  const runtime: ModuleFederationRuntime = {
    async loadRemote(remoteName: string, _modulePath?: string) {
      const remoteUrl = config.remotes[remoteName];
      if (!remoteUrl) {
        throw new Error(`Remote "${remoteName}" not configured`);
      }

      try {
        await loadRemoteScript(remoteUrl, remoteName);
        // @ts-ignore
        const container = window[remoteName];
        if (!container) {
          throw new Error(`Remote container "${remoteName}" not found after loading script`);
        }
        return container;
      } catch (error) {
        console.error(`Failed to load remote ${remoteName}:`, error);
        throw error;
      }
    },

    registerRemote(name: string, url: string) {
      setConfig(prev => ({
        ...prev,
        remotes: { ...prev.remotes, [name]: url },
      }));
    },

    unregisterRemote(name: string) {
      setConfig(prev => {
        const { [name]: _, ...remotes } = prev.remotes;
        return { ...prev, remotes };
      });
    },

    getRemoteUrl(name: string) {
      return config.remotes[name];
    },

    setRemoteUrl(name: string, url: string) {
      setConfig(prev => ({
        ...prev,
        remotes: { ...prev.remotes, [name]: url },
      }));
    },

    async reloadRemote(name: string) {
      // Remove existing script
      const existingScript = document.querySelector(`script[data-remote="${name}"]`);
      if (existingScript) {
        existingScript.remove();
      }
      // Clear from window
      // @ts-ignore
      delete window[name];
      // Reload
      await runtime.loadRemote(name);
    },
  };

  // Load runtime configuration
  useEffect(() => {
    const loadRuntimeConfig = async () => {
      try {
        setIsLoading(true);
        
        // Try to load from API
        const response = await fetch('/api/config');
        if (response.ok) {
          const runtimeConfig = await response.json();
          setConfig(prev => ({ ...prev, ...runtimeConfig }));
        }
        
        // Load from window if available
        // @ts-ignore
        if (window.__LUMINAR_CONFIG__) {
          // @ts-ignore
          setConfig(prev => ({ ...prev, ...window.__LUMINAR_CONFIG__ }));
        }

        setError(null);
      } catch (err) {
        console.error('Failed to load runtime configuration:', err);
        setError(err as Error);
      } finally {
        setIsLoading(false);
      }
    };

    loadRuntimeConfig();
  }, []);

  // Health check for remotes
  useEffect(() => {
    const checkRemotes = async () => {
      for (const [name, url] of Object.entries(config.remotes)) {
        const available = await isRemoteAvailable(name);
        if (!available) {
          console.warn(`Remote ${name} is not available at ${url}`);
        }
      }
    };

    if (Object.keys(config.remotes).length > 0) {
      checkRemotes();
    }
  }, [config.remotes]);

  const value: ModuleFederationContextValue = {
    runtime,
    config,
    featureFlags: config.featureFlags || {},
    isLoading,
    error,
  };

  return (
    <ModuleFederationContext.Provider value={value}>
      {children}
    </ModuleFederationContext.Provider>
  );
}

/**
 * Hook to access Module Federation runtime
 */
export function useModuleFederation() {
  const context = useContext(ModuleFederationContext);
  if (!context) {
    throw new Error('useModuleFederation must be used within ModuleFederationProvider');
  }
  return context;
}