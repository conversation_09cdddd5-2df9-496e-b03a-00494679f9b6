import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { z } from 'zod';
import axios, { AxiosError } from 'axios';

// src/utils/index.ts
function cn(...inputs) {
  return twMerge(clsx(inputs));
}
function formatBytes(bytes, decimals = 2) {
  if (!bytes) return "0 Bytes";
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
}
function formatNumber(num) {
  if (num >= 1e6) {
    return (num / 1e6).toFixed(1) + "M";
  }
  if (num >= 1e3) {
    return (num / 1e3).toFixed(1) + "K";
  }
  return num.toString();
}
function formatCurrency(amount, currency = "USD") {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency
  }).format(amount);
}
function debounce(func, wait) {
  let timeout;
  return (...args) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
function throttle(func, limit) {
  let inThrottle;
  return (...args) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
function generateId(prefix = "") {
  const id = Math.random().toString(36).substr(2, 9);
  return prefix ? `${prefix}_${id}` : id;
}
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
function capitalize(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
function truncate(str, length) {
  if (str.length <= length) return str;
  return str.slice(0, length) + "...";
}
function isEmpty(value) {
  if (value == null) return true;
  if (typeof value === "string") return value.trim() === "";
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === "object") return Object.keys(value).length === 0;
  return false;
}
function deepClone(obj) {
  if (obj === null || typeof obj !== "object") return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map((item) => deepClone(item));
  if (typeof obj === "object") {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  return obj;
}
function get(obj, path, defaultValue) {
  const keys = path.split(".");
  let result = obj;
  for (const key of keys) {
    if (result == null || typeof result !== "object") {
      return defaultValue;
    }
    result = result[key];
  }
  return result !== void 0 ? result : defaultValue;
}
function set(obj, path, value) {
  const keys = path.split(".");
  let current = obj;
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current) || typeof current[key] !== "object") {
      current[key] = {};
    }
    current = current[key];
  }
  current[keys[keys.length - 1]] = value;
}
function omit(obj, keys) {
  const result = { ...obj };
  keys.forEach((key) => delete result[key]);
  return result;
}
function pick(obj, keys) {
  const result = {};
  keys.forEach((key) => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
}

// src/constants/index.ts
var API_CONFIG = {
  TIMEOUT: 1e4,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1e3,
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100
};
var AUTH_CONFIG = {
  SESSION_TIMEOUT: 30 * 60 * 1e3,
  // 30 minutes
  REFRESH_INTERVAL: 10 * 60 * 1e3,
  // 10 minutes
  TOKEN_STORAGE_KEY: "luminar_auth_token",
  REFRESH_TOKEN_STORAGE_KEY: "luminar_refresh_token",
  USER_STORAGE_KEY: "luminar_user",
  REMEMBER_ME_DURATION: 30 * 24 * 60 * 60 * 1e3
  // 30 days
};
var FILE_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024,
  // 10MB
  ALLOWED_IMAGE_TYPES: ["image/jpeg", "image/png", "image/gif", "image/webp"],
  ALLOWED_DOCUMENT_TYPES: [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "text/plain",
    "text/csv"
  ],
  CHUNK_SIZE: 1024 * 1024
  // 1MB chunks for large file uploads
};
var UI_CONFIG = {
  DEBOUNCE_DELAY: 300,
  THROTTLE_DELAY: 100,
  ANIMATION_DURATION: 200,
  TOAST_DURATION: 5e3,
  MODAL_Z_INDEX: 1e3,
  DROPDOWN_Z_INDEX: 999,
  TOOLTIP_Z_INDEX: 998
};
var THEME_CONFIG = {
  DEFAULT_THEME: "light",
  STORAGE_KEY: "luminar_theme",
  BREAKPOINTS: {
    sm: "640px",
    md: "768px",
    lg: "1024px",
    xl: "1280px",
    "2xl": "1536px"
  },
  COLORS: {
    primary: {
      50: "#eff6ff",
      100: "#dbeafe",
      200: "#bfdbfe",
      300: "#93c5fd",
      400: "#60a5fa",
      500: "#3b82f6",
      600: "#2563eb",
      700: "#1d4ed8",
      800: "#1e40af",
      900: "#1e3a8a"
    },
    gray: {
      50: "#f9fafb",
      100: "#f3f4f6",
      200: "#e5e7eb",
      300: "#d1d5db",
      400: "#9ca3af",
      500: "#6b7280",
      600: "#4b5563",
      700: "#374151",
      800: "#1f2937",
      900: "#111827"
    }
  }
};
var VALIDATION_RULES = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_REGEX: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  PHONE_REGEX: /^\+?[\d\s\-\(\)]+$/,
  URL_REGEX: /^https?:\/\/.+/,
  USERNAME_REGEX: /^[a-zA-Z0-9_-]{3,20}$/
};
var DATE_FORMATS = {
  ISO: "yyyy-MM-dd",
  US: "MM/dd/yyyy",
  EU: "dd/MM/yyyy",
  DISPLAY: "MMM d, yyyy",
  DISPLAY_WITH_TIME: "MMM d, yyyy h:mm a",
  TIME_12H: "h:mm a",
  TIME_24H: "HH:mm",
  DATETIME_ISO: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx"
};
var HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
};
var ERROR_CODES = {
  // Authentication errors
  AUTH_INVALID_CREDENTIALS: "AUTH_INVALID_CREDENTIALS",
  AUTH_TOKEN_EXPIRED: "AUTH_TOKEN_EXPIRED",
  AUTH_TOKEN_INVALID: "AUTH_TOKEN_INVALID",
  AUTH_INSUFFICIENT_PERMISSIONS: "AUTH_INSUFFICIENT_PERMISSIONS",
  AUTH_ACCOUNT_LOCKED: "AUTH_ACCOUNT_LOCKED",
  AUTH_ACCOUNT_NOT_VERIFIED: "AUTH_ACCOUNT_NOT_VERIFIED",
  // Validation errors
  VALIDATION_REQUIRED_FIELD: "VALIDATION_REQUIRED_FIELD",
  VALIDATION_INVALID_FORMAT: "VALIDATION_INVALID_FORMAT",
  VALIDATION_MIN_LENGTH: "VALIDATION_MIN_LENGTH",
  VALIDATION_MAX_LENGTH: "VALIDATION_MAX_LENGTH",
  VALIDATION_INVALID_EMAIL: "VALIDATION_INVALID_EMAIL",
  VALIDATION_PASSWORD_WEAK: "VALIDATION_PASSWORD_WEAK",
  // Network errors
  NETWORK_CONNECTION_ERROR: "NETWORK_CONNECTION_ERROR",
  NETWORK_TIMEOUT: "NETWORK_TIMEOUT",
  NETWORK_SERVER_ERROR: "NETWORK_SERVER_ERROR",
  // File upload errors
  FILE_TOO_LARGE: "FILE_TOO_LARGE",
  FILE_INVALID_TYPE: "FILE_INVALID_TYPE",
  FILE_UPLOAD_FAILED: "FILE_UPLOAD_FAILED",
  // Generic errors
  UNKNOWN_ERROR: "UNKNOWN_ERROR",
  OPERATION_FAILED: "OPERATION_FAILED",
  RESOURCE_NOT_FOUND: "RESOURCE_NOT_FOUND",
  RESOURCE_ALREADY_EXISTS: "RESOURCE_ALREADY_EXISTS"
};
var USER_ROLES = {
  SUPER_ADMIN: "super_admin",
  ADMIN: "admin",
  MANAGER: "manager",
  USER: "user",
  GUEST: "guest"
};
var PERMISSIONS = {
  // User management
  USERS_READ: "users:read",
  USERS_WRITE: "users:write",
  USERS_DELETE: "users:delete",
  // Content management
  CONTENT_READ: "content:read",
  CONTENT_WRITE: "content:write",
  CONTENT_DELETE: "content:delete",
  CONTENT_PUBLISH: "content:publish",
  // Analytics
  ANALYTICS_READ: "analytics:read",
  ANALYTICS_EXPORT: "analytics:export",
  // System administration
  SYSTEM_CONFIG: "system:config",
  SYSTEM_LOGS: "system:logs",
  SYSTEM_BACKUP: "system:backup"
};
var ROUTES = {
  HOME: "/",
  LOGIN: "/login",
  REGISTER: "/register",
  FORGOT_PASSWORD: "/forgot-password",
  RESET_PASSWORD: "/reset-password",
  DASHBOARD: "/dashboard",
  PROFILE: "/profile",
  SETTINGS: "/settings",
  USERS: "/users",
  ANALYTICS: "/analytics",
  HELP: "/help",
  PRIVACY: "/privacy",
  TERMS: "/terms"
};
var STORAGE_KEYS = {
  THEME: "luminar_theme",
  LANGUAGE: "luminar_language",
  SIDEBAR_COLLAPSED: "luminar_sidebar_collapsed",
  TABLE_PREFERENCES: "luminar_table_preferences",
  RECENT_SEARCHES: "luminar_recent_searches",
  DRAFT_DATA: "luminar_draft_data"
};
var EVENTS = {
  AUTH_LOGIN: "auth:login",
  AUTH_LOGOUT: "auth:logout",
  AUTH_SESSION_EXPIRED: "auth:session-expired",
  THEME_CHANGED: "theme:changed",
  NOTIFICATION_RECEIVED: "notification:received",
  FILE_UPLOAD_PROGRESS: "file:upload-progress",
  FILE_UPLOAD_COMPLETE: "file:upload-complete",
  FILE_UPLOAD_ERROR: "file:upload-error"
};
var FEATURE_FLAGS = {
  ENABLE_DARK_MODE: "enable_dark_mode",
  ENABLE_NOTIFICATIONS: "enable_notifications",
  ENABLE_FILE_UPLOAD: "enable_file_upload",
  ENABLE_ANALYTICS: "enable_analytics",
  ENABLE_REAL_TIME: "enable_real_time",
  ENABLE_OFFLINE_MODE: "enable_offline_mode"
};
var APP_METADATA = {
  NAME: "Luminar",
  DESCRIPTION: "Learning & Development Platform",
  VERSION: "1.0.0",
  AUTHOR: "Luminar Team",
  SUPPORT_EMAIL: "<EMAIL>",
  DOCUMENTATION_URL: "https://docs.luminar.ai",
  GITHUB_URL: "https://github.com/luminar/platform"
};
var emailSchema = z.string().email("Invalid email format").min(1, "Email is required");
var passwordSchema = z.string().min(VALIDATION_RULES.PASSWORD_MIN_LENGTH, `Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters`).regex(
  VALIDATION_RULES.PASSWORD_REGEX,
  "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
);
var nameSchema = z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters").trim();
var phoneSchema = z.string().regex(VALIDATION_RULES.PHONE_REGEX, "Invalid phone number format").optional();
var urlSchema = z.string().url("Invalid URL format").optional();
var idSchema = z.string().uuid("Invalid ID format");
var slugSchema = z.string().regex(/^[a-z0-9-]+$/, "Slug can only contain lowercase letters, numbers, and hyphens").min(1, "Slug is required");
var paginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  offset: z.number().int().min(0).optional()
});
var sortSchema = z.object({
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).default("asc")
});
var searchSchema = z.object({
  query: z.string().optional(),
  filters: z.record(z.any()).optional()
});
var baseQuerySchema = paginationSchema.merge(sortSchema).merge(searchSchema);
var loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, "Password is required"),
  rememberMe: z.boolean().default(false)
});
var registerSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
  name: nameSchema,
  acceptTerms: z.boolean().refine((val) => val === true, {
    message: "You must accept the terms and conditions"
  })
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"]
});
var forgotPasswordSchema = z.object({
  email: emailSchema
});
var resetPasswordSchema = z.object({
  token: z.string().min(1, "Reset token is required"),
  password: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"]
});
var changePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"]
});
var userProfileSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  phone: phoneSchema,
  avatar: urlSchema,
  bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
  timezone: z.string().optional(),
  language: z.string().optional()
});
var userPreferencesSchema = z.object({
  theme: z.enum(["light", "dark", "system"]).default("system"),
  notifications: z.object({
    email: z.boolean().default(true),
    push: z.boolean().default(true),
    sms: z.boolean().default(false)
  }).default({}),
  privacy: z.object({
    profileVisible: z.boolean().default(true),
    activityVisible: z.boolean().default(true)
  }).default({})
});
var fileUploadSchema = z.object({
  name: z.string().min(1, "File name is required"),
  size: z.number().int().min(1, "File size must be greater than 0"),
  type: z.string().min(1, "File type is required"),
  data: z.instanceof(File).or(z.string())
  // File object or base64 string
});
var apiResponseSchema = z.object({
  data: z.any(),
  message: z.string().optional(),
  success: z.boolean(),
  timestamp: z.string(),
  requestId: z.string().optional()
});
var paginatedResponseSchema = apiResponseSchema.extend({
  data: z.array(z.any()),
  pagination: z.object({
    page: z.number().int(),
    limit: z.number().int(),
    total: z.number().int(),
    totalPages: z.number().int(),
    hasNext: z.boolean(),
    hasPrev: z.boolean()
  })
});
var apiErrorSchema = z.object({
  code: z.string(),
  message: z.string(),
  details: z.record(z.any()).optional(),
  timestamp: z.string(),
  path: z.string().optional(),
  requestId: z.string().optional()
});

// src/errors/index.ts
var LuminarError = class _LuminarError extends Error {
  constructor(message, code = ERROR_CODES.UNKNOWN_ERROR, statusCode, details) {
    super(message);
    this.name = "LuminarError";
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
    this.timestamp = (/* @__PURE__ */ new Date()).toISOString();
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, _LuminarError);
    }
  }
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      details: this.details,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }
};
var AuthError = class extends LuminarError {
  constructor(message, code = ERROR_CODES.AUTH_INVALID_CREDENTIALS, details) {
    super(message, code, 401, details);
    this.name = "AuthError";
  }
};
var ValidationError = class extends LuminarError {
  constructor(message, code = ERROR_CODES.VALIDATION_REQUIRED_FIELD, details) {
    super(message, code, 400, details);
    this.name = "ValidationError";
  }
};
var NetworkError = class extends LuminarError {
  constructor(message, code = ERROR_CODES.NETWORK_CONNECTION_ERROR, details) {
    super(message, code, 500, details);
    this.name = "NetworkError";
  }
};
var FileError = class extends LuminarError {
  constructor(message, code = ERROR_CODES.FILE_UPLOAD_FAILED, details) {
    super(message, code, 400, details);
    this.name = "FileError";
  }
};
var PermissionError = class extends LuminarError {
  constructor(message, code = ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS, details) {
    super(message, code, 403, details);
    this.name = "PermissionError";
  }
};
var NotFoundError = class extends LuminarError {
  constructor(message, code = ERROR_CODES.RESOURCE_NOT_FOUND, details) {
    super(message, code, 404, details);
    this.name = "NotFoundError";
  }
};
var ConflictError = class extends LuminarError {
  constructor(message, code = ERROR_CODES.RESOURCE_ALREADY_EXISTS, details) {
    super(message, code, 409, details);
    this.name = "ConflictError";
  }
};
var createAuthError = {
  invalidCredentials: (details) => new AuthError("Invalid email or password", ERROR_CODES.AUTH_INVALID_CREDENTIALS, details),
  tokenExpired: (details) => new AuthError("Authentication token has expired", ERROR_CODES.AUTH_TOKEN_EXPIRED, details),
  tokenInvalid: (details) => new AuthError("Invalid authentication token", ERROR_CODES.AUTH_TOKEN_INVALID, details),
  insufficientPermissions: (details) => new PermissionError("Insufficient permissions to perform this action", ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS, details),
  accountLocked: (details) => new AuthError("Account has been locked due to multiple failed login attempts", ERROR_CODES.AUTH_ACCOUNT_LOCKED, details),
  accountNotVerified: (details) => new AuthError("Account email has not been verified", ERROR_CODES.AUTH_ACCOUNT_NOT_VERIFIED, details)
};
var createValidationError = {
  requiredField: (fieldName, details) => new ValidationError(`${fieldName} is required`, ERROR_CODES.VALIDATION_REQUIRED_FIELD, { field: fieldName, ...details }),
  invalidFormat: (fieldName, details) => new ValidationError(`${fieldName} has invalid format`, ERROR_CODES.VALIDATION_INVALID_FORMAT, { field: fieldName, ...details }),
  minLength: (fieldName, minLength, details) => new ValidationError(`${fieldName} must be at least ${minLength} characters`, ERROR_CODES.VALIDATION_MIN_LENGTH, { field: fieldName, minLength, ...details }),
  maxLength: (fieldName, maxLength, details) => new ValidationError(`${fieldName} must be less than ${maxLength} characters`, ERROR_CODES.VALIDATION_MAX_LENGTH, { field: fieldName, maxLength, ...details }),
  invalidEmail: (details) => new ValidationError("Invalid email format", ERROR_CODES.VALIDATION_INVALID_EMAIL, details),
  weakPassword: (details) => new ValidationError("Password does not meet security requirements", ERROR_CODES.VALIDATION_PASSWORD_WEAK, details)
};
var createNetworkError = {
  connectionError: (details) => new NetworkError("Unable to connect to server", ERROR_CODES.NETWORK_CONNECTION_ERROR, details),
  timeout: (details) => new NetworkError("Request timed out", ERROR_CODES.NETWORK_TIMEOUT, details),
  serverError: (details) => new NetworkError("Internal server error", ERROR_CODES.NETWORK_SERVER_ERROR, details)
};
var createFileError = {
  tooLarge: (maxSize, details) => new FileError(`File size exceeds maximum allowed size of ${maxSize} bytes`, ERROR_CODES.FILE_TOO_LARGE, { maxSize, ...details }),
  invalidType: (allowedTypes, details) => new FileError(`File type not allowed. Allowed types: ${allowedTypes.join(", ")}`, ERROR_CODES.FILE_INVALID_TYPE, { allowedTypes, ...details }),
  uploadFailed: (details) => new FileError("File upload failed", ERROR_CODES.FILE_UPLOAD_FAILED, details)
};
function isLuminarError(error) {
  return error instanceof LuminarError;
}
function isAuthError(error) {
  return error instanceof AuthError;
}
function isValidationError(error) {
  return error instanceof ValidationError;
}
function isNetworkError(error) {
  return error instanceof NetworkError;
}
function isFileError(error) {
  return error instanceof FileError;
}
function isPermissionError(error) {
  return error instanceof PermissionError;
}
function isNotFoundError(error) {
  return error instanceof NotFoundError;
}
function isConflictError(error) {
  return error instanceof ConflictError;
}
function toLuminarError(error) {
  if (isLuminarError(error)) {
    return error;
  }
  if (error instanceof Error) {
    return new LuminarError(error.message, ERROR_CODES.UNKNOWN_ERROR, void 0, {
      originalError: error.name,
      stack: error.stack
    });
  }
  if (typeof error === "string") {
    return new LuminarError(error, ERROR_CODES.UNKNOWN_ERROR);
  }
  return new LuminarError("An unknown error occurred", ERROR_CODES.UNKNOWN_ERROR, void 0, {
    originalError: error
  });
}
function logError(error, context) {
  const logData = {
    ...error.toJSON(),
    context
  };
  if (process.env.NODE_ENV === "development") {
    console.error("Luminar Error:", logData);
  }
}
function getErrorMessage(error) {
  if (isLuminarError(error)) {
    return error.message;
  }
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === "string") {
    return error;
  }
  return "An unexpected error occurred";
}
function createErrorFromStatus(status, message, details) {
  switch (status) {
    case 400:
      return new ValidationError(message || "Bad Request", ERROR_CODES.VALIDATION_INVALID_FORMAT, details);
    case 401:
      return new AuthError(message || "Unauthorized", ERROR_CODES.AUTH_INVALID_CREDENTIALS, details);
    case 403:
      return new PermissionError(message || "Forbidden", ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS, details);
    case 404:
      return new NotFoundError(message || "Not Found", ERROR_CODES.RESOURCE_NOT_FOUND, details);
    case 409:
      return new ConflictError(message || "Conflict", ERROR_CODES.RESOURCE_ALREADY_EXISTS, details);
    case 422:
      return new ValidationError(message || "Unprocessable Entity", ERROR_CODES.VALIDATION_INVALID_FORMAT, details);
    case 500:
    case 502:
    case 503:
      return new NetworkError(message || "Server Error", ERROR_CODES.NETWORK_SERVER_ERROR, details);
    default:
      return new LuminarError(message || "Unknown Error", ERROR_CODES.UNKNOWN_ERROR, status, details);
  }
}

// src/api/types.ts
var ErrorType = /* @__PURE__ */ ((ErrorType2) => {
  ErrorType2["NETWORK_ERROR"] = "NETWORK_ERROR";
  ErrorType2["AUTHENTICATION_ERROR"] = "AUTHENTICATION_ERROR";
  ErrorType2["AUTHORIZATION_ERROR"] = "AUTHORIZATION_ERROR";
  ErrorType2["VALIDATION_ERROR"] = "VALIDATION_ERROR";
  ErrorType2["SERVER_ERROR"] = "SERVER_ERROR";
  ErrorType2["CLIENT_ERROR"] = "CLIENT_ERROR";
  ErrorType2["TIMEOUT_ERROR"] = "TIMEOUT_ERROR";
  ErrorType2["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
  return ErrorType2;
})(ErrorType || {});

// src/api/error-handler.ts
var ApiErrorHandler = class {
  handleAxiosError(error) {
    const context = {
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      timestamp: /* @__PURE__ */ new Date(),
      requestId: this.generateRequestId()
    };
    if (error.response) {
      return this.handleResponseError(error, context);
    } else if (error.request) {
      return this.handleNetworkError(error, context);
    } else {
      return this.handleUnknownError(error, context);
    }
  }
  handleResponseError(error, context) {
    const status = error.response.status;
    const responseData = error.response.data;
    let errorType;
    let message;
    let code;
    if (status === 401) {
      errorType = "AUTHENTICATION_ERROR" /* AUTHENTICATION_ERROR */;
      message = "Authentication required. Please log in again.";
      code = "AUTH_REQUIRED";
    } else if (status === 403) {
      errorType = "AUTHORIZATION_ERROR" /* AUTHORIZATION_ERROR */;
      message = "You do not have permission to perform this action.";
      code = "INSUFFICIENT_PERMISSIONS";
    } else if (status >= 400 && status < 500) {
      errorType = "CLIENT_ERROR" /* CLIENT_ERROR */;
      message = responseData?.message || "Invalid request. Please check your input.";
      code = responseData?.code || "CLIENT_ERROR";
    } else if (status >= 500) {
      errorType = "SERVER_ERROR" /* SERVER_ERROR */;
      message = "Server error occurred. Please try again later.";
      code = "SERVER_ERROR";
    } else {
      errorType = "UNKNOWN_ERROR" /* UNKNOWN_ERROR */;
      message = "An unexpected error occurred.";
      code = "UNKNOWN_ERROR";
    }
    return {
      message,
      code,
      field: responseData?.field,
      details: {
        type: errorType,
        status,
        statusText: error.response.statusText,
        context,
        originalMessage: error.message,
        responseData
      }
    };
  }
  handleNetworkError(error, context) {
    let message;
    let code;
    if (error.code === "ECONNABORTED" || error.message.includes("timeout")) {
      message = "Request timed out. Please check your connection and try again.";
      code = "TIMEOUT_ERROR";
    } else if (error.code === "ERR_NETWORK" || error.message.includes("Network Error")) {
      message = "Network error. Please check your internet connection.";
      code = "NETWORK_ERROR";
    } else {
      message = "Unable to connect to the server. Please try again later.";
      code = "CONNECTION_ERROR";
    }
    return {
      message,
      code,
      details: {
        type: "NETWORK_ERROR" /* NETWORK_ERROR */,
        context,
        originalMessage: error.message,
        errorCode: error.code
      }
    };
  }
  handleUnknownError(error, context) {
    return {
      message: "An unexpected error occurred. Please try again.",
      code: "UNKNOWN_ERROR",
      details: {
        type: "UNKNOWN_ERROR" /* UNKNOWN_ERROR */,
        context,
        originalMessage: error.message
      }
    };
  }
  categorizeError(error) {
    if (error instanceof AxiosError) {
      if (error.response) {
        const status = error.response.status;
        if (status === 401) return "AUTHENTICATION_ERROR" /* AUTHENTICATION_ERROR */;
        if (status === 403) return "AUTHORIZATION_ERROR" /* AUTHORIZATION_ERROR */;
        if (status >= 400 && status < 500) return "CLIENT_ERROR" /* CLIENT_ERROR */;
        if (status >= 500) return "SERVER_ERROR" /* SERVER_ERROR */;
      } else if (error.request) {
        if (error.code === "ECONNABORTED" || error.message.includes("timeout")) {
          return "TIMEOUT_ERROR" /* TIMEOUT_ERROR */;
        }
        return "NETWORK_ERROR" /* NETWORK_ERROR */;
      }
    }
    return "UNKNOWN_ERROR" /* UNKNOWN_ERROR */;
  }
  formatUserMessage(error) {
    return error.message;
  }
  shouldRetry(error) {
    const errorType = error.details?.type;
    return errorType === "NETWORK_ERROR" /* NETWORK_ERROR */ || errorType === "SERVER_ERROR" /* SERVER_ERROR */ || errorType === "TIMEOUT_ERROR" /* TIMEOUT_ERROR */;
  }
  logError(error, context) {
    const logContext = {
      ...error.details?.context,
      ...context,
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
    console.error("[API Error]", {
      message: error.message,
      code: error.code,
      field: error.field,
      context: logContext,
      details: error.details
    });
    if (typeof window !== "undefined" && window.__SENTRY__) {
      window.__SENTRY__.captureException(new Error(error.message), {
        tags: {
          errorType: error.details?.type,
          errorCode: error.code
        },
        extra: {
          context: logContext,
          details: error.details
        }
      });
    }
  }
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
};
function isApiError(error) {
  return error && typeof error === "object" && "message" in error && "code" in error;
}
function createApiError(message, code, type = "UNKNOWN_ERROR" /* UNKNOWN_ERROR */, field, details) {
  return {
    message,
    code,
    field,
    details: {
      type,
      timestamp: /* @__PURE__ */ new Date(),
      ...details
    }
  };
}

// src/api/client.ts
var LuminarAPIClientImpl = class {
  constructor(config) {
    this.requestInterceptors = /* @__PURE__ */ new Map();
    this.responseInterceptors = /* @__PURE__ */ new Map();
    this.interceptorIdCounter = 0;
    this.config = { ...config };
    this.errorHandler = new ApiErrorHandler();
    this.axiosInstance = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 1e4,
      headers: {
        "Content-Type": "application/json",
        ...config.defaultHeaders
      }
    });
    this.setupInterceptors();
  }
  setupInterceptors() {
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        let modifiedConfig = { ...config };
        for (const interceptor of this.requestInterceptors.values()) {
          if (interceptor.onRequest) {
            const requestData = {
              url: modifiedConfig.url || "",
              method: modifiedConfig.method || "get",
              data: modifiedConfig.data,
              headers: modifiedConfig.headers || {},
              timeout: modifiedConfig.timeout,
              retries: modifiedConfig.retries,
              retryDelay: modifiedConfig.retryDelay,
              signal: modifiedConfig.signal
            };
            const result = await interceptor.onRequest(requestData);
            modifiedConfig = {
              ...modifiedConfig,
              url: result.url,
              method: result.method,
              data: result.data,
              headers: result.headers,
              timeout: result.timeout,
              signal: result.signal
            };
            modifiedConfig.retries = result.retries;
            modifiedConfig.retryDelay = result.retryDelay;
          }
        }
        if (this.config.enableLogging) {
          console.log(`[API Request] ${modifiedConfig.method?.toUpperCase()} ${modifiedConfig.url}`);
        }
        return modifiedConfig;
      },
      async (error) => {
        let modifiedError = error;
        for (const interceptor of this.requestInterceptors.values()) {
          if (interceptor.onRequestError) {
            modifiedError = await interceptor.onRequestError(modifiedError);
          }
        }
        return Promise.reject(modifiedError);
      }
    );
    this.axiosInstance.interceptors.response.use(
      async (response) => {
        const apiResponse = this.transformResponse(response);
        let modifiedResponse = apiResponse;
        for (const interceptor of this.responseInterceptors.values()) {
          if (interceptor.onResponse) {
            modifiedResponse = await interceptor.onResponse(modifiedResponse);
          }
        }
        if (this.config.enableLogging) {
          console.log(`[API Response] ${response.status} ${response.config.url}`);
        }
        return response;
      },
      async (error) => {
        const apiError = this.errorHandler.handleAxiosError(error);
        let modifiedError = apiError;
        for (const interceptor of this.responseInterceptors.values()) {
          if (interceptor.onResponseError) {
            modifiedError = await interceptor.onResponseError(modifiedError);
          }
        }
        if (this.config.enableLogging) {
          console.error(`[API Error] ${error.response?.status || "Network"} ${error.config?.url}`, modifiedError);
        }
        return Promise.reject(modifiedError);
      }
    );
  }
  transformResponse(response) {
    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      message: response.data?.message,
      errors: response.data?.errors,
      metadata: response.data?.metadata
    };
  }
  async executeWithRetry(requestFn, config) {
    const retryConfig = {
      maxAttempts: config?.retries || this.config.retries || 3,
      backoffStrategy: "exponential",
      baseDelay: config?.retryDelay || this.config.retryDelay || 1e3,
      maxDelay: 1e4,
      retryCondition: (error) => {
        if (error instanceof AxiosError) {
          return !error.response || error.response.status >= 500 && error.response.status < 600;
        }
        return false;
      }
    };
    let lastError;
    for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
      try {
        const response = await requestFn();
        return this.transformResponse(response);
      } catch (error) {
        lastError = error;
        if (attempt === retryConfig.maxAttempts || !retryConfig.retryCondition?.(lastError)) {
          throw lastError;
        }
        const delay = Math.min(retryConfig.baseDelay * Math.pow(2, attempt - 1), retryConfig.maxDelay) ;
        if (this.config.enableLogging) {
          console.log(`[API Retry] Attempt ${attempt + 1}/${retryConfig.maxAttempts} in ${delay}ms`);
        }
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
    throw lastError;
  }
  // HTTP Methods
  async get(url, config) {
    return this.executeWithRetry(
      () => this.axiosInstance.get(url, this.mergeConfig(config)),
      config
    );
  }
  async post(url, data, config) {
    return this.executeWithRetry(
      () => this.axiosInstance.post(url, data, this.mergeConfig(config)),
      config
    );
  }
  async put(url, data, config) {
    return this.executeWithRetry(
      () => this.axiosInstance.put(url, data, this.mergeConfig(config)),
      config
    );
  }
  async patch(url, data, config) {
    return this.executeWithRetry(
      () => this.axiosInstance.patch(url, data, this.mergeConfig(config)),
      config
    );
  }
  async delete(url, config) {
    return this.executeWithRetry(
      () => this.axiosInstance.delete(url, this.mergeConfig(config)),
      config
    );
  }
  // Interceptor Management
  addRequestInterceptor(interceptor) {
    const id = ++this.interceptorIdCounter;
    this.requestInterceptors.set(id, interceptor);
    return id;
  }
  addResponseInterceptor(interceptor) {
    const id = ++this.interceptorIdCounter;
    this.responseInterceptors.set(id, interceptor);
    return id;
  }
  removeRequestInterceptor(id) {
    this.requestInterceptors.delete(id);
  }
  removeResponseInterceptor(id) {
    this.responseInterceptors.delete(id);
  }
  // Configuration Management
  setBaseURL(url) {
    this.config.baseURL = url;
    this.axiosInstance.defaults.baseURL = url;
  }
  setDefaultHeaders(headers) {
    this.config.defaultHeaders = { ...this.config.defaultHeaders, ...headers };
    Object.assign(this.axiosInstance.defaults.headers, headers);
  }
  getConfig() {
    return { ...this.config };
  }
  mergeConfig(config) {
    if (!config) return {};
    return {
      headers: config.headers,
      timeout: config.timeout,
      signal: config.signal
    };
  }
};
function createLuminarAPIClient(config) {
  return new LuminarAPIClientImpl(config);
}
var defaultClient = null;
function getDefaultAPIClient() {
  if (!defaultClient) {
    throw new Error("Default API client not initialized. Call initializeDefaultAPIClient() first.");
  }
  return defaultClient;
}
function initializeDefaultAPIClient(config) {
  defaultClient = createLuminarAPIClient(config);
}
function setDefaultAPIClient(client) {
  defaultClient = client;
}

// src/api/auth-interceptor.ts
var AuthenticationInterceptor = class {
  constructor(config) {
    this.refreshPromise = null;
    this.requestQueue = [];
    this.config = {
      authHeaderName: "Authorization",
      authHeaderPrefix: "Bearer",
      excludeUrls: [],
      ...config
    };
  }
  getRequestInterceptor() {
    return {
      onRequest: async (config) => {
        const { url, headers = {} } = config;
        if (this.shouldExcludeUrl(url)) {
          return config;
        }
        try {
          const token = await this.getValidToken();
          if (token) {
            headers[this.config.authHeaderName] = `${this.config.authHeaderPrefix} ${token}`;
          }
        } catch (error) {
          console.error("[Auth Interceptor] Failed to get valid token:", error);
          this.config.onAuthError?.(error);
        }
        return {
          ...config,
          headers
        };
      },
      onRequestError: async (error) => {
        return error;
      }
    };
  }
  getResponseInterceptor() {
    return {
      onResponse: async (response) => {
        return response;
      },
      onResponseError: async (error) => {
        const originalRequest = error.config;
        if (this.isAuthError(error) && originalRequest && !originalRequest._retry) {
          originalRequest._retry = true;
          try {
            const token = await this.refreshToken();
            if (token) {
              originalRequest.headers[this.config.authHeaderName] = `${this.config.authHeaderPrefix} ${token}`;
            }
          } catch (refreshError) {
            console.error("[Auth Interceptor] Token refresh failed:", refreshError);
            this.config.onAuthError?.(refreshError);
            this.config.tokenManager.clearTokens();
          }
        }
        return Promise.reject(error);
      }
    };
  }
  async getValidToken() {
    const accessToken = this.config.tokenManager.getAccessToken();
    if (!accessToken) {
      return null;
    }
    if (this.config.tokenManager.isTokenExpired(accessToken)) {
      try {
        return await this.refreshToken();
      } catch (error) {
        console.error("[Auth Interceptor] Token refresh failed:", error);
        return null;
      }
    }
    return accessToken;
  }
  async refreshToken() {
    if (this.refreshPromise) {
      return this.refreshPromise;
    }
    this.refreshPromise = this.performTokenRefresh();
    try {
      const result = await this.refreshPromise;
      this.refreshPromise = null;
      this.processRequestQueue(result);
      return result;
    } catch (error) {
      this.refreshPromise = null;
      this.rejectRequestQueue(error);
      throw error;
    }
  }
  async performTokenRefresh() {
    const refreshToken = this.config.tokenManager.getRefreshToken();
    if (!refreshToken) {
      throw new Error("No refresh token available");
    }
    try {
      const tokens = await this.config.tokenManager.refreshAccessToken();
      this.config.tokenManager.setTokens(tokens.accessToken, tokens.refreshToken);
      this.config.onTokenRefresh?.(tokens);
      return tokens.accessToken;
    } catch (error) {
      this.config.tokenManager.clearTokens();
      throw error;
    }
  }
  processRequestQueue(token) {
    this.requestQueue.forEach(({ resolve }) => resolve(token));
    this.requestQueue = [];
  }
  rejectRequestQueue(error) {
    this.requestQueue.forEach(({ reject }) => reject(error));
    this.requestQueue = [];
  }
  shouldExcludeUrl(url) {
    return this.config.excludeUrls?.some(
      (excludeUrl) => url.includes(excludeUrl)
    ) || false;
  }
  isAuthError(error) {
    return error?.details?.status === 401 || error?.details?.type === "AUTHENTICATION_ERROR";
  }
};
var LocalStorageTokenManager = class {
  constructor() {
    this.accessTokenKey = "luminar_access_token";
    this.refreshTokenKey = "luminar_refresh_token";
  }
  getAccessToken() {
    if (typeof window === "undefined") return null;
    return localStorage.getItem(this.accessTokenKey);
  }
  getRefreshToken() {
    if (typeof window === "undefined") return null;
    return localStorage.getItem(this.refreshTokenKey);
  }
  setTokens(accessToken, refreshToken) {
    if (typeof window === "undefined") return;
    localStorage.setItem(this.accessTokenKey, accessToken);
    if (refreshToken) {
      localStorage.setItem(this.refreshTokenKey, refreshToken);
    }
  }
  clearTokens() {
    if (typeof window === "undefined") return;
    localStorage.removeItem(this.accessTokenKey);
    localStorage.removeItem(this.refreshTokenKey);
  }
  isTokenExpired(token) {
    try {
      const payload = JSON.parse(atob(token.split(".")[1]));
      const currentTime = Math.floor(Date.now() / 1e3);
      return payload.exp < currentTime;
    } catch (error) {
      return true;
    }
  }
  async refreshAccessToken() {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      throw new Error("No refresh token available");
    }
    throw new Error("Token refresh endpoint not configured. Please implement refreshAccessToken method.");
  }
};
function createAuthInterceptor(config = {}) {
  const defaultConfig = {
    tokenManager: new LocalStorageTokenManager(),
    ...config
  };
  return new AuthenticationInterceptor(defaultConfig);
}

// src/api/index.ts
function createConfiguredAPIClient(config) {
  const client = createLuminarAPIClient({
    baseURL: config.baseURL,
    timeout: config.timeout || 1e4,
    retries: config.retries || 3,
    enableLogging: config.enableLogging || false
  });
  if (config.enableAuth !== false) {
    const authInterceptor = createAuthInterceptor({
      tokenManager: config.tokenManager,
      onAuthError: (error) => {
        console.error("[API Auth Error]", error);
      },
      onTokenRefresh: (tokens) => {
        console.log("[API] Tokens refreshed successfully");
      }
    });
    client.addRequestInterceptor(authInterceptor.getRequestInterceptor());
    client.addResponseInterceptor(authInterceptor.getResponseInterceptor());
  }
  return client;
}

// src/services/users.service.ts
var UsersServiceImpl = class {
  constructor(apiClient) {
    this.basePath = "/api/users";
    this.apiClient = apiClient;
  }
  async getUsers(params) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== void 0 && value !== null) {
          if (Array.isArray(value)) {
            value.forEach((v) => queryParams.append(key, v.toString()));
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });
    }
    const url = queryParams.toString() ? `${this.basePath}?${queryParams.toString()}` : this.basePath;
    return this.apiClient.get(url);
  }
  async getUser(id) {
    if (!id) {
      throw new Error("User ID is required");
    }
    return this.apiClient.get(`${this.basePath}/${id}`);
  }
  async createUser(user) {
    this.validateCreateUserDto(user);
    return this.apiClient.post(this.basePath, user);
  }
  async updateUser(id, user) {
    if (!id) {
      throw new Error("User ID is required");
    }
    return this.apiClient.put(`${this.basePath}/${id}`, user);
  }
  async deleteUser(id) {
    if (!id) {
      throw new Error("User ID is required");
    }
    return this.apiClient.delete(`${this.basePath}/${id}`);
  }
  async getUserRoles(id) {
    if (!id) {
      throw new Error("User ID is required");
    }
    return this.apiClient.get(`${this.basePath}/${id}/roles`);
  }
  async updateUserRoles(id, roleIds) {
    if (!id) {
      throw new Error("User ID is required");
    }
    if (!Array.isArray(roleIds)) {
      throw new Error("Role IDs must be an array");
    }
    return this.apiClient.put(`${this.basePath}/${id}/roles`, { roleIds });
  }
  async getUserPermissions(id) {
    if (!id) {
      throw new Error("User ID is required");
    }
    return this.apiClient.get(`${this.basePath}/${id}/permissions`);
  }
  // Additional utility methods
  async searchUsers(query, limit = 10) {
    if (!query || query.trim().length === 0) {
      throw new Error("Search query is required");
    }
    return this.getUsers({
      search: query.trim(),
      limit
    });
  }
  async getUsersByRole(roleId) {
    if (!roleId) {
      throw new Error("Role ID is required");
    }
    return this.getUsers({
      role: roleId
    });
  }
  async getActiveUsers() {
    return this.getUsers({
      isActive: true
    });
  }
  async deactivateUser(id) {
    return this.updateUser(id, { isActive: false });
  }
  async activateUser(id) {
    return this.updateUser(id, { isActive: true });
  }
  validateCreateUserDto(user) {
    if (!user.email || !this.isValidEmail(user.email)) {
      throw new Error("Valid email is required");
    }
    if (!user.name || user.name.trim().length === 0) {
      throw new Error("Name is required");
    }
    if (!user.password || user.password.length < 8) {
      throw new Error("Password must be at least 8 characters long");
    }
    if (user.roleIds && !Array.isArray(user.roleIds)) {
      throw new Error("Role IDs must be an array");
    }
  }
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
};
function createUsersService(apiClient) {
  return new UsersServiceImpl(apiClient);
}

// src/services/types.ts
var TrainingDifficulty = /* @__PURE__ */ ((TrainingDifficulty2) => {
  TrainingDifficulty2["BEGINNER"] = "beginner";
  TrainingDifficulty2["INTERMEDIATE"] = "intermediate";
  TrainingDifficulty2["ADVANCED"] = "advanced";
  TrainingDifficulty2["EXPERT"] = "expert";
  return TrainingDifficulty2;
})(TrainingDifficulty || {});
var TrainingStatus = /* @__PURE__ */ ((TrainingStatus2) => {
  TrainingStatus2["DRAFT"] = "draft";
  TrainingStatus2["PUBLISHED"] = "published";
  TrainingStatus2["SCHEDULED"] = "scheduled";
  TrainingStatus2["IN_PROGRESS"] = "in_progress";
  TrainingStatus2["COMPLETED"] = "completed";
  TrainingStatus2["CANCELLED"] = "cancelled";
  TrainingStatus2["ARCHIVED"] = "archived";
  return TrainingStatus2;
})(TrainingStatus || {});
var EmailStatus = /* @__PURE__ */ ((EmailStatus2) => {
  EmailStatus2["DRAFT"] = "draft";
  EmailStatus2["QUEUED"] = "queued";
  EmailStatus2["SENDING"] = "sending";
  EmailStatus2["SENT"] = "sent";
  EmailStatus2["DELIVERED"] = "delivered";
  EmailStatus2["OPENED"] = "opened";
  EmailStatus2["CLICKED"] = "clicked";
  EmailStatus2["BOUNCED"] = "bounced";
  EmailStatus2["FAILED"] = "failed";
  return EmailStatus2;
})(EmailStatus || {});
var WinCategory = /* @__PURE__ */ ((WinCategory2) => {
  WinCategory2["PERSONAL"] = "personal";
  WinCategory2["TEAM"] = "team";
  WinCategory2["PROJECT"] = "project";
  WinCategory2["PROCESS"] = "process";
  WinCategory2["INNOVATION"] = "innovation";
  WinCategory2["CUSTOMER"] = "customer";
  WinCategory2["FINANCIAL"] = "financial";
  WinCategory2["OTHER"] = "other";
  return WinCategory2;
})(WinCategory || {});
var WinImpact = /* @__PURE__ */ ((WinImpact2) => {
  WinImpact2["LOW"] = "low";
  WinImpact2["MEDIUM"] = "medium";
  WinImpact2["HIGH"] = "high";
  WinImpact2["CRITICAL"] = "critical";
  return WinImpact2;
})(WinImpact || {});

// src/services/training.service.ts
var TrainingServiceImpl = class {
  constructor(apiClient) {
    this.basePath = "/api/trainings";
    this.apiClient = apiClient;
  }
  async getTrainings(params) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== void 0 && value !== null) {
          if (Array.isArray(value)) {
            value.forEach((v) => queryParams.append(key, v.toString()));
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });
    }
    const url = queryParams.toString() ? `${this.basePath}?${queryParams.toString()}` : this.basePath;
    return this.apiClient.get(url);
  }
  async getTraining(id) {
    if (!id) {
      throw new Error("Training ID is required");
    }
    return this.apiClient.get(`${this.basePath}/${id}`);
  }
  async createTraining(training) {
    this.validateCreateTrainingDto(training);
    return this.apiClient.post(this.basePath, training);
  }
  async updateTraining(id, training) {
    if (!id) {
      throw new Error("Training ID is required");
    }
    return this.apiClient.put(`${this.basePath}/${id}`, training);
  }
  async deleteTraining(id) {
    if (!id) {
      throw new Error("Training ID is required");
    }
    return this.apiClient.delete(`${this.basePath}/${id}`);
  }
  async enrollInTraining(trainingId, userId) {
    if (!trainingId) {
      throw new Error("Training ID is required");
    }
    if (!userId) {
      throw new Error("User ID is required");
    }
    return this.apiClient.post(`${this.basePath}/${trainingId}/enroll`, { userId });
  }
  async unenrollFromTraining(trainingId, userId) {
    if (!trainingId) {
      throw new Error("Training ID is required");
    }
    if (!userId) {
      throw new Error("User ID is required");
    }
    return this.apiClient.delete(`${this.basePath}/${trainingId}/enroll/${userId}`);
  }
  async getTrainingParticipants(id) {
    if (!id) {
      throw new Error("Training ID is required");
    }
    return this.apiClient.get(`${this.basePath}/${id}/participants`);
  }
  async getTrainingCategories() {
    return this.apiClient.get("/api/training-categories");
  }
  // Additional utility methods
  async getPublicTrainings() {
    return this.getTrainings({
      isPublic: true,
      status: "published" /* PUBLISHED */
    });
  }
  async getTrainingsByInstructor(instructorId) {
    if (!instructorId) {
      throw new Error("Instructor ID is required");
    }
    return this.getTrainings({
      instructorId
    });
  }
  async getTrainingsByCategory(categoryId) {
    if (!categoryId) {
      throw new Error("Category ID is required");
    }
    return this.getTrainings({
      categoryId
    });
  }
  async getTrainingsByDifficulty(difficulty) {
    return this.getTrainings({
      difficulty
    });
  }
  async searchTrainings(query, limit = 10) {
    if (!query || query.trim().length === 0) {
      throw new Error("Search query is required");
    }
    return this.getTrainings({
      search: query.trim(),
      limit
    });
  }
  async getUpcomingTrainings() {
    const now = (/* @__PURE__ */ new Date()).toISOString();
    return this.getTrainings({
      status: "scheduled" /* SCHEDULED */,
      startAfter: now
    });
  }
  async getCompletedTrainings() {
    return this.getTrainings({
      status: "completed" /* COMPLETED */
    });
  }
  async publishTraining(id) {
    return this.updateTraining(id, { status: "published" /* PUBLISHED */ });
  }
  async scheduleTraining(id, startDate, endDate) {
    const updateData = {
      status: "scheduled" /* SCHEDULED */,
      startDate
    };
    if (endDate) {
      updateData.endDate = endDate;
    }
    return this.updateTraining(id, updateData);
  }
  async startTraining(id) {
    return this.updateTraining(id, { status: "in_progress" /* IN_PROGRESS */ });
  }
  async completeTraining(id) {
    return this.updateTraining(id, { status: "completed" /* COMPLETED */ });
  }
  async cancelTraining(id) {
    return this.updateTraining(id, { status: "cancelled" /* CANCELLED */ });
  }
  async archiveTraining(id) {
    return this.updateTraining(id, { status: "archived" /* ARCHIVED */ });
  }
  validateCreateTrainingDto(training) {
    if (!training.title || training.title.trim().length === 0) {
      throw new Error("Training title is required");
    }
    if (!training.description || training.description.trim().length === 0) {
      throw new Error("Training description is required");
    }
    if (!training.categoryId) {
      throw new Error("Training category is required");
    }
    if (!training.instructorId) {
      throw new Error("Training instructor is required");
    }
    if (!training.duration || training.duration <= 0) {
      throw new Error("Training duration must be greater than 0");
    }
    if (!Object.values(TrainingDifficulty).includes(training.difficulty)) {
      throw new Error("Invalid training difficulty");
    }
    if (training.maxParticipants && training.maxParticipants <= 0) {
      throw new Error("Max participants must be greater than 0");
    }
    if (training.startDate && training.endDate) {
      if (new Date(training.startDate) >= new Date(training.endDate)) {
        throw new Error("End date must be after start date");
      }
    }
    if (training.tags && !Array.isArray(training.tags)) {
      throw new Error("Tags must be an array");
    }
  }
};
function createTrainingService(apiClient) {
  return new TrainingServiceImpl(apiClient);
}

// src/services/vendors.service.ts
var VendorsServiceImpl = class {
  constructor(apiClient) {
    this.basePath = "/api/vendors";
    this.apiClient = apiClient;
  }
  async getVendors(params) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== void 0 && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const url = queryParams.toString() ? `${this.basePath}?${queryParams.toString()}` : this.basePath;
    return this.apiClient.get(url);
  }
  async getVendor(id) {
    if (!id) {
      throw new Error("Vendor ID is required");
    }
    return this.apiClient.get(`${this.basePath}/${id}`);
  }
  async createVendor(vendor) {
    this.validateCreateVendorDto(vendor);
    return this.apiClient.post(this.basePath, vendor);
  }
  async updateVendor(id, vendor) {
    if (!id) {
      throw new Error("Vendor ID is required");
    }
    return this.apiClient.put(`${this.basePath}/${id}`, vendor);
  }
  async deleteVendor(id) {
    if (!id) {
      throw new Error("Vendor ID is required");
    }
    return this.apiClient.delete(`${this.basePath}/${id}`);
  }
  async rateVendor(id, rating) {
    if (!id) {
      throw new Error("Vendor ID is required");
    }
    if (rating < 1 || rating > 5) {
      throw new Error("Rating must be between 1 and 5");
    }
    return this.apiClient.post(`${this.basePath}/${id}/rate`, { rating });
  }
  async verifyVendor(id) {
    if (!id) {
      throw new Error("Vendor ID is required");
    }
    return this.apiClient.post(`${this.basePath}/${id}/verify`);
  }
  // Additional utility methods
  async searchVendors(query, limit = 10) {
    if (!query || query.trim().length === 0) {
      throw new Error("Search query is required");
    }
    return this.getVendors({
      search: query.trim(),
      limit
    });
  }
  async getActiveVendors() {
    return this.getVendors({
      isActive: true
    });
  }
  async getVerifiedVendors() {
    return this.getVendors({
      isVerified: true,
      isActive: true
    });
  }
  async getVendorsByCategory(category) {
    if (!category) {
      throw new Error("Category is required");
    }
    return this.getVendors({
      category
    });
  }
  async getTopRatedVendors(minRating = 4) {
    return this.getVendors({
      minRating,
      isActive: true,
      sort: "rating",
      order: "desc"
    });
  }
  async getVendorsByLocation(city, country) {
    const params = {
      isActive: true
    };
    if (city) params.city = city;
    if (country) params.country = country;
    return this.getVendors(params);
  }
  async deactivateVendor(id) {
    return this.updateVendor(id, { isActive: false });
  }
  async activateVendor(id) {
    return this.updateVendor(id, { isActive: true });
  }
  validateCreateVendorDto(vendor) {
    if (!vendor.name || vendor.name.trim().length === 0) {
      throw new Error("Vendor name is required");
    }
    if (vendor.email && !this.isValidEmail(vendor.email)) {
      throw new Error("Valid email is required");
    }
    if (vendor.website && !this.isValidUrl(vendor.website)) {
      throw new Error("Valid website URL is required");
    }
    if (vendor.contactPerson) {
      if (!vendor.contactPerson.name || vendor.contactPerson.name.trim().length === 0) {
        throw new Error("Contact person name is required");
      }
      if (!vendor.contactPerson.email || !this.isValidEmail(vendor.contactPerson.email)) {
        throw new Error("Valid contact person email is required");
      }
    }
    if (vendor.address) {
      const requiredFields = ["street", "city", "country"];
      for (const field of requiredFields) {
        if (!vendor.address[field] || vendor.address[field]?.trim().length === 0) {
          throw new Error(`Address ${field} is required`);
        }
      }
    }
    if (vendor.services && Array.isArray(vendor.services)) {
      vendor.services.forEach((service, index) => {
        if (!service.name || service.name.trim().length === 0) {
          throw new Error(`Service ${index + 1} name is required`);
        }
        if (!service.category || service.category.trim().length === 0) {
          throw new Error(`Service ${index + 1} category is required`);
        }
      });
    }
  }
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
};
function createVendorsService(apiClient) {
  return new VendorsServiceImpl(apiClient);
}

// src/services/email.service.ts
var EmailServiceImpl = class {
  constructor(apiClient) {
    this.basePath = "/api/emails";
    this.apiClient = apiClient;
  }
  async getEmails(params) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== void 0 && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }
    const url = queryParams.toString() ? `${this.basePath}?${queryParams.toString()}` : this.basePath;
    return this.apiClient.get(url);
  }
  async getEmail(id) {
    if (!id) {
      throw new Error("Email ID is required");
    }
    return this.apiClient.get(`${this.basePath}/${id}`);
  }
  async sendEmail(email) {
    this.validateSendEmailDto(email);
    return this.apiClient.post(`${this.basePath}/send`, email);
  }
  async scheduleEmail(email) {
    this.validateSendEmailDto(email);
    if (!email.scheduledAt) {
      throw new Error("Scheduled date is required for scheduled emails");
    }
    if (new Date(email.scheduledAt) <= /* @__PURE__ */ new Date()) {
      throw new Error("Scheduled date must be in the future");
    }
    return this.apiClient.post(`${this.basePath}/schedule`, email);
  }
  async cancelScheduledEmail(id) {
    if (!id) {
      throw new Error("Email ID is required");
    }
    return this.apiClient.delete(`${this.basePath}/${id}/cancel`);
  }
  async getEmailTemplates() {
    return this.apiClient.get("/api/email-templates");
  }
  async getEmailStats(id) {
    if (!id) {
      throw new Error("Email ID is required");
    }
    return this.apiClient.get(`${this.basePath}/${id}/stats`);
  }
  // Additional utility methods
  async getSentEmails() {
    return this.getEmails({
      status: "sent" /* SENT */
    });
  }
  async getFailedEmails() {
    return this.getEmails({
      status: "failed" /* FAILED */
    });
  }
  async getScheduledEmails() {
    return this.getEmails({
      status: "queued" /* QUEUED */
    });
  }
  async getEmailsByTemplate(templateId) {
    if (!templateId) {
      throw new Error("Template ID is required");
    }
    return this.getEmails({
      templateId
    });
  }
  async searchEmails(query, limit = 10) {
    if (!query || query.trim().length === 0) {
      throw new Error("Search query is required");
    }
    return this.getEmails({
      search: query.trim(),
      limit
    });
  }
  async getEmailsByRecipient(recipient) {
    if (!recipient) {
      throw new Error("Recipient email is required");
    }
    return this.getEmails({
      to: recipient
    });
  }
  async getEmailsBySender(sender) {
    if (!sender) {
      throw new Error("Sender email is required");
    }
    return this.getEmails({
      from: sender
    });
  }
  async resendEmail(id) {
    if (!id) {
      throw new Error("Email ID is required");
    }
    return this.apiClient.post(`${this.basePath}/${id}/resend`);
  }
  async sendBulkEmail(emails) {
    if (!Array.isArray(emails) || emails.length === 0) {
      throw new Error("Emails array is required and must not be empty");
    }
    emails.forEach((email, index) => {
      try {
        this.validateSendEmailDto(email);
      } catch (error) {
        throw new Error(`Email ${index + 1}: ${error.message}`);
      }
    });
    return this.apiClient.post(`${this.basePath}/bulk-send`, { emails });
  }
  async sendTemplateEmail(templateId, to, templateData, options) {
    if (!templateId) {
      throw new Error("Template ID is required");
    }
    if (!Array.isArray(to) || to.length === 0) {
      throw new Error("Recipients are required");
    }
    const emailData = {
      to,
      subject: "",
      // Will be filled by template
      body: "",
      // Will be filled by template
      templateId,
      templateData,
      ...options
    };
    return this.sendEmail(emailData);
  }
  validateSendEmailDto(email) {
    if (!Array.isArray(email.to) || email.to.length === 0) {
      throw new Error("At least one recipient is required");
    }
    const allEmails = [
      ...email.to,
      ...email.cc || [],
      ...email.bcc || []
    ];
    allEmails.forEach((emailAddr) => {
      if (!this.isValidEmail(emailAddr)) {
        throw new Error(`Invalid email address: ${emailAddr}`);
      }
    });
    if (!email.templateId) {
      if (!email.subject || email.subject.trim().length === 0) {
        throw new Error("Email subject is required");
      }
      if (!email.body || email.body.trim().length === 0) {
        throw new Error("Email body is required");
      }
    }
    if (email.attachments && Array.isArray(email.attachments)) {
      email.attachments.forEach((attachment, index) => {
        if (!attachment.filename || attachment.filename.trim().length === 0) {
          throw new Error(`Attachment ${index + 1} filename is required`);
        }
        if (!attachment.contentType || attachment.contentType.trim().length === 0) {
          throw new Error(`Attachment ${index + 1} content type is required`);
        }
        if (!attachment.data && !attachment.url) {
          throw new Error(`Attachment ${index + 1} must have either data or URL`);
        }
      });
    }
    if (email.templateId && (!email.templateData || typeof email.templateData !== "object")) {
      throw new Error("Template data is required when using email templates");
    }
  }
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
};
function createEmailService(apiClient) {
  return new EmailServiceImpl(apiClient);
}

// src/services/wins.service.ts
var WinsServiceImpl = class {
  constructor(apiClient) {
    this.basePath = "/api/wins";
    this.apiClient = apiClient;
  }
  async getWins(params) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== void 0 && value !== null) {
          if (Array.isArray(value)) {
            value.forEach((v) => queryParams.append(key, v.toString()));
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });
    }
    const url = queryParams.toString() ? `${this.basePath}?${queryParams.toString()}` : this.basePath;
    return this.apiClient.get(url);
  }
  async getWin(id) {
    if (!id) {
      throw new Error("Win ID is required");
    }
    return this.apiClient.get(`${this.basePath}/${id}`);
  }
  async createWin(win) {
    this.validateCreateWinDto(win);
    return this.apiClient.post(this.basePath, win);
  }
  async updateWin(id, win) {
    if (!id) {
      throw new Error("Win ID is required");
    }
    return this.apiClient.put(`${this.basePath}/${id}`, win);
  }
  async deleteWin(id) {
    if (!id) {
      throw new Error("Win ID is required");
    }
    return this.apiClient.delete(`${this.basePath}/${id}`);
  }
  async likeWin(id) {
    if (!id) {
      throw new Error("Win ID is required");
    }
    return this.apiClient.post(`${this.basePath}/${id}/like`);
  }
  async unlikeWin(id) {
    if (!id) {
      throw new Error("Win ID is required");
    }
    return this.apiClient.delete(`${this.basePath}/${id}/like`);
  }
  async featureWin(id) {
    if (!id) {
      throw new Error("Win ID is required");
    }
    return this.apiClient.post(`${this.basePath}/${id}/feature`);
  }
  async unfeatureWin(id) {
    if (!id) {
      throw new Error("Win ID is required");
    }
    return this.apiClient.delete(`${this.basePath}/${id}/feature`);
  }
  // Additional utility methods
  async getPublicWins() {
    return this.getWins({
      isPublic: true
    });
  }
  async getFeaturedWins() {
    return this.getWins({
      isFeatured: true,
      isPublic: true
    });
  }
  async getWinsByCategory(category) {
    return this.getWins({
      category
    });
  }
  async getWinsByImpact(impact) {
    return this.getWins({
      impact
    });
  }
  async getWinsByAuthor(authorId) {
    if (!authorId) {
      throw new Error("Author ID is required");
    }
    return this.getWins({
      authorId
    });
  }
  async getWinsByParticipant(participantId) {
    if (!participantId) {
      throw new Error("Participant ID is required");
    }
    return this.getWins({
      participantId
    });
  }
  async searchWins(query, limit = 10) {
    if (!query || query.trim().length === 0) {
      throw new Error("Search query is required");
    }
    return this.getWins({
      search: query.trim(),
      limit
    });
  }
  async getRecentWins(days = 30) {
    const date = /* @__PURE__ */ new Date();
    date.setDate(date.getDate() - days);
    return this.getWins({
      achievedAfter: date.toISOString(),
      sort: "achievedAt",
      order: "desc"
    });
  }
  async getTopWins(limit = 10) {
    return this.getWins({
      sort: "likes",
      order: "desc",
      limit
    });
  }
  async getWinsByTags(tags) {
    if (!Array.isArray(tags) || tags.length === 0) {
      throw new Error("Tags array is required and must not be empty");
    }
    return this.getWins({
      tags
    });
  }
  async getHighImpactWins() {
    return this.getWins({
      impact: "high" /* HIGH */
    });
  }
  async getCriticalImpactWins() {
    return this.getWins({
      impact: "critical" /* CRITICAL */
    });
  }
  async getTeamWins() {
    return this.getWins({
      category: "team" /* TEAM */
    });
  }
  async getPersonalWins() {
    return this.getWins({
      category: "personal" /* PERSONAL */
    });
  }
  async getProjectWins() {
    return this.getWins({
      category: "project" /* PROJECT */
    });
  }
  async getInnovationWins() {
    return this.getWins({
      category: "innovation" /* INNOVATION */
    });
  }
  async incrementWinViews(id) {
    if (!id) {
      throw new Error("Win ID is required");
    }
    return this.apiClient.post(`${this.basePath}/${id}/view`);
  }
  validateCreateWinDto(win) {
    if (!win.title || win.title.trim().length === 0) {
      throw new Error("Win title is required");
    }
    if (!win.description || win.description.trim().length === 0) {
      throw new Error("Win description is required");
    }
    if (!Object.values(WinCategory).includes(win.category)) {
      throw new Error("Invalid win category");
    }
    if (!Object.values(WinImpact).includes(win.impact)) {
      throw new Error("Invalid win impact level");
    }
    if (win.participantIds && !Array.isArray(win.participantIds)) {
      throw new Error("Participant IDs must be an array");
    }
    if (win.tags && !Array.isArray(win.tags)) {
      throw new Error("Tags must be an array");
    }
    if (win.metrics && Array.isArray(win.metrics)) {
      win.metrics.forEach((metric, index) => {
        if (!metric.name || metric.name.trim().length === 0) {
          throw new Error(`Metric ${index + 1} name is required`);
        }
        if (typeof metric.value !== "number") {
          throw new Error(`Metric ${index + 1} value must be a number`);
        }
        if (!metric.unit || metric.unit.trim().length === 0) {
          throw new Error(`Metric ${index + 1} unit is required`);
        }
      });
    }
    if (win.attachments && Array.isArray(win.attachments)) {
      win.attachments.forEach((attachment, index) => {
        if (!attachment.filename || attachment.filename.trim().length === 0) {
          throw new Error(`Attachment ${index + 1} filename is required`);
        }
        if (!attachment.contentType || attachment.contentType.trim().length === 0) {
          throw new Error(`Attachment ${index + 1} content type is required`);
        }
        if (!attachment.url || attachment.url.trim().length === 0) {
          throw new Error(`Attachment ${index + 1} URL is required`);
        }
      });
    }
    if (win.achievedAt && new Date(win.achievedAt) > /* @__PURE__ */ new Date()) {
      throw new Error("Achievement date cannot be in the future");
    }
  }
};
function createWinsService(apiClient) {
  return new WinsServiceImpl(apiClient);
}

// src/services/index.ts
function createAllServices(apiClient) {
  return {
    users: createUsersService(apiClient),
    training: createTrainingService(apiClient),
    vendors: createVendorsService(apiClient),
    email: createEmailService(apiClient),
    wins: createWinsService(apiClient)
  };
}
function createConfiguredServices(config) {
  const services = createAllServices(config.apiClient);
  if (config.enableLogging) {
    Object.keys(services).forEach((serviceName) => {
      const service = services[serviceName];
      wrapServiceWithLogging(service, serviceName);
    });
  }
  return services;
}
function wrapServiceWithLogging(service, serviceName) {
  const originalMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(service)).filter((name) => name !== "constructor" && typeof service[name] === "function");
  originalMethods.forEach((methodName) => {
    const originalMethod = service[methodName];
    service[methodName] = async function(...args) {
      console.log(`[${serviceName}Service] ${methodName}`, args);
      try {
        const result = await originalMethod.apply(this, args);
        console.log(`[${serviceName}Service] ${methodName} - Success`);
        return result;
      } catch (error) {
        console.error(`[${serviceName}Service] ${methodName} - Error:`, error);
        throw error;
      }
    };
  });
}

export { API_CONFIG, APP_METADATA, AUTH_CONFIG, ApiErrorHandler, AuthError, AuthenticationInterceptor, ConflictError, DATE_FORMATS, ERROR_CODES, EVENTS, EmailServiceImpl, EmailStatus, ErrorType, FEATURE_FLAGS, FILE_CONFIG, FileError, HTTP_STATUS, LocalStorageTokenManager, LuminarAPIClientImpl, LuminarError, NetworkError, NotFoundError, PERMISSIONS, PermissionError, ROUTES, STORAGE_KEYS, THEME_CONFIG, TrainingDifficulty, TrainingServiceImpl, TrainingStatus, UI_CONFIG, USER_ROLES, UsersServiceImpl, VALIDATION_RULES, ValidationError, VendorsServiceImpl, WinCategory, WinImpact, WinsServiceImpl, apiErrorSchema, apiResponseSchema, baseQuerySchema, capitalize, changePasswordSchema, cn, createAllServices, createApiError, createAuthError, createAuthInterceptor, createConfiguredAPIClient, createConfiguredServices, createEmailService, createErrorFromStatus, createFileError, createLuminarAPIClient, createNetworkError, createTrainingService, createUsersService, createValidationError, createVendorsService, createWinsService, debounce, deepClone, emailSchema, fileUploadSchema, forgotPasswordSchema, formatBytes, formatCurrency, formatNumber, generateId, get, getDefaultAPIClient, getErrorMessage, idSchema, initializeDefaultAPIClient, isApiError, isAuthError, isConflictError, isEmpty, isFileError, isLuminarError, isNetworkError, isNotFoundError, isPermissionError, isValidationError, logError, loginSchema, nameSchema, omit, paginatedResponseSchema, paginationSchema, passwordSchema, phoneSchema, pick, registerSchema, resetPasswordSchema, searchSchema, set, setDefaultAPIClient, sleep, slugSchema, sortSchema, throttle, toLuminarError, truncate, urlSchema, userPreferencesSchema, userProfileSchema };
//# sourceMappingURL=index.mjs.map
//# sourceMappingURL=index.mjs.map