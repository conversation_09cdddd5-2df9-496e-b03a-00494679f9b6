'use strict';

var msw = require('msw');
var faker = require('@faker-js/faker');
var node = require('msw/node');
var browser = require('msw/browser');

// src/mocks/api.ts
var MockAPI = class {
  baseUrl;
  handlers = [];
  constructor(baseUrl = "http://localhost:3000/api") {
    this.baseUrl = baseUrl;
  }
  // Add a GET endpoint
  get(path, response, options = {}) {
    const handler = msw.http.get(`${this.baseUrl}${path}`, async () => {
      if (options.delay) {
        await new Promise((resolve) => setTimeout(resolve, options.delay));
      }
      return msw.HttpResponse.json(response, { status: options.status || 200 });
    });
    this.handlers.push(handler);
    return this;
  }
  // Add a POST endpoint
  post(path, handler, options = {}) {
    const mockHandler = msw.http.post(`${this.baseUrl}${path}`, async ({ request }) => {
      const body = await request.json();
      if (options.delay) {
        await new Promise((resolve) => setTimeout(resolve, options.delay));
      }
      const response = await handler(body);
      return msw.HttpResponse.json(response);
    });
    this.handlers.push(mockHandler);
    return this;
  }
  // Add a PUT endpoint
  put(path, handler, options = {}) {
    const mockHandler = msw.http.put(`${this.baseUrl}${path}`, async ({ params, request }) => {
      const body = await request.json();
      if (options.delay) {
        await new Promise((resolve) => setTimeout(resolve, options.delay));
      }
      const response = await handler(params, body);
      return msw.HttpResponse.json(response);
    });
    this.handlers.push(mockHandler);
    return this;
  }
  // Add a DELETE endpoint
  delete(path, response = { success: true }, options = {}) {
    const handler = msw.http.delete(`${this.baseUrl}${path}`, async () => {
      if (options.delay) {
        await new Promise((resolve) => setTimeout(resolve, options.delay));
      }
      return msw.HttpResponse.json(response);
    });
    this.handlers.push(handler);
    return this;
  }
  // Add error response
  error(path, error, status = 500) {
    const handler = msw.http.get(`${this.baseUrl}${path}`, () => {
      return msw.HttpResponse.json({ error }, { status });
    });
    this.handlers.push(handler);
    return this;
  }
  // Get all handlers
  getHandlers() {
    return this.handlers;
  }
  // Create paginated response
  paginated(path, itemGenerator, totalItems = 100) {
    const handler = msw.http.get(`${this.baseUrl}${path}`, ({ request }) => {
      const url = new URL(request.url);
      const page = parseInt(url.searchParams.get("page") || "1");
      const limit = parseInt(url.searchParams.get("limit") || "10");
      const offset = (page - 1) * limit;
      const items = Array.from({ length: Math.min(limit, totalItems - offset) }, itemGenerator);
      return msw.HttpResponse.json({
        data: items,
        pagination: {
          page,
          limit,
          total: totalItems,
          totalPages: Math.ceil(totalItems / limit),
          hasNext: page < Math.ceil(totalItems / limit),
          hasPrev: page > 1
        }
      });
    });
    this.handlers.push(handler);
    return this;
  }
  // Create CRUD endpoints for a resource
  crud(resource, generator) {
    const items = /* @__PURE__ */ new Map();
    this.get(`/${resource}`, () => {
      return Array.from(items.values());
    });
    this.handlers.push(
      msw.http.get(`${this.baseUrl}/${resource}/:id`, ({ params }) => {
        const item = items.get(params.id);
        if (!item) {
          return msw.HttpResponse.json({ error: "Not found" }, { status: 404 });
        }
        return msw.HttpResponse.json(item);
      })
    );
    this.post(`/${resource}`, (body) => {
      const id = Date.now().toString();
      const item = { ...generator(), ...body, id };
      items.set(id, item);
      return item;
    });
    this.handlers.push(
      msw.http.put(`${this.baseUrl}/${resource}/:id`, async ({ params, request }) => {
        const body = await request.json();
        const id = params.id;
        const existing = items.get(id);
        if (!existing) {
          return msw.HttpResponse.json({ error: "Not found" }, { status: 404 });
        }
        const updated = { ...existing, ...body };
        items.set(id, updated);
        return msw.HttpResponse.json(updated);
      })
    );
    this.handlers.push(
      msw.http.delete(`${this.baseUrl}/${resource}/:id`, ({ params }) => {
        const id = params.id;
        if (!items.has(id)) {
          return msw.HttpResponse.json({ error: "Not found" }, { status: 404 });
        }
        items.delete(id);
        return new msw.HttpResponse(null, { status: 204 });
      })
    );
    return this;
  }
  // Create file upload endpoint
  fileUpload(path, handler) {
    const uploadHandler = msw.http.post(`${this.baseUrl}${path}`, async ({ request }) => {
      const formData = await request.formData();
      const file = formData.get("file");
      if (!file) {
        return msw.HttpResponse.json({ error: "No file provided" }, { status: 400 });
      }
      const response = handler ? await handler(file) : {
        id: Date.now().toString(),
        filename: file.name,
        size: file.size,
        type: file.type,
        url: `/uploads/${file.name}`
      };
      return msw.HttpResponse.json(response);
    });
    this.handlers.push(uploadHandler);
    return this;
  }
};
var createMockAuthAPI = () => {
  return new MockAPI().post("/auth/login", ({ email, password }) => {
    if (email === "<EMAIL>" && password === "password") {
      return {
        user: { id: "1", email, name: "Test User" },
        token: "mock-jwt-token"
      };
    }
    throw new Error("Invalid credentials");
  }).post("/auth/logout", () => ({ success: true })).get("/auth/me", { id: "1", email: "<EMAIL>", name: "Test User" });
};
var createMockUserAPI = () => {
  return new MockAPI().crud("users", () => ({
    name: "John Doe",
    email: "<EMAIL>",
    role: "user"
  }));
};
var createMockUser = (overrides = {}) => ({
  id: faker.faker.string.uuid(),
  email: faker.faker.internet.email(),
  name: faker.faker.person.fullName(),
  avatar: faker.faker.image.avatar(),
  role: "user",
  createdAt: faker.faker.date.past().toISOString(),
  updatedAt: faker.faker.date.recent().toISOString(),
  ...overrides
});
var createMockApiResponse = (data, overrides = {}) => ({
  data,
  status: "success",
  message: "Operation successful",
  timestamp: (/* @__PURE__ */ new Date()).toISOString(),
  ...overrides
});
var createMockProduct = (overrides = {}) => ({
  id: faker.faker.string.uuid(),
  name: faker.faker.commerce.productName(),
  description: faker.faker.commerce.productDescription(),
  price: parseFloat(faker.faker.commerce.price()),
  image: faker.faker.image.url(),
  category: faker.faker.commerce.department(),
  inStock: faker.faker.datatype.boolean(),
  rating: faker.faker.number.float({ min: 1, max: 5, fractionDigits: 1 }),
  ...overrides
});
var createMockProducts = (count, overrides = {}) => {
  return Array.from({ length: count }, () => createMockProduct(overrides));
};

// src/mocks/handlers.ts
var API_BASE_URL = process.env.VITE_API_URL || "http://localhost:3000/api";
var defaultHandlers = [
  // Auth endpoints
  msw.http.post(`${API_BASE_URL}/auth/login`, async ({ request }) => {
    const body = await request.json();
    await msw.delay(300);
    if (body.email === "<EMAIL>" && body.password === "password") {
      return msw.HttpResponse.json(
        createMockApiResponse({
          user: createMockUser({ email: body.email }),
          token: "mock-jwt-token"
        })
      );
    }
    return msw.HttpResponse.json(
      { error: "Invalid credentials" },
      { status: 401 }
    );
  }),
  msw.http.post(`${API_BASE_URL}/auth/logout`, async () => {
    await msw.delay(100);
    return msw.HttpResponse.json({ success: true });
  }),
  msw.http.get(`${API_BASE_URL}/auth/me`, async () => {
    await msw.delay(200);
    return msw.HttpResponse.json(
      createMockApiResponse(createMockUser())
    );
  }),
  // User endpoints
  msw.http.get(`${API_BASE_URL}/users`, async ({ request }) => {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    await msw.delay(300);
    const users = Array.from({ length: limit }, () => createMockUser());
    return msw.HttpResponse.json(
      createMockApiResponse({
        data: users,
        pagination: {
          page,
          limit,
          total: 100,
          totalPages: 10
        }
      })
    );
  }),
  msw.http.get(`${API_BASE_URL}/users/:id`, async ({ params }) => {
    await msw.delay(200);
    return msw.HttpResponse.json(
      createMockApiResponse(createMockUser({ id: params.id }))
    );
  }),
  msw.http.post(`${API_BASE_URL}/users`, async ({ request }) => {
    const body = await request.json();
    await msw.delay(300);
    return msw.HttpResponse.json(
      createMockApiResponse(createMockUser(body)),
      { status: 201 }
    );
  }),
  msw.http.put(`${API_BASE_URL}/users/:id`, async ({ request, params }) => {
    const body = await request.json();
    await msw.delay(300);
    return msw.HttpResponse.json(
      createMockApiResponse(createMockUser({ ...body, id: params.id }))
    );
  }),
  msw.http.delete(`${API_BASE_URL}/users/:id`, async () => {
    await msw.delay(200);
    return msw.HttpResponse.json({ success: true });
  }),
  // Products endpoints
  msw.http.get(`${API_BASE_URL}/products`, async () => {
    await msw.delay(400);
    return msw.HttpResponse.json(
      createMockApiResponse(createMockProducts(20))
    );
  }),
  // Generic error handler
  msw.http.get(`${API_BASE_URL}/error`, () => {
    return msw.HttpResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  })
];
var createErrorHandlers = (endpoints) => {
  return endpoints.map(
    (endpoint) => msw.http.get(`${API_BASE_URL}${endpoint}`, () => {
      return msw.HttpResponse.json(
        { error: "Test error" },
        { status: 500 }
      );
    })
  );
};
var createDelayedHandlers = (_delay) => {
  return defaultHandlers.map((handler) => {
    return handler;
  });
};
var createCrudHandlers = (resource, generator, baseUrl = API_BASE_URL) => {
  const resourceUrl = `${baseUrl}/${resource}`;
  return [
    // List
    msw.http.get(resourceUrl, async () => {
      await msw.delay(200);
      const items = Array.from({ length: 10 }, generator);
      return msw.HttpResponse.json(createMockApiResponse(items));
    }),
    // Get by ID
    msw.http.get(`${resourceUrl}/:id`, async ({ params }) => {
      await msw.delay(150);
      return msw.HttpResponse.json(
        createMockApiResponse({ ...generator(), id: params.id })
      );
    }),
    // Create
    msw.http.post(resourceUrl, async ({ request }) => {
      const body = await request.json();
      await msw.delay(250);
      return msw.HttpResponse.json(
        createMockApiResponse({ ...generator(), ...body }),
        { status: 201 }
      );
    }),
    // Update
    msw.http.put(`${resourceUrl}/:id`, async ({ request, params }) => {
      const body = await request.json();
      await msw.delay(200);
      return msw.HttpResponse.json(
        createMockApiResponse({ ...generator(), ...body, id: params.id })
      );
    }),
    // Delete
    msw.http.delete(`${resourceUrl}/:id`, async () => {
      await msw.delay(150);
      return new msw.HttpResponse(null, { status: 204 });
    })
  ];
};
var createWebSocketHandlers = () => {
  return [];
};
var createGraphQLHandlers = (endpoint = `${API_BASE_URL}/graphql`) => {
  return [
    msw.http.post(endpoint, async ({ request }) => {
      const body = await request.json();
      const { query, variables } = body;
      if (query.includes("GetUser")) {
        return msw.HttpResponse.json({
          data: {
            user: createMockUser(variables?.id ? { id: variables.id } : {})
          }
        });
      }
      return msw.HttpResponse.json({
        errors: [{ message: "Query not mocked" }]
      });
    })
  ];
};
var server = node.setupServer(...defaultHandlers);
var startMockServer = () => {
  server.listen({
    onUnhandledRequest: "bypass"
    // or 'warn' or 'error'
  });
};
var stopMockServer = () => {
  server.close();
};
var resetMockServer = () => {
  server.resetHandlers();
};
var addMockHandler = (...handlers) => {
  server.use(...handlers);
};
var configureMockServer = (options) => {
  server.listen(options);
};
var serverState = {};
var setServerState = (key, value) => {
  serverState[key] = value;
};
var getServerState = (key) => {
  return serverState[key];
};
var clearServerState = () => {
  serverState = {};
};
var mockScenarios = {
  // Simulate network failure
  networkError: () => {
    server.use(
      msw.http.all("*", () => {
        return msw.HttpResponse.error();
      })
    );
  },
  // Simulate slow network
  slowNetwork: (_delayMs = 3e3) => {
    server.use(
      ...defaultHandlers.map((handler) => {
        return handler;
      })
    );
  },
  // Simulate server errors
  serverErrors: () => {
    server.use(
      msw.http.all("*", () => {
        return msw.HttpResponse.json(
          { error: "Internal Server Error" },
          { status: 500 }
        );
      })
    );
  },
  // Reset to default
  reset: () => {
    server.resetHandlers(...defaultHandlers);
  }
};
var worker = browser.setupWorker(...defaultHandlers);
var startMockWorker = async () => {
  if (typeof window !== "undefined") {
    return worker.start({
      onUnhandledRequest: "bypass",
      serviceWorker: {
        url: "/mockServiceWorker.js"
      }
    });
  }
  return void 0;
};
var stopMockWorker = () => {
  if (typeof window !== "undefined") {
    worker.stop();
  }
};
var enableMockingInDevelopment = () => {
  if (process.env.NODE_ENV === "development" && process.env.VITE_ENABLE_MOCKING === "true") {
    startMockWorker().then(() => {
      console.log("[MSW] Mocking enabled");
    });
  }
};
var enableMockingForStorybook = () => {
  if (typeof window !== "undefined" && window.location.pathname.includes("iframe.html")) {
    startMockWorker();
  }
};

exports.MockAPI = MockAPI;
exports.addMockHandler = addMockHandler;
exports.clearServerState = clearServerState;
exports.configureMockServer = configureMockServer;
exports.createCrudHandlers = createCrudHandlers;
exports.createDelayedHandlers = createDelayedHandlers;
exports.createErrorHandlers = createErrorHandlers;
exports.createGraphQLHandlers = createGraphQLHandlers;
exports.createMockAuthAPI = createMockAuthAPI;
exports.createMockUserAPI = createMockUserAPI;
exports.createWebSocketHandlers = createWebSocketHandlers;
exports.defaultHandlers = defaultHandlers;
exports.enableMockingForStorybook = enableMockingForStorybook;
exports.enableMockingInDevelopment = enableMockingInDevelopment;
exports.getServerState = getServerState;
exports.mockScenarios = mockScenarios;
exports.resetMockServer = resetMockServer;
exports.server = server;
exports.setServerState = setServerState;
exports.startMockServer = startMockServer;
exports.startMockWorker = startMockWorker;
exports.stopMockServer = stopMockServer;
exports.stopMockWorker = stopMockWorker;
exports.worker = worker;
//# sourceMappingURL=index.cjs.map
//# sourceMappingURL=index.cjs.map