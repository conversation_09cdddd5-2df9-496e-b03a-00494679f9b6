import { expect } from 'vitest'

// Extend Vitest's expect with custom matchers
interface CustomMatchers<R = unknown> {
  toBeWithinRange(min: number, max: number): R
  toBeValidEmail(): R
  toBeValidUrl(): R
  toBeValidDate(): R
  toHaveBeenCalledWithMatch(expected: any): R
  toHaveClass(className: string): R
  toHaveStyles(styles: Record<string, string>): R
  toBeAccessible(): R
  toHaveAttribute(name: string, value?: string): R
  toBeRenderedWithProps(props: Record<string, any>): R
}

declare module 'vitest' {
  interface Assertion<T = any> extends CustomMatchers<T> {}
  interface AsymmetricMatchersContaining extends CustomMatchers {}
}

// Custom matcher implementations
expect.extend({
  toBeWithinRange(received: number, min: number, max: number) {
    const pass = received >= min && received <= max
    
    return {
      pass,
      message: () =>
        pass
          ? `expected ${received} not to be within range ${min} - ${max}`
          : `expected ${received} to be within range ${min} - ${max}`,
    }
  },

  toBeValidEmail(received: string) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const pass = emailRegex.test(received)
    
    return {
      pass,
      message: () =>
        pass
          ? `expected "${received}" not to be a valid email`
          : `expected "${received}" to be a valid email`,
    }
  },

  toBeValidUrl(received: string) {
    let pass = false
    try {
      new URL(received)
      pass = true
    } catch {
      pass = false
    }
    
    return {
      pass,
      message: () =>
        pass
          ? `expected "${received}" not to be a valid URL`
          : `expected "${received}" to be a valid URL`,
    }
  },

  toBeValidDate(received: any) {
    const date = new Date(received)
    const pass = !isNaN(date.getTime())
    
    return {
      pass,
      message: () =>
        pass
          ? `expected "${received}" not to be a valid date`
          : `expected "${received}" to be a valid date`,
    }
  },

  toHaveBeenCalledWithMatch(received: any, expected: any) {
    if (typeof received.mock === 'undefined') {
      return {
        pass: false,
        message: () => 'expected a mock function',
      }
    }
    
    const calls = received.mock.calls
    const pass = calls.some((call: any[]) =>
      call.some(arg => {
        if (expected instanceof RegExp) {
          return expected.test(String(arg))
        }
        return JSON.stringify(arg).includes(JSON.stringify(expected))
      })
    )
    
    return {
      pass,
      message: () =>
        pass
          ? `expected mock not to have been called with matching ${expected}`
          : `expected mock to have been called with matching ${expected}`,
    }
  },

  toHaveClass(received: HTMLElement, className: string) {
    const pass = received.classList.contains(className)
    
    return {
      pass,
      message: () =>
        pass
          ? `expected element not to have class "${className}"`
          : `expected element to have class "${className}"`,
    }
  },

  toHaveStyles(received: HTMLElement, styles: Record<string, string>) {
    const computed = window.getComputedStyle(received)
    const mismatches: string[] = []
    
    Object.entries(styles).forEach(([property, expectedValue]) => {
      const actualValue = computed.getPropertyValue(property)
      if (actualValue !== expectedValue) {
        mismatches.push(
          `${property}: expected "${expectedValue}", got "${actualValue}"`
        )
      }
    })
    
    const pass = mismatches.length === 0
    
    return {
      pass,
      message: () =>
        pass
          ? `expected element not to have styles ${JSON.stringify(styles)}`
          : `expected element to have styles:\n${mismatches.join('\n')}`,
    }
  },

  toBeAccessible(received: HTMLElement) {
    const issues: string[] = []
    
    // Check for alt text on images
    const images = received.querySelectorAll('img')
    images.forEach(img => {
      if (!img.alt) {
        issues.push(`Image missing alt attribute: ${img.src}`)
      }
    })
    
    // Check for labels on form inputs
    const inputs = received.querySelectorAll('input, textarea, select')
    inputs.forEach(input => {
      const id = input.id
      if (id) {
        const label = received.querySelector(`label[for="${id}"]`)
        if (!label) {
          issues.push(`Input missing label: ${input.outerHTML}`)
        }
      }
    })
    
    // Check for ARIA labels on interactive elements
    const buttons = received.querySelectorAll('button')
    buttons.forEach(button => {
      if (!button.textContent?.trim() && !button.getAttribute('aria-label')) {
        issues.push(`Button missing text or aria-label: ${button.outerHTML}`)
      }
    })
    
    const pass = issues.length === 0
    
    return {
      pass,
      message: () =>
        pass
          ? 'expected element not to be accessible'
          : `expected element to be accessible:\n${issues.join('\n')}`,
    }
  },

  toHaveAttribute(received: HTMLElement, name: string, value?: string) {
    const hasAttribute = received.hasAttribute(name)
    const attributeValue = received.getAttribute(name)
    
    const pass = value === undefined
      ? hasAttribute
      : hasAttribute && attributeValue === value
    
    return {
      pass,
      message: () => {
        if (value === undefined) {
          return pass
            ? `expected element not to have attribute "${name}"`
            : `expected element to have attribute "${name}"`
        }
        return pass
          ? `expected element not to have attribute "${name}" with value "${value}"`
          : `expected element to have attribute "${name}" with value "${value}", got "${attributeValue}"`
      },
    }
  },

  toBeRenderedWithProps(received: any, props: Record<string, any>) {
    if (!received.mock) {
      return {
        pass: false,
        message: () => 'expected a mock component',
      }
    }
    
    const calls = received.mock.calls
    const pass = calls.some((call: any[]) => {
      const receivedProps = call[0]
      return Object.entries(props).every(([key, value]) => {
        return JSON.stringify(receivedProps[key]) === JSON.stringify(value)
      })
    })
    
    return {
      pass,
      message: () =>
        pass
          ? `expected component not to be rendered with props ${JSON.stringify(props)}`
          : `expected component to be rendered with props ${JSON.stringify(props)}`,
    }
  },
})

// Helper functions for custom assertions
export const customAssertions = {
  // Assert element has specific data attributes
  assertDataAttributes: (
    element: HTMLElement,
    attributes: Record<string, string>
  ) => {
    Object.entries(attributes).forEach(([key, value]) => {
      expect(element.dataset[key]).toBe(value)
    })
  },

  // Assert element matches snapshot with options
  assertSnapshot: (element: HTMLElement, options?: any) => {
    expect(element).toMatchSnapshot(options)
  },

  // Assert async operation completes within time
  assertCompletesWithin: async (
    operation: () => Promise<any>,
    ms: number
  ) => {
    const start = Date.now()
    await operation()
    const duration = Date.now() - start
    expect(duration).toBeLessThan(ms)
  },
}