import axios from 'axios';
import type { ABTestConfig, ABTestVariant } from './types';
import { FeatureFlagService } from './feature-flag-service';

export class ABTestingService {
  private static instance: ABTestingService;
  private tests: Map<string, ABTestConfig> = new Map();
  private apiEndpoint: string;
  private featureFlagService: FeatureFlagService;

  private constructor(apiEndpoint: string) {
    this.apiEndpoint = apiEndpoint;
    this.featureFlagService = FeatureFlagService.getInstance();
  }

  static getInstance(apiEndpoint: string = '/api/ab-tests'): ABTestingService {
    if (!ABTestingService.instance) {
      ABTestingService.instance = new ABTestingService(apiEndpoint);
    }
    return ABTestingService.instance;
  }

  /**
   * Initialize A/B testing service
   */
  async initialize(): Promise<void> {
    await this.fetchActiveTests();
  }

  /**
   * Fetch active A/B tests
   */
  private async fetchActiveTests(): Promise<void> {
    try {
      const response = await axios.get<ABTestConfig[]>(
        `${this.apiEndpoint}/active`
      );
      
      this.tests.clear();
      response.data.forEach(test => {
        this.tests.set(test.id, test);
      });
    } catch (error) {
      console.error('Failed to fetch A/B tests:', error);
    }
  }

  /**
   * Get variant for an A/B test
   */
  getTestVariant(testId: string): ABTestVariant | null {
    const test = this.tests.get(testId);
    if (!test || test.status !== 'running') return null;

    // Check if user is in test traffic allocation
    const userContext = this.featureFlagService.getUserContext();
    const hash = this.hashString(`${userContext.userId || 'anonymous'}:${testId}`);
    const percentage = Math.abs(hash % 100);
    
    if (percentage >= test.trafficAllocation) {
      return null; // User not in test
    }

    // Get variant from feature flag
    const variantKey = this.featureFlagService.getVariant(test.featureFlag);
    if (!variantKey) return null;

    const variant = test.variants.find(v => v.key === variantKey);
    if (variant) {
      this.trackImpression(testId, variant.key);
    }
    return variant || null;
  }

  /**
   * Track conversion event
   */
  async trackConversion(
    testId: string,
    metricKey: string,
    value: number = 1
  ): Promise<void> {
    const test = this.tests.get(testId);
    if (!test || test.status !== 'running') return;

    const variant = this.getTestVariant(testId);
    if (!variant) return;

    try {
      await axios.post(`${this.apiEndpoint}/${testId}/conversions`, {
        variant: variant.key,
        metric: metricKey,
        value,
        userId: this.featureFlagService.getUserContext().userId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Failed to track conversion:', error);
    }
  }

  /**
   * Track impression event
   */
  private async trackImpression(
    testId: string,
    variantKey: string
  ): Promise<void> {
    try {
      await axios.post(`${this.apiEndpoint}/${testId}/impressions`, {
        variant: variantKey,
        userId: this.featureFlagService.getUserContext().userId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Failed to track impression:', error);
    }
  }

  /**
   * Get test results
   */
  async getTestResults(testId: string): Promise<ABTestResults | null> {
    try {
      const response = await axios.get<ABTestResults>(
        `${this.apiEndpoint}/${testId}/results`
      );
      return response.data;
    } catch (error) {
      console.error('Failed to fetch test results:', error);
      return null;
    }
  }

  /**
   * Calculate statistical significance
   */
  calculateSignificance(
    control: ABTestVariant,
    variant: ABTestVariant
  ): number {
    if (!control.impressions || !variant.impressions) return 0;

    const p1 = (control.conversions || 0) / control.impressions;
    const p2 = (variant.conversions || 0) / variant.impressions;
    const n1 = control.impressions;
    const n2 = variant.impressions;

    const pooledP = ((control.conversions || 0) + (variant.conversions || 0)) / (n1 + n2);
    const se = Math.sqrt(pooledP * (1 - pooledP) * (1/n1 + 1/n2));
    
    if (se === 0) return 0;
    
    const z = (p2 - p1) / se;
    const significance = this.normalCDF(Math.abs(z)) * 2 - 1;
    
    return Math.round(significance * 100);
  }

  /**
   * Normal cumulative distribution function
   */
  private normalCDF(x: number): number {
    const a1 = 0.254829592;
    const a2 = -0.284496736;
    const a3 = 1.421413741;
    const a4 = -1.453152027;
    const a5 = 1.061405429;
    const p = 0.3275911;

    const sign = x < 0 ? -1 : 1;
    x = Math.abs(x) / Math.sqrt(2.0);

    const t = 1.0 / (1.0 + p * x);
    const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

    return 0.5 * (1.0 + sign * y);
  }

  /**
   * Hash string to number
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash;
  }

  /**
   * Create new A/B test
   */
  async createTest(config: Partial<ABTestConfig>): Promise<ABTestConfig | null> {
    try {
      const response = await axios.post<ABTestConfig>(
        this.apiEndpoint,
        config
      );
      const test = response.data;
      this.tests.set(test.id, test);
      return test;
    } catch (error) {
      console.error('Failed to create A/B test:', error);
      return null;
    }
  }

  /**
   * Update test status
   */
  async updateTestStatus(
    testId: string,
    status: ABTestConfig['status']
  ): Promise<void> {
    try {
      await axios.patch(`${this.apiEndpoint}/${testId}`, { status });
      const test = this.tests.get(testId);
      if (test) {
        test.status = status;
      }
    } catch (error) {
      console.error('Failed to update test status:', error);
    }
  }
}

interface ABTestResults {
  test: ABTestConfig;
  variants: ABTestVariantResult[];
  winner?: string;
  confidence?: number;
  duration: number;
  totalImpressions: number;
  totalConversions: number;
}

interface ABTestVariantResult extends ABTestVariant {
  metrics: Record<string, {
    value: number;
    count: number;
    average: number;
  }>;
  uplift?: number;
  confidence?: number;
}