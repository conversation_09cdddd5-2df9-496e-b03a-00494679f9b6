import * as vitest from 'vitest';
import { vi } from 'vitest';
export { afterAll, afterEach, beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';
import * as react_jsx_runtime from 'react/jsx-runtime';
import React, { ReactNode, ReactElement } from 'react';
import { RenderOptions, RenderResult } from '@testing-library/react';
export * from '@testing-library/react';
import { QueryClient } from '@tanstack/react-query';
import userEvent__default from '@testing-library/user-event';
export * from '@testing-library/user-event';
export { default as userEvent } from '@testing-library/user-event';
import * as msw_node from 'msw/node';
import * as msw from 'msw';
import { HttpResponse } from 'msw';

interface TestProvidersProps {
    children: ReactNode;
    queryClient?: QueryClient;
    initialEntries?: string[];
    routeConfig?: any;
}
declare function createTestQueryClient(): QueryClient;
declare function TestProviders({ children, queryClient }: TestProvidersProps): react_jsx_runtime.JSX.Element;
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
    queryClient?: QueryClient;
    initialEntries?: string[];
    routeConfig?: any;
    withProviders?: boolean;
}
declare function renderWithProviders(ui: ReactElement, options?: CustomRenderOptions): RenderResult & {
    user: ReturnType<typeof userEvent__default.setup>;
};
interface MockApiClient {
    get: ReturnType<typeof vi.fn>;
    post: ReturnType<typeof vi.fn>;
    put: ReturnType<typeof vi.fn>;
    patch: ReturnType<typeof vi.fn>;
    delete: ReturnType<typeof vi.fn>;
    addRequestInterceptor: ReturnType<typeof vi.fn>;
    addResponseInterceptor: ReturnType<typeof vi.fn>;
    removeRequestInterceptor: ReturnType<typeof vi.fn>;
    removeResponseInterceptor: ReturnType<typeof vi.fn>;
    setBaseURL: ReturnType<typeof vi.fn>;
    setDefaultHeaders: ReturnType<typeof vi.fn>;
    getConfig: ReturnType<typeof vi.fn>;
}
declare function createMockApiClient(): MockApiClient;
interface MockAuthState {
    isAuthenticated: boolean;
    user?: any;
    permissions?: string[];
    roles?: string[];
}
declare function createMockAuthProvider(initialState?: MockAuthState): {
    MockAuthProvider: ({ children, state }: {
        children: ReactNode;
        state?: MockAuthState;
    }) => react_jsx_runtime.JSX.Element;
    useAuth: () => MockAuthState;
    AuthContext: React.Context<MockAuthState>;
};
declare const testDataFactory: {
    createUser: (overrides?: Partial<any>) => {
        id: string;
        name: string;
        email: string;
        roles: string[];
        permissions: string[];
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
    };
    createTraining: (overrides?: Partial<any>) => {
        id: string;
        title: string;
        description: string;
        category: {
            id: string;
            name: string;
        };
        duration: number;
        difficulty: string;
        instructor: {
            id: string;
            name: string;
            email: string;
            roles: string[];
            permissions: string[];
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
        };
        participants: never[];
        status: string;
        isPublic: boolean;
        createdAt: Date;
        updatedAt: Date;
    };
    createApiResponse: <T>(data: T, overrides?: Partial<any>) => {
        data: T;
        status: number;
        statusText: string;
        message: string;
    };
    createApiError: (overrides?: Partial<any>) => {
        message: string;
        code: string;
        details: {
            type: string;
            status: number;
            timestamp: Date;
        };
    };
};
declare const mockHandlers: {
    success: <T>(data: T) => {
        status: number;
        json: {
            data: T;
            status: number;
            statusText: string;
            message: string;
        };
    };
    error: (status?: number, message?: string) => {
        status: number;
        json: {
            message: string;
            code: string;
            details: {
                type: string;
                status: number;
                timestamp: Date;
            };
        };
    };
    delay: (ms?: number) => Promise<unknown>;
};
declare const componentTestUtils: {
    waitForComponent: (getByTestId: (id: string) => HTMLElement, testId: string) => Promise<HTMLElement>;
    simulateNetworkDelay: (ms?: number) => Promise<unknown>;
    mockIntersectionObserver: () => vitest.Mock<(...args: any[]) => any>;
    mockResizeObserver: () => vitest.Mock<(...args: any[]) => any>;
};
declare const a11yTestUtils: {
    checkAriaAttributes: (element: HTMLElement, requiredAttributes: string[]) => void;
    checkKeyboardNavigation: (user: ReturnType<typeof userEvent__default.setup>, element: HTMLElement) => Promise<void>;
    checkColorContrast: (element: HTMLElement) => void;
};
declare const performanceTestUtils: {
    measureRenderTime: (renderFn: () => Promise<void> | void) => Promise<number>;
    checkMemoryLeaks: (cleanup: () => void) => () => void;
};
declare const customMatchers: {
    toBeAccessible: (element: HTMLElement) => {
        message: () => string;
        pass: boolean;
    };
};

declare const handlers: msw.HttpHandler[];
declare const server: msw_node.SetupServerApi;
declare const mockServerUtils: {
    addHandler: (handler: any) => void;
    resetHandlers: () => void;
    createErrorResponse: (status: number, message: string) => HttpResponse<msw.JsonBodyType>;
    createDelayedResponse: (data: any, delay?: number) => Promise<unknown>;
    mockNetworkError: (url: string) => void;
    mockTimeout: (url: string, timeout?: number) => void;
};
declare const setupMockServer: () => void;

/**
 * Component Test Patterns - Reusable testing patterns for components
 */

interface ComponentTestPattern<P = any> {
    component: (props: P) => ReactElement;
    defaultProps: P;
    testCases: ComponentTestCase<P>[];
}
interface ComponentTestCase<P = any> {
    name: string;
    props?: Partial<P>;
    setup?: () => void | Promise<void>;
    test: (props: P) => void | Promise<void>;
    cleanup?: () => void | Promise<void>;
}
declare function createComponentTester<P extends object>(pattern: ComponentTestPattern<P>): {
    testAll: () => void;
    testCase: (caseName: string) => void;
    addTestCase: (testCase: ComponentTestCase<P>) => void;
};
declare const commonTestPatterns: {
    button: {
        rendering: (getButton: () => HTMLElement) => {
            name: string;
            test: () => void;
        };
        clickHandler: (getButton: () => HTMLElement, onClick: () => void) => {
            name: string;
            test: () => Promise<void>;
        };
        disabled: (getButton: () => HTMLElement) => {
            name: string;
            props: {
                disabled: boolean;
            };
            test: () => void;
        };
        loading: (getButton: () => HTMLElement) => {
            name: string;
            props: {
                loading: boolean;
            };
            test: () => void;
        };
        accessibility: (getButton: () => HTMLElement) => {
            name: string;
            test: () => void;
        };
    };
    input: {
        rendering: (getInput: () => HTMLElement) => {
            name: string;
            test: () => void;
        };
        valueChange: (getInput: () => HTMLElement, onChange: () => void) => {
            name: string;
            test: () => Promise<void>;
        };
        validation: (getInput: () => HTMLElement) => {
            name: string;
            props: {
                error: string;
            };
            test: () => void;
        };
        required: (getInput: () => HTMLElement) => {
            name: string;
            props: {
                required: boolean;
            };
            test: () => void;
        };
    };
    modal: {
        rendering: (isOpen: boolean) => {
            name: string;
            props: {
                isOpen: boolean;
            };
            test: () => void;
        };
        closeOnEscape: (onClose: () => void) => {
            name: string;
            props: {
                isOpen: boolean;
            };
            test: () => Promise<void>;
        };
        closeOnOverlay: (onClose: () => void) => {
            name: string;
            props: {
                isOpen: boolean;
            };
            test: () => Promise<void>;
        };
        focusTrap: () => {
            name: string;
            props: {
                isOpen: boolean;
            };
            test: () => Promise<void>;
        };
    };
    list: {
        rendering: (items: any[]) => {
            name: string;
            props: {
                items: any[];
            };
            test: () => void;
        };
        emptyState: () => {
            name: string;
            props: {
                items: never[];
            };
            test: () => void;
        };
        loading: () => {
            name: string;
            props: {
                loading: boolean;
            };
            test: () => void;
        };
        selection: (onSelect: () => void) => {
            name: string;
            test: () => Promise<void>;
        };
    };
};
declare const a11yTestPatterns: {
    keyboardNavigation: (getElement: () => HTMLElement) => {
        name: string;
        test: () => Promise<void>;
    };
    screenReader: (getElement: () => HTMLElement, expectedLabel: string) => {
        name: string;
        test: () => void;
    };
    ariaAttributes: (getElement: () => HTMLElement, requiredAttributes: string[]) => {
        name: string;
        test: () => void;
    };
    colorContrast: (getElement: () => HTMLElement) => {
        name: string;
        test: () => void;
    };
};
declare const performanceTestPatterns: {
    renderPerformance: (component: ReactElement, threshold?: number) => {
        name: string;
        test: () => Promise<void>;
    };
    memoryUsage: (component: ReactElement) => {
        name: string;
        test: () => Promise<void>;
    };
    rerenderOptimization: (component: ReactElement, propChanges: any[]) => {
        name: string;
        test: () => Promise<void>;
    };
};
declare const integrationTestPatterns: {
    apiIntegration: (apiCall: () => Promise<any>, expectedResult: any) => {
        name: string;
        test: () => Promise<void>;
    };
    errorHandling: (apiCall: () => Promise<any>, expectedError: any) => {
        name: string;
        test: () => Promise<void>;
    };
    loadingStates: (component: ReactElement) => {
        name: string;
        test: () => Promise<void>;
    };
};

export { type ComponentTestCase, type ComponentTestPattern, type MockApiClient, type MockAuthState, TestProviders, a11yTestPatterns, a11yTestUtils, commonTestPatterns, componentTestUtils, createComponentTester, createMockApiClient, createMockAuthProvider, createTestQueryClient, customMatchers, handlers, integrationTestPatterns, mockHandlers, mockServerUtils, performanceTestPatterns, performanceTestUtils, renderWithProviders, server, setupMockServer, testDataFactory };
