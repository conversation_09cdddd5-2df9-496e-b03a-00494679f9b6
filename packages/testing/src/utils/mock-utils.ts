import { vi, Mock } from 'vitest'

// Mock factory utilities
export const createMockFunction = <T extends (...args: any[]) => any>(
  name?: string,
  defaultImplementation?: T
): Mock<T> => {
  const mock = vi.fn(defaultImplementation) as Mock<T>
  if (name) {
    mock.mockName(name)
  }
  return mock
}

export const createMockObject = <T extends Record<string, any>>(
  shape: { [K in keyof T]: T[K] | 'function' | 'value' }
): T => {
  const mock = {} as T
  
  Object.entries(shape).forEach(([key, value]) => {
    if (value === 'function') {
      (mock as any)[key] = vi.fn()
    } else if (value === 'value') {
      (mock as any)[key] = undefined
    } else {
      (mock as any)[key] = value
    }
  })
  
  return mock
}

// Mock module utilities
export const mockModule = (modulePath: string, factory: () => any) => {
  vi.mock(modulePath, factory)
}

export const mockModulePartial = (
  modulePath: string,
  mocks: Record<string, any>
) => {
  vi.mock(modulePath, async (importOriginal) => {
    const original = await importOriginal() as any
    return {
      ...original,
      ...mocks,
    }
  })
}

// API mock utilities
export const createMockResponse = <T = any>(
  data: T,
  options: Partial<Response> = {}
): Response => {
  return {
    ok: true,
    status: 200,
    statusText: 'OK',
    headers: new Headers(),
    redirected: false,
    type: 'basic',
    url: '',
    clone: vi.fn(),
    body: null,
    bodyUsed: false,
    arrayBuffer: vi.fn().mockResolvedValue(new ArrayBuffer(0)),
    blob: vi.fn().mockResolvedValue(new Blob()),
    formData: vi.fn().mockResolvedValue(new FormData()),
    json: vi.fn().mockResolvedValue(data),
    text: vi.fn().mockResolvedValue(JSON.stringify(data)),
    ...options,
  } as Response
}

export const createMockFetch = (responses: Record<string, any>) => {
  return vi.fn((url: string, _options?: RequestInit) => {
    const urlString = url
    const response = responses[urlString]
    
    if (!response) {
      return Promise.reject(new Error(`No mock response for ${urlString}`))
    }
    
    if (response instanceof Error) {
      return Promise.reject(response)
    }
    
    return Promise.resolve(createMockResponse(response))
  })
}

// Event mock utilities
export const createMockEvent = (
  type: string,
  properties: Partial<Event> = {}
): Event => {
  const event = new Event(type)
  Object.assign(event, properties)
  return event
}

export const createMockMouseEvent = (
  type: string,
  properties: Partial<MouseEvent> = {}
): MouseEvent => {
  return new MouseEvent(type, {
    bubbles: true,
    cancelable: true,
    ...properties,
  })
}

export const createMockKeyboardEvent = (
  type: string,
  properties: Partial<KeyboardEvent> = {}
): KeyboardEvent => {
  return new KeyboardEvent(type, {
    bubbles: true,
    cancelable: true,
    ...properties,
  })
}

// Timer mock utilities
export const mockTimers = () => {
  vi.useFakeTimers()
  
  return {
    advance: (ms: number) => vi.advanceTimersByTime(ms),
    runAll: () => vi.runAllTimers(),
    runPending: () => vi.runOnlyPendingTimers(),
    clear: () => vi.clearAllTimers(),
    restore: () => vi.useRealTimers(),
  }
}

// Storage mock utilities
export const createMockStorage = (): Storage => {
  const storage = new Map<string, string>()
  
  return {
    getItem: vi.fn((key: string) => storage.get(key) || null),
    setItem: vi.fn((key: string, value: string) => storage.set(key, value)),
    removeItem: vi.fn((key: string) => storage.delete(key)),
    clear: vi.fn(() => storage.clear()),
    get length() {
      return storage.size
    },
    key: vi.fn((index: number) => {
      const keys = Array.from(storage.keys())
      return keys[index] || null
    }),
  }
}

// WebSocket mock utilities
export const createMockWebSocket = () => {
  const listeners = new Map<string, Function[]>()
  
  const mockWebSocket = {
    addEventListener: vi.fn((event: string, handler: Function) => {
      if (!listeners.has(event)) {
        listeners.set(event, [])
      }
      listeners.get(event)!.push(handler)
    }),
    removeEventListener: vi.fn((event: string, handler: Function) => {
      const handlers = listeners.get(event) || []
      const index = handlers.indexOf(handler)
      if (index !== -1) {
        handlers.splice(index, 1)
      }
    }),
    send: vi.fn(),
    close: vi.fn(),
    emit: (event: string, data?: any) => {
      const handlers = listeners.get(event) || []
      handlers.forEach(handler => handler(data))
    },
  }
  
  return mockWebSocket
}

// Promise mock utilities
export const createMockPromise = <T = any>() => {
  let resolve: (value: T) => void
  let reject: (error: any) => void
  
  const promise = new Promise<T>((res, rej) => {
    resolve = res
    reject = rej
  })
  
  return {
    promise,
    resolve: resolve!,
    reject: reject!,
  }
}

// Class mock utilities
export const mockClass = <T extends object>(
  ClassConstructor: new (...args: any[]) => T,
  implementations?: Partial<T>
): Mock<(...args: any[]) => T> => {
  const MockedClass = vi.fn().mockImplementation(() => {
    const instance = {} as T
    
    // Copy prototype methods
    Object.getOwnPropertyNames(ClassConstructor.prototype).forEach(name => {
      if (name !== 'constructor') {
        (instance as any)[name] = vi.fn()
      }
    })
    
    // Apply custom implementations
    if (implementations) {
      Object.assign(instance, implementations)
    }
    
    return instance
  })
  
  return MockedClass as Mock<(...args: any[]) => T>
}