{"version": 3, "sources": ["../src/utils/index.ts", "../src/constants/index.ts", "../src/schemas/index.ts", "../src/errors/index.ts", "../src/api/types.ts", "../src/api/error-handler.ts", "../src/api/client.ts", "../src/api/auth-interceptor.ts", "../src/api/index.ts", "../src/services/users.service.ts", "../src/services/types.ts", "../src/services/training.service.ts", "../src/services/vendors.service.ts", "../src/services/email.service.ts", "../src/services/wins.service.ts", "../src/services/index.ts"], "names": ["ErrorType", "AxiosError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TrainingStatus", "EmailStatus", "WinCategory", "WinImpact"], "mappings": ";;;;;;AAOO,SAAS,MAAM,MAAsB,EAAA;AAC1C,EAAO,OAAA,OAAA,CAAQ,IAAK,CAAA,MAAM,CAAC,CAAA;AAC7B;AAKO,SAAS,WAAA,CAAY,KAAe,EAAA,QAAA,GAAW,CAAW,EAAA;AAC/D,EAAI,IAAA,CAAC,OAAc,OAAA,SAAA;AAEnB,EAAA,MAAM,CAAI,GAAA,IAAA;AACV,EAAM,MAAA,EAAA,GAAK,QAAW,GAAA,CAAA,GAAI,CAAI,GAAA,QAAA;AAC9B,EAAM,MAAA,KAAA,GAAQ,CAAC,OAAA,EAAS,IAAM,EAAA,IAAA,EAAM,MAAM,IAAM,EAAA,IAAA,EAAM,IAAM,EAAA,IAAA,EAAM,IAAI,CAAA;AAEtE,EAAM,MAAA,CAAA,GAAI,IAAK,CAAA,KAAA,CAAM,IAAK,CAAA,GAAA,CAAI,KAAK,CAAI,GAAA,IAAA,CAAK,GAAI,CAAA,CAAC,CAAC,CAAA;AAElD,EAAA,OAAO,UAAY,CAAA,CAAA,KAAA,GAAQ,IAAK,CAAA,GAAA,CAAI,CAAG,EAAA,CAAC,CAAG,EAAA,OAAA,CAAQ,EAAE,CAAC,CAAI,GAAA,GAAA,GAAM,MAAM,CAAC,CAAA;AACzE;AAKO,SAAS,aAAa,GAAqB,EAAA;AAChD,EAAA,IAAI,OAAO,GAAS,EAAA;AAClB,IAAA,OAAA,CAAQ,GAAM,GAAA,GAAA,EAAS,OAAQ,CAAA,CAAC,CAAI,GAAA,GAAA;AAAA;AAEtC,EAAA,IAAI,OAAO,GAAM,EAAA;AACf,IAAA,OAAA,CAAQ,GAAM,GAAA,GAAA,EAAM,OAAQ,CAAA,CAAC,CAAI,GAAA,GAAA;AAAA;AAEnC,EAAA,OAAO,IAAI,QAAS,EAAA;AACtB;AAKO,SAAS,cAAA,CAAe,MAAgB,EAAA,QAAA,GAAW,KAAe,EAAA;AACvE,EAAO,OAAA,IAAI,IAAK,CAAA,YAAA,CAAa,OAAS,EAAA;AAAA,IACpC,KAAO,EAAA,UAAA;AAAA,IACP;AAAA,GACD,CAAE,CAAA,MAAA,CAAO,MAAM,CAAA;AAClB;AAKO,SAAS,QAAA,CACd,MACA,IACkC,EAAA;AAClC,EAAI,IAAA,OAAA;AAEJ,EAAA,OAAO,IAAI,IAAwB,KAAA;AACjC,IAAA,YAAA,CAAa,OAAO,CAAA;AACpB,IAAA,OAAA,GAAU,WAAW,MAAM,IAAA,CAAK,GAAG,IAAI,GAAG,IAAI,CAAA;AAAA,GAChD;AACF;AAKO,SAAS,QAAA,CACd,MACA,KACkC,EAAA;AAClC,EAAI,IAAA,UAAA;AAEJ,EAAA,OAAO,IAAI,IAAwB,KAAA;AACjC,IAAA,IAAI,CAAC,UAAY,EAAA;AACf,MAAA,IAAA,CAAK,GAAG,IAAI,CAAA;AACZ,MAAa,UAAA,GAAA,IAAA;AACb,MAAW,UAAA,CAAA,MAAM,UAAa,GAAA,KAAA,EAAO,KAAK,CAAA;AAAA;AAC5C,GACF;AACF;AAKO,SAAS,UAAA,CAAW,SAAS,EAAY,EAAA;AAC9C,EAAM,MAAA,EAAA,GAAK,KAAK,MAAO,EAAA,CAAE,SAAS,EAAE,CAAA,CAAE,MAAO,CAAA,CAAA,EAAG,CAAC,CAAA;AACjD,EAAA,OAAO,MAAS,GAAA,CAAA,EAAG,MAAM,CAAA,CAAA,EAAI,EAAE,CAAK,CAAA,GAAA,EAAA;AACtC;AAKO,SAAS,MAAM,EAA2B,EAAA;AAC/C,EAAA,OAAO,IAAI,OAAQ,CAAA,CAAA,OAAA,KAAW,UAAW,CAAA,OAAA,EAAS,EAAE,CAAC,CAAA;AACvD;AAKO,SAAS,WAAW,GAAqB,EAAA;AAC9C,EAAO,OAAA,GAAA,CAAI,OAAO,CAAC,CAAA,CAAE,aAAgB,GAAA,GAAA,CAAI,MAAM,CAAC,CAAA;AAClD;AAKO,SAAS,QAAA,CAAS,KAAa,MAAwB,EAAA;AAC5D,EAAI,IAAA,GAAA,CAAI,MAAU,IAAA,MAAA,EAAe,OAAA,GAAA;AACjC,EAAA,OAAO,GAAI,CAAA,KAAA,CAAM,CAAG,EAAA,MAAM,CAAI,GAAA,KAAA;AAChC;AAKO,SAAS,QAAQ,KAAqB,EAAA;AAC3C,EAAI,IAAA,KAAA,IAAS,MAAa,OAAA,IAAA;AAC1B,EAAA,IAAI,OAAO,KAAU,KAAA,QAAA,EAAiB,OAAA,KAAA,CAAM,MAAW,KAAA,EAAA;AACvD,EAAA,IAAI,MAAM,OAAQ,CAAA,KAAK,CAAG,EAAA,OAAO,MAAM,MAAW,KAAA,CAAA;AAClD,EAAI,IAAA,OAAO,UAAU,QAAU,EAAA,OAAO,OAAO,IAAK,CAAA,KAAK,EAAE,MAAW,KAAA,CAAA;AACpE,EAAO,OAAA,KAAA;AACT;AAKO,SAAS,UAAa,GAAW,EAAA;AACtC,EAAA,IAAI,GAAQ,KAAA,IAAA,IAAQ,OAAO,GAAA,KAAQ,UAAiB,OAAA,GAAA;AACpD,EAAA,IAAI,eAAe,IAAM,EAAA,OAAO,IAAI,IAAK,CAAA,GAAA,CAAI,SAAS,CAAA;AACtD,EAAI,IAAA,GAAA,YAAe,OAAc,OAAA,GAAA,CAAI,IAAI,CAAQ,IAAA,KAAA,SAAA,CAAU,IAAI,CAAC,CAAA;AAChE,EAAI,IAAA,OAAO,QAAQ,QAAU,EAAA;AAC3B,IAAA,MAAM,YAAY,EAAC;AACnB,IAAA,KAAA,MAAW,OAAO,GAAK,EAAA;AACrB,MAAI,IAAA,GAAA,CAAI,cAAe,CAAA,GAAG,CAAG,EAAA;AAC3B,QAAA,SAAA,CAAU,GAAG,CAAA,GAAI,SAAU,CAAA,GAAA,CAAI,GAAG,CAAC,CAAA;AAAA;AACrC;AAEF,IAAO,OAAA,SAAA;AAAA;AAET,EAAO,OAAA,GAAA;AACT;AAKO,SAAS,GAAA,CAAI,GAAU,EAAA,IAAA,EAAc,YAAyB,EAAA;AACnE,EAAM,MAAA,IAAA,GAAO,IAAK,CAAA,KAAA,CAAM,GAAG,CAAA;AAC3B,EAAA,IAAI,MAAS,GAAA,GAAA;AAEb,EAAA,KAAA,MAAW,OAAO,IAAM,EAAA;AACtB,IAAA,IAAI,MAAU,IAAA,IAAA,IAAQ,OAAO,MAAA,KAAW,QAAU,EAAA;AAChD,MAAO,OAAA,YAAA;AAAA;AAET,IAAA,MAAA,GAAS,OAAO,GAAG,CAAA;AAAA;AAGrB,EAAO,OAAA,MAAA,KAAW,SAAY,MAAS,GAAA,YAAA;AACzC;AAKO,SAAS,GAAA,CAAI,GAAU,EAAA,IAAA,EAAc,KAAkB,EAAA;AAC5D,EAAM,MAAA,IAAA,GAAO,IAAK,CAAA,KAAA,CAAM,GAAG,CAAA;AAC3B,EAAA,IAAI,OAAU,GAAA,GAAA;AAEd,EAAA,KAAA,IAAS,IAAI,CAAG,EAAA,CAAA,GAAI,IAAK,CAAA,MAAA,GAAS,GAAG,CAAK,EAAA,EAAA;AACxC,IAAM,MAAA,GAAA,GAAM,KAAK,CAAC,CAAA;AAClB,IAAA,IAAI,EAAE,GAAO,IAAA,OAAA,CAAA,IAAY,OAAO,OAAQ,CAAA,GAAG,MAAM,QAAU,EAAA;AACzD,MAAQ,OAAA,CAAA,GAAG,IAAI,EAAC;AAAA;AAElB,IAAA,OAAA,GAAU,QAAQ,GAAG,CAAA;AAAA;AAGvB,EAAA,OAAA,CAAQ,IAAK,CAAA,IAAA,CAAK,MAAS,GAAA,CAAC,CAAC,CAAI,GAAA,KAAA;AACnC;AAKO,SAAS,IAAA,CACd,KACA,IACY,EAAA;AACZ,EAAM,MAAA,MAAA,GAAS,EAAE,GAAG,GAAI,EAAA;AACxB,EAAA,IAAA,CAAK,OAAQ,CAAA,CAAA,GAAA,KAAO,OAAO,MAAA,CAAO,GAAG,CAAC,CAAA;AACtC,EAAO,OAAA,MAAA;AACT;AAKO,SAAS,IAAA,CACd,KACA,IACY,EAAA;AACZ,EAAA,MAAM,SAAS,EAAC;AAChB,EAAA,IAAA,CAAK,QAAQ,CAAO,GAAA,KAAA;AAClB,IAAA,IAAI,OAAO,GAAK,EAAA;AACd,MAAO,MAAA,CAAA,GAAG,CAAI,GAAA,GAAA,CAAI,GAAG,CAAA;AAAA;AACvB,GACD,CAAA;AACD,EAAO,OAAA,MAAA;AACT;;;ACtMO,IAAM,UAAa,GAAA;AAAA,EACxB,OAAS,EAAA,GAAA;AAAA,EACT,cAAgB,EAAA,CAAA;AAAA,EAChB,WAAa,EAAA,GAAA;AAAA,EACb,iBAAmB,EAAA,EAAA;AAAA,EACnB,aAAe,EAAA;AACjB;AAGO,IAAM,WAAc,GAAA;AAAA,EACzB,eAAA,EAAiB,KAAK,EAAK,GAAA,GAAA;AAAA;AAAA,EAC3B,gBAAA,EAAkB,KAAK,EAAK,GAAA,GAAA;AAAA;AAAA,EAC5B,iBAAmB,EAAA,oBAAA;AAAA,EACnB,yBAA2B,EAAA,uBAAA;AAAA,EAC3B,gBAAkB,EAAA,cAAA;AAAA,EAClB,oBAAsB,EAAA,EAAA,GAAK,EAAK,GAAA,EAAA,GAAK,EAAK,GAAA;AAAA;AAC5C;AAGO,IAAM,WAAc,GAAA;AAAA,EACzB,aAAA,EAAe,KAAK,IAAO,GAAA,IAAA;AAAA;AAAA,EAC3B,mBAAqB,EAAA,CAAC,YAAc,EAAA,WAAA,EAAa,aAAa,YAAY,CAAA;AAAA,EAC1E,sBAAwB,EAAA;AAAA,IACtB,iBAAA;AAAA,IACA,oBAAA;AAAA,IACA,yEAAA;AAAA,IACA,0BAAA;AAAA,IACA,mEAAA;AAAA,IACA,YAAA;AAAA,IACA;AAAA,GACF;AAAA,EACA,YAAY,IAAO,GAAA;AAAA;AACrB;AAGO,IAAM,SAAY,GAAA;AAAA,EACvB,cAAgB,EAAA,GAAA;AAAA,EAChB,cAAgB,EAAA,GAAA;AAAA,EAChB,kBAAoB,EAAA,GAAA;AAAA,EACpB,cAAgB,EAAA,GAAA;AAAA,EAChB,aAAe,EAAA,GAAA;AAAA,EACf,gBAAkB,EAAA,GAAA;AAAA,EAClB,eAAiB,EAAA;AACnB;AAGO,IAAM,YAAe,GAAA;AAAA,EAC1B,aAAe,EAAA,OAAA;AAAA,EACf,WAAa,EAAA,eAAA;AAAA,EACb,WAAa,EAAA;AAAA,IACX,EAAI,EAAA,OAAA;AAAA,IACJ,EAAI,EAAA,OAAA;AAAA,IACJ,EAAI,EAAA,QAAA;AAAA,IACJ,EAAI,EAAA,QAAA;AAAA,IACJ,KAAO,EAAA;AAAA,GACT;AAAA,EACA,MAAQ,EAAA;AAAA,IACN,OAAS,EAAA;AAAA,MACP,EAAI,EAAA,SAAA;AAAA,MACJ,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA;AAAA,KACP;AAAA,IACA,IAAM,EAAA;AAAA,MACJ,EAAI,EAAA,SAAA;AAAA,MACJ,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA,SAAA;AAAA,MACL,GAAK,EAAA;AAAA;AACP;AAEJ;AAGO,IAAM,gBAAmB,GAAA;AAAA,EAC9B,WAAa,EAAA,4BAAA;AAAA,EACb,mBAAqB,EAAA,CAAA;AAAA,EACrB,cAAgB,EAAA,iEAAA;AAAA,EAChB,WAAa,EAAA,oBAAA;AAAA,EACb,SAAW,EAAA,gBAAA;AAAA,EACX,cAAgB,EAAA;AAClB;AAGO,IAAM,YAAe,GAAA;AAAA,EAC1B,GAAK,EAAA,YAAA;AAAA,EACL,EAAI,EAAA,YAAA;AAAA,EACJ,EAAI,EAAA,YAAA;AAAA,EACJ,OAAS,EAAA,aAAA;AAAA,EACT,iBAAmB,EAAA,oBAAA;AAAA,EACnB,QAAU,EAAA,QAAA;AAAA,EACV,QAAU,EAAA,OAAA;AAAA,EACV,YAAc,EAAA;AAChB;AAGO,IAAM,WAAc,GAAA;AAAA,EACzB,EAAI,EAAA,GAAA;AAAA,EACJ,OAAS,EAAA,GAAA;AAAA,EACT,UAAY,EAAA,GAAA;AAAA,EACZ,WAAa,EAAA,GAAA;AAAA,EACb,YAAc,EAAA,GAAA;AAAA,EACd,SAAW,EAAA,GAAA;AAAA,EACX,SAAW,EAAA,GAAA;AAAA,EACX,QAAU,EAAA,GAAA;AAAA,EACV,oBAAsB,EAAA,GAAA;AAAA,EACtB,qBAAuB,EAAA,GAAA;AAAA,EACvB,WAAa,EAAA,GAAA;AAAA,EACb,mBAAqB,EAAA;AACvB;AAGO,IAAM,WAAc,GAAA;AAAA;AAAA,EAEzB,wBAA0B,EAAA,0BAAA;AAAA,EAC1B,kBAAoB,EAAA,oBAAA;AAAA,EACpB,kBAAoB,EAAA,oBAAA;AAAA,EACpB,6BAA+B,EAAA,+BAAA;AAAA,EAC/B,mBAAqB,EAAA,qBAAA;AAAA,EACrB,yBAA2B,EAAA,2BAAA;AAAA;AAAA,EAG3B,yBAA2B,EAAA,2BAAA;AAAA,EAC3B,yBAA2B,EAAA,2BAAA;AAAA,EAC3B,qBAAuB,EAAA,uBAAA;AAAA,EACvB,qBAAuB,EAAA,uBAAA;AAAA,EACvB,wBAA0B,EAAA,0BAAA;AAAA,EAC1B,wBAA0B,EAAA,0BAAA;AAAA;AAAA,EAG1B,wBAA0B,EAAA,0BAAA;AAAA,EAC1B,eAAiB,EAAA,iBAAA;AAAA,EACjB,oBAAsB,EAAA,sBAAA;AAAA;AAAA,EAGtB,cAAgB,EAAA,gBAAA;AAAA,EAChB,iBAAmB,EAAA,mBAAA;AAAA,EACnB,kBAAoB,EAAA,oBAAA;AAAA;AAAA,EAGpB,aAAe,EAAA,eAAA;AAAA,EACf,gBAAkB,EAAA,kBAAA;AAAA,EAClB,kBAAoB,EAAA,oBAAA;AAAA,EACpB,uBAAyB,EAAA;AAC3B;AAGO,IAAM,UAAa,GAAA;AAAA,EACxB,WAAa,EAAA,aAAA;AAAA,EACb,KAAO,EAAA,OAAA;AAAA,EACP,OAAS,EAAA,SAAA;AAAA,EACT,IAAM,EAAA,MAAA;AAAA,EACN,KAAO,EAAA;AACT;AAEO,IAAM,WAAc,GAAA;AAAA;AAAA,EAEzB,UAAY,EAAA,YAAA;AAAA,EACZ,WAAa,EAAA,aAAA;AAAA,EACb,YAAc,EAAA,cAAA;AAAA;AAAA,EAGd,YAAc,EAAA,cAAA;AAAA,EACd,aAAe,EAAA,eAAA;AAAA,EACf,cAAgB,EAAA,gBAAA;AAAA,EAChB,eAAiB,EAAA,iBAAA;AAAA;AAAA,EAGjB,cAAgB,EAAA,gBAAA;AAAA,EAChB,gBAAkB,EAAA,kBAAA;AAAA;AAAA,EAGlB,aAAe,EAAA,eAAA;AAAA,EACf,WAAa,EAAA,aAAA;AAAA,EACb,aAAe,EAAA;AACjB;AAGO,IAAM,MAAS,GAAA;AAAA,EACpB,IAAM,EAAA,GAAA;AAAA,EACN,KAAO,EAAA,QAAA;AAAA,EACP,QAAU,EAAA,WAAA;AAAA,EACV,eAAiB,EAAA,kBAAA;AAAA,EACjB,cAAgB,EAAA,iBAAA;AAAA,EAChB,SAAW,EAAA,YAAA;AAAA,EACX,OAAS,EAAA,UAAA;AAAA,EACT,QAAU,EAAA,WAAA;AAAA,EACV,KAAO,EAAA,QAAA;AAAA,EACP,SAAW,EAAA,YAAA;AAAA,EACX,IAAM,EAAA,OAAA;AAAA,EACN,OAAS,EAAA,UAAA;AAAA,EACT,KAAO,EAAA;AACT;AAGO,IAAM,YAAe,GAAA;AAAA,EAC1B,KAAO,EAAA,eAAA;AAAA,EACP,QAAU,EAAA,kBAAA;AAAA,EACV,iBAAmB,EAAA,2BAAA;AAAA,EACnB,iBAAmB,EAAA,2BAAA;AAAA,EACnB,eAAiB,EAAA,yBAAA;AAAA,EACjB,UAAY,EAAA;AACd;AAGO,IAAM,MAAS,GAAA;AAAA,EACpB,UAAY,EAAA,YAAA;AAAA,EACZ,WAAa,EAAA,aAAA;AAAA,EACb,oBAAsB,EAAA,sBAAA;AAAA,EACtB,aAAe,EAAA,eAAA;AAAA,EACf,qBAAuB,EAAA,uBAAA;AAAA,EACvB,oBAAsB,EAAA,sBAAA;AAAA,EACtB,oBAAsB,EAAA,sBAAA;AAAA,EACtB,iBAAmB,EAAA;AACrB;AAGO,IAAM,aAAgB,GAAA;AAAA,EAC3B,gBAAkB,EAAA,kBAAA;AAAA,EAClB,oBAAsB,EAAA,sBAAA;AAAA,EACtB,kBAAoB,EAAA,oBAAA;AAAA,EACpB,gBAAkB,EAAA,kBAAA;AAAA,EAClB,gBAAkB,EAAA,kBAAA;AAAA,EAClB,mBAAqB,EAAA;AACvB;AAGO,IAAM,YAAe,GAAA;AAAA,EAC1B,IAAM,EAAA,SAAA;AAAA,EACN,WAAa,EAAA,iCAAA;AAAA,EACb,OAAS,EAAA,OAAA;AAAA,EACT,MAAQ,EAAA,cAAA;AAAA,EACR,aAAe,EAAA,oBAAA;AAAA,EACf,iBAAmB,EAAA,yBAAA;AAAA,EACnB,UAAY,EAAA;AACd;ACnPa,IAAA,WAAA,GAAc,EACxB,MAAO,EAAA,CACP,MAAM,sBAAsB,CAAA,CAC5B,GAAI,CAAA,CAAA,EAAG,mBAAmB;AAEhB,IAAA,cAAA,GAAiB,CAC3B,CAAA,MAAA,EACA,CAAA,GAAA,CAAI,gBAAiB,CAAA,mBAAA,EAAqB,CAA6B,0BAAA,EAAA,gBAAA,CAAiB,mBAAmB,CAAA,WAAA,CAAa,CACxH,CAAA,KAAA;AAAA,EACC,gBAAiB,CAAA,cAAA;AAAA,EACjB;AACF;AAEK,IAAM,UAAa,GAAA,CAAA,CACvB,MAAO,EAAA,CACP,GAAI,CAAA,CAAA,EAAG,kBAAkB,CAAA,CACzB,GAAI,CAAA,GAAA,EAAK,uCAAuC,CAAA,CAChD,IAAK;AAEK,IAAA,WAAA,GAAc,EACxB,MAAO,EAAA,CACP,MAAM,gBAAiB,CAAA,WAAA,EAAa,6BAA6B,CAAA,CACjE,QAAS;AAEL,IAAM,YAAY,CACtB,CAAA,MAAA,GACA,GAAI,CAAA,oBAAoB,EACxB,QAAS;AAGL,IAAM,QAAW,GAAA,CAAA,CAAE,MAAO,EAAA,CAAE,KAAK,mBAAmB;AAE9C,IAAA,UAAA,GAAa,CACvB,CAAA,MAAA,EACA,CAAA,KAAA,CAAM,gBAAgB,+DAA+D,CAAA,CACrF,GAAI,CAAA,CAAA,EAAG,kBAAkB;AAGf,IAAA,gBAAA,GAAmB,EAAE,MAAO,CAAA;AAAA,EACvC,IAAA,EAAM,CAAE,CAAA,MAAA,EAAS,CAAA,GAAA,GAAM,GAAI,CAAA,CAAC,CAAE,CAAA,OAAA,CAAQ,CAAC,CAAA;AAAA,EACvC,KAAO,EAAA,CAAA,CAAE,MAAO,EAAA,CAAE,GAAI,EAAA,CAAE,GAAI,CAAA,CAAC,CAAE,CAAA,GAAA,CAAI,GAAG,CAAA,CAAE,QAAQ,EAAE,CAAA;AAAA,EAClD,MAAA,EAAQ,EAAE,MAAO,EAAA,CAAE,KAAM,CAAA,GAAA,CAAI,CAAC,CAAA,CAAE,QAAS;AAC3C,CAAC;AAEY,IAAA,UAAA,GAAa,EAAE,MAAO,CAAA;AAAA,EACjC,MAAQ,EAAA,CAAA,CAAE,MAAO,EAAA,CAAE,QAAS,EAAA;AAAA,EAC5B,SAAA,EAAW,EAAE,IAAK,CAAA,CAAC,OAAO,MAAM,CAAC,CAAE,CAAA,OAAA,CAAQ,KAAK;AAClD,CAAC;AAEY,IAAA,YAAA,GAAe,EAAE,MAAO,CAAA;AAAA,EACnC,KAAO,EAAA,CAAA,CAAE,MAAO,EAAA,CAAE,QAAS,EAAA;AAAA,EAC3B,SAAS,CAAE,CAAA,MAAA,CAAO,EAAE,GAAI,EAAC,EAAE,QAAS;AACtC,CAAC;AAEM,IAAM,kBAAkB,gBAC5B,CAAA,KAAA,CAAM,UAAU,CAAA,CAChB,MAAM,YAAY;AAGR,IAAA,WAAA,GAAc,EAAE,MAAO,CAAA;AAAA,EAClC,KAAO,EAAA,WAAA;AAAA,EACP,UAAU,CAAE,CAAA,MAAA,EAAS,CAAA,GAAA,CAAI,GAAG,sBAAsB,CAAA;AAAA,EAClD,UAAY,EAAA,CAAA,CAAE,OAAQ,EAAA,CAAE,QAAQ,KAAK;AACvC,CAAC;AAEY,IAAA,cAAA,GAAiB,EAAE,MAAO,CAAA;AAAA,EACrC,KAAO,EAAA,WAAA;AAAA,EACP,QAAU,EAAA,cAAA;AAAA,EACV,eAAA,EAAiB,EAAE,MAAO,EAAA;AAAA,EAC1B,IAAM,EAAA,UAAA;AAAA,EACN,aAAa,CAAE,CAAA,OAAA,GAAU,MAAO,CAAA,CAAA,GAAA,KAAO,QAAQ,IAAM,EAAA;AAAA,IACnD,OAAS,EAAA;AAAA,GACV;AACH,CAAC,EAAE,MAAO,CAAA,CAAA,IAAA,KAAQ,IAAK,CAAA,QAAA,KAAa,KAAK,eAAiB,EAAA;AAAA,EACxD,OAAS,EAAA,wBAAA;AAAA,EACT,IAAA,EAAM,CAAC,iBAAiB;AAC1B,CAAC;AAEY,IAAA,oBAAA,GAAuB,EAAE,MAAO,CAAA;AAAA,EAC3C,KAAO,EAAA;AACT,CAAC;AAEY,IAAA,mBAAA,GAAsB,EAAE,MAAO,CAAA;AAAA,EAC1C,OAAO,CAAE,CAAA,MAAA,EAAS,CAAA,GAAA,CAAI,GAAG,yBAAyB,CAAA;AAAA,EAClD,QAAU,EAAA,cAAA;AAAA,EACV,eAAA,EAAiB,EAAE,MAAO;AAC5B,CAAC,EAAE,MAAO,CAAA,CAAA,IAAA,KAAQ,IAAK,CAAA,QAAA,KAAa,KAAK,eAAiB,EAAA;AAAA,EACxD,OAAS,EAAA,wBAAA;AAAA,EACT,IAAA,EAAM,CAAC,iBAAiB;AAC1B,CAAC;AAEY,IAAA,oBAAA,GAAuB,EAAE,MAAO,CAAA;AAAA,EAC3C,iBAAiB,CAAE,CAAA,MAAA,EAAS,CAAA,GAAA,CAAI,GAAG,8BAA8B,CAAA;AAAA,EACjE,WAAa,EAAA,cAAA;AAAA,EACb,eAAA,EAAiB,EAAE,MAAO;AAC5B,CAAC,EAAE,MAAO,CAAA,CAAA,IAAA,KAAQ,IAAK,CAAA,WAAA,KAAgB,KAAK,eAAiB,EAAA;AAAA,EAC3D,OAAS,EAAA,wBAAA;AAAA,EACT,IAAA,EAAM,CAAC,iBAAiB;AAC1B,CAAC;AAGY,IAAA,iBAAA,GAAoB,EAAE,MAAO,CAAA;AAAA,EACxC,IAAM,EAAA,UAAA;AAAA,EACN,KAAO,EAAA,WAAA;AAAA,EACP,KAAO,EAAA,WAAA;AAAA,EACP,MAAQ,EAAA,SAAA;AAAA,EACR,GAAA,EAAK,EAAE,MAAO,EAAA,CAAE,IAAI,GAAK,EAAA,sCAAsC,EAAE,QAAS,EAAA;AAAA,EAC1E,QAAU,EAAA,CAAA,CAAE,MAAO,EAAA,CAAE,QAAS,EAAA;AAAA,EAC9B,QAAU,EAAA,CAAA,CAAE,MAAO,EAAA,CAAE,QAAS;AAChC,CAAC;AAEY,IAAA,qBAAA,GAAwB,EAAE,MAAO,CAAA;AAAA,EAC5C,KAAA,EAAO,CAAE,CAAA,IAAA,CAAK,CAAC,OAAA,EAAS,QAAQ,QAAQ,CAAC,CAAE,CAAA,OAAA,CAAQ,QAAQ,CAAA;AAAA,EAC3D,aAAA,EAAe,EAAE,MAAO,CAAA;AAAA,IACtB,KAAO,EAAA,CAAA,CAAE,OAAQ,EAAA,CAAE,QAAQ,IAAI,CAAA;AAAA,IAC/B,IAAM,EAAA,CAAA,CAAE,OAAQ,EAAA,CAAE,QAAQ,IAAI,CAAA;AAAA,IAC9B,GAAK,EAAA,CAAA,CAAE,OAAQ,EAAA,CAAE,QAAQ,KAAK;AAAA,GAC/B,CAAA,CAAE,OAAQ,CAAA,EAAE,CAAA;AAAA,EACb,OAAA,EAAS,EAAE,MAAO,CAAA;AAAA,IAChB,cAAgB,EAAA,CAAA,CAAE,OAAQ,EAAA,CAAE,QAAQ,IAAI,CAAA;AAAA,IACxC,eAAiB,EAAA,CAAA,CAAE,OAAQ,EAAA,CAAE,QAAQ,IAAI;AAAA,GAC1C,CAAA,CAAE,OAAQ,CAAA,EAAE;AACf,CAAC;AAGY,IAAA,gBAAA,GAAmB,EAAE,MAAO,CAAA;AAAA,EACvC,MAAM,CAAE,CAAA,MAAA,EAAS,CAAA,GAAA,CAAI,GAAG,uBAAuB,CAAA;AAAA,EAC/C,IAAA,EAAM,EAAE,MAAO,EAAA,CAAE,KAAM,CAAA,GAAA,CAAI,GAAG,kCAAkC,CAAA;AAAA,EAChE,MAAM,CAAE,CAAA,MAAA,EAAS,CAAA,GAAA,CAAI,GAAG,uBAAuB,CAAA;AAAA,EAC/C,IAAA,EAAM,EAAE,UAAW,CAAA,IAAI,EAAE,EAAG,CAAA,CAAA,CAAE,QAAQ;AAAA;AACxC,CAAC;AAGY,IAAA,iBAAA,GAAoB,EAAE,MAAO,CAAA;AAAA,EACxC,IAAA,EAAM,EAAE,GAAI,EAAA;AAAA,EACZ,OAAS,EAAA,CAAA,CAAE,MAAO,EAAA,CAAE,QAAS,EAAA;AAAA,EAC7B,OAAA,EAAS,EAAE,OAAQ,EAAA;AAAA,EACnB,SAAA,EAAW,EAAE,MAAO,EAAA;AAAA,EACpB,SAAW,EAAA,CAAA,CAAE,MAAO,EAAA,CAAE,QAAS;AACjC,CAAC;AAEY,IAAA,uBAAA,GAA0B,kBAAkB,MAAO,CAAA;AAAA,EAC9D,IAAM,EAAA,CAAA,CAAE,KAAM,CAAA,CAAA,CAAE,KAAK,CAAA;AAAA,EACrB,UAAA,EAAY,EAAE,MAAO,CAAA;AAAA,IACnB,IAAM,EAAA,CAAA,CAAE,MAAO,EAAA,CAAE,GAAI,EAAA;AAAA,IACrB,KAAO,EAAA,CAAA,CAAE,MAAO,EAAA,CAAE,GAAI,EAAA;AAAA,IACtB,KAAO,EAAA,CAAA,CAAE,MAAO,EAAA,CAAE,GAAI,EAAA;AAAA,IACtB,UAAY,EAAA,CAAA,CAAE,MAAO,EAAA,CAAE,GAAI,EAAA;AAAA,IAC3B,OAAA,EAAS,EAAE,OAAQ,EAAA;AAAA,IACnB,OAAA,EAAS,EAAE,OAAQ;AAAA,GACpB;AACH,CAAC;AAEY,IAAA,cAAA,GAAiB,EAAE,MAAO,CAAA;AAAA,EACrC,IAAA,EAAM,EAAE,MAAO,EAAA;AAAA,EACf,OAAA,EAAS,EAAE,MAAO,EAAA;AAAA,EAClB,SAAS,CAAE,CAAA,MAAA,CAAO,EAAE,GAAI,EAAC,EAAE,QAAS,EAAA;AAAA,EACpC,SAAA,EAAW,EAAE,MAAO,EAAA;AAAA,EACpB,IAAM,EAAA,CAAA,CAAE,MAAO,EAAA,CAAE,QAAS,EAAA;AAAA,EAC1B,SAAW,EAAA,CAAA,CAAE,MAAO,EAAA,CAAE,QAAS;AACjC,CAAC;;;ACnKY,IAAA,YAAA,GAAN,MAAM,aAAA,SAAqB,KAAM,CAAA;AAAA,EAMtC,YACE,OACA,EAAA,IAAA,GAAe,WAAY,CAAA,aAAA,EAC3B,YACA,OACA,EAAA;AACA,IAAA,KAAA,CAAM,OAAO,CAAA;AACb,IAAA,IAAA,CAAK,IAAO,GAAA,cAAA;AACZ,IAAA,IAAA,CAAK,IAAO,GAAA,IAAA;AACZ,IAAA,IAAA,CAAK,UAAa,GAAA,UAAA;AAClB,IAAA,IAAA,CAAK,OAAU,GAAA,OAAA;AACf,IAAA,IAAA,CAAK,SAAY,GAAA,iBAAA,IAAI,IAAK,EAAA,EAAE,WAAY,EAAA;AAGxC,IAAA,IAAI,MAAM,iBAAmB,EAAA;AAC3B,MAAM,KAAA,CAAA,iBAAA,CAAkB,MAAM,aAAY,CAAA;AAAA;AAC5C;AACF,EAEA,MAAS,GAAA;AACP,IAAO,OAAA;AAAA,MACL,MAAM,IAAK,CAAA,IAAA;AAAA,MACX,SAAS,IAAK,CAAA,OAAA;AAAA,MACd,MAAM,IAAK,CAAA,IAAA;AAAA,MACX,YAAY,IAAK,CAAA,UAAA;AAAA,MACjB,SAAS,IAAK,CAAA,OAAA;AAAA,MACd,WAAW,IAAK,CAAA,SAAA;AAAA,MAChB,OAAO,IAAK,CAAA;AAAA,KACd;AAAA;AAEJ;AAKa,IAAA,SAAA,GAAN,cAAwB,YAAa,CAAA;AAAA,EAC1C,WAAY,CAAA,OAAA,EAAiB,IAAe,GAAA,WAAA,CAAY,0BAA0B,OAA+B,EAAA;AAC/G,IAAM,KAAA,CAAA,OAAA,EAAS,IAAM,EAAA,GAAA,EAAK,OAAO,CAAA;AACjC,IAAA,IAAA,CAAK,IAAO,GAAA,WAAA;AAAA;AAEhB;AAKa,IAAA,eAAA,GAAN,cAA8B,YAAa,CAAA;AAAA,EAChD,WAAY,CAAA,OAAA,EAAiB,IAAe,GAAA,WAAA,CAAY,2BAA2B,OAA+B,EAAA;AAChH,IAAM,KAAA,CAAA,OAAA,EAAS,IAAM,EAAA,GAAA,EAAK,OAAO,CAAA;AACjC,IAAA,IAAA,CAAK,IAAO,GAAA,iBAAA;AAAA;AAEhB;AAKa,IAAA,YAAA,GAAN,cAA2B,YAAa,CAAA;AAAA,EAC7C,WAAY,CAAA,OAAA,EAAiB,IAAe,GAAA,WAAA,CAAY,0BAA0B,OAA+B,EAAA;AAC/G,IAAM,KAAA,CAAA,OAAA,EAAS,IAAM,EAAA,GAAA,EAAK,OAAO,CAAA;AACjC,IAAA,IAAA,CAAK,IAAO,GAAA,cAAA;AAAA;AAEhB;AAKa,IAAA,SAAA,GAAN,cAAwB,YAAa,CAAA;AAAA,EAC1C,WAAY,CAAA,OAAA,EAAiB,IAAe,GAAA,WAAA,CAAY,oBAAoB,OAA+B,EAAA;AACzG,IAAM,KAAA,CAAA,OAAA,EAAS,IAAM,EAAA,GAAA,EAAK,OAAO,CAAA;AACjC,IAAA,IAAA,CAAK,IAAO,GAAA,WAAA;AAAA;AAEhB;AAKa,IAAA,eAAA,GAAN,cAA8B,YAAa,CAAA;AAAA,EAChD,WAAY,CAAA,OAAA,EAAiB,IAAe,GAAA,WAAA,CAAY,+BAA+B,OAA+B,EAAA;AACpH,IAAM,KAAA,CAAA,OAAA,EAAS,IAAM,EAAA,GAAA,EAAK,OAAO,CAAA;AACjC,IAAA,IAAA,CAAK,IAAO,GAAA,iBAAA;AAAA;AAEhB;AAKa,IAAA,aAAA,GAAN,cAA4B,YAAa,CAAA;AAAA,EAC9C,WAAY,CAAA,OAAA,EAAiB,IAAe,GAAA,WAAA,CAAY,oBAAoB,OAA+B,EAAA;AACzG,IAAM,KAAA,CAAA,OAAA,EAAS,IAAM,EAAA,GAAA,EAAK,OAAO,CAAA;AACjC,IAAA,IAAA,CAAK,IAAO,GAAA,eAAA;AAAA;AAEhB;AAKa,IAAA,aAAA,GAAN,cAA4B,YAAa,CAAA;AAAA,EAC9C,WAAY,CAAA,OAAA,EAAiB,IAAe,GAAA,WAAA,CAAY,yBAAyB,OAA+B,EAAA;AAC9G,IAAM,KAAA,CAAA,OAAA,EAAS,IAAM,EAAA,GAAA,EAAK,OAAO,CAAA;AACjC,IAAA,IAAA,CAAK,IAAO,GAAA,eAAA;AAAA;AAEhB;AAKO,IAAM,eAAkB,GAAA;AAAA,EAC7B,kBAAA,EAAoB,CAAC,OACnB,KAAA,IAAI,UAAU,2BAA6B,EAAA,WAAA,CAAY,0BAA0B,OAAO,CAAA;AAAA,EAE1F,YAAA,EAAc,CAAC,OACb,KAAA,IAAI,UAAU,kCAAoC,EAAA,WAAA,CAAY,oBAAoB,OAAO,CAAA;AAAA,EAE3F,YAAA,EAAc,CAAC,OACb,KAAA,IAAI,UAAU,8BAAgC,EAAA,WAAA,CAAY,oBAAoB,OAAO,CAAA;AAAA,EAEvF,uBAAA,EAAyB,CAAC,OACxB,KAAA,IAAI,gBAAgB,iDAAmD,EAAA,WAAA,CAAY,+BAA+B,OAAO,CAAA;AAAA,EAE3H,aAAA,EAAe,CAAC,OACd,KAAA,IAAI,UAAU,+DAAiE,EAAA,WAAA,CAAY,qBAAqB,OAAO,CAAA;AAAA,EAEzH,kBAAA,EAAoB,CAAC,OACnB,KAAA,IAAI,UAAU,qCAAuC,EAAA,WAAA,CAAY,2BAA2B,OAAO;AACvG;AAEO,IAAM,qBAAwB,GAAA;AAAA,EACnC,eAAe,CAAC,SAAA,EAAmB,OACjC,KAAA,IAAI,gBAAgB,CAAG,EAAA,SAAS,CAAgB,YAAA,CAAA,EAAA,WAAA,CAAY,2BAA2B,EAAE,KAAA,EAAO,SAAW,EAAA,GAAG,SAAS,CAAA;AAAA,EAEzH,eAAe,CAAC,SAAA,EAAmB,OACjC,KAAA,IAAI,gBAAgB,CAAG,EAAA,SAAS,CAAuB,mBAAA,CAAA,EAAA,WAAA,CAAY,2BAA2B,EAAE,KAAA,EAAO,SAAW,EAAA,GAAG,SAAS,CAAA;AAAA,EAEhI,SAAA,EAAW,CAAC,SAAmB,EAAA,SAAA,EAAmB,YAChD,IAAI,eAAA,CAAgB,GAAG,SAAS,CAAA,kBAAA,EAAqB,SAAS,CAAe,WAAA,CAAA,EAAA,WAAA,CAAY,uBAAuB,EAAE,KAAA,EAAO,WAAW,SAAW,EAAA,GAAG,SAAS,CAAA;AAAA,EAE7J,SAAA,EAAW,CAAC,SAAmB,EAAA,SAAA,EAAmB,YAChD,IAAI,eAAA,CAAgB,GAAG,SAAS,CAAA,mBAAA,EAAsB,SAAS,CAAe,WAAA,CAAA,EAAA,WAAA,CAAY,uBAAuB,EAAE,KAAA,EAAO,WAAW,SAAW,EAAA,GAAG,SAAS,CAAA;AAAA,EAE9J,YAAA,EAAc,CAAC,OACb,KAAA,IAAI,gBAAgB,sBAAwB,EAAA,WAAA,CAAY,0BAA0B,OAAO,CAAA;AAAA,EAE3F,YAAA,EAAc,CAAC,OACb,KAAA,IAAI,gBAAgB,8CAAgD,EAAA,WAAA,CAAY,0BAA0B,OAAO;AACrH;AAEO,IAAM,kBAAqB,GAAA;AAAA,EAChC,eAAA,EAAiB,CAAC,OAChB,KAAA,IAAI,aAAa,6BAA+B,EAAA,WAAA,CAAY,0BAA0B,OAAO,CAAA;AAAA,EAE/F,OAAA,EAAS,CAAC,OACR,KAAA,IAAI,aAAa,mBAAqB,EAAA,WAAA,CAAY,iBAAiB,OAAO,CAAA;AAAA,EAE5E,WAAA,EAAa,CAAC,OACZ,KAAA,IAAI,aAAa,uBAAyB,EAAA,WAAA,CAAY,sBAAsB,OAAO;AACvF;AAEO,IAAM,eAAkB,GAAA;AAAA,EAC7B,QAAU,EAAA,CAAC,OAAiB,EAAA,OAAA,KAC1B,IAAI,SAAU,CAAA,CAAA,0CAAA,EAA6C,OAAO,CAAA,MAAA,CAAA,EAAU,YAAY,cAAgB,EAAA,EAAE,OAAS,EAAA,GAAG,SAAS,CAAA;AAAA,EAEjI,aAAa,CAAC,YAAA,EAAwB,YACpC,IAAI,SAAA,CAAU,yCAAyC,YAAa,CAAA,IAAA,CAAK,IAAI,CAAC,IAAI,WAAY,CAAA,iBAAA,EAAmB,EAAE,YAAc,EAAA,GAAG,SAAS,CAAA;AAAA,EAE/I,YAAA,EAAc,CAAC,OACb,KAAA,IAAI,UAAU,oBAAsB,EAAA,WAAA,CAAY,oBAAoB,OAAO;AAC/E;AAKO,SAAS,eAAe,KAAmC,EAAA;AAChE,EAAA,OAAO,KAAiB,YAAA,YAAA;AAC1B;AAEO,SAAS,YAAY,KAAgC,EAAA;AAC1D,EAAA,OAAO,KAAiB,YAAA,SAAA;AAC1B;AAEO,SAAS,kBAAkB,KAAsC,EAAA;AACtE,EAAA,OAAO,KAAiB,YAAA,eAAA;AAC1B;AAEO,SAAS,eAAe,KAAmC,EAAA;AAChE,EAAA,OAAO,KAAiB,YAAA,YAAA;AAC1B;AAEO,SAAS,YAAY,KAAgC,EAAA;AAC1D,EAAA,OAAO,KAAiB,YAAA,SAAA;AAC1B;AAEO,SAAS,kBAAkB,KAAsC,EAAA;AACtE,EAAA,OAAO,KAAiB,YAAA,eAAA;AAC1B;AAEO,SAAS,gBAAgB,KAAoC,EAAA;AAClE,EAAA,OAAO,KAAiB,YAAA,aAAA;AAC1B;AAEO,SAAS,gBAAgB,KAAoC,EAAA;AAClE,EAAA,OAAO,KAAiB,YAAA,aAAA;AAC1B;AAKO,SAAS,eAAe,KAA8B,EAAA;AAC3D,EAAI,IAAA,cAAA,CAAe,KAAK,CAAG,EAAA;AACzB,IAAO,OAAA,KAAA;AAAA;AAGT,EAAA,IAAI,iBAAiB,KAAO,EAAA;AAC1B,IAAA,OAAO,IAAI,YAAa,CAAA,KAAA,CAAM,OAAS,EAAA,WAAA,CAAY,eAAe,MAAW,EAAA;AAAA,MAC3E,eAAe,KAAM,CAAA,IAAA;AAAA,MACrB,OAAO,KAAM,CAAA;AAAA,KACd,CAAA;AAAA;AAGH,EAAI,IAAA,OAAO,UAAU,QAAU,EAAA;AAC7B,IAAA,OAAO,IAAI,YAAA,CAAa,KAAO,EAAA,WAAA,CAAY,aAAa,CAAA;AAAA;AAG1D,EAAA,OAAO,IAAI,YAAA,CAAa,2BAA6B,EAAA,WAAA,CAAY,eAAe,MAAW,EAAA;AAAA,IACzF,aAAe,EAAA;AAAA,GAChB,CAAA;AACH;AAKO,SAAS,QAAA,CAAS,OAAqB,OAA+B,EAAA;AAC3E,EAAA,MAAM,OAAU,GAAA;AAAA,IACd,GAAG,MAAM,MAAO,EAAA;AAAA,IAChB;AAAA,GACF;AAGA,EAAI,IAAA,OAAA,CAAQ,GAAI,CAAA,QAAA,KAAa,aAAe,EAAA;AAC1C,IAAQ,OAAA,CAAA,KAAA,CAAM,kBAAkB,OAAO,CAAA;AAAA;AAK3C;AAKO,SAAS,gBAAgB,KAAwB,EAAA;AACtD,EAAI,IAAA,cAAA,CAAe,KAAK,CAAG,EAAA;AACzB,IAAA,OAAO,KAAM,CAAA,OAAA;AAAA;AAGf,EAAA,IAAI,iBAAiB,KAAO,EAAA;AAC1B,IAAA,OAAO,KAAM,CAAA,OAAA;AAAA;AAGf,EAAI,IAAA,OAAO,UAAU,QAAU,EAAA;AAC7B,IAAO,OAAA,KAAA;AAAA;AAGT,EAAO,OAAA,8BAAA;AACT;AAKO,SAAS,qBAAA,CAAsB,MAAgB,EAAA,OAAA,EAAkB,OAA6C,EAAA;AACnH,EAAA,QAAQ,MAAQ;AAAA,IACd,KAAK,GAAA;AACH,MAAA,OAAO,IAAI,eAAgB,CAAA,OAAA,IAAW,aAAe,EAAA,WAAA,CAAY,2BAA2B,OAAO,CAAA;AAAA,IACrG,KAAK,GAAA;AACH,MAAA,OAAO,IAAI,SAAU,CAAA,OAAA,IAAW,cAAgB,EAAA,WAAA,CAAY,0BAA0B,OAAO,CAAA;AAAA,IAC/F,KAAK,GAAA;AACH,MAAA,OAAO,IAAI,eAAgB,CAAA,OAAA,IAAW,WAAa,EAAA,WAAA,CAAY,+BAA+B,OAAO,CAAA;AAAA,IACvG,KAAK,GAAA;AACH,MAAA,OAAO,IAAI,aAAc,CAAA,OAAA,IAAW,WAAa,EAAA,WAAA,CAAY,oBAAoB,OAAO,CAAA;AAAA,IAC1F,KAAK,GAAA;AACH,MAAA,OAAO,IAAI,aAAc,CAAA,OAAA,IAAW,UAAY,EAAA,WAAA,CAAY,yBAAyB,OAAO,CAAA;AAAA,IAC9F,KAAK,GAAA;AACH,MAAA,OAAO,IAAI,eAAgB,CAAA,OAAA,IAAW,sBAAwB,EAAA,WAAA,CAAY,2BAA2B,OAAO,CAAA;AAAA,IAC9G,KAAK,GAAA;AAAA,IACL,KAAK,GAAA;AAAA,IACL,KAAK,GAAA;AACH,MAAA,OAAO,IAAI,YAAa,CAAA,OAAA,IAAW,cAAgB,EAAA,WAAA,CAAY,sBAAsB,OAAO,CAAA;AAAA,IAC9F;AACE,MAAA,OAAO,IAAI,YAAa,CAAA,OAAA,IAAW,iBAAiB,WAAY,CAAA,aAAA,EAAe,QAAQ,OAAO,CAAA;AAAA;AAEpG;;;AC9NY,IAAA,SAAA,qBAAAA,UAAL,KAAA;AACL,EAAAA,WAAA,eAAgB,CAAA,GAAA,eAAA;AAChB,EAAAA,WAAA,sBAAuB,CAAA,GAAA,sBAAA;AACvB,EAAAA,WAAA,qBAAsB,CAAA,GAAA,qBAAA;AACtB,EAAAA,WAAA,kBAAmB,CAAA,GAAA,kBAAA;AACnB,EAAAA,WAAA,cAAe,CAAA,GAAA,cAAA;AACf,EAAAA,WAAA,cAAe,CAAA,GAAA,cAAA;AACf,EAAAA,WAAA,eAAgB,CAAA,GAAA,eAAA;AAChB,EAAAA,WAAA,eAAgB,CAAA,GAAA,eAAA;AARN,EAAAA,OAAAA,UAAAA;AAAA,CAAA,EAAA,SAAA,IAAA,EAAA;;;ACrEL,IAAM,kBAAN,MAAsB;AAAA,EAC3B,iBAAiB,KAA6B,EAAA;AAC5C,IAAA,MAAM,OAAwB,GAAA;AAAA,MAC5B,GAAA,EAAK,MAAM,MAAQ,EAAA,GAAA;AAAA,MACnB,MAAQ,EAAA,KAAA,CAAM,MAAQ,EAAA,MAAA,EAAQ,WAAY,EAAA;AAAA,MAC1C,SAAA,sBAAe,IAAK,EAAA;AAAA,MACpB,SAAA,EAAW,KAAK,iBAAkB;AAAA,KACpC;AAEA,IAAA,IAAI,MAAM,QAAU,EAAA;AAElB,MAAO,OAAA,IAAA,CAAK,mBAAoB,CAAA,KAAA,EAAO,OAAO,CAAA;AAAA,KAChD,MAAA,IAAW,MAAM,OAAS,EAAA;AAExB,MAAO,OAAA,IAAA,CAAK,kBAAmB,CAAA,KAAA,EAAO,OAAO,CAAA;AAAA,KACxC,MAAA;AAEL,MAAO,OAAA,IAAA,CAAK,kBAAmB,CAAA,KAAA,EAAO,OAAO,CAAA;AAAA;AAC/C;AACF,EAEQ,mBAAA,CAAoB,OAAmB,OAAiC,EAAA;AAC9E,IAAM,MAAA,MAAA,GAAS,MAAM,QAAU,CAAA,MAAA;AAC/B,IAAM,MAAA,YAAA,GAAe,MAAM,QAAU,CAAA,IAAA;AAErC,IAAI,IAAA,SAAA;AACJ,IAAI,IAAA,OAAA;AACJ,IAAI,IAAA,IAAA;AAEJ,IAAA,IAAI,WAAW,GAAK,EAAA;AAClB,MAAA,SAAA,GAAA,sBAAA;AACA,MAAU,OAAA,GAAA,+CAAA;AACV,MAAO,IAAA,GAAA,eAAA;AAAA,KACT,MAAA,IAAW,WAAW,GAAK,EAAA;AACzB,MAAA,SAAA,GAAA,qBAAA;AACA,MAAU,OAAA,GAAA,oDAAA;AACV,MAAO,IAAA,GAAA,0BAAA;AAAA,KACE,MAAA,IAAA,MAAA,IAAU,GAAO,IAAA,MAAA,GAAS,GAAK,EAAA;AACxC,MAAA,SAAA,GAAA,cAAA;AACA,MAAA,OAAA,GAAU,cAAc,OAAW,IAAA,2CAAA;AACnC,MAAA,IAAA,GAAO,cAAc,IAAQ,IAAA,cAAA;AAAA,KAC/B,MAAA,IAAW,UAAU,GAAK,EAAA;AACxB,MAAA,SAAA,GAAA,cAAA;AACA,MAAU,OAAA,GAAA,gDAAA;AACV,MAAO,IAAA,GAAA,cAAA;AAAA,KACF,MAAA;AACL,MAAA,SAAA,GAAA,eAAA;AACA,MAAU,OAAA,GAAA,+BAAA;AACV,MAAO,IAAA,GAAA,eAAA;AAAA;AAGT,IAAO,OAAA;AAAA,MACL,OAAA;AAAA,MACA,IAAA;AAAA,MACA,OAAO,YAAc,EAAA,KAAA;AAAA,MACrB,OAAS,EAAA;AAAA,QACP,IAAM,EAAA,SAAA;AAAA,QACN,MAAA;AAAA,QACA,UAAA,EAAY,MAAM,QAAU,CAAA,UAAA;AAAA,QAC5B,OAAA;AAAA,QACA,iBAAiB,KAAM,CAAA,OAAA;AAAA,QACvB;AAAA;AACF,KACF;AAAA;AACF,EAEQ,kBAAA,CAAmB,OAAmB,OAAiC,EAAA;AAC7E,IAAI,IAAA,OAAA;AACJ,IAAI,IAAA,IAAA;AAEJ,IAAA,IAAI,MAAM,IAAS,KAAA,cAAA,IAAkB,MAAM,OAAQ,CAAA,QAAA,CAAS,SAAS,CAAG,EAAA;AACtE,MAAU,OAAA,GAAA,gEAAA;AACV,MAAO,IAAA,GAAA,eAAA;AAAA,KACT,MAAA,IAAW,MAAM,IAAS,KAAA,aAAA,IAAiB,MAAM,OAAQ,CAAA,QAAA,CAAS,eAAe,CAAG,EAAA;AAClF,MAAU,OAAA,GAAA,uDAAA;AACV,MAAO,IAAA,GAAA,eAAA;AAAA,KACF,MAAA;AACL,MAAU,OAAA,GAAA,0DAAA;AACV,MAAO,IAAA,GAAA,kBAAA;AAAA;AAGT,IAAO,OAAA;AAAA,MACL,OAAA;AAAA,MACA,IAAA;AAAA,MACA,OAAS,EAAA;AAAA,QACP,IAAA,EAAA,eAAA;AAAA,QACA,OAAA;AAAA,QACA,iBAAiB,KAAM,CAAA,OAAA;AAAA,QACvB,WAAW,KAAM,CAAA;AAAA;AACnB,KACF;AAAA;AACF,EAEQ,kBAAA,CAAmB,OAAmB,OAAiC,EAAA;AAC7E,IAAO,OAAA;AAAA,MACL,OAAS,EAAA,iDAAA;AAAA,MACT,IAAM,EAAA,eAAA;AAAA,MACN,OAAS,EAAA;AAAA,QACP,IAAA,EAAA,eAAA;AAAA,QACA,OAAA;AAAA,QACA,iBAAiB,KAAM,CAAA;AAAA;AACzB,KACF;AAAA;AACF,EAEA,gBAAgB,KAAyB,EAAA;AACvC,IAAA,IAAI,iBAAiB,UAAY,EAAA;AAC/B,MAAA,IAAI,MAAM,QAAU,EAAA;AAClB,QAAM,MAAA,MAAA,GAAS,MAAM,QAAS,CAAA,MAAA;AAC9B,QAAA,IAAI,WAAW,GAAK,EAAA,OAAA,sBAAA;AACpB,QAAA,IAAI,WAAW,GAAK,EAAA,OAAA,qBAAA;AACpB,QAAI,IAAA,MAAA,IAAU,GAAO,IAAA,MAAA,GAAS,GAAK,EAAA,OAAA,cAAA;AACnC,QAAA,IAAI,UAAU,GAAK,EAAA,OAAA,cAAA;AAAA,OACrB,MAAA,IAAW,MAAM,OAAS,EAAA;AACxB,QAAA,IAAI,MAAM,IAAS,KAAA,cAAA,IAAkB,MAAM,OAAQ,CAAA,QAAA,CAAS,SAAS,CAAG,EAAA;AACtE,UAAA,OAAA,eAAA;AAAA;AAEF,QAAA,OAAA,eAAA;AAAA;AACF;AAEF,IAAA,OAAA,eAAA;AAAA;AACF,EAEA,kBAAkB,KAAyB,EAAA;AAEzC,IAAA,OAAO,KAAM,CAAA,OAAA;AAAA;AACf,EAEA,YAAY,KAA0B,EAAA;AACpC,IAAM,MAAA,SAAA,GAAY,MAAM,OAAS,EAAA,IAAA;AAGjC,IAAA,OAAO,qDACA,SACA,KAAA,cAAA,uBAAA,SAAA,KAAA,eAAA;AAAA;AACT,EAEA,QAAA,CAAS,OAAiB,OAAuC,EAAA;AAC/D,IAAA,MAAM,UAAa,GAAA;AAAA,MACjB,GAAG,MAAM,OAAS,EAAA,OAAA;AAAA,MAClB,GAAG,OAAA;AAAA,MACH,SAAW,EAAA,iBAAA,IAAI,IAAK,EAAA,EAAE,WAAY;AAAA,KACpC;AAEA,IAAA,OAAA,CAAQ,MAAM,aAAe,EAAA;AAAA,MAC3B,SAAS,KAAM,CAAA,OAAA;AAAA,MACf,MAAM,KAAM,CAAA,IAAA;AAAA,MACZ,OAAO,KAAM,CAAA,KAAA;AAAA,MACb,OAAS,EAAA,UAAA;AAAA,MACT,SAAS,KAAM,CAAA;AAAA,KAChB,CAAA;AAID,IAAA,IAAI,OAAO,MAAA,KAAW,WAAgB,IAAA,MAAA,CAAe,UAAY,EAAA;AAE/D,MAAC,OAAe,UAAW,CAAA,gBAAA,CAAiB,IAAI,KAAM,CAAA,KAAA,CAAM,OAAO,CAAG,EAAA;AAAA,QACpE,IAAM,EAAA;AAAA,UACJ,SAAA,EAAW,MAAM,OAAS,EAAA,IAAA;AAAA,UAC1B,WAAW,KAAM,CAAA;AAAA,SACnB;AAAA,QACA,KAAO,EAAA;AAAA,UACL,OAAS,EAAA,UAAA;AAAA,UACT,SAAS,KAAM,CAAA;AAAA;AACjB,OACD,CAAA;AAAA;AACH;AACF,EAEQ,iBAA4B,GAAA;AAClC,IAAA,OAAO,CAAO,IAAA,EAAA,IAAA,CAAK,GAAI,EAAC,IAAI,IAAK,CAAA,MAAA,EAAS,CAAA,QAAA,CAAS,EAAE,CAAA,CAAE,MAAO,CAAA,CAAA,EAAG,CAAC,CAAC,CAAA,CAAA;AAAA;AAEvE;AAGO,SAAS,WAAW,KAA+B,EAAA;AACxD,EAAA,OAAO,SAAS,OAAO,KAAA,KAAU,QAAY,IAAA,SAAA,IAAa,SAAS,MAAU,IAAA,KAAA;AAC/E;AAEO,SAAS,cACd,CAAA,OAAA,EACA,IACA,EAAA,IAAA,GAAA,eAAA,sBACA,OACA,OACU,EAAA;AACV,EAAO,OAAA;AAAA,IACL,OAAA;AAAA,IACA,IAAA;AAAA,IACA,KAAA;AAAA,IACA,OAAS,EAAA;AAAA,MACP,IAAA;AAAA,MACA,SAAA,sBAAe,IAAK,EAAA;AAAA,MACpB,GAAG;AAAA;AACL,GACF;AACF;;;ACxLO,IAAM,uBAAN,MAAuD;AAAA,EAQ5D,YAAY,MAAgC,EAAA;AAL5C,IAAQ,IAAA,CAAA,mBAAA,uBAA2D,GAAI,EAAA;AACvE,IAAQ,IAAA,CAAA,oBAAA,uBAA6D,GAAI,EAAA;AACzE,IAAA,IAAA,CAAQ,oBAAuB,GAAA,CAAA;AAI7B,IAAK,IAAA,CAAA,MAAA,GAAS,EAAE,GAAG,MAAO,EAAA;AAC1B,IAAK,IAAA,CAAA,YAAA,GAAe,IAAI,eAAgB,EAAA;AAExC,IAAK,IAAA,CAAA,aAAA,GAAgB,MAAM,MAAO,CAAA;AAAA,MAChC,SAAS,MAAO,CAAA,OAAA;AAAA,MAChB,OAAA,EAAS,OAAO,OAAW,IAAA,GAAA;AAAA,MAC3B,OAAS,EAAA;AAAA,QACP,cAAgB,EAAA,kBAAA;AAAA,QAChB,GAAG,MAAO,CAAA;AAAA;AACZ,KACD,CAAA;AAED,IAAA,IAAA,CAAK,iBAAkB,EAAA;AAAA;AACzB,EAEQ,iBAA0B,GAAA;AAEhC,IAAK,IAAA,CAAA,aAAA,CAAc,aAAa,OAAQ,CAAA,GAAA;AAAA,MACtC,OAAO,MAAW,KAAA;AAEhB,QAAI,IAAA,cAAA,GAAiB,EAAE,GAAG,MAAO,EAAA;AAEjC,QAAA,KAAA,MAAW,WAAe,IAAA,IAAA,CAAK,mBAAoB,CAAA,MAAA,EAAU,EAAA;AAC3D,UAAA,IAAI,YAAY,SAAW,EAAA;AACzB,YAAA,MAAM,WAA2E,GAAA;AAAA,cAC/E,GAAA,EAAK,eAAe,GAAO,IAAA,EAAA;AAAA,cAC3B,MAAA,EAAQ,eAAe,MAAU,IAAA,KAAA;AAAA,cACjC,MAAM,cAAe,CAAA,IAAA;AAAA,cACrB,OAAA,EAAU,cAAe,CAAA,OAAA,IAAsC,EAAC;AAAA,cAChE,SAAS,cAAe,CAAA,OAAA;AAAA,cACxB,SAAU,cAAuB,CAAA,OAAA;AAAA,cACjC,YAAa,cAAuB,CAAA,UAAA;AAAA,cACpC,QAAQ,cAAe,CAAA;AAAA,aACzB;AAEA,YAAA,MAAM,MAAS,GAAA,MAAM,WAAY,CAAA,SAAA,CAAU,WAAW,CAAA;AACtD,YAAiB,cAAA,GAAA;AAAA,cACf,GAAG,cAAA;AAAA,cACH,KAAK,MAAO,CAAA,GAAA;AAAA,cACZ,QAAQ,MAAO,CAAA,MAAA;AAAA,cACf,MAAM,MAAO,CAAA,IAAA;AAAA,cACb,SAAS,MAAO,CAAA,OAAA;AAAA,cAChB,SAAS,MAAO,CAAA,OAAA;AAAA,cAChB,QAAQ,MAAO,CAAA;AAAA,aACjB;AACA,YAAC,cAAA,CAAuB,UAAU,MAAO,CAAA,OAAA;AACzC,YAAC,cAAA,CAAuB,aAAa,MAAO,CAAA,UAAA;AAAA;AAC9C;AAGF,QAAI,IAAA,IAAA,CAAK,OAAO,aAAe,EAAA;AAC7B,UAAQ,OAAA,CAAA,GAAA,CAAI,iBAAiB,cAAe,CAAA,MAAA,EAAQ,aAAa,CAAA,CAAA,EAAI,cAAe,CAAA,GAAG,CAAE,CAAA,CAAA;AAAA;AAG3F,QAAO,OAAA,cAAA;AAAA,OACT;AAAA,MACA,OAAO,KAAU,KAAA;AAEf,QAAA,IAAI,aAAgB,GAAA,KAAA;AAEpB,QAAA,KAAA,MAAW,WAAe,IAAA,IAAA,CAAK,mBAAoB,CAAA,MAAA,EAAU,EAAA;AAC3D,UAAA,IAAI,YAAY,cAAgB,EAAA;AAC9B,YAAgB,aAAA,GAAA,MAAM,WAAY,CAAA,cAAA,CAAe,aAAa,CAAA;AAAA;AAChE;AAGF,QAAO,OAAA,OAAA,CAAQ,OAAO,aAAa,CAAA;AAAA;AACrC,KACF;AAGA,IAAK,IAAA,CAAA,aAAA,CAAc,aAAa,QAAS,CAAA,GAAA;AAAA,MACvC,OAAO,QAAa,KAAA;AAClB,QAAM,MAAA,WAAA,GAAc,IAAK,CAAA,iBAAA,CAAkB,QAAQ,CAAA;AAGnD,QAAA,IAAI,gBAAmB,GAAA,WAAA;AAEvB,QAAA,KAAA,MAAW,WAAe,IAAA,IAAA,CAAK,oBAAqB,CAAA,MAAA,EAAU,EAAA;AAC5D,UAAA,IAAI,YAAY,UAAY,EAAA;AAC1B,YAAmB,gBAAA,GAAA,MAAM,WAAY,CAAA,UAAA,CAAW,gBAAgB,CAAA;AAAA;AAClE;AAGF,QAAI,IAAA,IAAA,CAAK,OAAO,aAAe,EAAA;AAC7B,UAAQ,OAAA,CAAA,GAAA,CAAI,kBAAkB,QAAS,CAAA,MAAM,IAAI,QAAS,CAAA,MAAA,CAAO,GAAG,CAAE,CAAA,CAAA;AAAA;AAGxE,QAAO,OAAA,QAAA;AAAA,OACT;AAAA,MACA,OAAO,KAAU,KAAA;AACf,QAAA,MAAM,QAAW,GAAA,IAAA,CAAK,YAAa,CAAA,gBAAA,CAAiB,KAAK,CAAA;AAGzD,QAAA,IAAI,aAAkC,GAAA,QAAA;AAEtC,QAAA,KAAA,MAAW,WAAe,IAAA,IAAA,CAAK,oBAAqB,CAAA,MAAA,EAAU,EAAA;AAC5D,UAAA,IAAI,YAAY,eAAiB,EAAA;AAC/B,YAAgB,aAAA,GAAA,MAAM,WAAY,CAAA,eAAA,CAAgB,aAAa,CAAA;AAAA;AACjE;AAGF,QAAI,IAAA,IAAA,CAAK,OAAO,aAAe,EAAA;AAC7B,UAAQ,OAAA,CAAA,KAAA,CAAM,CAAe,YAAA,EAAA,KAAA,CAAM,QAAU,EAAA,MAAA,IAAU,SAAS,CAAA,CAAA,EAAI,KAAM,CAAA,MAAA,EAAQ,GAAG,CAAA,CAAA,EAAI,aAAa,CAAA;AAAA;AAGxG,QAAO,OAAA,OAAA,CAAQ,OAAO,aAAa,CAAA;AAAA;AACrC,KACF;AAAA;AACF,EAEQ,kBAAqB,QAAyC,EAAA;AACpE,IAAO,OAAA;AAAA,MACL,MAAM,QAAS,CAAA,IAAA;AAAA,MACf,QAAQ,QAAS,CAAA,MAAA;AAAA,MACjB,YAAY,QAAS,CAAA,UAAA;AAAA,MACrB,OAAA,EAAS,SAAS,IAAM,EAAA,OAAA;AAAA,MACxB,MAAA,EAAQ,SAAS,IAAM,EAAA,MAAA;AAAA,MACvB,QAAA,EAAU,SAAS,IAAM,EAAA;AAAA,KAC3B;AAAA;AACF,EAEA,MAAc,gBACZ,CAAA,SAAA,EACA,MACyB,EAAA;AACzB,IAAA,MAAM,WAA2B,GAAA;AAAA,MAC/B,WAAa,EAAA,MAAA,EAAQ,OAAW,IAAA,IAAA,CAAK,OAAO,OAAW,IAAA,CAAA;AAAA,MACvD,eAAiB,EAAA,aAAA;AAAA,MACjB,SAAW,EAAA,MAAA,EAAQ,UAAc,IAAA,IAAA,CAAK,OAAO,UAAc,IAAA,GAAA;AAAA,MAC3D,QAAU,EAAA,GAAA;AAAA,MACV,cAAA,EAAgB,CAAC,KAAiB,KAAA;AAChC,QAAA,IAAI,iBAAiBC,UAAY,EAAA;AAE/B,UAAO,OAAA,CAAC,MAAM,QAAa,IAAA,KAAA,CAAM,SAAS,MAAU,IAAA,GAAA,IAAO,KAAM,CAAA,QAAA,CAAS,MAAS,GAAA,GAAA;AAAA;AAErF,QAAO,OAAA,KAAA;AAAA;AACT,KACF;AAEA,IAAI,IAAA,SAAA;AAEJ,IAAA,KAAA,IAAS,OAAU,GAAA,CAAA,EAAG,OAAW,IAAA,WAAA,CAAY,aAAa,OAAW,EAAA,EAAA;AACnE,MAAI,IAAA;AACF,QAAM,MAAA,QAAA,GAAW,MAAM,SAAU,EAAA;AACjC,QAAO,OAAA,IAAA,CAAK,kBAAqB,QAAQ,CAAA;AAAA,eAClC,KAAO,EAAA;AACd,QAAY,SAAA,GAAA,KAAA;AAEZ,QAAA,IAAI,YAAY,WAAY,CAAA,WAAA,IAAe,CAAC,WAAY,CAAA,cAAA,GAAiB,SAAS,CAAG,EAAA;AACnF,UAAM,MAAA,SAAA;AAAA;AAIR,QAAA,MAAM,QACF,IAAA,CAAK,IAAI,WAAY,CAAA,SAAA,GAAY,IAAK,CAAA,GAAA,CAAI,GAAG,OAAU,GAAA,CAAC,GAAG,WAAY,CAAA,QAAQ,EACnE;AAEhB,QAAI,IAAA,IAAA,CAAK,OAAO,aAAe,EAAA;AAC7B,UAAQ,OAAA,CAAA,GAAA,CAAI,uBAAuB,OAAU,GAAA,CAAC,IAAI,WAAY,CAAA,WAAW,CAAO,IAAA,EAAA,KAAK,CAAI,EAAA,CAAA,CAAA;AAAA;AAG3F,QAAA,MAAM,IAAI,OAAQ,CAAA,CAAA,OAAA,KAAW,UAAW,CAAA,OAAA,EAAS,KAAK,CAAC,CAAA;AAAA;AACzD;AAGF,IAAM,MAAA,SAAA;AAAA;AACR;AAAA,EAGA,MAAM,GAAa,CAAA,GAAA,EAAa,MAAiD,EAAA;AAC/E,IAAA,OAAO,IAAK,CAAA,gBAAA;AAAA,MACV,MAAM,KAAK,aAAc,CAAA,GAAA,CAAI,KAAK,IAAK,CAAA,WAAA,CAAY,MAAM,CAAC,CAAA;AAAA,MAC1D;AAAA,KACF;AAAA;AACF,EAEA,MAAM,IAAA,CAAc,GAAa,EAAA,IAAA,EAAY,MAAiD,EAAA;AAC5F,IAAA,OAAO,IAAK,CAAA,gBAAA;AAAA,MACV,MAAM,KAAK,aAAc,CAAA,IAAA,CAAK,KAAK,IAAM,EAAA,IAAA,CAAK,WAAY,CAAA,MAAM,CAAC,CAAA;AAAA,MACjE;AAAA,KACF;AAAA;AACF,EAEA,MAAM,GAAA,CAAa,GAAa,EAAA,IAAA,EAAY,MAAiD,EAAA;AAC3F,IAAA,OAAO,IAAK,CAAA,gBAAA;AAAA,MACV,MAAM,KAAK,aAAc,CAAA,GAAA,CAAI,KAAK,IAAM,EAAA,IAAA,CAAK,WAAY,CAAA,MAAM,CAAC,CAAA;AAAA,MAChE;AAAA,KACF;AAAA;AACF,EAEA,MAAM,KAAA,CAAe,GAAa,EAAA,IAAA,EAAY,MAAiD,EAAA;AAC7F,IAAA,OAAO,IAAK,CAAA,gBAAA;AAAA,MACV,MAAM,KAAK,aAAc,CAAA,KAAA,CAAM,KAAK,IAAM,EAAA,IAAA,CAAK,WAAY,CAAA,MAAM,CAAC,CAAA;AAAA,MAClE;AAAA,KACF;AAAA;AACF,EAEA,MAAM,MAAgB,CAAA,GAAA,EAAa,MAAiD,EAAA;AAClF,IAAA,OAAO,IAAK,CAAA,gBAAA;AAAA,MACV,MAAM,KAAK,aAAc,CAAA,MAAA,CAAO,KAAK,IAAK,CAAA,WAAA,CAAY,MAAM,CAAC,CAAA;AAAA,MAC7D;AAAA,KACF;AAAA;AACF;AAAA,EAGA,sBAAsB,WAAyC,EAAA;AAC7D,IAAM,MAAA,EAAA,GAAK,EAAE,IAAK,CAAA,oBAAA;AAClB,IAAK,IAAA,CAAA,mBAAA,CAAoB,GAAI,CAAA,EAAA,EAAI,WAAW,CAAA;AAC5C,IAAO,OAAA,EAAA;AAAA;AACT,EAEA,uBAAuB,WAA0C,EAAA;AAC/D,IAAM,MAAA,EAAA,GAAK,EAAE,IAAK,CAAA,oBAAA;AAClB,IAAK,IAAA,CAAA,oBAAA,CAAqB,GAAI,CAAA,EAAA,EAAI,WAAW,CAAA;AAC7C,IAAO,OAAA,EAAA;AAAA;AACT,EAEA,yBAAyB,EAAkB,EAAA;AACzC,IAAK,IAAA,CAAA,mBAAA,CAAoB,OAAO,EAAE,CAAA;AAAA;AACpC,EAEA,0BAA0B,EAAkB,EAAA;AAC1C,IAAK,IAAA,CAAA,oBAAA,CAAqB,OAAO,EAAE,CAAA;AAAA;AACrC;AAAA,EAGA,WAAW,GAAmB,EAAA;AAC5B,IAAA,IAAA,CAAK,OAAO,OAAU,GAAA,GAAA;AACtB,IAAK,IAAA,CAAA,aAAA,CAAc,SAAS,OAAU,GAAA,GAAA;AAAA;AACxC,EAEA,kBAAkB,OAAuC,EAAA;AACvD,IAAK,IAAA,CAAA,MAAA,CAAO,iBAAiB,EAAE,GAAG,KAAK,MAAO,CAAA,cAAA,EAAgB,GAAG,OAAQ,EAAA;AACzE,IAAA,MAAA,CAAO,MAAO,CAAA,IAAA,CAAK,aAAc,CAAA,QAAA,CAAS,SAAS,OAAO,CAAA;AAAA;AAC5D,EAEA,SAAoC,GAAA;AAClC,IAAO,OAAA,EAAE,GAAG,IAAA,CAAK,MAAO,EAAA;AAAA;AAC1B,EAEQ,YAAY,MAA4C,EAAA;AAC9D,IAAI,IAAA,CAAC,MAAQ,EAAA,OAAO,EAAC;AAErB,IAAO,OAAA;AAAA,MACL,SAAS,MAAO,CAAA,OAAA;AAAA,MAChB,SAAS,MAAO,CAAA,OAAA;AAAA,MAChB,QAAQ,MAAO,CAAA;AAAA,KACjB;AAAA;AAEJ;AAGO,SAAS,uBAAuB,MAAkD,EAAA;AACvF,EAAO,OAAA,IAAI,qBAAqB,MAAM,CAAA;AACxC;AAGA,IAAI,aAAyC,GAAA,IAAA;AAEtC,SAAS,mBAAwC,GAAA;AACtD,EAAA,IAAI,CAAC,aAAe,EAAA;AAClB,IAAM,MAAA,IAAI,MAAM,8EAA8E,CAAA;AAAA;AAEhG,EAAO,OAAA,aAAA;AACT;AAEO,SAAS,2BAA2B,MAAsC,EAAA;AAC/E,EAAA,aAAA,GAAgB,uBAAuB,MAAM,CAAA;AAC/C;AAEO,SAAS,oBAAoB,MAAgC,EAAA;AAClE,EAAgB,aAAA,GAAA,MAAA;AAClB;;;ACrRO,IAAM,4BAAN,MAAgC;AAAA,EAQrC,YAAY,MAA+B,EAAA;AAN3C,IAAA,IAAA,CAAQ,cAAsC,GAAA,IAAA;AAC9C,IAAA,IAAA,CAAQ,eAGH,EAAC;AAGJ,IAAA,IAAA,CAAK,MAAS,GAAA;AAAA,MACZ,cAAgB,EAAA,eAAA;AAAA,MAChB,gBAAkB,EAAA,QAAA;AAAA,MAClB,aAAa,EAAC;AAAA,MACd,GAAG;AAAA,KACL;AAAA;AACF,EAEA,qBAA4C,GAAA;AAC1C,IAAO,OAAA;AAAA,MACL,SAAA,EAAW,OAAO,MAAW,KAAA;AAC3B,QAAA,MAAM,EAAE,GAAA,EAAK,OAAU,GAAA,IAAO,GAAA,MAAA;AAG9B,QAAI,IAAA,IAAA,CAAK,gBAAiB,CAAA,GAAG,CAAG,EAAA;AAC9B,UAAO,OAAA,MAAA;AAAA;AAGT,QAAI,IAAA;AACF,UAAM,MAAA,KAAA,GAAQ,MAAM,IAAA,CAAK,aAAc,EAAA;AACvC,UAAA,IAAI,KAAO,EAAA;AACT,YAAQ,OAAA,CAAA,IAAA,CAAK,OAAO,cAAe,CAAA,GAAI,GAAG,IAAK,CAAA,MAAA,CAAO,gBAAgB,CAAA,CAAA,EAAI,KAAK,CAAA,CAAA;AAAA;AACjF,iBACO,KAAO,EAAA;AACd,UAAQ,OAAA,CAAA,KAAA,CAAM,iDAAiD,KAAK,CAAA;AACpE,UAAK,IAAA,CAAA,MAAA,CAAO,cAAc,KAAK,CAAA;AAAA;AAGjC,QAAO,OAAA;AAAA,UACL,GAAG,MAAA;AAAA,UACH;AAAA,SACF;AAAA,OACF;AAAA,MACA,cAAA,EAAgB,OAAO,KAAU,KAAA;AAC/B,QAAO,OAAA,KAAA;AAAA;AACT,KACF;AAAA;AACF,EAEA,sBAA8C,GAAA;AAC5C,IAAO,OAAA;AAAA,MACL,UAAA,EAAY,OAAO,QAAa,KAAA;AAC9B,QAAO,OAAA,QAAA;AAAA,OACT;AAAA,MACA,eAAA,EAAiB,OAAO,KAAU,KAAA;AAChC,QAAA,MAAM,kBAAmB,KAAc,CAAA,MAAA;AAGvC,QAAA,IAAI,KAAK,WAAY,CAAA,KAAK,KAAK,eAAmB,IAAA,CAAC,gBAAgB,MAAQ,EAAA;AACzE,UAAA,eAAA,CAAgB,MAAS,GAAA,IAAA;AAEzB,UAAI,IAAA;AACF,YAAM,MAAA,KAAA,GAAQ,MAAM,IAAA,CAAK,YAAa,EAAA;AACtC,YAAA,IAAI,KAAO,EAAA;AACT,cAAgB,eAAA,CAAA,OAAA,CAAQ,IAAK,CAAA,MAAA,CAAO,cAAe,CAAA,GAAI,GAAG,IAAK,CAAA,MAAA,CAAO,gBAAgB,CAAA,CAAA,EAAI,KAAK,CAAA,CAAA;AAAA;AAGjG,mBACO,YAAc,EAAA;AACrB,YAAQ,OAAA,CAAA,KAAA,CAAM,4CAA4C,YAAY,CAAA;AACtE,YAAK,IAAA,CAAA,MAAA,CAAO,cAAc,YAAY,CAAA;AACtC,YAAK,IAAA,CAAA,MAAA,CAAO,aAAa,WAAY,EAAA;AAAA;AACvC;AAGF,QAAO,OAAA,OAAA,CAAQ,OAAO,KAAK,CAAA;AAAA;AAC7B,KACF;AAAA;AACF,EAEA,MAAc,aAAwC,GAAA;AACpD,IAAA,MAAM,WAAc,GAAA,IAAA,CAAK,MAAO,CAAA,YAAA,CAAa,cAAe,EAAA;AAE5D,IAAA,IAAI,CAAC,WAAa,EAAA;AAChB,MAAO,OAAA,IAAA;AAAA;AAIT,IAAA,IAAI,IAAK,CAAA,MAAA,CAAO,YAAa,CAAA,cAAA,CAAe,WAAW,CAAG,EAAA;AACxD,MAAI,IAAA;AACF,QAAO,OAAA,MAAM,KAAK,YAAa,EAAA;AAAA,eACxB,KAAO,EAAA;AACd,QAAQ,OAAA,CAAA,KAAA,CAAM,4CAA4C,KAAK,CAAA;AAC/D,QAAO,OAAA,IAAA;AAAA;AACT;AAGF,IAAO,OAAA,WAAA;AAAA;AACT,EAEA,MAAc,YAAgC,GAAA;AAE5C,IAAA,IAAI,KAAK,cAAgB,EAAA;AACvB,MAAA,OAAO,IAAK,CAAA,cAAA;AAAA;AAId,IAAK,IAAA,CAAA,cAAA,GAAiB,KAAK,mBAAoB,EAAA;AAE/C,IAAI,IAAA;AACF,MAAM,MAAA,MAAA,GAAS,MAAM,IAAK,CAAA,cAAA;AAC1B,MAAA,IAAA,CAAK,cAAiB,GAAA,IAAA;AAGtB,MAAA,IAAA,CAAK,oBAAoB,MAAM,CAAA;AAE/B,MAAO,OAAA,MAAA;AAAA,aACA,KAAO,EAAA;AACd,MAAA,IAAA,CAAK,cAAiB,GAAA,IAAA;AAGtB,MAAA,IAAA,CAAK,mBAAmB,KAAK,CAAA;AAE7B,MAAM,MAAA,KAAA;AAAA;AACR;AACF,EAEA,MAAc,mBAAuC,GAAA;AACnD,IAAA,MAAM,YAAe,GAAA,IAAA,CAAK,MAAO,CAAA,YAAA,CAAa,eAAgB,EAAA;AAE9D,IAAA,IAAI,CAAC,YAAc,EAAA;AACjB,MAAM,MAAA,IAAI,MAAM,4BAA4B,CAAA;AAAA;AAG9C,IAAI,IAAA;AACF,MAAA,MAAM,MAAS,GAAA,MAAM,IAAK,CAAA,MAAA,CAAO,aAAa,kBAAmB,EAAA;AAGjE,MAAA,IAAA,CAAK,OAAO,YAAa,CAAA,SAAA,CAAU,MAAO,CAAA,WAAA,EAAa,OAAO,YAAY,CAAA;AAG1E,MAAK,IAAA,CAAA,MAAA,CAAO,iBAAiB,MAAM,CAAA;AAEnC,MAAA,OAAO,MAAO,CAAA,WAAA;AAAA,aACP,KAAO,EAAA;AAEd,MAAK,IAAA,CAAA,MAAA,CAAO,aAAa,WAAY,EAAA;AACrC,MAAM,MAAA,KAAA;AAAA;AACR;AACF,EAEQ,oBAAoB,KAAqB,EAAA;AAC/C,IAAK,IAAA,CAAA,YAAA,CAAa,QAAQ,CAAC,EAAE,SAAc,KAAA,OAAA,CAAQ,KAAK,CAAC,CAAA;AACzD,IAAA,IAAA,CAAK,eAAe,EAAC;AAAA;AACvB,EAEQ,mBAAmB,KAAkB,EAAA;AAC3C,IAAK,IAAA,CAAA,YAAA,CAAa,QAAQ,CAAC,EAAE,QAAa,KAAA,MAAA,CAAO,KAAK,CAAC,CAAA;AACvD,IAAA,IAAA,CAAK,eAAe,EAAC;AAAA;AACvB,EAEQ,iBAAiB,GAAsB,EAAA;AAC7C,IAAO,OAAA,IAAA,CAAK,OAAO,WAAa,EAAA,IAAA;AAAA,MAAK,CAAA,UAAA,KACnC,GAAI,CAAA,QAAA,CAAS,UAAU;AAAA,KACpB,IAAA,KAAA;AAAA;AACP,EAEQ,YAAY,KAAqB,EAAA;AACvC,IAAA,OAAO,OAAO,OAAS,EAAA,MAAA,KAAW,GAC3B,IAAA,KAAA,EAAO,SAAS,IAAS,KAAA,sBAAA;AAAA;AAEpC;AAGO,IAAM,2BAAN,MAA2D;AAAA,EAA3D,WAAA,GAAA;AACL,IAAA,IAAA,CAAQ,cAAiB,GAAA,sBAAA;AACzB,IAAA,IAAA,CAAQ,eAAkB,GAAA,uBAAA;AAAA;AAAA,EAE1B,cAAgC,GAAA;AAC9B,IAAI,IAAA,OAAO,MAAW,KAAA,WAAA,EAAoB,OAAA,IAAA;AAC1C,IAAO,OAAA,YAAA,CAAa,OAAQ,CAAA,IAAA,CAAK,cAAc,CAAA;AAAA;AACjD,EAEA,eAAiC,GAAA;AAC/B,IAAI,IAAA,OAAO,MAAW,KAAA,WAAA,EAAoB,OAAA,IAAA;AAC1C,IAAO,OAAA,YAAA,CAAa,OAAQ,CAAA,IAAA,CAAK,eAAe,CAAA;AAAA;AAClD,EAEA,SAAA,CAAU,aAAqB,YAA6B,EAAA;AAC1D,IAAI,IAAA,OAAO,WAAW,WAAa,EAAA;AAEnC,IAAa,YAAA,CAAA,OAAA,CAAQ,IAAK,CAAA,cAAA,EAAgB,WAAW,CAAA;AACrD,IAAA,IAAI,YAAc,EAAA;AAChB,MAAa,YAAA,CAAA,OAAA,CAAQ,IAAK,CAAA,eAAA,EAAiB,YAAY,CAAA;AAAA;AACzD;AACF,EAEA,WAAoB,GAAA;AAClB,IAAI,IAAA,OAAO,WAAW,WAAa,EAAA;AAEnC,IAAa,YAAA,CAAA,UAAA,CAAW,KAAK,cAAc,CAAA;AAC3C,IAAa,YAAA,CAAA,UAAA,CAAW,KAAK,eAAe,CAAA;AAAA;AAC9C,EAEA,eAAe,KAAwB,EAAA;AACrC,IAAI,IAAA;AAEF,MAAM,MAAA,OAAA,GAAU,IAAK,CAAA,KAAA,CAAM,IAAK,CAAA,KAAA,CAAM,MAAM,GAAG,CAAA,CAAE,CAAC,CAAC,CAAC,CAAA;AACpD,MAAA,MAAM,cAAc,IAAK,CAAA,KAAA,CAAM,IAAK,CAAA,GAAA,KAAQ,GAAI,CAAA;AAEhD,MAAA,OAAO,QAAQ,GAAM,GAAA,WAAA;AAAA,aACd,KAAO,EAAA;AAEd,MAAO,OAAA,IAAA;AAAA;AACT;AACF,EAEA,MAAM,kBAA8E,GAAA;AAClF,IAAM,MAAA,YAAA,GAAe,KAAK,eAAgB,EAAA;AAE1C,IAAA,IAAI,CAAC,YAAc,EAAA;AACjB,MAAM,MAAA,IAAI,MAAM,4BAA4B,CAAA;AAAA;AAK9C,IAAM,MAAA,IAAI,MAAM,oFAAoF,CAAA;AAAA;AAExG;AAGO,SAAS,qBAAA,CAAsB,MAAyC,GAAA,EAA+B,EAAA;AAC5G,EAAA,MAAM,aAAuC,GAAA;AAAA,IAC3C,YAAA,EAAc,IAAI,wBAAyB,EAAA;AAAA,IAC3C,GAAG;AAAA,GACL;AAEA,EAAO,OAAA,IAAI,0BAA0B,aAAa,CAAA;AACpD;;;AC1MO,SAAS,0BAA0B,MAOrB,EAAA;AACnB,EAAA,MAAM,SAAS,sBAAuB,CAAA;AAAA,IACpC,SAAS,MAAO,CAAA,OAAA;AAAA,IAChB,OAAA,EAAS,OAAO,OAAW,IAAA,GAAA;AAAA,IAC3B,OAAA,EAAS,OAAO,OAAW,IAAA,CAAA;AAAA,IAC3B,aAAA,EAAe,OAAO,aAAiB,IAAA;AAAA,GACxC,CAAA;AAGD,EAAI,IAAA,MAAA,CAAO,eAAe,KAAO,EAAA;AAC/B,IAAA,MAAM,kBAAkB,qBAAsB,CAAA;AAAA,MAC5C,cAAc,MAAO,CAAA,YAAA;AAAA,MACrB,WAAA,EAAa,CAAC,KAAe,KAAA;AAC3B,QAAQ,OAAA,CAAA,KAAA,CAAM,oBAAoB,KAAK,CAAA;AAAA,OAEzC;AAAA,MACA,cAAA,EAAgB,CAAC,MAA2D,KAAA;AAC1E,QAAA,OAAA,CAAQ,IAAI,qCAAqC,CAAA;AAAA;AACnD,KACD,CAAA;AAED,IAAO,MAAA,CAAA,qBAAA,CAAsB,eAAgB,CAAA,qBAAA,EAAuB,CAAA;AACpE,IAAO,MAAA,CAAA,sBAAA,CAAuB,eAAgB,CAAA,sBAAA,EAAwB,CAAA;AAAA;AAGxE,EAAO,OAAA,MAAA;AACT;;;AC5EO,IAAM,mBAAN,MAA+C;AAAA,EAIpD,YAAY,SAA6B,EAAA;AAFzC,IAAA,IAAA,CAAQ,QAAW,GAAA,YAAA;AAGjB,IAAA,IAAA,CAAK,SAAY,GAAA,SAAA;AAAA;AACnB,EAEA,MAAM,SAAS,MAAwD,EAAA;AACrE,IAAM,MAAA,WAAA,GAAc,IAAI,eAAgB,EAAA;AAExC,IAAA,IAAI,MAAQ,EAAA;AACV,MAAO,MAAA,CAAA,OAAA,CAAQ,MAAM,CAAE,CAAA,OAAA,CAAQ,CAAC,CAAC,GAAA,EAAK,KAAK,CAAM,KAAA;AAC/C,QAAI,IAAA,KAAA,KAAU,MAAa,IAAA,KAAA,KAAU,IAAM,EAAA;AACzC,UAAI,IAAA,KAAA,CAAM,OAAQ,CAAA,KAAK,CAAG,EAAA;AACxB,YAAM,KAAA,CAAA,OAAA,CAAQ,OAAK,WAAY,CAAA,MAAA,CAAO,KAAK,CAAE,CAAA,QAAA,EAAU,CAAC,CAAA;AAAA,WACnD,MAAA;AACL,YAAA,WAAA,CAAY,MAAO,CAAA,GAAA,EAAK,KAAM,CAAA,QAAA,EAAU,CAAA;AAAA;AAC1C;AACF,OACD,CAAA;AAAA;AAGH,IAAA,MAAM,GAAM,GAAA,WAAA,CAAY,QAAS,EAAA,GAC7B,CAAG,EAAA,IAAA,CAAK,QAAQ,CAAA,CAAA,EAAI,WAAY,CAAA,QAAA,EAAU,CAAA,CAAA,GAC1C,IAAK,CAAA,QAAA;AAET,IAAO,OAAA,IAAA,CAAK,SAAU,CAAA,GAAA,CAAY,GAAG,CAAA;AAAA;AACvC,EAEA,MAAM,QAAQ,EAAwC,EAAA;AACpD,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA;AAAA;AAGvC,IAAO,OAAA,IAAA,CAAK,UAAU,GAAU,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAE,CAAA,CAAA;AAAA;AAC1D,EAEA,MAAM,WAAW,IAAiD,EAAA;AAChE,IAAA,IAAA,CAAK,sBAAsB,IAAI,CAAA;AAC/B,IAAA,OAAO,IAAK,CAAA,SAAA,CAAU,IAAW,CAAA,IAAA,CAAK,UAAU,IAAI,CAAA;AAAA;AACtD,EAEA,MAAM,UAAW,CAAA,EAAA,EAAY,IAAiD,EAAA;AAC5E,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA;AAAA;AAGvC,IAAO,OAAA,IAAA,CAAK,UAAU,GAAU,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAA,CAAA,EAAI,IAAI,CAAA;AAAA;AAChE,EAEA,MAAM,WAAW,EAAwC,EAAA;AACvD,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA;AAAA;AAGvC,IAAO,OAAA,IAAA,CAAK,UAAU,MAAa,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAE,CAAA,CAAA;AAAA;AAC7D,EAEA,MAAM,aAAa,EAA0C,EAAA;AAC3D,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA;AAAA;AAGvC,IAAO,OAAA,IAAA,CAAK,UAAU,GAAY,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAQ,MAAA,CAAA,CAAA;AAAA;AAClE,EAEA,MAAM,eAAgB,CAAA,EAAA,EAAY,OAA+C,EAAA;AAC/E,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA;AAAA;AAGvC,IAAA,IAAI,CAAC,KAAA,CAAM,OAAQ,CAAA,OAAO,CAAG,EAAA;AAC3B,MAAM,MAAA,IAAI,MAAM,2BAA2B,CAAA;AAAA;AAG7C,IAAO,OAAA,IAAA,CAAK,SAAU,CAAA,GAAA,CAAU,CAAG,EAAA,IAAA,CAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAA,MAAA,CAAA,EAAU,EAAE,OAAA,EAAS,CAAA;AAAA;AAC7E,EAEA,MAAM,mBAAmB,EAAgD,EAAA;AACvE,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA;AAAA;AAGvC,IAAO,OAAA,IAAA,CAAK,UAAU,GAAkB,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAc,YAAA,CAAA,CAAA;AAAA;AAC9E;AAAA,EAGA,MAAM,WAAA,CAAY,KAAe,EAAA,KAAA,GAAQ,EAAkC,EAAA;AACzE,IAAA,IAAI,CAAC,KAAS,IAAA,KAAA,CAAM,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACvC,MAAM,MAAA,IAAI,MAAM,0BAA0B,CAAA;AAAA;AAG5C,IAAA,OAAO,KAAK,QAAS,CAAA;AAAA,MACnB,MAAA,EAAQ,MAAM,IAAK,EAAA;AAAA,MACnB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,eAAe,MAA8C,EAAA;AACjE,IAAA,IAAI,CAAC,MAAQ,EAAA;AACX,MAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA;AAAA;AAGvC,IAAA,OAAO,KAAK,QAAS,CAAA;AAAA,MACnB,IAAM,EAAA;AAAA,KACP,CAAA;AAAA;AACH,EAEA,MAAM,cAA+C,GAAA;AACnD,IAAA,OAAO,KAAK,QAAS,CAAA;AAAA,MACnB,QAAU,EAAA;AAAA,KACX,CAAA;AAAA;AACH,EAEA,MAAM,eAAe,EAAwC,EAAA;AAC3D,IAAA,OAAO,KAAK,UAAW,CAAA,EAAA,EAAI,EAAE,QAAA,EAAU,OAAO,CAAA;AAAA;AAChD,EAEA,MAAM,aAAa,EAAwC,EAAA;AACzD,IAAA,OAAO,KAAK,UAAW,CAAA,EAAA,EAAI,EAAE,QAAA,EAAU,MAAM,CAAA;AAAA;AAC/C,EAEQ,sBAAsB,IAA2B,EAAA;AACvD,IAAI,IAAA,CAAC,KAAK,KAAS,IAAA,CAAC,KAAK,YAAa,CAAA,IAAA,CAAK,KAAK,CAAG,EAAA;AACjD,MAAM,MAAA,IAAI,MAAM,yBAAyB,CAAA;AAAA;AAG3C,IAAI,IAAA,CAAC,KAAK,IAAQ,IAAA,IAAA,CAAK,KAAK,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AAC/C,MAAM,MAAA,IAAI,MAAM,kBAAkB,CAAA;AAAA;AAGpC,IAAA,IAAI,CAAC,IAAK,CAAA,QAAA,IAAY,IAAK,CAAA,QAAA,CAAS,SAAS,CAAG,EAAA;AAC9C,MAAM,MAAA,IAAI,MAAM,6CAA6C,CAAA;AAAA;AAG/D,IAAA,IAAI,KAAK,OAAW,IAAA,CAAC,MAAM,OAAQ,CAAA,IAAA,CAAK,OAAO,CAAG,EAAA;AAChD,MAAM,MAAA,IAAI,MAAM,2BAA2B,CAAA;AAAA;AAC7C;AACF,EAEQ,aAAa,KAAwB,EAAA;AAC3C,IAAA,MAAM,UAAa,GAAA,4BAAA;AACnB,IAAO,OAAA,UAAA,CAAW,KAAK,KAAK,CAAA;AAAA;AAEhC;AAGO,SAAS,mBAAmB,SAA2C,EAAA;AAC5E,EAAO,OAAA,IAAI,iBAAiB,SAAS,CAAA;AACvC;;;ACxDY,IAAA,kBAAA,qBAAAC,mBAAL,KAAA;AACL,EAAAA,oBAAA,UAAW,CAAA,GAAA,UAAA;AACX,EAAAA,oBAAA,cAAe,CAAA,GAAA,cAAA;AACf,EAAAA,oBAAA,UAAW,CAAA,GAAA,UAAA;AACX,EAAAA,oBAAA,QAAS,CAAA,GAAA,QAAA;AAJC,EAAAA,OAAAA,mBAAAA;AAAA,CAAA,EAAA,kBAAA,IAAA,EAAA;AAOA,IAAA,cAAA,qBAAAC,eAAL,KAAA;AACL,EAAAA,gBAAA,OAAQ,CAAA,GAAA,OAAA;AACR,EAAAA,gBAAA,WAAY,CAAA,GAAA,WAAA;AACZ,EAAAA,gBAAA,WAAY,CAAA,GAAA,WAAA;AACZ,EAAAA,gBAAA,aAAc,CAAA,GAAA,aAAA;AACd,EAAAA,gBAAA,WAAY,CAAA,GAAA,WAAA;AACZ,EAAAA,gBAAA,WAAY,CAAA,GAAA,WAAA;AACZ,EAAAA,gBAAA,UAAW,CAAA,GAAA,UAAA;AAPD,EAAAA,OAAAA,eAAAA;AAAA,CAAA,EAAA,cAAA,IAAA,EAAA;AAkKA,IAAA,WAAA,qBAAAC,YAAL,KAAA;AACL,EAAAA,aAAA,OAAQ,CAAA,GAAA,OAAA;AACR,EAAAA,aAAA,QAAS,CAAA,GAAA,QAAA;AACT,EAAAA,aAAA,SAAU,CAAA,GAAA,SAAA;AACV,EAAAA,aAAA,MAAO,CAAA,GAAA,MAAA;AACP,EAAAA,aAAA,WAAY,CAAA,GAAA,WAAA;AACZ,EAAAA,aAAA,QAAS,CAAA,GAAA,QAAA;AACT,EAAAA,aAAA,SAAU,CAAA,GAAA,SAAA;AACV,EAAAA,aAAA,SAAU,CAAA,GAAA,SAAA;AACV,EAAAA,aAAA,QAAS,CAAA,GAAA,QAAA;AATC,EAAAA,OAAAA,YAAAA;AAAA,CAAA,EAAA,WAAA,IAAA,EAAA;AAuDA,IAAA,WAAA,qBAAAC,YAAL,KAAA;AACL,EAAAA,aAAA,UAAW,CAAA,GAAA,UAAA;AACX,EAAAA,aAAA,MAAO,CAAA,GAAA,MAAA;AACP,EAAAA,aAAA,SAAU,CAAA,GAAA,SAAA;AACV,EAAAA,aAAA,SAAU,CAAA,GAAA,SAAA;AACV,EAAAA,aAAA,YAAa,CAAA,GAAA,YAAA;AACb,EAAAA,aAAA,UAAW,CAAA,GAAA,UAAA;AACX,EAAAA,aAAA,WAAY,CAAA,GAAA,WAAA;AACZ,EAAAA,aAAA,OAAQ,CAAA,GAAA,OAAA;AARE,EAAAA,OAAAA,YAAAA;AAAA,CAAA,EAAA,WAAA,IAAA,EAAA;AAWA,IAAA,SAAA,qBAAAC,UAAL,KAAA;AACL,EAAAA,WAAA,KAAM,CAAA,GAAA,KAAA;AACN,EAAAA,WAAA,QAAS,CAAA,GAAA,QAAA;AACT,EAAAA,WAAA,MAAO,CAAA,GAAA,MAAA;AACP,EAAAA,WAAA,UAAW,CAAA,GAAA,UAAA;AAJD,EAAAA,OAAAA,UAAAA;AAAA,CAAA,EAAA,SAAA,IAAA,EAAA;;;ACvUL,IAAM,sBAAN,MAAqD;AAAA,EAI1D,YAAY,SAA6B,EAAA;AAFzC,IAAA,IAAA,CAAQ,QAAW,GAAA,gBAAA;AAGjB,IAAA,IAAA,CAAK,SAAY,GAAA,SAAA;AAAA;AACnB,EAEA,MAAM,aAAa,MAAgE,EAAA;AACjF,IAAM,MAAA,WAAA,GAAc,IAAI,eAAgB,EAAA;AAExC,IAAA,IAAI,MAAQ,EAAA;AACV,MAAO,MAAA,CAAA,OAAA,CAAQ,MAAM,CAAE,CAAA,OAAA,CAAQ,CAAC,CAAC,GAAA,EAAK,KAAK,CAAM,KAAA;AAC/C,QAAI,IAAA,KAAA,KAAU,MAAa,IAAA,KAAA,KAAU,IAAM,EAAA;AACzC,UAAI,IAAA,KAAA,CAAM,OAAQ,CAAA,KAAK,CAAG,EAAA;AACxB,YAAM,KAAA,CAAA,OAAA,CAAQ,OAAK,WAAY,CAAA,MAAA,CAAO,KAAK,CAAE,CAAA,QAAA,EAAU,CAAC,CAAA;AAAA,WACnD,MAAA;AACL,YAAA,WAAA,CAAY,MAAO,CAAA,GAAA,EAAK,KAAM,CAAA,QAAA,EAAU,CAAA;AAAA;AAC1C;AACF,OACD,CAAA;AAAA;AAGH,IAAA,MAAM,GAAM,GAAA,WAAA,CAAY,QAAS,EAAA,GAC7B,CAAG,EAAA,IAAA,CAAK,QAAQ,CAAA,CAAA,EAAI,WAAY,CAAA,QAAA,EAAU,CAAA,CAAA,GAC1C,IAAK,CAAA,QAAA;AAET,IAAO,OAAA,IAAA,CAAK,SAAU,CAAA,GAAA,CAAgB,GAAG,CAAA;AAAA;AAC3C,EAEA,MAAM,YAAY,EAA4C,EAAA;AAC5D,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,yBAAyB,CAAA;AAAA;AAG3C,IAAO,OAAA,IAAA,CAAK,UAAU,GAAc,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAE,CAAA,CAAA;AAAA;AAC9D,EAEA,MAAM,eAAe,QAA6D,EAAA;AAChF,IAAA,IAAA,CAAK,0BAA0B,QAAQ,CAAA;AACvC,IAAA,OAAO,IAAK,CAAA,SAAA,CAAU,IAAe,CAAA,IAAA,CAAK,UAAU,QAAQ,CAAA;AAAA;AAC9D,EAEA,MAAM,cAAe,CAAA,EAAA,EAAY,QAA6D,EAAA;AAC5F,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,yBAAyB,CAAA;AAAA;AAG3C,IAAO,OAAA,IAAA,CAAK,UAAU,GAAc,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAA,CAAA,EAAI,QAAQ,CAAA;AAAA;AACxE,EAEA,MAAM,eAAe,EAAwC,EAAA;AAC3D,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,yBAAyB,CAAA;AAAA;AAG3C,IAAO,OAAA,IAAA,CAAK,UAAU,MAAa,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAE,CAAA,CAAA;AAAA;AAC7D,EAEA,MAAM,gBAAiB,CAAA,UAAA,EAAoB,MAA4C,EAAA;AACrF,IAAA,IAAI,CAAC,UAAY,EAAA;AACf,MAAM,MAAA,IAAI,MAAM,yBAAyB,CAAA;AAAA;AAE3C,IAAA,IAAI,CAAC,MAAQ,EAAA;AACX,MAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA;AAAA;AAGvC,IAAO,OAAA,IAAA,CAAK,SAAU,CAAA,IAAA,CAAW,CAAG,EAAA,IAAA,CAAK,QAAQ,CAAA,CAAA,EAAI,UAAU,CAAA,OAAA,CAAA,EAAW,EAAE,MAAA,EAAQ,CAAA;AAAA;AACtF,EAEA,MAAM,oBAAqB,CAAA,UAAA,EAAoB,MAA4C,EAAA;AACzF,IAAA,IAAI,CAAC,UAAY,EAAA;AACf,MAAM,MAAA,IAAI,MAAM,yBAAyB,CAAA;AAAA;AAE3C,IAAA,IAAI,CAAC,MAAQ,EAAA;AACX,MAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA;AAAA;AAGvC,IAAO,OAAA,IAAA,CAAK,SAAU,CAAA,MAAA,CAAa,CAAG,EAAA,IAAA,CAAK,QAAQ,CAAI,CAAA,EAAA,UAAU,CAAW,QAAA,EAAA,MAAM,CAAE,CAAA,CAAA;AAAA;AACtF,EAEA,MAAM,wBAAwB,EAA0C,EAAA;AACtE,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,yBAAyB,CAAA;AAAA;AAG3C,IAAO,OAAA,IAAA,CAAK,UAAU,GAAY,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAe,aAAA,CAAA,CAAA;AAAA;AACzE,EAEA,MAAM,qBAAkE,GAAA;AACtE,IAAO,OAAA,IAAA,CAAK,SAAU,CAAA,GAAA,CAAwB,0BAA0B,CAAA;AAAA;AAC1E;AAAA,EAGA,MAAM,kBAAuD,GAAA;AAC3D,IAAA,OAAO,KAAK,YAAa,CAAA;AAAA,MACvB,QAAU,EAAA,IAAA;AAAA,MACV,MAAA,EAAA,WAAA;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,yBAAyB,YAAwD,EAAA;AACrF,IAAA,IAAI,CAAC,YAAc,EAAA;AACjB,MAAM,MAAA,IAAI,MAAM,2BAA2B,CAAA;AAAA;AAG7C,IAAA,OAAO,KAAK,YAAa,CAAA;AAAA,MACvB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,uBAAuB,UAAsD,EAAA;AACjF,IAAA,IAAI,CAAC,UAAY,EAAA;AACf,MAAM,MAAA,IAAI,MAAM,yBAAyB,CAAA;AAAA;AAG3C,IAAA,OAAO,KAAK,YAAa,CAAA;AAAA,MACvB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,yBAAyB,UAAkE,EAAA;AAC/F,IAAA,OAAO,KAAK,YAAa,CAAA;AAAA,MACvB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,eAAA,CAAgB,KAAe,EAAA,KAAA,GAAQ,EAAsC,EAAA;AACjF,IAAA,IAAI,CAAC,KAAS,IAAA,KAAA,CAAM,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACvC,MAAM,MAAA,IAAI,MAAM,0BAA0B,CAAA;AAAA;AAG5C,IAAA,OAAO,KAAK,YAAa,CAAA;AAAA,MACvB,MAAA,EAAQ,MAAM,IAAK,EAAA;AAAA,MACnB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,oBAAyD,GAAA;AAC7D,IAAA,MAAM,GAAM,GAAA,iBAAA,IAAI,IAAK,EAAA,EAAE,WAAY,EAAA;AAEnC,IAAA,OAAO,KAAK,YAAa,CAAA;AAAA,MACvB,MAAA,EAAA,WAAA;AAAA,MACA,UAAY,EAAA;AAAA,KACb,CAAA;AAAA;AACH,EAEA,MAAM,qBAA0D,GAAA;AAC9D,IAAA,OAAO,KAAK,YAAa,CAAA;AAAA,MACvB,MAAA,EAAA,WAAA;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,gBAAgB,EAA4C,EAAA;AAChE,IAAA,OAAO,IAAK,CAAA,cAAA,CAAe,EAAI,EAAA,EAAE,qCAAkC,CAAA;AAAA;AACrE,EAEA,MAAM,gBAAA,CAAiB,EAAY,EAAA,SAAA,EAAiB,OAAgD,EAAA;AAClG,IAAA,MAAM,UAAgC,GAAA;AAAA,MACpC,MAAA,EAAA,WAAA;AAAA,MACA;AAAA,KACF;AAEA,IAAA,IAAI,OAAS,EAAA;AACX,MAAA,UAAA,CAAW,OAAU,GAAA,OAAA;AAAA;AAGvB,IAAO,OAAA,IAAA,CAAK,cAAe,CAAA,EAAA,EAAI,UAAU,CAAA;AAAA;AAC3C,EAEA,MAAM,cAAc,EAA4C,EAAA;AAC9D,IAAA,OAAO,IAAK,CAAA,cAAA,CAAe,EAAI,EAAA,EAAE,yCAAoC,CAAA;AAAA;AACvE,EAEA,MAAM,iBAAiB,EAA4C,EAAA;AACjE,IAAA,OAAO,IAAK,CAAA,cAAA,CAAe,EAAI,EAAA,EAAE,qCAAkC,CAAA;AAAA;AACrE,EAEA,MAAM,eAAe,EAA4C,EAAA;AAC/D,IAAA,OAAO,IAAK,CAAA,cAAA,CAAe,EAAI,EAAA,EAAE,qCAAkC,CAAA;AAAA;AACrE,EAEA,MAAM,gBAAgB,EAA4C,EAAA;AAChE,IAAA,OAAO,IAAK,CAAA,cAAA,CAAe,EAAI,EAAA,EAAE,mCAAiC,CAAA;AAAA;AACpE,EAEQ,0BAA0B,QAAmC,EAAA;AACnE,IAAI,IAAA,CAAC,SAAS,KAAS,IAAA,QAAA,CAAS,MAAM,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACzD,MAAM,MAAA,IAAI,MAAM,4BAA4B,CAAA;AAAA;AAG9C,IAAI,IAAA,CAAC,SAAS,WAAe,IAAA,QAAA,CAAS,YAAY,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACrE,MAAM,MAAA,IAAI,MAAM,kCAAkC,CAAA;AAAA;AAGpD,IAAI,IAAA,CAAC,SAAS,UAAY,EAAA;AACxB,MAAM,MAAA,IAAI,MAAM,+BAA+B,CAAA;AAAA;AAGjD,IAAI,IAAA,CAAC,SAAS,YAAc,EAAA;AAC1B,MAAM,MAAA,IAAI,MAAM,iCAAiC,CAAA;AAAA;AAGnD,IAAA,IAAI,CAAC,QAAA,CAAS,QAAY,IAAA,QAAA,CAAS,YAAY,CAAG,EAAA;AAChD,MAAM,MAAA,IAAI,MAAM,0CAA0C,CAAA;AAAA;AAG5D,IAAI,IAAA,CAAC,OAAO,MAAO,CAAA,kBAAkB,EAAE,QAAS,CAAA,QAAA,CAAS,UAAU,CAAG,EAAA;AACpE,MAAM,MAAA,IAAI,MAAM,6BAA6B,CAAA;AAAA;AAG/C,IAAA,IAAI,QAAS,CAAA,eAAA,IAAmB,QAAS,CAAA,eAAA,IAAmB,CAAG,EAAA;AAC7D,MAAM,MAAA,IAAI,MAAM,yCAAyC,CAAA;AAAA;AAG3D,IAAI,IAAA,QAAA,CAAS,SAAa,IAAA,QAAA,CAAS,OAAS,EAAA;AAC1C,MAAI,IAAA,IAAI,KAAK,QAAS,CAAA,SAAS,KAAK,IAAI,IAAA,CAAK,QAAS,CAAA,OAAO,CAAG,EAAA;AAC9D,QAAM,MAAA,IAAI,MAAM,mCAAmC,CAAA;AAAA;AACrD;AAGF,IAAA,IAAI,SAAS,IAAQ,IAAA,CAAC,MAAM,OAAQ,CAAA,QAAA,CAAS,IAAI,CAAG,EAAA;AAClD,MAAM,MAAA,IAAI,MAAM,uBAAuB,CAAA;AAAA;AACzC;AAEJ;AAGO,SAAS,sBAAsB,SAA8C,EAAA;AAClF,EAAO,OAAA,IAAI,oBAAoB,SAAS,CAAA;AAC1C;;;AC1OO,IAAM,qBAAN,MAAmD;AAAA,EAIxD,YAAY,SAA6B,EAAA;AAFzC,IAAA,IAAA,CAAQ,QAAW,GAAA,cAAA;AAGjB,IAAA,IAAA,CAAK,SAAY,GAAA,SAAA;AAAA;AACnB,EAEA,MAAM,WAAW,MAA4D,EAAA;AAC3E,IAAM,MAAA,WAAA,GAAc,IAAI,eAAgB,EAAA;AAExC,IAAA,IAAI,MAAQ,EAAA;AACV,MAAO,MAAA,CAAA,OAAA,CAAQ,MAAM,CAAE,CAAA,OAAA,CAAQ,CAAC,CAAC,GAAA,EAAK,KAAK,CAAM,KAAA;AAC/C,QAAI,IAAA,KAAA,KAAU,MAAa,IAAA,KAAA,KAAU,IAAM,EAAA;AACzC,UAAA,WAAA,CAAY,MAAO,CAAA,GAAA,EAAK,KAAM,CAAA,QAAA,EAAU,CAAA;AAAA;AAC1C,OACD,CAAA;AAAA;AAGH,IAAA,MAAM,GAAM,GAAA,WAAA,CAAY,QAAS,EAAA,GAC7B,CAAG,EAAA,IAAA,CAAK,QAAQ,CAAA,CAAA,EAAI,WAAY,CAAA,QAAA,EAAU,CAAA,CAAA,GAC1C,IAAK,CAAA,QAAA;AAET,IAAO,OAAA,IAAA,CAAK,SAAU,CAAA,GAAA,CAAc,GAAG,CAAA;AAAA;AACzC,EAEA,MAAM,UAAU,EAA0C,EAAA;AACxD,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,uBAAuB,CAAA;AAAA;AAGzC,IAAO,OAAA,IAAA,CAAK,UAAU,GAAY,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAE,CAAA,CAAA;AAAA;AAC5D,EAEA,MAAM,aAAa,MAAuD,EAAA;AACxE,IAAA,IAAA,CAAK,wBAAwB,MAAM,CAAA;AACnC,IAAA,OAAO,IAAK,CAAA,SAAA,CAAU,IAAa,CAAA,IAAA,CAAK,UAAU,MAAM,CAAA;AAAA;AAC1D,EAEA,MAAM,YAAa,CAAA,EAAA,EAAY,MAAuD,EAAA;AACpF,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,uBAAuB,CAAA;AAAA;AAGzC,IAAO,OAAA,IAAA,CAAK,UAAU,GAAY,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAA,CAAA,EAAI,MAAM,CAAA;AAAA;AACpE,EAEA,MAAM,aAAa,EAAwC,EAAA;AACzD,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,uBAAuB,CAAA;AAAA;AAGzC,IAAO,OAAA,IAAA,CAAK,UAAU,MAAa,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAE,CAAA,CAAA;AAAA;AAC7D,EAEA,MAAM,UAAW,CAAA,EAAA,EAAY,MAA8C,EAAA;AACzE,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,uBAAuB,CAAA;AAAA;AAGzC,IAAI,IAAA,MAAA,GAAS,CAAK,IAAA,MAAA,GAAS,CAAG,EAAA;AAC5B,MAAM,MAAA,IAAI,MAAM,gCAAgC,CAAA;AAAA;AAGlD,IAAO,OAAA,IAAA,CAAK,SAAU,CAAA,IAAA,CAAa,CAAG,EAAA,IAAA,CAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAA,KAAA,CAAA,EAAS,EAAE,MAAA,EAAQ,CAAA;AAAA;AAC9E,EAEA,MAAM,aAAa,EAA0C,EAAA;AAC3D,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,uBAAuB,CAAA;AAAA;AAGzC,IAAO,OAAA,IAAA,CAAK,UAAU,IAAa,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAS,OAAA,CAAA,CAAA;AAAA;AACpE;AAAA,EAGA,MAAM,aAAA,CAAc,KAAe,EAAA,KAAA,GAAQ,EAAoC,EAAA;AAC7E,IAAA,IAAI,CAAC,KAAS,IAAA,KAAA,CAAM,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACvC,MAAM,MAAA,IAAI,MAAM,0BAA0B,CAAA;AAAA;AAG5C,IAAA,OAAO,KAAK,UAAW,CAAA;AAAA,MACrB,MAAA,EAAQ,MAAM,IAAK,EAAA;AAAA,MACnB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,gBAAmD,GAAA;AACvD,IAAA,OAAO,KAAK,UAAW,CAAA;AAAA,MACrB,QAAU,EAAA;AAAA,KACX,CAAA;AAAA;AACH,EAEA,MAAM,kBAAqD,GAAA;AACzD,IAAA,OAAO,KAAK,UAAW,CAAA;AAAA,MACrB,UAAY,EAAA,IAAA;AAAA,MACZ,QAAU,EAAA;AAAA,KACX,CAAA;AAAA;AACH,EAEA,MAAM,qBAAqB,QAAkD,EAAA;AAC3E,IAAA,IAAI,CAAC,QAAU,EAAA;AACb,MAAM,MAAA,IAAI,MAAM,sBAAsB,CAAA;AAAA;AAGxC,IAAA,OAAO,KAAK,UAAW,CAAA;AAAA,MACrB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,kBAAmB,CAAA,SAAA,GAAY,CAAmC,EAAA;AACtE,IAAA,OAAO,KAAK,UAAW,CAAA;AAAA,MACrB,SAAA;AAAA,MACA,QAAU,EAAA,IAAA;AAAA,MACV,IAAM,EAAA,QAAA;AAAA,MACN,KAAO,EAAA;AAAA,KACR,CAAA;AAAA;AACH,EAEA,MAAM,oBAAqB,CAAA,IAAA,EAAe,OAAkD,EAAA;AAC1F,IAAA,MAAM,MAA4B,GAAA;AAAA,MAChC,QAAU,EAAA;AAAA,KACZ;AAEA,IAAI,IAAA,IAAA,SAAa,IAAO,GAAA,IAAA;AACxB,IAAI,IAAA,OAAA,SAAgB,OAAU,GAAA,OAAA;AAE9B,IAAO,OAAA,IAAA,CAAK,WAAW,MAAM,CAAA;AAAA;AAC/B,EAEA,MAAM,iBAAiB,EAA0C,EAAA;AAC/D,IAAA,OAAO,KAAK,YAAa,CAAA,EAAA,EAAI,EAAE,QAAA,EAAU,OAAO,CAAA;AAAA;AAClD,EAEA,MAAM,eAAe,EAA0C,EAAA;AAC7D,IAAA,OAAO,KAAK,YAAa,CAAA,EAAA,EAAI,EAAE,QAAA,EAAU,MAAM,CAAA;AAAA;AACjD,EAEQ,wBAAwB,MAA+B,EAAA;AAC7D,IAAI,IAAA,CAAC,OAAO,IAAQ,IAAA,MAAA,CAAO,KAAK,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACnD,MAAM,MAAA,IAAI,MAAM,yBAAyB,CAAA;AAAA;AAG3C,IAAA,IAAI,OAAO,KAAS,IAAA,CAAC,KAAK,YAAa,CAAA,MAAA,CAAO,KAAK,CAAG,EAAA;AACpD,MAAM,MAAA,IAAI,MAAM,yBAAyB,CAAA;AAAA;AAG3C,IAAA,IAAI,OAAO,OAAW,IAAA,CAAC,KAAK,UAAW,CAAA,MAAA,CAAO,OAAO,CAAG,EAAA;AACtD,MAAM,MAAA,IAAI,MAAM,+BAA+B,CAAA;AAAA;AAGjD,IAAA,IAAI,OAAO,aAAe,EAAA;AACxB,MAAI,IAAA,CAAC,MAAO,CAAA,aAAA,CAAc,IAAQ,IAAA,MAAA,CAAO,cAAc,IAAK,CAAA,IAAA,EAAO,CAAA,MAAA,KAAW,CAAG,EAAA;AAC/E,QAAM,MAAA,IAAI,MAAM,iCAAiC,CAAA;AAAA;AAGnD,MAAI,IAAA,CAAC,MAAO,CAAA,aAAA,CAAc,KAAS,IAAA,CAAC,KAAK,YAAa,CAAA,MAAA,CAAO,aAAc,CAAA,KAAK,CAAG,EAAA;AACjF,QAAM,MAAA,IAAI,MAAM,wCAAwC,CAAA;AAAA;AAC1D;AAGF,IAAA,IAAI,OAAO,OAAS,EAAA;AAClB,MAAA,MAAM,cAAiB,GAAA,CAAC,QAAU,EAAA,MAAA,EAAQ,SAAS,CAAA;AACnD,MAAA,KAAA,MAAW,SAAS,cAAgB,EAAA;AAClC,QAAA,IAAI,CAAC,MAAA,CAAO,OAAQ,CAAA,KAAoC,CACpD,IAAA,MAAA,CAAO,OAAQ,CAAA,KAAoC,CAAG,EAAA,IAAA,EAAO,CAAA,MAAA,KAAW,CAAG,EAAA;AAC7E,UAAA,MAAM,IAAI,KAAA,CAAM,CAAW,QAAA,EAAA,KAAK,CAAc,YAAA,CAAA,CAAA;AAAA;AAChD;AACF;AAGF,IAAA,IAAI,OAAO,QAAY,IAAA,KAAA,CAAM,OAAQ,CAAA,MAAA,CAAO,QAAQ,CAAG,EAAA;AACrD,MAAA,MAAA,CAAO,QAAS,CAAA,OAAA,CAAQ,CAAC,OAAA,EAAS,KAAU,KAAA;AAC1C,QAAI,IAAA,CAAC,QAAQ,IAAQ,IAAA,OAAA,CAAQ,KAAK,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACrD,UAAA,MAAM,IAAI,KAAA,CAAM,CAAW,QAAA,EAAA,KAAA,GAAQ,CAAC,CAAmB,iBAAA,CAAA,CAAA;AAAA;AAEzD,QAAI,IAAA,CAAC,QAAQ,QAAY,IAAA,OAAA,CAAQ,SAAS,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AAC7D,UAAA,MAAM,IAAI,KAAA,CAAM,CAAW,QAAA,EAAA,KAAA,GAAQ,CAAC,CAAuB,qBAAA,CAAA,CAAA;AAAA;AAC7D,OACD,CAAA;AAAA;AACH;AACF,EAEQ,aAAa,KAAwB,EAAA;AAC3C,IAAA,MAAM,UAAa,GAAA,4BAAA;AACnB,IAAO,OAAA,UAAA,CAAW,KAAK,KAAK,CAAA;AAAA;AAC9B,EAEQ,WAAW,GAAsB,EAAA;AACvC,IAAI,IAAA;AACF,MAAA,IAAI,IAAI,GAAG,CAAA;AACX,MAAO,OAAA,IAAA;AAAA,KACD,CAAA,MAAA;AACN,MAAO,OAAA,KAAA;AAAA;AACT;AAEJ;AAGO,SAAS,qBAAqB,SAA6C,EAAA;AAChF,EAAO,OAAA,IAAI,mBAAmB,SAAS,CAAA;AACzC;;;ACzMO,IAAM,mBAAN,MAA+C;AAAA,EAIpD,YAAY,SAA6B,EAAA;AAFzC,IAAA,IAAA,CAAQ,QAAW,GAAA,aAAA;AAGjB,IAAA,IAAA,CAAK,SAAY,GAAA,SAAA;AAAA;AACnB,EAEA,MAAM,UAAU,MAA0D,EAAA;AACxE,IAAM,MAAA,WAAA,GAAc,IAAI,eAAgB,EAAA;AAExC,IAAA,IAAI,MAAQ,EAAA;AACV,MAAO,MAAA,CAAA,OAAA,CAAQ,MAAM,CAAE,CAAA,OAAA,CAAQ,CAAC,CAAC,GAAA,EAAK,KAAK,CAAM,KAAA;AAC/C,QAAI,IAAA,KAAA,KAAU,MAAa,IAAA,KAAA,KAAU,IAAM,EAAA;AACzC,UAAA,WAAA,CAAY,MAAO,CAAA,GAAA,EAAK,KAAM,CAAA,QAAA,EAAU,CAAA;AAAA;AAC1C,OACD,CAAA;AAAA;AAGH,IAAA,MAAM,GAAM,GAAA,WAAA,CAAY,QAAS,EAAA,GAC7B,CAAG,EAAA,IAAA,CAAK,QAAQ,CAAA,CAAA,EAAI,WAAY,CAAA,QAAA,EAAU,CAAA,CAAA,GAC1C,IAAK,CAAA,QAAA;AAET,IAAO,OAAA,IAAA,CAAK,SAAU,CAAA,GAAA,CAAa,GAAG,CAAA;AAAA;AACxC,EAEA,MAAM,SAAS,EAAyC,EAAA;AACtD,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,sBAAsB,CAAA;AAAA;AAGxC,IAAO,OAAA,IAAA,CAAK,UAAU,GAAW,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAE,CAAA,CAAA;AAAA;AAC3D,EAEA,MAAM,UAAU,KAAkD,EAAA;AAChE,IAAA,IAAA,CAAK,qBAAqB,KAAK,CAAA;AAC/B,IAAA,OAAO,KAAK,SAAU,CAAA,IAAA,CAAY,GAAG,IAAK,CAAA,QAAQ,SAAS,KAAK,CAAA;AAAA;AAClE,EAEA,MAAM,cAAc,KAAkD,EAAA;AACpE,IAAA,IAAA,CAAK,qBAAqB,KAAK,CAAA;AAE/B,IAAI,IAAA,CAAC,MAAM,WAAa,EAAA;AACtB,MAAM,MAAA,IAAI,MAAM,iDAAiD,CAAA;AAAA;AAGnE,IAAA,IAAI,IAAI,IAAK,CAAA,KAAA,CAAM,WAAW,CAAK,oBAAA,IAAI,MAAQ,EAAA;AAC7C,MAAM,MAAA,IAAI,MAAM,sCAAsC,CAAA;AAAA;AAGxD,IAAA,OAAO,KAAK,SAAU,CAAA,IAAA,CAAY,GAAG,IAAK,CAAA,QAAQ,aAAa,KAAK,CAAA;AAAA;AACtE,EAEA,MAAM,qBAAqB,EAAwC,EAAA;AACjE,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,sBAAsB,CAAA;AAAA;AAGxC,IAAO,OAAA,IAAA,CAAK,UAAU,MAAa,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAS,OAAA,CAAA,CAAA;AAAA;AACpE,EAEA,MAAM,iBAAiD,GAAA;AACrD,IAAO,OAAA,IAAA,CAAK,SAAU,CAAA,GAAA,CAAW,sBAAsB,CAAA;AAAA;AACzD,EAEA,MAAM,cAAc,EAAuC,EAAA;AACzD,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,sBAAsB,CAAA;AAAA;AAGxC,IAAO,OAAA,IAAA,CAAK,UAAU,GAAS,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAQ,MAAA,CAAA,CAAA;AAAA;AAC/D;AAAA,EAGA,MAAM,aAA+C,GAAA;AACnD,IAAA,OAAO,KAAK,SAAU,CAAA;AAAA,MACpB,MAAA,EAAA,MAAA;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,eAAiD,GAAA;AACrD,IAAA,OAAO,KAAK,SAAU,CAAA;AAAA,MACpB,MAAA,EAAA,QAAA;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,kBAAoD,GAAA;AACxD,IAAA,OAAO,KAAK,SAAU,CAAA;AAAA,MACpB,MAAA,EAAA,QAAA;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,oBAAoB,UAAmD,EAAA;AAC3E,IAAA,IAAI,CAAC,UAAY,EAAA;AACf,MAAM,MAAA,IAAI,MAAM,yBAAyB,CAAA;AAAA;AAG3C,IAAA,OAAO,KAAK,SAAU,CAAA;AAAA,MACpB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,YAAA,CAAa,KAAe,EAAA,KAAA,GAAQ,EAAmC,EAAA;AAC3E,IAAA,IAAI,CAAC,KAAS,IAAA,KAAA,CAAM,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACvC,MAAM,MAAA,IAAI,MAAM,0BAA0B,CAAA;AAAA;AAG5C,IAAA,OAAO,KAAK,SAAU,CAAA;AAAA,MACpB,MAAA,EAAQ,MAAM,IAAK,EAAA;AAAA,MACnB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,qBAAqB,SAAkD,EAAA;AAC3E,IAAA,IAAI,CAAC,SAAW,EAAA;AACd,MAAM,MAAA,IAAI,MAAM,6BAA6B,CAAA;AAAA;AAG/C,IAAA,OAAO,KAAK,SAAU,CAAA;AAAA,MACpB,EAAI,EAAA;AAAA,KACL,CAAA;AAAA;AACH,EAEA,MAAM,kBAAkB,MAA+C,EAAA;AACrE,IAAA,IAAI,CAAC,MAAQ,EAAA;AACX,MAAM,MAAA,IAAI,MAAM,0BAA0B,CAAA;AAAA;AAG5C,IAAA,OAAO,KAAK,SAAU,CAAA;AAAA,MACpB,IAAM,EAAA;AAAA,KACP,CAAA;AAAA;AACH,EAEA,MAAM,YAAY,EAAyC,EAAA;AACzD,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,sBAAsB,CAAA;AAAA;AAGxC,IAAO,OAAA,IAAA,CAAK,UAAU,IAAY,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAS,OAAA,CAAA,CAAA;AAAA;AACnE,EAEA,MAAM,cAAc,MAAuD,EAAA;AACzE,IAAA,IAAI,CAAC,KAAM,CAAA,OAAA,CAAQ,MAAM,CAAK,IAAA,MAAA,CAAO,WAAW,CAAG,EAAA;AACjD,MAAM,MAAA,IAAI,MAAM,gDAAgD,CAAA;AAAA;AAGlE,IAAO,MAAA,CAAA,OAAA,CAAQ,CAAC,KAAA,EAAO,KAAU,KAAA;AAC/B,MAAI,IAAA;AACF,QAAA,IAAA,CAAK,qBAAqB,KAAK,CAAA;AAAA,eACxB,KAAO,EAAA;AACd,QAAM,MAAA,IAAI,MAAM,CAAS,MAAA,EAAA,KAAA,GAAQ,CAAC,CAAM,EAAA,EAAA,KAAA,CAAgB,OAAO,CAAE,CAAA,CAAA;AAAA;AACnE,KACD,CAAA;AAED,IAAO,OAAA,IAAA,CAAK,UAAU,IAAc,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,UAAA,CAAA,EAAc,EAAE,MAAA,EAAQ,CAAA;AAAA;AAC9E,EAEA,MAAM,iBAAA,CACJ,UACA,EAAA,EAAA,EACA,cACA,OAC6B,EAAA;AAC7B,IAAA,IAAI,CAAC,UAAY,EAAA;AACf,MAAM,MAAA,IAAI,MAAM,yBAAyB,CAAA;AAAA;AAG3C,IAAA,IAAI,CAAC,KAAM,CAAA,OAAA,CAAQ,EAAE,CAAK,IAAA,EAAA,CAAG,WAAW,CAAG,EAAA;AACzC,MAAM,MAAA,IAAI,MAAM,yBAAyB,CAAA;AAAA;AAG3C,IAAA,MAAM,SAA0B,GAAA;AAAA,MAC9B,EAAA;AAAA,MACA,OAAS,EAAA,EAAA;AAAA;AAAA,MACT,IAAM,EAAA,EAAA;AAAA;AAAA,MACN,UAAA;AAAA,MACA,YAAA;AAAA,MACA,GAAG;AAAA,KACL;AAEA,IAAO,OAAA,IAAA,CAAK,UAAU,SAAS,CAAA;AAAA;AACjC,EAEQ,qBAAqB,KAA2B,EAAA;AACtD,IAAI,IAAA,CAAC,MAAM,OAAQ,CAAA,KAAA,CAAM,EAAE,CAAK,IAAA,KAAA,CAAM,EAAG,CAAA,MAAA,KAAW,CAAG,EAAA;AACrD,MAAM,MAAA,IAAI,MAAM,oCAAoC,CAAA;AAAA;AAItD,IAAA,MAAM,SAAY,GAAA;AAAA,MAChB,GAAG,KAAM,CAAA,EAAA;AAAA,MACT,GAAI,KAAM,CAAA,EAAA,IAAM,EAAC;AAAA,MACjB,GAAI,KAAM,CAAA,GAAA,IAAO;AAAC,KACpB;AAEA,IAAA,SAAA,CAAU,QAAQ,CAAa,SAAA,KAAA;AAC7B,MAAA,IAAI,CAAC,IAAA,CAAK,YAAa,CAAA,SAAS,CAAG,EAAA;AACjC,QAAA,MAAM,IAAI,KAAA,CAAM,CAA0B,uBAAA,EAAA,SAAS,CAAE,CAAA,CAAA;AAAA;AACvD,KACD,CAAA;AAED,IAAI,IAAA,CAAC,MAAM,UAAY,EAAA;AACrB,MAAI,IAAA,CAAC,MAAM,OAAW,IAAA,KAAA,CAAM,QAAQ,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACvD,QAAM,MAAA,IAAI,MAAM,2BAA2B,CAAA;AAAA;AAG7C,MAAI,IAAA,CAAC,MAAM,IAAQ,IAAA,KAAA,CAAM,KAAK,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACjD,QAAM,MAAA,IAAI,MAAM,wBAAwB,CAAA;AAAA;AAC1C;AAIF,IAAA,IAAI,MAAM,WAAe,IAAA,KAAA,CAAM,OAAQ,CAAA,KAAA,CAAM,WAAW,CAAG,EAAA;AACzD,MAAA,KAAA,CAAM,WAAY,CAAA,OAAA,CAAQ,CAAC,UAAA,EAAY,KAAU,KAAA;AAC/C,QAAI,IAAA,CAAC,WAAW,QAAY,IAAA,UAAA,CAAW,SAAS,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACnE,UAAA,MAAM,IAAI,KAAA,CAAM,CAAc,WAAA,EAAA,KAAA,GAAQ,CAAC,CAAuB,qBAAA,CAAA,CAAA;AAAA;AAEhE,QAAI,IAAA,CAAC,WAAW,WAAe,IAAA,UAAA,CAAW,YAAY,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACzE,UAAA,MAAM,IAAI,KAAA,CAAM,CAAc,WAAA,EAAA,KAAA,GAAQ,CAAC,CAA2B,yBAAA,CAAA,CAAA;AAAA;AAEpE,QAAA,IAAI,CAAC,UAAA,CAAW,IAAQ,IAAA,CAAC,WAAW,GAAK,EAAA;AACvC,UAAA,MAAM,IAAI,KAAA,CAAM,CAAc,WAAA,EAAA,KAAA,GAAQ,CAAC,CAA+B,6BAAA,CAAA,CAAA;AAAA;AACxE,OACD,CAAA;AAAA;AAIH,IAAI,IAAA,KAAA,CAAM,eAAe,CAAC,KAAA,CAAM,gBAAgB,OAAO,KAAA,CAAM,iBAAiB,QAAW,CAAA,EAAA;AACvF,MAAM,MAAA,IAAI,MAAM,sDAAsD,CAAA;AAAA;AACxE;AACF,EAEQ,aAAa,KAAwB,EAAA;AAC3C,IAAA,MAAM,UAAa,GAAA,4BAAA;AACnB,IAAO,OAAA,UAAA,CAAW,KAAK,KAAK,CAAA;AAAA;AAEhC;AAGO,SAAS,mBAAmB,SAA2C,EAAA;AAC5E,EAAO,OAAA,IAAI,iBAAiB,SAAS,CAAA;AACvC;;;AC/OO,IAAM,kBAAN,MAA6C;AAAA,EAIlD,YAAY,SAA6B,EAAA;AAFzC,IAAA,IAAA,CAAQ,QAAW,GAAA,WAAA;AAGjB,IAAA,IAAA,CAAK,SAAY,GAAA,SAAA;AAAA;AACnB,EAEA,MAAM,QAAQ,MAAsD,EAAA;AAClE,IAAM,MAAA,WAAA,GAAc,IAAI,eAAgB,EAAA;AAExC,IAAA,IAAI,MAAQ,EAAA;AACV,MAAO,MAAA,CAAA,OAAA,CAAQ,MAAM,CAAE,CAAA,OAAA,CAAQ,CAAC,CAAC,GAAA,EAAK,KAAK,CAAM,KAAA;AAC/C,QAAI,IAAA,KAAA,KAAU,MAAa,IAAA,KAAA,KAAU,IAAM,EAAA;AACzC,UAAI,IAAA,KAAA,CAAM,OAAQ,CAAA,KAAK,CAAG,EAAA;AACxB,YAAM,KAAA,CAAA,OAAA,CAAQ,OAAK,WAAY,CAAA,MAAA,CAAO,KAAK,CAAE,CAAA,QAAA,EAAU,CAAC,CAAA;AAAA,WACnD,MAAA;AACL,YAAA,WAAA,CAAY,MAAO,CAAA,GAAA,EAAK,KAAM,CAAA,QAAA,EAAU,CAAA;AAAA;AAC1C;AACF,OACD,CAAA;AAAA;AAGH,IAAA,MAAM,GAAM,GAAA,WAAA,CAAY,QAAS,EAAA,GAC7B,CAAG,EAAA,IAAA,CAAK,QAAQ,CAAA,CAAA,EAAI,WAAY,CAAA,QAAA,EAAU,CAAA,CAAA,GAC1C,IAAK,CAAA,QAAA;AAET,IAAO,OAAA,IAAA,CAAK,SAAU,CAAA,GAAA,CAAW,GAAG,CAAA;AAAA;AACtC,EAEA,MAAM,OAAO,EAAuC,EAAA;AAClD,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,oBAAoB,CAAA;AAAA;AAGtC,IAAO,OAAA,IAAA,CAAK,UAAU,GAAS,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAE,CAAA,CAAA;AAAA;AACzD,EAEA,MAAM,UAAU,GAA8C,EAAA;AAC5D,IAAA,IAAA,CAAK,qBAAqB,GAAG,CAAA;AAC7B,IAAA,OAAO,IAAK,CAAA,SAAA,CAAU,IAAU,CAAA,IAAA,CAAK,UAAU,GAAG,CAAA;AAAA;AACpD,EAEA,MAAM,SAAU,CAAA,EAAA,EAAY,GAA8C,EAAA;AACxE,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,oBAAoB,CAAA;AAAA;AAGtC,IAAO,OAAA,IAAA,CAAK,UAAU,GAAS,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAA,CAAA,EAAI,GAAG,CAAA;AAAA;AAC9D,EAEA,MAAM,UAAU,EAAwC,EAAA;AACtD,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,oBAAoB,CAAA;AAAA;AAGtC,IAAO,OAAA,IAAA,CAAK,UAAU,MAAa,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAE,CAAA,CAAA;AAAA;AAC7D,EAEA,MAAM,QAAQ,EAAuC,EAAA;AACnD,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,oBAAoB,CAAA;AAAA;AAGtC,IAAO,OAAA,IAAA,CAAK,UAAU,IAAU,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAO,KAAA,CAAA,CAAA;AAAA;AAC/D,EAEA,MAAM,UAAU,EAAuC,EAAA;AACrD,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,oBAAoB,CAAA;AAAA;AAGtC,IAAO,OAAA,IAAA,CAAK,UAAU,MAAY,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAO,KAAA,CAAA,CAAA;AAAA;AACjE,EAEA,MAAM,WAAW,EAAuC,EAAA;AACtD,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,oBAAoB,CAAA;AAAA;AAGtC,IAAO,OAAA,IAAA,CAAK,UAAU,IAAU,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAU,QAAA,CAAA,CAAA;AAAA;AAClE,EAEA,MAAM,aAAa,EAAuC,EAAA;AACxD,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,oBAAoB,CAAA;AAAA;AAGtC,IAAO,OAAA,IAAA,CAAK,UAAU,MAAY,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAU,QAAA,CAAA,CAAA;AAAA;AACpE;AAAA,EAGA,MAAM,aAA6C,GAAA;AACjD,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB,QAAU,EAAA;AAAA,KACX,CAAA;AAAA;AACH,EAEA,MAAM,eAA+C,GAAA;AACnD,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB,UAAY,EAAA,IAAA;AAAA,MACZ,QAAU,EAAA;AAAA,KACX,CAAA;AAAA;AACH,EAEA,MAAM,kBAAkB,QAAoD,EAAA;AAC1E,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,gBAAgB,MAAgD,EAAA;AACpE,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,gBAAgB,QAA+C,EAAA;AACnE,IAAA,IAAI,CAAC,QAAU,EAAA;AACb,MAAM,MAAA,IAAI,MAAM,uBAAuB,CAAA;AAAA;AAGzC,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,qBAAqB,aAAoD,EAAA;AAC7E,IAAA,IAAI,CAAC,aAAe,EAAA;AAClB,MAAM,MAAA,IAAI,MAAM,4BAA4B,CAAA;AAAA;AAG9C,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,UAAA,CAAW,KAAe,EAAA,KAAA,GAAQ,EAAiC,EAAA;AACvE,IAAA,IAAI,CAAC,KAAS,IAAA,KAAA,CAAM,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACvC,MAAM,MAAA,IAAI,MAAM,0BAA0B,CAAA;AAAA;AAG5C,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB,MAAA,EAAQ,MAAM,IAAK,EAAA;AAAA,MACnB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,aAAc,CAAA,IAAA,GAAO,EAAiC,EAAA;AAC1D,IAAM,MAAA,IAAA,uBAAW,IAAK,EAAA;AACtB,IAAA,IAAA,CAAK,OAAQ,CAAA,IAAA,CAAK,OAAQ,EAAA,GAAI,IAAI,CAAA;AAElC,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB,aAAA,EAAe,KAAK,WAAY,EAAA;AAAA,MAChC,IAAM,EAAA,YAAA;AAAA,MACN,KAAO,EAAA;AAAA,KACR,CAAA;AAAA;AACH,EAEA,MAAM,UAAW,CAAA,KAAA,GAAQ,EAAiC,EAAA;AACxD,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB,IAAM,EAAA,OAAA;AAAA,MACN,KAAO,EAAA,MAAA;AAAA,MACP;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,cAAc,IAA6C,EAAA;AAC/D,IAAA,IAAI,CAAC,KAAM,CAAA,OAAA,CAAQ,IAAI,CAAK,IAAA,IAAA,CAAK,WAAW,CAAG,EAAA;AAC7C,MAAM,MAAA,IAAI,MAAM,8CAA8C,CAAA;AAAA;AAGhE,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,iBAAiD,GAAA;AACrD,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB,MAAA,EAAA,MAAA;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,qBAAqD,GAAA;AACzD,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB,MAAA,EAAA,UAAA;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,WAA2C,GAAA;AAC/C,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB,QAAA,EAAA,MAAA;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,eAA+C,GAAA;AACnD,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB,QAAA,EAAA,UAAA;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,cAA8C,GAAA;AAClD,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB,QAAA,EAAA,SAAA;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,iBAAiD,GAAA;AACrD,IAAA,OAAO,KAAK,OAAQ,CAAA;AAAA,MAClB,QAAA,EAAA,YAAA;AAAA,KACD,CAAA;AAAA;AACH,EAEA,MAAM,kBAAkB,EAAuC,EAAA;AAC7D,IAAA,IAAI,CAAC,EAAI,EAAA;AACP,MAAM,MAAA,IAAI,MAAM,oBAAoB,CAAA;AAAA;AAGtC,IAAO,OAAA,IAAA,CAAK,UAAU,IAAU,CAAA,CAAA,EAAG,KAAK,QAAQ,CAAA,CAAA,EAAI,EAAE,CAAO,KAAA,CAAA,CAAA;AAAA;AAC/D,EAEQ,qBAAqB,GAAyB,EAAA;AACpD,IAAI,IAAA,CAAC,IAAI,KAAS,IAAA,GAAA,CAAI,MAAM,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AAC/C,MAAM,MAAA,IAAI,MAAM,uBAAuB,CAAA;AAAA;AAGzC,IAAI,IAAA,CAAC,IAAI,WAAe,IAAA,GAAA,CAAI,YAAY,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AAC3D,MAAM,MAAA,IAAI,MAAM,6BAA6B,CAAA;AAAA;AAG/C,IAAI,IAAA,CAAC,OAAO,MAAO,CAAA,WAAW,EAAE,QAAS,CAAA,GAAA,CAAI,QAAQ,CAAG,EAAA;AACtD,MAAM,MAAA,IAAI,MAAM,sBAAsB,CAAA;AAAA;AAGxC,IAAI,IAAA,CAAC,OAAO,MAAO,CAAA,SAAS,EAAE,QAAS,CAAA,GAAA,CAAI,MAAM,CAAG,EAAA;AAClD,MAAM,MAAA,IAAI,MAAM,0BAA0B,CAAA;AAAA;AAG5C,IAAA,IAAI,IAAI,cAAkB,IAAA,CAAC,MAAM,OAAQ,CAAA,GAAA,CAAI,cAAc,CAAG,EAAA;AAC5D,MAAM,MAAA,IAAI,MAAM,kCAAkC,CAAA;AAAA;AAGpD,IAAA,IAAI,IAAI,IAAQ,IAAA,CAAC,MAAM,OAAQ,CAAA,GAAA,CAAI,IAAI,CAAG,EAAA;AACxC,MAAM,MAAA,IAAI,MAAM,uBAAuB,CAAA;AAAA;AAGzC,IAAA,IAAI,IAAI,OAAW,IAAA,KAAA,CAAM,OAAQ,CAAA,GAAA,CAAI,OAAO,CAAG,EAAA;AAC7C,MAAA,GAAA,CAAI,OAAQ,CAAA,OAAA,CAAQ,CAAC,MAAA,EAAQ,KAAU,KAAA;AACrC,QAAI,IAAA,CAAC,OAAO,IAAQ,IAAA,MAAA,CAAO,KAAK,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACnD,UAAA,MAAM,IAAI,KAAA,CAAM,CAAU,OAAA,EAAA,KAAA,GAAQ,CAAC,CAAmB,iBAAA,CAAA,CAAA;AAAA;AAExD,QAAI,IAAA,OAAO,MAAO,CAAA,KAAA,KAAU,QAAU,EAAA;AACpC,UAAA,MAAM,IAAI,KAAA,CAAM,CAAU,OAAA,EAAA,KAAA,GAAQ,CAAC,CAAyB,uBAAA,CAAA,CAAA;AAAA;AAE9D,QAAI,IAAA,CAAC,OAAO,IAAQ,IAAA,MAAA,CAAO,KAAK,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACnD,UAAA,MAAM,IAAI,KAAA,CAAM,CAAU,OAAA,EAAA,KAAA,GAAQ,CAAC,CAAmB,iBAAA,CAAA,CAAA;AAAA;AACxD,OACD,CAAA;AAAA;AAGH,IAAA,IAAI,IAAI,WAAe,IAAA,KAAA,CAAM,OAAQ,CAAA,GAAA,CAAI,WAAW,CAAG,EAAA;AACrD,MAAA,GAAA,CAAI,WAAY,CAAA,OAAA,CAAQ,CAAC,UAAA,EAAY,KAAU,KAAA;AAC7C,QAAI,IAAA,CAAC,WAAW,QAAY,IAAA,UAAA,CAAW,SAAS,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACnE,UAAA,MAAM,IAAI,KAAA,CAAM,CAAc,WAAA,EAAA,KAAA,GAAQ,CAAC,CAAuB,qBAAA,CAAA,CAAA;AAAA;AAEhE,QAAI,IAAA,CAAC,WAAW,WAAe,IAAA,UAAA,CAAW,YAAY,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACzE,UAAA,MAAM,IAAI,KAAA,CAAM,CAAc,WAAA,EAAA,KAAA,GAAQ,CAAC,CAA2B,yBAAA,CAAA,CAAA;AAAA;AAEpE,QAAI,IAAA,CAAC,WAAW,GAAO,IAAA,UAAA,CAAW,IAAI,IAAK,EAAA,CAAE,WAAW,CAAG,EAAA;AACzD,UAAA,MAAM,IAAI,KAAA,CAAM,CAAc,WAAA,EAAA,KAAA,GAAQ,CAAC,CAAkB,gBAAA,CAAA,CAAA;AAAA;AAC3D,OACD,CAAA;AAAA;AAGH,IAAI,IAAA,GAAA,CAAI,cAAc,IAAI,IAAA,CAAK,IAAI,UAAU,CAAA,mBAAQ,IAAA,IAAA,EAAQ,EAAA;AAC3D,MAAM,MAAA,IAAI,MAAM,0CAA0C,CAAA;AAAA;AAC5D;AAEJ;AAGO,SAAS,kBAAkB,SAA0C,EAAA;AAC1E,EAAO,OAAA,IAAI,gBAAgB,SAAS,CAAA;AACtC;;;ACvNO,SAAS,kBAAkB,SAA6B,EAAA;AAC7D,EAAO,OAAA;AAAA,IACL,KAAA,EAAO,mBAAmB,SAAS,CAAA;AAAA,IACnC,QAAA,EAAU,sBAAsB,SAAS,CAAA;AAAA,IACzC,OAAA,EAAS,qBAAqB,SAAS,CAAA;AAAA,IACvC,KAAA,EAAO,mBAAmB,SAAS,CAAA;AAAA,IACnC,IAAA,EAAM,kBAAkB,SAAS;AAAA,GACnC;AACF;AASO,SAAS,yBAAyB,MAAuB,EAAA;AAC9D,EAAM,MAAA,QAAA,GAAW,iBAAkB,CAAA,MAAA,CAAO,SAAS,CAAA;AAGnD,EAAA,IAAI,OAAO,aAAe,EAAA;AACxB,IAAA,MAAA,CAAO,IAAK,CAAA,QAAQ,CAAE,CAAA,OAAA,CAAQ,CAAe,WAAA,KAAA;AAC3C,MAAM,MAAA,OAAA,GAAU,SAAS,WAAoC,CAAA;AAC7D,MAAA,sBAAA,CAAuB,SAAS,WAAW,CAAA;AAAA,KAC5C,CAAA;AAAA;AAGH,EAAO,OAAA,QAAA;AACT;AAGA,SAAS,sBAAA,CAAuB,SAAc,WAAqB,EAAA;AACjE,EAAA,MAAM,kBAAkB,MAAO,CAAA,mBAAA,CAAoB,MAAO,CAAA,cAAA,CAAe,OAAO,CAAC,CAAA,CAC9E,MAAO,CAAA,CAAA,IAAA,KAAQ,SAAS,aAAiB,IAAA,OAAO,OAAQ,CAAA,IAAI,MAAM,UAAU,CAAA;AAE/E,EAAA,eAAA,CAAgB,QAAQ,CAAc,UAAA,KAAA;AACpC,IAAM,MAAA,cAAA,GAAiB,QAAQ,UAAU,CAAA;AACzC,IAAQ,OAAA,CAAA,UAAU,CAAI,GAAA,eAAA,GAAkB,IAAa,EAAA;AACnD,MAAA,OAAA,CAAQ,IAAI,CAAI,CAAA,EAAA,WAAW,CAAY,SAAA,EAAA,UAAU,IAAI,IAAI,CAAA;AACzD,MAAI,IAAA;AACF,QAAA,MAAM,MAAS,GAAA,MAAM,cAAe,CAAA,KAAA,CAAM,MAAM,IAAI,CAAA;AACpD,QAAA,OAAA,CAAQ,GAAI,CAAA,CAAA,CAAA,EAAI,WAAW,CAAA,SAAA,EAAY,UAAU,CAAY,UAAA,CAAA,CAAA;AAC7D,QAAO,OAAA,MAAA;AAAA,eACA,KAAO,EAAA;AACd,QAAA,OAAA,CAAQ,MAAM,CAAI,CAAA,EAAA,WAAW,CAAY,SAAA,EAAA,UAAU,aAAa,KAAK,CAAA;AACrE,QAAM,MAAA,KAAA;AAAA;AACR,KACF;AAAA,GACD,CAAA;AACH", "file": "index.mjs", "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\n/**\n * Combines class names using clsx and tailwind-merge\n * This replaces all duplicate cn() functions across apps\n */\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n/**\n * Format bytes to human readable string\n */\nexport function formatBytes(bytes: number, decimals = 2): string {\n  if (!bytes) return '0 Bytes'\n\n  const k = 1024\n  const dm = decimals < 0 ? 0 : decimals\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']\n\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]\n}\n\n/**\n * Format number with K/M suffixes\n */\nexport function formatNumber(num: number): string {\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + 'M'\n  }\n  if (num >= 1000) {\n    return (num / 1000).toFixed(1) + 'K'\n  }\n  return num.toString()\n}\n\n/**\n * Format currency\n */\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount)\n}\n\n/**\n * Debounce function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n/**\n * Throttle function\n */\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => inThrottle = false, limit)\n    }\n  }\n}\n\n/**\n * Generate unique ID\n */\nexport function generateId(prefix = ''): string {\n  const id = Math.random().toString(36).substr(2, 9)\n  return prefix ? `${prefix}_${id}` : id\n}\n\n/**\n * Sleep utility\n */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\n/**\n * Capitalize first letter\n */\nexport function capitalize(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n\n/**\n * Truncate string with ellipsis\n */\nexport function truncate(str: string, length: number): string {\n  if (str.length <= length) return str\n  return str.slice(0, length) + '...'\n}\n\n/**\n * Check if value is empty (null, undefined, empty string, empty array, empty object)\n */\nexport function isEmpty(value: any): boolean {\n  if (value == null) return true\n  if (typeof value === 'string') return value.trim() === ''\n  if (Array.isArray(value)) return value.length === 0\n  if (typeof value === 'object') return Object.keys(value).length === 0\n  return false\n}\n\n/**\n * Deep clone object\n */\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== 'object') return obj\n  if (obj instanceof Date) return new Date(obj.getTime()) as T\n  if (obj instanceof Array) return obj.map(item => deepClone(item)) as T\n  if (typeof obj === 'object') {\n    const clonedObj = {} as T\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key])\n      }\n    }\n    return clonedObj\n  }\n  return obj\n}\n\n/**\n * Get nested object property safely\n */\nexport function get(obj: any, path: string, defaultValue?: any): any {\n  const keys = path.split('.')\n  let result = obj\n\n  for (const key of keys) {\n    if (result == null || typeof result !== 'object') {\n      return defaultValue\n    }\n    result = result[key]\n  }\n\n  return result !== undefined ? result : defaultValue\n}\n\n/**\n * Set nested object property\n */\nexport function set(obj: any, path: string, value: any): void {\n  const keys = path.split('.')\n  let current = obj\n\n  for (let i = 0; i < keys.length - 1; i++) {\n    const key = keys[i]\n    if (!(key in current) || typeof current[key] !== 'object') {\n      current[key] = {}\n    }\n    current = current[key]\n  }\n\n  current[keys[keys.length - 1]] = value\n}\n\n/**\n * Omit properties from object\n */\nexport function omit<T extends Record<string, any>, K extends keyof T>(\n  obj: T,\n  keys: K[]\n): Omit<T, K> {\n  const result = { ...obj }\n  keys.forEach(key => delete result[key])\n  return result\n}\n\n/**\n * Pick properties from object\n */\nexport function pick<T extends Record<string, any>, K extends keyof T>(\n  obj: T,\n  keys: K[]\n): Pick<T, K> {\n  const result = {} as Pick<T, K>\n  keys.forEach(key => {\n    if (key in obj) {\n      result[key] = obj[key]\n    }\n  })\n  return result\n}\n", "/**\n * Application-wide constants for Luminar platform\n */\n\n// API Configuration\nexport const API_CONFIG = {\n  TIMEOUT: 10000,\n  RETRY_ATTEMPTS: 3,\n  RETRY_DELAY: 1000,\n  DEFAULT_PAGE_SIZE: 20,\n  MAX_PAGE_SIZE: 100,\n} as const\n\n// Authentication Configuration\nexport const AUTH_CONFIG = {\n  SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes\n  REFRESH_INTERVAL: 10 * 60 * 1000, // 10 minutes\n  TOKEN_STORAGE_KEY: 'luminar_auth_token',\n  REFRESH_TOKEN_STORAGE_KEY: 'luminar_refresh_token',\n  USER_STORAGE_KEY: 'luminar_user',\n  REMEMBER_ME_DURATION: 30 * 24 * 60 * 60 * 1000, // 30 days\n} as const\n\n// File Upload Configuration\nexport const FILE_CONFIG = {\n  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB\n  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\n  ALLOWED_DOCUMENT_TYPES: [\n    'application/pdf',\n    'application/msword',\n    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n    'application/vnd.ms-excel',\n    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n    'text/plain',\n    'text/csv',\n  ],\n  CHUNK_SIZE: 1024 * 1024, // 1MB chunks for large file uploads\n} as const\n\n// UI Configuration\nexport const UI_CONFIG = {\n  DEBOUNCE_DELAY: 300,\n  THROTTLE_DELAY: 100,\n  ANIMATION_DURATION: 200,\n  TOAST_DURATION: 5000,\n  MODAL_Z_INDEX: 1000,\n  DROPDOWN_Z_INDEX: 999,\n  TOOLTIP_Z_INDEX: 998,\n} as const\n\n// Theme Configuration\nexport const THEME_CONFIG = {\n  DEFAULT_THEME: 'light',\n  STORAGE_KEY: 'luminar_theme',\n  BREAKPOINTS: {\n    sm: '640px',\n    md: '768px',\n    lg: '1024px',\n    xl: '1280px',\n    '2xl': '1536px',\n  },\n  COLORS: {\n    primary: {\n      50: '#eff6ff',\n      100: '#dbeafe',\n      200: '#bfdbfe',\n      300: '#93c5fd',\n      400: '#60a5fa',\n      500: '#3b82f6',\n      600: '#2563eb',\n      700: '#1d4ed8',\n      800: '#1e40af',\n      900: '#1e3a8a',\n    },\n    gray: {\n      50: '#f9fafb',\n      100: '#f3f4f6',\n      200: '#e5e7eb',\n      300: '#d1d5db',\n      400: '#9ca3af',\n      500: '#6b7280',\n      600: '#4b5563',\n      700: '#374151',\n      800: '#1f2937',\n      900: '#111827',\n    },\n  },\n} as const\n\n// Validation Rules\nexport const VALIDATION_RULES = {\n  EMAIL_REGEX: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PASSWORD_MIN_LENGTH: 8,\n  PASSWORD_REGEX: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\n  PHONE_REGEX: /^\\+?[\\d\\s\\-\\(\\)]+$/,\n  URL_REGEX: /^https?:\\/\\/.+/,\n  USERNAME_REGEX: /^[a-zA-Z0-9_-]{3,20}$/,\n} as const\n\n// Date and Time Formats\nexport const DATE_FORMATS = {\n  ISO: 'yyyy-MM-dd',\n  US: 'MM/dd/yyyy',\n  EU: 'dd/MM/yyyy',\n  DISPLAY: 'MMM d, yyyy',\n  DISPLAY_WITH_TIME: 'MMM d, yyyy h:mm a',\n  TIME_12H: 'h:mm a',\n  TIME_24H: 'HH:mm',\n  DATETIME_ISO: \"yyyy-MM-dd'T'HH:mm:ss.SSSxxx\",\n} as const\n\n// HTTP Status Codes\nexport const HTTP_STATUS = {\n  OK: 200,\n  CREATED: 201,\n  NO_CONTENT: 204,\n  BAD_REQUEST: 400,\n  UNAUTHORIZED: 401,\n  FORBIDDEN: 403,\n  NOT_FOUND: 404,\n  CONFLICT: 409,\n  UNPROCESSABLE_ENTITY: 422,\n  INTERNAL_SERVER_ERROR: 500,\n  BAD_GATEWAY: 502,\n  SERVICE_UNAVAILABLE: 503,\n} as const\n\n// Error Codes\nexport const ERROR_CODES = {\n  // Authentication errors\n  AUTH_INVALID_CREDENTIALS: 'AUTH_INVALID_CREDENTIALS',\n  AUTH_TOKEN_EXPIRED: 'AUTH_TOKEN_EXPIRED',\n  AUTH_TOKEN_INVALID: 'AUTH_TOKEN_INVALID',\n  AUTH_INSUFFICIENT_PERMISSIONS: 'AUTH_INSUFFICIENT_PERMISSIONS',\n  AUTH_ACCOUNT_LOCKED: 'AUTH_ACCOUNT_LOCKED',\n  AUTH_ACCOUNT_NOT_VERIFIED: 'AUTH_ACCOUNT_NOT_VERIFIED',\n\n  // Validation errors\n  VALIDATION_REQUIRED_FIELD: 'VALIDATION_REQUIRED_FIELD',\n  VALIDATION_INVALID_FORMAT: 'VALIDATION_INVALID_FORMAT',\n  VALIDATION_MIN_LENGTH: 'VALIDATION_MIN_LENGTH',\n  VALIDATION_MAX_LENGTH: 'VALIDATION_MAX_LENGTH',\n  VALIDATION_INVALID_EMAIL: 'VALIDATION_INVALID_EMAIL',\n  VALIDATION_PASSWORD_WEAK: 'VALIDATION_PASSWORD_WEAK',\n\n  // Network errors\n  NETWORK_CONNECTION_ERROR: 'NETWORK_CONNECTION_ERROR',\n  NETWORK_TIMEOUT: 'NETWORK_TIMEOUT',\n  NETWORK_SERVER_ERROR: 'NETWORK_SERVER_ERROR',\n\n  // File upload errors\n  FILE_TOO_LARGE: 'FILE_TOO_LARGE',\n  FILE_INVALID_TYPE: 'FILE_INVALID_TYPE',\n  FILE_UPLOAD_FAILED: 'FILE_UPLOAD_FAILED',\n\n  // Generic errors\n  UNKNOWN_ERROR: 'UNKNOWN_ERROR',\n  OPERATION_FAILED: 'OPERATION_FAILED',\n  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',\n  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',\n} as const\n\n// User Roles and Permissions\nexport const USER_ROLES = {\n  SUPER_ADMIN: 'super_admin',\n  ADMIN: 'admin',\n  MANAGER: 'manager',\n  USER: 'user',\n  GUEST: 'guest',\n} as const\n\nexport const PERMISSIONS = {\n  // User management\n  USERS_READ: 'users:read',\n  USERS_WRITE: 'users:write',\n  USERS_DELETE: 'users:delete',\n\n  // Content management\n  CONTENT_READ: 'content:read',\n  CONTENT_WRITE: 'content:write',\n  CONTENT_DELETE: 'content:delete',\n  CONTENT_PUBLISH: 'content:publish',\n\n  // Analytics\n  ANALYTICS_READ: 'analytics:read',\n  ANALYTICS_EXPORT: 'analytics:export',\n\n  // System administration\n  SYSTEM_CONFIG: 'system:config',\n  SYSTEM_LOGS: 'system:logs',\n  SYSTEM_BACKUP: 'system:backup',\n} as const\n\n// Application Routes\nexport const ROUTES = {\n  HOME: '/',\n  LOGIN: '/login',\n  REGISTER: '/register',\n  FORGOT_PASSWORD: '/forgot-password',\n  RESET_PASSWORD: '/reset-password',\n  DASHBOARD: '/dashboard',\n  PROFILE: '/profile',\n  SETTINGS: '/settings',\n  USERS: '/users',\n  ANALYTICS: '/analytics',\n  HELP: '/help',\n  PRIVACY: '/privacy',\n  TERMS: '/terms',\n} as const\n\n// Local Storage Keys\nexport const STORAGE_KEYS = {\n  THEME: 'luminar_theme',\n  LANGUAGE: 'luminar_language',\n  SIDEBAR_COLLAPSED: 'luminar_sidebar_collapsed',\n  TABLE_PREFERENCES: 'luminar_table_preferences',\n  RECENT_SEARCHES: 'luminar_recent_searches',\n  DRAFT_DATA: 'luminar_draft_data',\n} as const\n\n// Event Names for Custom Events\nexport const EVENTS = {\n  AUTH_LOGIN: 'auth:login',\n  AUTH_LOGOUT: 'auth:logout',\n  AUTH_SESSION_EXPIRED: 'auth:session-expired',\n  THEME_CHANGED: 'theme:changed',\n  NOTIFICATION_RECEIVED: 'notification:received',\n  FILE_UPLOAD_PROGRESS: 'file:upload-progress',\n  FILE_UPLOAD_COMPLETE: 'file:upload-complete',\n  FILE_UPLOAD_ERROR: 'file:upload-error',\n} as const\n\n// Feature Flags\nexport const FEATURE_FLAGS = {\n  ENABLE_DARK_MODE: 'enable_dark_mode',\n  ENABLE_NOTIFICATIONS: 'enable_notifications',\n  ENABLE_FILE_UPLOAD: 'enable_file_upload',\n  ENABLE_ANALYTICS: 'enable_analytics',\n  ENABLE_REAL_TIME: 'enable_real_time',\n  ENABLE_OFFLINE_MODE: 'enable_offline_mode',\n} as const\n\n// Application Metadata\nexport const APP_METADATA = {\n  NAME: 'Luminar',\n  DESCRIPTION: 'Learning & Development Platform',\n  VERSION: '1.0.0',\n  AUTHOR: 'Luminar Team',\n  SUPPORT_EMAIL: '<EMAIL>',\n  DOCUMENTATION_URL: 'https://docs.luminar.ai',\n  GITHUB_URL: 'https://github.com/luminar/platform',\n} as const\n", "import { z } from 'zod'\nimport { VALIDATION_RULES } from '../constants'\n\n/**\n * Common validation schemas for Luminar applications\n */\n\n// Basic field schemas\nexport const emailSchema = z\n  .string()\n  .email('Invalid email format')\n  .min(1, 'Email is required')\n\nexport const passwordSchema = z\n  .string()\n  .min(VALIDATION_RULES.PASSWORD_MIN_LENGTH, `Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters`)\n  .regex(\n    VALIDATION_RULES.PASSWORD_REGEX,\n    'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'\n  )\n\nexport const nameSchema = z\n  .string()\n  .min(1, 'Name is required')\n  .max(100, 'Name must be less than 100 characters')\n  .trim()\n\nexport const phoneSchema = z\n  .string()\n  .regex(VALIDATION_RULES.PHONE_REGEX, 'Invalid phone number format')\n  .optional()\n\nexport const urlSchema = z\n  .string()\n  .url('Invalid URL format')\n  .optional()\n\n// ID schemas\nexport const idSchema = z.string().uuid('Invalid ID format')\n\nexport const slugSchema = z\n  .string()\n  .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')\n  .min(1, 'Slug is required')\n\n// Pagination schemas\nexport const paginationSchema = z.object({\n  page: z.number().int().min(1).default(1),\n  limit: z.number().int().min(1).max(100).default(20),\n  offset: z.number().int().min(0).optional(),\n})\n\nexport const sortSchema = z.object({\n  sortBy: z.string().optional(),\n  sortOrder: z.enum(['asc', 'desc']).default('asc'),\n})\n\nexport const searchSchema = z.object({\n  query: z.string().optional(),\n  filters: z.record(z.any()).optional(),\n})\n\nexport const baseQuerySchema = paginationSchema\n  .merge(sortSchema)\n  .merge(searchSchema)\n\n// Authentication schemas\nexport const loginSchema = z.object({\n  email: emailSchema,\n  password: z.string().min(1, 'Password is required'),\n  rememberMe: z.boolean().default(false),\n})\n\nexport const registerSchema = z.object({\n  email: emailSchema,\n  password: passwordSchema,\n  confirmPassword: z.string(),\n  name: nameSchema,\n  acceptTerms: z.boolean().refine(val => val === true, {\n    message: 'You must accept the terms and conditions',\n  }),\n}).refine(data => data.password === data.confirmPassword, {\n  message: 'Passwords do not match',\n  path: ['confirmPassword'],\n})\n\nexport const forgotPasswordSchema = z.object({\n  email: emailSchema,\n})\n\nexport const resetPasswordSchema = z.object({\n  token: z.string().min(1, 'Reset token is required'),\n  password: passwordSchema,\n  confirmPassword: z.string(),\n}).refine(data => data.password === data.confirmPassword, {\n  message: 'Passwords do not match',\n  path: ['confirmPassword'],\n})\n\nexport const changePasswordSchema = z.object({\n  currentPassword: z.string().min(1, 'Current password is required'),\n  newPassword: passwordSchema,\n  confirmPassword: z.string(),\n}).refine(data => data.newPassword === data.confirmPassword, {\n  message: 'Passwords do not match',\n  path: ['confirmPassword'],\n})\n\n// User schemas\nexport const userProfileSchema = z.object({\n  name: nameSchema,\n  email: emailSchema,\n  phone: phoneSchema,\n  avatar: urlSchema,\n  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),\n  timezone: z.string().optional(),\n  language: z.string().optional(),\n})\n\nexport const userPreferencesSchema = z.object({\n  theme: z.enum(['light', 'dark', 'system']).default('system'),\n  notifications: z.object({\n    email: z.boolean().default(true),\n    push: z.boolean().default(true),\n    sms: z.boolean().default(false),\n  }).default({}),\n  privacy: z.object({\n    profileVisible: z.boolean().default(true),\n    activityVisible: z.boolean().default(true),\n  }).default({}),\n})\n\n// File upload schemas\nexport const fileUploadSchema = z.object({\n  name: z.string().min(1, 'File name is required'),\n  size: z.number().int().min(1, 'File size must be greater than 0'),\n  type: z.string().min(1, 'File type is required'),\n  data: z.instanceof(File).or(z.string()), // File object or base64 string\n})\n\n// API response schemas\nexport const apiResponseSchema = z.object({\n  data: z.any(),\n  message: z.string().optional(),\n  success: z.boolean(),\n  timestamp: z.string(),\n  requestId: z.string().optional(),\n})\n\nexport const paginatedResponseSchema = apiResponseSchema.extend({\n  data: z.array(z.any()),\n  pagination: z.object({\n    page: z.number().int(),\n    limit: z.number().int(),\n    total: z.number().int(),\n    totalPages: z.number().int(),\n    hasNext: z.boolean(),\n    hasPrev: z.boolean(),\n  }),\n})\n\nexport const apiErrorSchema = z.object({\n  code: z.string(),\n  message: z.string(),\n  details: z.record(z.any()).optional(),\n  timestamp: z.string(),\n  path: z.string().optional(),\n  requestId: z.string().optional(),\n})\n\n// Form validation helpers\nexport type LoginFormData = z.infer<typeof loginSchema>\nexport type RegisterFormData = z.infer<typeof registerSchema>\nexport type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>\nexport type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>\nexport type ChangePasswordFormData = z.infer<typeof changePasswordSchema>\nexport type UserProfileFormData = z.infer<typeof userProfileSchema>\nexport type UserPreferencesFormData = z.infer<typeof userPreferencesSchema>\nexport type FileUploadFormData = z.infer<typeof fileUploadSchema>\nexport type PaginationParams = z.infer<typeof paginationSchema>\nexport type SortParams = z.infer<typeof sortSchema>\nexport type SearchParams = z.infer<typeof searchSchema>\nexport type BaseQueryParams = z.infer<typeof baseQuerySchema>\n", "import { ERROR_CODES } from '../constants'\n\n/**\n * Base error class for Luminar applications\n */\nexport class LuminarError extends Error {\n  public readonly code: string\n  public readonly statusCode?: number\n  public readonly details?: Record<string, any>\n  public readonly timestamp: string\n\n  constructor(\n    message: string,\n    code: string = ERROR_CODES.UNKNOWN_ERROR,\n    statusCode?: number,\n    details?: Record<string, any>\n  ) {\n    super(message)\n    this.name = 'LuminarError'\n    this.code = code\n    this.statusCode = statusCode\n    this.details = details\n    this.timestamp = new Date().toISOString()\n\n    // Maintains proper stack trace for where our error was thrown (only available on V8)\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, LuminarError)\n    }\n  }\n\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      code: this.code,\n      statusCode: this.statusCode,\n      details: this.details,\n      timestamp: this.timestamp,\n      stack: this.stack,\n    }\n  }\n}\n\n/**\n * Authentication related errors\n */\nexport class AuthError extends LuminarError {\n  constructor(message: string, code: string = ERROR_CODES.AUTH_INVALID_CREDENTIALS, details?: Record<string, any>) {\n    super(message, code, 401, details)\n    this.name = 'AuthError'\n  }\n}\n\n/**\n * Validation related errors\n */\nexport class ValidationError extends LuminarError {\n  constructor(message: string, code: string = ERROR_CODES.VALIDATION_REQUIRED_FIELD, details?: Record<string, any>) {\n    super(message, code, 400, details)\n    this.name = 'ValidationError'\n  }\n}\n\n/**\n * Network related errors\n */\nexport class NetworkError extends LuminarError {\n  constructor(message: string, code: string = ERROR_CODES.NETWORK_CONNECTION_ERROR, details?: Record<string, any>) {\n    super(message, code, 500, details)\n    this.name = 'NetworkError'\n  }\n}\n\n/**\n * File upload related errors\n */\nexport class FileError extends LuminarError {\n  constructor(message: string, code: string = ERROR_CODES.FILE_UPLOAD_FAILED, details?: Record<string, any>) {\n    super(message, code, 400, details)\n    this.name = 'FileError'\n  }\n}\n\n/**\n * Permission related errors\n */\nexport class PermissionError extends LuminarError {\n  constructor(message: string, code: string = ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS, details?: Record<string, any>) {\n    super(message, code, 403, details)\n    this.name = 'PermissionError'\n  }\n}\n\n/**\n * Resource not found errors\n */\nexport class NotFoundError extends LuminarError {\n  constructor(message: string, code: string = ERROR_CODES.RESOURCE_NOT_FOUND, details?: Record<string, any>) {\n    super(message, code, 404, details)\n    this.name = 'NotFoundError'\n  }\n}\n\n/**\n * Conflict errors (resource already exists)\n */\nexport class ConflictError extends LuminarError {\n  constructor(message: string, code: string = ERROR_CODES.RESOURCE_ALREADY_EXISTS, details?: Record<string, any>) {\n    super(message, code, 409, details)\n    this.name = 'ConflictError'\n  }\n}\n\n/**\n * Error factory functions for common scenarios\n */\nexport const createAuthError = {\n  invalidCredentials: (details?: Record<string, any>) =>\n    new AuthError('Invalid email or password', ERROR_CODES.AUTH_INVALID_CREDENTIALS, details),\n  \n  tokenExpired: (details?: Record<string, any>) =>\n    new AuthError('Authentication token has expired', ERROR_CODES.AUTH_TOKEN_EXPIRED, details),\n  \n  tokenInvalid: (details?: Record<string, any>) =>\n    new AuthError('Invalid authentication token', ERROR_CODES.AUTH_TOKEN_INVALID, details),\n  \n  insufficientPermissions: (details?: Record<string, any>) =>\n    new PermissionError('Insufficient permissions to perform this action', ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS, details),\n  \n  accountLocked: (details?: Record<string, any>) =>\n    new AuthError('Account has been locked due to multiple failed login attempts', ERROR_CODES.AUTH_ACCOUNT_LOCKED, details),\n  \n  accountNotVerified: (details?: Record<string, any>) =>\n    new AuthError('Account email has not been verified', ERROR_CODES.AUTH_ACCOUNT_NOT_VERIFIED, details),\n}\n\nexport const createValidationError = {\n  requiredField: (fieldName: string, details?: Record<string, any>) =>\n    new ValidationError(`${fieldName} is required`, ERROR_CODES.VALIDATION_REQUIRED_FIELD, { field: fieldName, ...details }),\n  \n  invalidFormat: (fieldName: string, details?: Record<string, any>) =>\n    new ValidationError(`${fieldName} has invalid format`, ERROR_CODES.VALIDATION_INVALID_FORMAT, { field: fieldName, ...details }),\n  \n  minLength: (fieldName: string, minLength: number, details?: Record<string, any>) =>\n    new ValidationError(`${fieldName} must be at least ${minLength} characters`, ERROR_CODES.VALIDATION_MIN_LENGTH, { field: fieldName, minLength, ...details }),\n  \n  maxLength: (fieldName: string, maxLength: number, details?: Record<string, any>) =>\n    new ValidationError(`${fieldName} must be less than ${maxLength} characters`, ERROR_CODES.VALIDATION_MAX_LENGTH, { field: fieldName, maxLength, ...details }),\n  \n  invalidEmail: (details?: Record<string, any>) =>\n    new ValidationError('Invalid email format', ERROR_CODES.VALIDATION_INVALID_EMAIL, details),\n  \n  weakPassword: (details?: Record<string, any>) =>\n    new ValidationError('Password does not meet security requirements', ERROR_CODES.VALIDATION_PASSWORD_WEAK, details),\n}\n\nexport const createNetworkError = {\n  connectionError: (details?: Record<string, any>) =>\n    new NetworkError('Unable to connect to server', ERROR_CODES.NETWORK_CONNECTION_ERROR, details),\n  \n  timeout: (details?: Record<string, any>) =>\n    new NetworkError('Request timed out', ERROR_CODES.NETWORK_TIMEOUT, details),\n  \n  serverError: (details?: Record<string, any>) =>\n    new NetworkError('Internal server error', ERROR_CODES.NETWORK_SERVER_ERROR, details),\n}\n\nexport const createFileError = {\n  tooLarge: (maxSize: number, details?: Record<string, any>) =>\n    new FileError(`File size exceeds maximum allowed size of ${maxSize} bytes`, ERROR_CODES.FILE_TOO_LARGE, { maxSize, ...details }),\n  \n  invalidType: (allowedTypes: string[], details?: Record<string, any>) =>\n    new FileError(`File type not allowed. Allowed types: ${allowedTypes.join(', ')}`, ERROR_CODES.FILE_INVALID_TYPE, { allowedTypes, ...details }),\n  \n  uploadFailed: (details?: Record<string, any>) =>\n    new FileError('File upload failed', ERROR_CODES.FILE_UPLOAD_FAILED, details),\n}\n\n/**\n * Error handler utility functions\n */\nexport function isLuminarError(error: any): error is LuminarError {\n  return error instanceof LuminarError\n}\n\nexport function isAuthError(error: any): error is AuthError {\n  return error instanceof AuthError\n}\n\nexport function isValidationError(error: any): error is ValidationError {\n  return error instanceof ValidationError\n}\n\nexport function isNetworkError(error: any): error is NetworkError {\n  return error instanceof NetworkError\n}\n\nexport function isFileError(error: any): error is FileError {\n  return error instanceof FileError\n}\n\nexport function isPermissionError(error: any): error is PermissionError {\n  return error instanceof PermissionError\n}\n\nexport function isNotFoundError(error: any): error is NotFoundError {\n  return error instanceof NotFoundError\n}\n\nexport function isConflictError(error: any): error is ConflictError {\n  return error instanceof ConflictError\n}\n\n/**\n * Convert unknown error to LuminarError\n */\nexport function toLuminarError(error: unknown): LuminarError {\n  if (isLuminarError(error)) {\n    return error\n  }\n\n  if (error instanceof Error) {\n    return new LuminarError(error.message, ERROR_CODES.UNKNOWN_ERROR, undefined, {\n      originalError: error.name,\n      stack: error.stack,\n    })\n  }\n\n  if (typeof error === 'string') {\n    return new LuminarError(error, ERROR_CODES.UNKNOWN_ERROR)\n  }\n\n  return new LuminarError('An unknown error occurred', ERROR_CODES.UNKNOWN_ERROR, undefined, {\n    originalError: error,\n  })\n}\n\n/**\n * Error logging utility\n */\nexport function logError(error: LuminarError, context?: Record<string, any>) {\n  const logData = {\n    ...error.toJSON(),\n    context,\n  }\n\n  // In development, log to console\n  if (process.env.NODE_ENV === 'development') {\n    console.error('Luminar Error:', logData)\n  }\n\n  // In production, you might want to send to a logging service\n  // Example: sendToLoggingService(logData)\n}\n\n/**\n * Error boundary helper for React components\n */\nexport function getErrorMessage(error: unknown): string {\n  if (isLuminarError(error)) {\n    return error.message\n  }\n\n  if (error instanceof Error) {\n    return error.message\n  }\n\n  if (typeof error === 'string') {\n    return error\n  }\n\n  return 'An unexpected error occurred'\n}\n\n/**\n * HTTP status code to error mapping\n */\nexport function createErrorFromStatus(status: number, message?: string, details?: Record<string, any>): LuminarError {\n  switch (status) {\n    case 400:\n      return new ValidationError(message || 'Bad Request', ERROR_CODES.VALIDATION_INVALID_FORMAT, details)\n    case 401:\n      return new AuthError(message || 'Unauthorized', ERROR_CODES.AUTH_INVALID_CREDENTIALS, details)\n    case 403:\n      return new PermissionError(message || 'Forbidden', ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS, details)\n    case 404:\n      return new NotFoundError(message || 'Not Found', ERROR_CODES.RESOURCE_NOT_FOUND, details)\n    case 409:\n      return new ConflictError(message || 'Conflict', ERROR_CODES.RESOURCE_ALREADY_EXISTS, details)\n    case 422:\n      return new ValidationError(message || 'Unprocessable Entity', ERROR_CODES.VALIDATION_INVALID_FORMAT, details)\n    case 500:\n    case 502:\n    case 503:\n      return new NetworkError(message || 'Server Error', ERROR_CODES.NETWORK_SERVER_ERROR, details)\n    default:\n      return new LuminarError(message || 'Unknown Error', ERROR_CODES.UNKNOWN_ERROR, status, details)\n  }\n}\n", "/**\n * Core API types and interfaces for the Luminar API Client\n */\n\nexport interface RequestConfig {\n  headers?: Record<string, string>;\n  timeout?: number;\n  retries?: number;\n  retryDelay?: number;\n  signal?: AbortSignal;\n}\n\nexport interface ApiResponse<T = any> {\n  data: T;\n  status: number;\n  statusText: string;\n  message?: string;\n  errors?: ApiError[];\n  metadata?: ApiMetadata;\n}\n\nexport interface ApiError {\n  field?: string;\n  message: string;\n  code: string;\n  details?: Record<string, any>;\n}\n\nexport interface ApiMetadata {\n  page?: number;\n  limit?: number;\n  total?: number;\n  hasNext?: boolean;\n  hasPrevious?: boolean;\n  timestamp?: string;\n}\n\nexport interface RequestInterceptor {\n  onRequest?: (config: RequestConfig & { url: string; method: string; data?: any }) => RequestConfig & { url: string; method: string; data?: any } | Promise<RequestConfig & { url: string; method: string; data?: any }>;\n  onRequestError?: (error: Error) => Error | Promise<Error>;\n}\n\nexport interface ResponseInterceptor {\n  onResponse?: <T>(response: ApiResponse<T>) => ApiResponse<T> | Promise<ApiResponse<T>>;\n  onResponseError?: (error: ApiError | Error) => ApiError | Error | Promise<ApiError | Error>;\n}\n\nexport interface LuminarAPIClientConfig {\n  baseURL: string;\n  timeout?: number;\n  defaultHeaders?: Record<string, string>;\n  retries?: number;\n  retryDelay?: number;\n  enableLogging?: boolean;\n}\n\nexport interface LuminarAPIClient {\n  // HTTP methods\n  get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>;\n  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;\n  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;\n  patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>>;\n  delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>>;\n  \n  // Interceptors\n  addRequestInterceptor(interceptor: RequestInterceptor): number;\n  addResponseInterceptor(interceptor: ResponseInterceptor): number;\n  removeRequestInterceptor(id: number): void;\n  removeResponseInterceptor(id: number): void;\n  \n  // Configuration\n  setBaseURL(url: string): void;\n  setDefaultHeaders(headers: Record<string, string>): void;\n  getConfig(): LuminarAPIClientConfig;\n}\n\nexport enum ErrorType {\n  NETWORK_ERROR = 'NETWORK_ERROR',\n  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',\n  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',\n  VALIDATION_ERROR = 'VALIDATION_ERROR',\n  SERVER_ERROR = 'SERVER_ERROR',\n  CLIENT_ERROR = 'CLIENT_ERROR',\n  TIMEOUT_ERROR = 'TIMEOUT_ERROR',\n  UNKNOWN_ERROR = 'UNKNOWN_ERROR'\n}\n\nexport interface ErrorContext {\n  url?: string;\n  method?: string;\n  component?: string;\n  action?: string;\n  user?: any;\n  timestamp: Date;\n  requestId?: string;\n}\n\nexport interface RetryConfig {\n  maxAttempts: number;\n  backoffStrategy: 'linear' | 'exponential';\n  baseDelay: number;\n  maxDelay: number;\n  retryCondition?: (error: Error) => boolean;\n}", "/**\n * API Error Handler - Comprehensive error handling and categorization\n */\n\nimport { AxiosError } from 'axios';\nimport { ApiError, ErrorType, ErrorContext } from './types';\n\nexport class ApiErrorHandler {\n  handleAxiosError(error: AxiosError): ApiError {\n    const context: ErrorContext = {\n      url: error.config?.url,\n      method: error.config?.method?.toUpperCase(),\n      timestamp: new Date(),\n      requestId: this.generateRequestId()\n    };\n\n    if (error.response) {\n      // Server responded with error status\n      return this.handleResponseError(error, context);\n    } else if (error.request) {\n      // Request was made but no response received\n      return this.handleNetworkError(error, context);\n    } else {\n      // Something else happened\n      return this.handleUnknownError(error, context);\n    }\n  }\n\n  private handleResponseError(error: AxiosError, context: ErrorContext): ApiError {\n    const status = error.response!.status;\n    const responseData = error.response!.data as any;\n\n    let errorType: ErrorType;\n    let message: string;\n    let code: string;\n\n    if (status === 401) {\n      errorType = ErrorType.AUTHENTICATION_ERROR;\n      message = 'Authentication required. Please log in again.';\n      code = 'AUTH_REQUIRED';\n    } else if (status === 403) {\n      errorType = ErrorType.AUTHORIZATION_ERROR;\n      message = 'You do not have permission to perform this action.';\n      code = 'INSUFFICIENT_PERMISSIONS';\n    } else if (status >= 400 && status < 500) {\n      errorType = ErrorType.CLIENT_ERROR;\n      message = responseData?.message || 'Invalid request. Please check your input.';\n      code = responseData?.code || 'CLIENT_ERROR';\n    } else if (status >= 500) {\n      errorType = ErrorType.SERVER_ERROR;\n      message = 'Server error occurred. Please try again later.';\n      code = 'SERVER_ERROR';\n    } else {\n      errorType = ErrorType.UNKNOWN_ERROR;\n      message = 'An unexpected error occurred.';\n      code = 'UNKNOWN_ERROR';\n    }\n\n    return {\n      message,\n      code,\n      field: responseData?.field,\n      details: {\n        type: errorType,\n        status,\n        statusText: error.response!.statusText,\n        context,\n        originalMessage: error.message,\n        responseData: responseData\n      }\n    };\n  }\n\n  private handleNetworkError(error: AxiosError, context: ErrorContext): ApiError {\n    let message: string;\n    let code: string;\n\n    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n      message = 'Request timed out. Please check your connection and try again.';\n      code = 'TIMEOUT_ERROR';\n    } else if (error.code === 'ERR_NETWORK' || error.message.includes('Network Error')) {\n      message = 'Network error. Please check your internet connection.';\n      code = 'NETWORK_ERROR';\n    } else {\n      message = 'Unable to connect to the server. Please try again later.';\n      code = 'CONNECTION_ERROR';\n    }\n\n    return {\n      message,\n      code,\n      details: {\n        type: ErrorType.NETWORK_ERROR,\n        context,\n        originalMessage: error.message,\n        errorCode: error.code\n      }\n    };\n  }\n\n  private handleUnknownError(error: AxiosError, context: ErrorContext): ApiError {\n    return {\n      message: 'An unexpected error occurred. Please try again.',\n      code: 'UNKNOWN_ERROR',\n      details: {\n        type: ErrorType.UNKNOWN_ERROR,\n        context,\n        originalMessage: error.message\n      }\n    };\n  }\n\n  categorizeError(error: Error): ErrorType {\n    if (error instanceof AxiosError) {\n      if (error.response) {\n        const status = error.response.status;\n        if (status === 401) return ErrorType.AUTHENTICATION_ERROR;\n        if (status === 403) return ErrorType.AUTHORIZATION_ERROR;\n        if (status >= 400 && status < 500) return ErrorType.CLIENT_ERROR;\n        if (status >= 500) return ErrorType.SERVER_ERROR;\n      } else if (error.request) {\n        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n          return ErrorType.TIMEOUT_ERROR;\n        }\n        return ErrorType.NETWORK_ERROR;\n      }\n    }\n    return ErrorType.UNKNOWN_ERROR;\n  }\n\n  formatUserMessage(error: ApiError): string {\n    // Return user-friendly message\n    return error.message;\n  }\n\n  shouldRetry(error: ApiError): boolean {\n    const errorType = error.details?.type;\n    \n    // Retry on network errors and server errors, but not on client errors\n    return errorType === ErrorType.NETWORK_ERROR || \n           errorType === ErrorType.SERVER_ERROR ||\n           errorType === ErrorType.TIMEOUT_ERROR;\n  }\n\n  logError(error: ApiError, context?: Partial<ErrorContext>): void {\n    const logContext = {\n      ...error.details?.context,\n      ...context,\n      timestamp: new Date().toISOString()\n    };\n\n    console.error('[API Error]', {\n      message: error.message,\n      code: error.code,\n      field: error.field,\n      context: logContext,\n      details: error.details\n    });\n\n    // In production, you might want to send this to an error tracking service\n    // like Sentry, LogRocket, or similar\n    if (typeof window !== 'undefined' && (window as any).__SENTRY__) {\n      // Example Sentry integration\n      (window as any).__SENTRY__.captureException(new Error(error.message), {\n        tags: {\n          errorType: error.details?.type,\n          errorCode: error.code\n        },\n        extra: {\n          context: logContext,\n          details: error.details\n        }\n      });\n    }\n  }\n\n  private generateRequestId(): string {\n    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n}\n\n// Utility functions for error handling\nexport function isApiError(error: any): error is ApiError {\n  return error && typeof error === 'object' && 'message' in error && 'code' in error;\n}\n\nexport function createApiError(\n  message: string,\n  code: string,\n  type: ErrorType = ErrorType.UNKNOWN_ERROR,\n  field?: string,\n  details?: any\n): ApiError {\n  return {\n    message,\n    code,\n    field,\n    details: {\n      type,\n      timestamp: new Date(),\n      ...details\n    }\n  };\n}", "/**\n * Luminar API Client - Unified HTTP client with interceptors, retry logic, and error handling\n */\n\nimport axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';\nimport {\n  LuminarAPIClient,\n  LuminarAPIClientConfig,\n  ApiResponse,\n  ApiError,\n  RequestConfig,\n  RequestInterceptor,\n  ResponseInterceptor,\n  ErrorType,\n  ErrorContext,\n  RetryConfig\n} from './types';\nimport { ApiErrorHandler } from './error-handler';\n\nexport class LuminarAPIClientImpl implements LuminarAPIClient {\n  private axiosInstance: AxiosInstance;\n  private config: LuminarAPIClientConfig;\n  private requestInterceptors: Map<number, RequestInterceptor> = new Map();\n  private responseInterceptors: Map<number, ResponseInterceptor> = new Map();\n  private interceptorIdCounter = 0;\n  private errorHandler: ApiErrorHandler;\n\n  constructor(config: LuminarAPIClientConfig) {\n    this.config = { ...config };\n    this.errorHandler = new ApiErrorHandler();\n    \n    this.axiosInstance = axios.create({\n      baseURL: config.baseURL,\n      timeout: config.timeout || 10000,\n      headers: {\n        'Content-Type': 'application/json',\n        ...config.defaultHeaders\n      }\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors(): void {\n    // Request interceptor\n    this.axiosInstance.interceptors.request.use(\n      async (config) => {\n        // Apply all registered request interceptors\n        let modifiedConfig = { ...config };\n        \n        for (const interceptor of this.requestInterceptors.values()) {\n          if (interceptor.onRequest) {\n            const requestData: RequestConfig & { url: string; method: string; data?: any } = {\n              url: modifiedConfig.url || '',\n              method: modifiedConfig.method || 'get',\n              data: modifiedConfig.data,\n              headers: (modifiedConfig.headers as Record<string, string>) || {},\n              timeout: modifiedConfig.timeout,\n              retries: (modifiedConfig as any).retries,\n              retryDelay: (modifiedConfig as any).retryDelay,\n              signal: modifiedConfig.signal as AbortSignal | undefined\n            };\n            \n            const result = await interceptor.onRequest(requestData);\n            modifiedConfig = {\n              ...modifiedConfig,\n              url: result.url,\n              method: result.method as any,\n              data: result.data,\n              headers: result.headers as any,\n              timeout: result.timeout,\n              signal: result.signal as any\n            };\n            (modifiedConfig as any).retries = result.retries;\n            (modifiedConfig as any).retryDelay = result.retryDelay;\n          }\n        }\n\n        if (this.config.enableLogging) {\n          console.log(`[API Request] ${modifiedConfig.method?.toUpperCase()} ${modifiedConfig.url}`);\n        }\n\n        return modifiedConfig;\n      },\n      async (error) => {\n        // Apply request error interceptors\n        let modifiedError = error;\n        \n        for (const interceptor of this.requestInterceptors.values()) {\n          if (interceptor.onRequestError) {\n            modifiedError = await interceptor.onRequestError(modifiedError);\n          }\n        }\n\n        return Promise.reject(modifiedError);\n      }\n    );\n\n    // Response interceptor\n    this.axiosInstance.interceptors.response.use(\n      async (response) => {\n        const apiResponse = this.transformResponse(response);\n        \n        // Apply all registered response interceptors\n        let modifiedResponse = apiResponse;\n        \n        for (const interceptor of this.responseInterceptors.values()) {\n          if (interceptor.onResponse) {\n            modifiedResponse = await interceptor.onResponse(modifiedResponse);\n          }\n        }\n\n        if (this.config.enableLogging) {\n          console.log(`[API Response] ${response.status} ${response.config.url}`);\n        }\n\n        return response;\n      },\n      async (error) => {\n        const apiError = this.errorHandler.handleAxiosError(error);\n        \n        // Apply response error interceptors\n        let modifiedError: ApiError | Error = apiError;\n        \n        for (const interceptor of this.responseInterceptors.values()) {\n          if (interceptor.onResponseError) {\n            modifiedError = await interceptor.onResponseError(modifiedError);\n          }\n        }\n\n        if (this.config.enableLogging) {\n          console.error(`[API Error] ${error.response?.status || 'Network'} ${error.config?.url}`, modifiedError);\n        }\n\n        return Promise.reject(modifiedError);\n      }\n    );\n  }\n\n  private transformResponse<T>(response: AxiosResponse): ApiResponse<T> {\n    return {\n      data: response.data,\n      status: response.status,\n      statusText: response.statusText,\n      message: response.data?.message,\n      errors: response.data?.errors,\n      metadata: response.data?.metadata\n    };\n  }\n\n  private async executeWithRetry<T>(\n    requestFn: () => Promise<AxiosResponse>,\n    config?: RequestConfig\n  ): Promise<ApiResponse<T>> {\n    const retryConfig: RetryConfig = {\n      maxAttempts: config?.retries || this.config.retries || 3,\n      backoffStrategy: 'exponential',\n      baseDelay: config?.retryDelay || this.config.retryDelay || 1000,\n      maxDelay: 10000,\n      retryCondition: (error: Error) => {\n        if (error instanceof AxiosError) {\n          // Retry on network errors or 5xx server errors\n          return !error.response || (error.response.status >= 500 && error.response.status < 600);\n        }\n        return false;\n      }\n    };\n\n    let lastError: Error;\n    \n    for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {\n      try {\n        const response = await requestFn();\n        return this.transformResponse<T>(response);\n      } catch (error) {\n        lastError = error as Error;\n        \n        if (attempt === retryConfig.maxAttempts || !retryConfig.retryCondition?.(lastError)) {\n          throw lastError;\n        }\n\n        // Calculate delay for next retry\n        const delay = retryConfig.backoffStrategy === 'exponential'\n          ? Math.min(retryConfig.baseDelay * Math.pow(2, attempt - 1), retryConfig.maxDelay)\n          : retryConfig.baseDelay;\n\n        if (this.config.enableLogging) {\n          console.log(`[API Retry] Attempt ${attempt + 1}/${retryConfig.maxAttempts} in ${delay}ms`);\n        }\n\n        await new Promise(resolve => setTimeout(resolve, delay));\n      }\n    }\n\n    throw lastError!;\n  }\n\n  // HTTP Methods\n  async get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {\n    return this.executeWithRetry<T>(\n      () => this.axiosInstance.get(url, this.mergeConfig(config)),\n      config\n    );\n  }\n\n  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {\n    return this.executeWithRetry<T>(\n      () => this.axiosInstance.post(url, data, this.mergeConfig(config)),\n      config\n    );\n  }\n\n  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {\n    return this.executeWithRetry<T>(\n      () => this.axiosInstance.put(url, data, this.mergeConfig(config)),\n      config\n    );\n  }\n\n  async patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {\n    return this.executeWithRetry<T>(\n      () => this.axiosInstance.patch(url, data, this.mergeConfig(config)),\n      config\n    );\n  }\n\n  async delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {\n    return this.executeWithRetry<T>(\n      () => this.axiosInstance.delete(url, this.mergeConfig(config)),\n      config\n    );\n  }\n\n  // Interceptor Management\n  addRequestInterceptor(interceptor: RequestInterceptor): number {\n    const id = ++this.interceptorIdCounter;\n    this.requestInterceptors.set(id, interceptor);\n    return id;\n  }\n\n  addResponseInterceptor(interceptor: ResponseInterceptor): number {\n    const id = ++this.interceptorIdCounter;\n    this.responseInterceptors.set(id, interceptor);\n    return id;\n  }\n\n  removeRequestInterceptor(id: number): void {\n    this.requestInterceptors.delete(id);\n  }\n\n  removeResponseInterceptor(id: number): void {\n    this.responseInterceptors.delete(id);\n  }\n\n  // Configuration Management\n  setBaseURL(url: string): void {\n    this.config.baseURL = url;\n    this.axiosInstance.defaults.baseURL = url;\n  }\n\n  setDefaultHeaders(headers: Record<string, string>): void {\n    this.config.defaultHeaders = { ...this.config.defaultHeaders, ...headers };\n    Object.assign(this.axiosInstance.defaults.headers, headers);\n  }\n\n  getConfig(): LuminarAPIClientConfig {\n    return { ...this.config };\n  }\n\n  private mergeConfig(config?: RequestConfig): AxiosRequestConfig {\n    if (!config) return {};\n    \n    return {\n      headers: config.headers,\n      timeout: config.timeout,\n      signal: config.signal\n    };\n  }\n}\n\n// Factory function to create API client instance\nexport function createLuminarAPIClient(config: LuminarAPIClientConfig): LuminarAPIClient {\n  return new LuminarAPIClientImpl(config);\n}\n\n// Default client instance (can be configured later)\nlet defaultClient: LuminarAPIClient | null = null;\n\nexport function getDefaultAPIClient(): LuminarAPIClient {\n  if (!defaultClient) {\n    throw new Error('Default API client not initialized. Call initializeDefaultAPIClient() first.');\n  }\n  return defaultClient;\n}\n\nexport function initializeDefaultAPIClient(config: LuminarAPIClientConfig): void {\n  defaultClient = createLuminarAPIClient(config);\n}\n\nexport function setDefaultAPIClient(client: LuminarAPIClient): void {\n  defaultClient = client;\n}", "/**\n * Authentication Interceptor - <PERSON>les token attachment and refresh\n */\n\nimport { RequestInterceptor, ResponseInterceptor } from './types';\n\nexport interface AuthTokenManager {\n  getAccessToken(): string | null;\n  getRefreshToken(): string | null;\n  setTokens(accessToken: string, refreshToken?: string): void;\n  clearTokens(): void;\n  isTokenExpired(token: string): boolean;\n  refreshAccessToken(): Promise<{ accessToken: string; refreshToken?: string }>;\n}\n\nexport interface AuthInterceptorConfig {\n  tokenManager: AuthTokenManager;\n  authHeaderName?: string;\n  authHeaderPrefix?: string;\n  excludeUrls?: string[];\n  onAuthError?: (error: any) => void;\n  onTokenRefresh?: (tokens: { accessToken: string; refreshToken?: string }) => void;\n}\n\nexport class AuthenticationInterceptor {\n  private config: AuthInterceptorConfig;\n  private refreshPromise: Promise<any> | null = null;\n  private requestQueue: Array<{\n    resolve: (token: string) => void;\n    reject: (error: any) => void;\n  }> = [];\n\n  constructor(config: AuthInterceptorConfig) {\n    this.config = {\n      authHeaderName: 'Authorization',\n      authHeaderPrefix: 'Bearer',\n      excludeUrls: [],\n      ...config\n    };\n  }\n\n  getRequestInterceptor(): RequestInterceptor {\n    return {\n      onRequest: async (config) => {\n        const { url, headers = {} } = config;\n\n        // Skip authentication for excluded URLs\n        if (this.shouldExcludeUrl(url)) {\n          return config;\n        }\n\n        try {\n          const token = await this.getValidToken();\n          if (token) {\n            headers[this.config.authHeaderName!] = `${this.config.authHeaderPrefix} ${token}`;\n          }\n        } catch (error) {\n          console.error('[Auth Interceptor] Failed to get valid token:', error);\n          this.config.onAuthError?.(error);\n        }\n\n        return {\n          ...config,\n          headers\n        };\n      },\n      onRequestError: async (error) => {\n        return error;\n      }\n    };\n  }\n\n  getResponseInterceptor(): ResponseInterceptor {\n    return {\n      onResponse: async (response) => {\n        return response;\n      },\n      onResponseError: async (error) => {\n        const originalRequest = (error as any).config;\n\n        // Handle 401 errors (token expired)\n        if (this.isAuthError(error) && originalRequest && !originalRequest._retry) {\n          originalRequest._retry = true;\n\n          try {\n            const token = await this.refreshToken();\n            if (token) {\n              originalRequest.headers[this.config.authHeaderName!] = `${this.config.authHeaderPrefix} ${token}`;\n              // Note: In a real implementation, you'd want to retry the original request\n              // This would require access to the axios instance\n            }\n          } catch (refreshError) {\n            console.error('[Auth Interceptor] Token refresh failed:', refreshError);\n            this.config.onAuthError?.(refreshError);\n            this.config.tokenManager.clearTokens();\n          }\n        }\n\n        return Promise.reject(error);\n      }\n    };\n  }\n\n  private async getValidToken(): Promise<string | null> {\n    const accessToken = this.config.tokenManager.getAccessToken();\n    \n    if (!accessToken) {\n      return null;\n    }\n\n    // Check if token is expired\n    if (this.config.tokenManager.isTokenExpired(accessToken)) {\n      try {\n        return await this.refreshToken();\n      } catch (error) {\n        console.error('[Auth Interceptor] Token refresh failed:', error);\n        return null;\n      }\n    }\n\n    return accessToken;\n  }\n\n  private async refreshToken(): Promise<string> {\n    // If refresh is already in progress, wait for it\n    if (this.refreshPromise) {\n      return this.refreshPromise;\n    }\n\n    // Start refresh process\n    this.refreshPromise = this.performTokenRefresh();\n\n    try {\n      const result = await this.refreshPromise;\n      this.refreshPromise = null;\n      \n      // Process queued requests\n      this.processRequestQueue(result);\n      \n      return result;\n    } catch (error) {\n      this.refreshPromise = null;\n      \n      // Reject queued requests\n      this.rejectRequestQueue(error);\n      \n      throw error;\n    }\n  }\n\n  private async performTokenRefresh(): Promise<string> {\n    const refreshToken = this.config.tokenManager.getRefreshToken();\n    \n    if (!refreshToken) {\n      throw new Error('No refresh token available');\n    }\n\n    try {\n      const tokens = await this.config.tokenManager.refreshAccessToken();\n      \n      // Update stored tokens\n      this.config.tokenManager.setTokens(tokens.accessToken, tokens.refreshToken);\n      \n      // Notify about token refresh\n      this.config.onTokenRefresh?.(tokens);\n      \n      return tokens.accessToken;\n    } catch (error) {\n      // Clear tokens on refresh failure\n      this.config.tokenManager.clearTokens();\n      throw error;\n    }\n  }\n\n  private processRequestQueue(token: string): void {\n    this.requestQueue.forEach(({ resolve }) => resolve(token));\n    this.requestQueue = [];\n  }\n\n  private rejectRequestQueue(error: any): void {\n    this.requestQueue.forEach(({ reject }) => reject(error));\n    this.requestQueue = [];\n  }\n\n  private shouldExcludeUrl(url: string): boolean {\n    return this.config.excludeUrls?.some(excludeUrl => \n      url.includes(excludeUrl)\n    ) || false;\n  }\n\n  private isAuthError(error: any): boolean {\n    return error?.details?.status === 401 || \n           error?.details?.type === 'AUTHENTICATION_ERROR';\n  }\n}\n\n// Default token manager implementation using localStorage\nexport class LocalStorageTokenManager implements AuthTokenManager {\n  private accessTokenKey = 'luminar_access_token';\n  private refreshTokenKey = 'luminar_refresh_token';\n\n  getAccessToken(): string | null {\n    if (typeof window === 'undefined') return null;\n    return localStorage.getItem(this.accessTokenKey);\n  }\n\n  getRefreshToken(): string | null {\n    if (typeof window === 'undefined') return null;\n    return localStorage.getItem(this.refreshTokenKey);\n  }\n\n  setTokens(accessToken: string, refreshToken?: string): void {\n    if (typeof window === 'undefined') return;\n    \n    localStorage.setItem(this.accessTokenKey, accessToken);\n    if (refreshToken) {\n      localStorage.setItem(this.refreshTokenKey, refreshToken);\n    }\n  }\n\n  clearTokens(): void {\n    if (typeof window === 'undefined') return;\n    \n    localStorage.removeItem(this.accessTokenKey);\n    localStorage.removeItem(this.refreshTokenKey);\n  }\n\n  isTokenExpired(token: string): boolean {\n    try {\n      // Decode JWT token to check expiration\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      \n      return payload.exp < currentTime;\n    } catch (error) {\n      // If we can't decode the token, consider it expired\n      return true;\n    }\n  }\n\n  async refreshAccessToken(): Promise<{ accessToken: string; refreshToken?: string }> {\n    const refreshToken = this.getRefreshToken();\n    \n    if (!refreshToken) {\n      throw new Error('No refresh token available');\n    }\n\n    // This would typically make a request to your auth endpoint\n    // For now, we'll throw an error to indicate this needs to be implemented\n    throw new Error('Token refresh endpoint not configured. Please implement refreshAccessToken method.');\n  }\n}\n\n// Factory function to create auth interceptor\nexport function createAuthInterceptor(config: Partial<AuthInterceptorConfig> = {}): AuthenticationInterceptor {\n  const defaultConfig: AuthInterceptorConfig = {\n    tokenManager: new LocalStorageTokenManager(),\n    ...config\n  };\n\n  return new AuthenticationInterceptor(defaultConfig);\n}", "/**\n * API Module - Unified API client and utilities\n */\n\nimport { \n  LuminarAP<PERSON>lient, \n  LuminarAPIClientConfig \n} from './types';\nimport { createLuminarAPIClient } from './client';\nimport { \n  AuthTokenManager, \n  createAuthInterceptor \n} from './auth-interceptor';\n\n// Core API client\nexport {\n  LuminarAPIClientImpl,\n  createLuminarAPIClient,\n  getDefaultAPIClient,\n  initializeDefaultAPIClient,\n  setDefaultAPIClient\n} from './client';\n\n// Types and interfaces\nexport type {\n  LuminarAPIClient,\n  LuminarAPIClientConfig,\n  ApiResponse,\n  ApiError,\n  ApiMetadata,\n  RequestConfig,\n  RequestInterceptor,\n  ResponseInterceptor,\n  ErrorContext,\n  RetryConfig\n} from './types';\n\nexport { ErrorType } from './types';\n\n// Error handling\nexport {\n  ApiErrorHandler,\n  isApiError,\n  createApiError\n} from './error-handler';\n\n// Authentication\nexport {\n  AuthenticationInterceptor,\n  LocalStorageTokenManager,\n  createAuthInterceptor\n} from './auth-interceptor';\n\nexport type {\n  AuthTokenManager,\n  AuthInterceptorConfig\n} from './auth-interceptor';\n\n// Utility function to create a configured API client with auth\nexport function createConfiguredAPIClient(config: {\n  baseURL: string;\n  tokenManager?: AuthTokenManager;\n  enableAuth?: boolean;\n  enableLogging?: boolean;\n  timeout?: number;\n  retries?: number;\n}): LuminarAPIClient {\n  const client = createLuminarAPIClient({\n    baseURL: config.baseURL,\n    timeout: config.timeout || 10000,\n    retries: config.retries || 3,\n    enableLogging: config.enableLogging || false\n  });\n\n  // Add authentication interceptor if enabled\n  if (config.enableAuth !== false) {\n    const authInterceptor = createAuthInterceptor({\n      tokenManager: config.tokenManager,\n      onAuthError: (error: any) => {\n        console.error('[API Auth Error]', error);\n        // You might want to redirect to login or show a notification\n      },\n      onTokenRefresh: (tokens: { accessToken: string; refreshToken?: string }) => {\n        console.log('[API] Tokens refreshed successfully');\n      }\n    });\n\n    client.addRequestInterceptor(authInterceptor.getRequestInterceptor());\n    client.addResponseInterceptor(authInterceptor.getResponseInterceptor());\n  }\n\n  return client;\n}", "/**\n * Users Service - User management operations\n */\n\nimport { LuminarAPIClient } from '../api/types';\nimport {\n  UsersService,\n  User,\n  Role,\n  Permission,\n  CreateUserDto,\n  UpdateUserDto,\n  UserQueryParams\n} from './types';\nimport { ApiResponse } from '../api/types';\n\nexport class UsersServiceImpl implements UsersService {\n  private apiClient: LuminarAPIClient;\n  private basePath = '/api/users';\n\n  constructor(apiClient: LuminarAPIClient) {\n    this.apiClient = apiClient;\n  }\n\n  async getUsers(params?: UserQueryParams): Promise<ApiResponse<User[]>> {\n    const queryParams = new URLSearchParams();\n    \n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined && value !== null) {\n          if (Array.isArray(value)) {\n            value.forEach(v => queryParams.append(key, v.toString()));\n          } else {\n            queryParams.append(key, value.toString());\n          }\n        }\n      });\n    }\n\n    const url = queryParams.toString() \n      ? `${this.basePath}?${queryParams.toString()}`\n      : this.basePath;\n\n    return this.apiClient.get<User[]>(url);\n  }\n\n  async getUser(id: string): Promise<ApiResponse<User>> {\n    if (!id) {\n      throw new Error('User ID is required');\n    }\n\n    return this.apiClient.get<User>(`${this.basePath}/${id}`);\n  }\n\n  async createUser(user: CreateUserDto): Promise<ApiResponse<User>> {\n    this.validateCreateUserDto(user);\n    return this.apiClient.post<User>(this.basePath, user);\n  }\n\n  async updateUser(id: string, user: UpdateUserDto): Promise<ApiResponse<User>> {\n    if (!id) {\n      throw new Error('User ID is required');\n    }\n\n    return this.apiClient.put<User>(`${this.basePath}/${id}`, user);\n  }\n\n  async deleteUser(id: string): Promise<ApiResponse<void>> {\n    if (!id) {\n      throw new Error('User ID is required');\n    }\n\n    return this.apiClient.delete<void>(`${this.basePath}/${id}`);\n  }\n\n  async getUserRoles(id: string): Promise<ApiResponse<Role[]>> {\n    if (!id) {\n      throw new Error('User ID is required');\n    }\n\n    return this.apiClient.get<Role[]>(`${this.basePath}/${id}/roles`);\n  }\n\n  async updateUserRoles(id: string, roleIds: string[]): Promise<ApiResponse<User>> {\n    if (!id) {\n      throw new Error('User ID is required');\n    }\n\n    if (!Array.isArray(roleIds)) {\n      throw new Error('Role IDs must be an array');\n    }\n\n    return this.apiClient.put<User>(`${this.basePath}/${id}/roles`, { roleIds });\n  }\n\n  async getUserPermissions(id: string): Promise<ApiResponse<Permission[]>> {\n    if (!id) {\n      throw new Error('User ID is required');\n    }\n\n    return this.apiClient.get<Permission[]>(`${this.basePath}/${id}/permissions`);\n  }\n\n  // Additional utility methods\n  async searchUsers(query: string, limit = 10): Promise<ApiResponse<User[]>> {\n    if (!query || query.trim().length === 0) {\n      throw new Error('Search query is required');\n    }\n\n    return this.getUsers({\n      search: query.trim(),\n      limit\n    });\n  }\n\n  async getUsersByRole(roleId: string): Promise<ApiResponse<User[]>> {\n    if (!roleId) {\n      throw new Error('Role ID is required');\n    }\n\n    return this.getUsers({\n      role: roleId\n    });\n  }\n\n  async getActiveUsers(): Promise<ApiResponse<User[]>> {\n    return this.getUsers({\n      isActive: true\n    });\n  }\n\n  async deactivateUser(id: string): Promise<ApiResponse<User>> {\n    return this.updateUser(id, { isActive: false });\n  }\n\n  async activateUser(id: string): Promise<ApiResponse<User>> {\n    return this.updateUser(id, { isActive: true });\n  }\n\n  private validateCreateUserDto(user: CreateUserDto): void {\n    if (!user.email || !this.isValidEmail(user.email)) {\n      throw new Error('Valid email is required');\n    }\n\n    if (!user.name || user.name.trim().length === 0) {\n      throw new Error('Name is required');\n    }\n\n    if (!user.password || user.password.length < 8) {\n      throw new Error('Password must be at least 8 characters long');\n    }\n\n    if (user.roleIds && !Array.isArray(user.roleIds)) {\n      throw new Error('Role IDs must be an array');\n    }\n  }\n\n  private isValidEmail(email: string): boolean {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  }\n}\n\n// Factory function to create users service\nexport function createUsersService(apiClient: LuminarAPIClient): UsersService {\n  return new UsersServiceImpl(apiClient);\n}", "/**\n * Domain Service Types and Interfaces\n */\n\nimport { ApiResponse } from '../api/types';\n\n// Common query parameters\nexport interface BaseQueryParams {\n  page?: number;\n  limit?: number;\n  sort?: string;\n  order?: 'asc' | 'desc';\n  search?: string;\n}\n\n// User-related types\nexport interface User {\n  id: string;\n  email: string;\n  name: string;\n  firstName?: string;\n  lastName?: string;\n  avatar?: string;\n  roles: Role[];\n  permissions: Permission[];\n  isActive: boolean;\n  lastLoginAt?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Role {\n  id: string;\n  name: string;\n  description?: string;\n  permissions: Permission[];\n  isSystem: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Permission {\n  id: string;\n  name: string;\n  resource: string;\n  action: string;\n  description?: string;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface CreateUserDto {\n  email: string;\n  name: string;\n  firstName?: string;\n  lastName?: string;\n  password: string;\n  roleIds?: string[];\n  isActive?: boolean;\n}\n\nexport interface UpdateUserDto {\n  name?: string;\n  firstName?: string;\n  lastName?: string;\n  avatar?: string;\n  roleIds?: string[];\n  isActive?: boolean;\n}\n\nexport interface UserQueryParams extends BaseQueryParams {\n  role?: string;\n  isActive?: boolean;\n  createdAfter?: string;\n  createdBefore?: string;\n}\n\n// Training-related types\nexport interface Training {\n  id: string;\n  title: string;\n  description: string;\n  content?: string;\n  category: TrainingCategory;\n  tags: string[];\n  duration: number; // in minutes\n  difficulty: TrainingDifficulty;\n  instructor: User;\n  participants: User[];\n  maxParticipants?: number;\n  status: TrainingStatus;\n  startDate?: Date;\n  endDate?: Date;\n  isPublic: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface TrainingCategory {\n  id: string;\n  name: string;\n  description?: string;\n  color?: string;\n  icon?: string;\n  parentId?: string;\n  children?: TrainingCategory[];\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport enum TrainingDifficulty {\n  BEGINNER = 'beginner',\n  INTERMEDIATE = 'intermediate',\n  ADVANCED = 'advanced',\n  EXPERT = 'expert'\n}\n\nexport enum TrainingStatus {\n  DRAFT = 'draft',\n  PUBLISHED = 'published',\n  SCHEDULED = 'scheduled',\n  IN_PROGRESS = 'in_progress',\n  COMPLETED = 'completed',\n  CANCELLED = 'cancelled',\n  ARCHIVED = 'archived'\n}\n\nexport interface CreateTrainingDto {\n  title: string;\n  description: string;\n  content?: string;\n  categoryId: string;\n  tags?: string[];\n  duration: number;\n  difficulty: TrainingDifficulty;\n  instructorId: string;\n  maxParticipants?: number;\n  startDate?: Date;\n  endDate?: Date;\n  isPublic?: boolean;\n}\n\nexport interface UpdateTrainingDto {\n  title?: string;\n  description?: string;\n  content?: string;\n  categoryId?: string;\n  tags?: string[];\n  duration?: number;\n  difficulty?: TrainingDifficulty;\n  instructorId?: string;\n  maxParticipants?: number;\n  startDate?: Date;\n  endDate?: Date;\n  isPublic?: boolean;\n  status?: TrainingStatus;\n}\n\nexport interface TrainingQueryParams extends BaseQueryParams {\n  categoryId?: string;\n  instructorId?: string;\n  difficulty?: TrainingDifficulty;\n  status?: TrainingStatus;\n  isPublic?: boolean;\n  startAfter?: string;\n  startBefore?: string;\n  tags?: string[];\n}\n\n// Vendor-related types\nexport interface Vendor {\n  id: string;\n  name: string;\n  description?: string;\n  website?: string;\n  email?: string;\n  phone?: string;\n  address?: Address;\n  contactPerson?: ContactPerson;\n  services: VendorService[];\n  rating?: number;\n  isActive: boolean;\n  isVerified: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Address {\n  street: string;\n  city: string;\n  state: string;\n  country: string;\n  postalCode: string;\n}\n\nexport interface ContactPerson {\n  name: string;\n  email: string;\n  phone?: string;\n  position?: string;\n}\n\nexport interface VendorService {\n  id: string;\n  name: string;\n  description?: string;\n  category: string;\n  price?: number;\n  currency?: string;\n  isActive: boolean;\n}\n\nexport interface CreateVendorDto {\n  name: string;\n  description?: string;\n  website?: string;\n  email?: string;\n  phone?: string;\n  address?: Address;\n  contactPerson?: ContactPerson;\n  services?: Omit<VendorService, 'id'>[];\n  isActive?: boolean;\n}\n\nexport interface UpdateVendorDto {\n  name?: string;\n  description?: string;\n  website?: string;\n  email?: string;\n  phone?: string;\n  address?: Address;\n  contactPerson?: ContactPerson;\n  services?: VendorService[];\n  rating?: number;\n  isActive?: boolean;\n  isVerified?: boolean;\n}\n\nexport interface VendorQueryParams extends BaseQueryParams {\n  category?: string;\n  isActive?: boolean;\n  isVerified?: boolean;\n  minRating?: number;\n  city?: string;\n  country?: string;\n}\n\n// Email-related types\nexport interface Email {\n  id: string;\n  from: string;\n  to: string[];\n  cc?: string[];\n  bcc?: string[];\n  subject: string;\n  body: string;\n  isHtml: boolean;\n  attachments?: EmailAttachment[];\n  status: EmailStatus;\n  sentAt?: Date;\n  deliveredAt?: Date;\n  openedAt?: Date;\n  clickedAt?: Date;\n  errorMessage?: string;\n  templateId?: string;\n  templateData?: Record<string, any>;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface EmailAttachment {\n  id: string;\n  filename: string;\n  contentType: string;\n  size: number;\n  url?: string;\n  data?: string; // base64 encoded\n}\n\nexport enum EmailStatus {\n  DRAFT = 'draft',\n  QUEUED = 'queued',\n  SENDING = 'sending',\n  SENT = 'sent',\n  DELIVERED = 'delivered',\n  OPENED = 'opened',\n  CLICKED = 'clicked',\n  BOUNCED = 'bounced',\n  FAILED = 'failed'\n}\n\nexport interface SendEmailDto {\n  to: string[];\n  cc?: string[];\n  bcc?: string[];\n  subject: string;\n  body: string;\n  isHtml?: boolean;\n  attachments?: Omit<EmailAttachment, 'id'>[];\n  templateId?: string;\n  templateData?: Record<string, any>;\n  scheduledAt?: Date;\n}\n\nexport interface EmailQueryParams extends BaseQueryParams {\n  status?: EmailStatus;\n  from?: string;\n  to?: string;\n  sentAfter?: string;\n  sentBefore?: string;\n  templateId?: string;\n}\n\n// Wins-related types\nexport interface Win {\n  id: string;\n  title: string;\n  description: string;\n  category: WinCategory;\n  impact: WinImpact;\n  author: User;\n  participants?: User[];\n  tags: string[];\n  metrics?: WinMetric[];\n  attachments?: WinAttachment[];\n  isPublic: boolean;\n  isFeatured: boolean;\n  likes: number;\n  views: number;\n  achievedAt: Date;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport enum WinCategory {\n  PERSONAL = 'personal',\n  TEAM = 'team',\n  PROJECT = 'project',\n  PROCESS = 'process',\n  INNOVATION = 'innovation',\n  CUSTOMER = 'customer',\n  FINANCIAL = 'financial',\n  OTHER = 'other'\n}\n\nexport enum WinImpact {\n  LOW = 'low',\n  MEDIUM = 'medium',\n  HIGH = 'high',\n  CRITICAL = 'critical'\n}\n\nexport interface WinMetric {\n  id: string;\n  name: string;\n  value: number;\n  unit: string;\n  description?: string;\n}\n\nexport interface WinAttachment {\n  id: string;\n  filename: string;\n  contentType: string;\n  size: number;\n  url: string;\n}\n\nexport interface CreateWinDto {\n  title: string;\n  description: string;\n  category: WinCategory;\n  impact: WinImpact;\n  participantIds?: string[];\n  tags?: string[];\n  metrics?: Omit<WinMetric, 'id'>[];\n  attachments?: Omit<WinAttachment, 'id'>[];\n  isPublic?: boolean;\n  achievedAt?: Date;\n}\n\nexport interface UpdateWinDto {\n  title?: string;\n  description?: string;\n  category?: WinCategory;\n  impact?: WinImpact;\n  participantIds?: string[];\n  tags?: string[];\n  metrics?: WinMetric[];\n  attachments?: WinAttachment[];\n  isPublic?: boolean;\n  isFeatured?: boolean;\n  achievedAt?: Date;\n}\n\nexport interface WinQueryParams extends BaseQueryParams {\n  category?: WinCategory;\n  impact?: WinImpact;\n  authorId?: string;\n  participantId?: string;\n  isPublic?: boolean;\n  isFeatured?: boolean;\n  achievedAfter?: string;\n  achievedBefore?: string;\n  tags?: string[];\n}\n\n// Service interfaces\nexport interface UsersService {\n  getUsers(params?: UserQueryParams): Promise<ApiResponse<User[]>>;\n  getUser(id: string): Promise<ApiResponse<User>>;\n  createUser(user: CreateUserDto): Promise<ApiResponse<User>>;\n  updateUser(id: string, user: UpdateUserDto): Promise<ApiResponse<User>>;\n  deleteUser(id: string): Promise<ApiResponse<void>>;\n  getUserRoles(id: string): Promise<ApiResponse<Role[]>>;\n  updateUserRoles(id: string, roleIds: string[]): Promise<ApiResponse<User>>;\n  getUserPermissions(id: string): Promise<ApiResponse<Permission[]>>;\n}\n\nexport interface TrainingService {\n  getTrainings(params?: TrainingQueryParams): Promise<ApiResponse<Training[]>>;\n  getTraining(id: string): Promise<ApiResponse<Training>>;\n  createTraining(training: CreateTrainingDto): Promise<ApiResponse<Training>>;\n  updateTraining(id: string, training: UpdateTrainingDto): Promise<ApiResponse<Training>>;\n  deleteTraining(id: string): Promise<ApiResponse<void>>;\n  enrollInTraining(trainingId: string, userId: string): Promise<ApiResponse<void>>;\n  unenrollFromTraining(trainingId: string, userId: string): Promise<ApiResponse<void>>;\n  getTrainingParticipants(id: string): Promise<ApiResponse<User[]>>;\n  getTrainingCategories(): Promise<ApiResponse<TrainingCategory[]>>;\n}\n\nexport interface VendorsService {\n  getVendors(params?: VendorQueryParams): Promise<ApiResponse<Vendor[]>>;\n  getVendor(id: string): Promise<ApiResponse<Vendor>>;\n  createVendor(vendor: CreateVendorDto): Promise<ApiResponse<Vendor>>;\n  updateVendor(id: string, vendor: UpdateVendorDto): Promise<ApiResponse<Vendor>>;\n  deleteVendor(id: string): Promise<ApiResponse<void>>;\n  rateVendor(id: string, rating: number): Promise<ApiResponse<Vendor>>;\n  verifyVendor(id: string): Promise<ApiResponse<Vendor>>;\n}\n\nexport interface EmailService {\n  getEmails(params?: EmailQueryParams): Promise<ApiResponse<Email[]>>;\n  getEmail(id: string): Promise<ApiResponse<Email>>;\n  sendEmail(email: SendEmailDto): Promise<ApiResponse<Email>>;\n  scheduleEmail(email: SendEmailDto): Promise<ApiResponse<Email>>;\n  cancelScheduledEmail(id: string): Promise<ApiResponse<void>>;\n  getEmailTemplates(): Promise<ApiResponse<any[]>>;\n  getEmailStats(id: string): Promise<ApiResponse<any>>;\n}\n\nexport interface WinsService {\n  getWins(params?: WinQueryParams): Promise<ApiResponse<Win[]>>;\n  getWin(id: string): Promise<ApiResponse<Win>>;\n  createWin(win: CreateWinDto): Promise<ApiResponse<Win>>;\n  updateWin(id: string, win: UpdateWinDto): Promise<ApiResponse<Win>>;\n  deleteWin(id: string): Promise<ApiResponse<void>>;\n  likeWin(id: string): Promise<ApiResponse<Win>>;\n  unlikeWin(id: string): Promise<ApiResponse<Win>>;\n  featureWin(id: string): Promise<ApiResponse<Win>>;\n  unfeatureWin(id: string): Promise<ApiResponse<Win>>;\n}", "/**\n * Training Service - Training management operations\n */\n\nimport { LuminarAPIClient } from '../api/types';\nimport {\n  TrainingService,\n  Training,\n  TrainingCategory,\n  User,\n  CreateTrainingDto,\n  UpdateTrainingDto,\n  TrainingQueryParams,\n  TrainingStatus,\n  TrainingDifficulty\n} from './types';\nimport { ApiResponse } from '../api/types';\n\nexport class TrainingServiceImpl implements TrainingService {\n  private apiClient: LuminarAPIClient;\n  private basePath = '/api/trainings';\n\n  constructor(apiClient: LuminarAPIClient) {\n    this.apiClient = apiClient;\n  }\n\n  async getTrainings(params?: TrainingQueryParams): Promise<ApiResponse<Training[]>> {\n    const queryParams = new URLSearchParams();\n    \n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined && value !== null) {\n          if (Array.isArray(value)) {\n            value.forEach(v => queryParams.append(key, v.toString()));\n          } else {\n            queryParams.append(key, value.toString());\n          }\n        }\n      });\n    }\n\n    const url = queryParams.toString() \n      ? `${this.basePath}?${queryParams.toString()}`\n      : this.basePath;\n\n    return this.apiClient.get<Training[]>(url);\n  }\n\n  async getTraining(id: string): Promise<ApiResponse<Training>> {\n    if (!id) {\n      throw new Error('Training ID is required');\n    }\n\n    return this.apiClient.get<Training>(`${this.basePath}/${id}`);\n  }\n\n  async createTraining(training: CreateTrainingDto): Promise<ApiResponse<Training>> {\n    this.validateCreateTrainingDto(training);\n    return this.apiClient.post<Training>(this.basePath, training);\n  }\n\n  async updateTraining(id: string, training: UpdateTrainingDto): Promise<ApiResponse<Training>> {\n    if (!id) {\n      throw new Error('Training ID is required');\n    }\n\n    return this.apiClient.put<Training>(`${this.basePath}/${id}`, training);\n  }\n\n  async deleteTraining(id: string): Promise<ApiResponse<void>> {\n    if (!id) {\n      throw new Error('Training ID is required');\n    }\n\n    return this.apiClient.delete<void>(`${this.basePath}/${id}`);\n  }\n\n  async enrollInTraining(trainingId: string, userId: string): Promise<ApiResponse<void>> {\n    if (!trainingId) {\n      throw new Error('Training ID is required');\n    }\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n\n    return this.apiClient.post<void>(`${this.basePath}/${trainingId}/enroll`, { userId });\n  }\n\n  async unenrollFromTraining(trainingId: string, userId: string): Promise<ApiResponse<void>> {\n    if (!trainingId) {\n      throw new Error('Training ID is required');\n    }\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n\n    return this.apiClient.delete<void>(`${this.basePath}/${trainingId}/enroll/${userId}`);\n  }\n\n  async getTrainingParticipants(id: string): Promise<ApiResponse<User[]>> {\n    if (!id) {\n      throw new Error('Training ID is required');\n    }\n\n    return this.apiClient.get<User[]>(`${this.basePath}/${id}/participants`);\n  }\n\n  async getTrainingCategories(): Promise<ApiResponse<TrainingCategory[]>> {\n    return this.apiClient.get<TrainingCategory[]>('/api/training-categories');\n  }\n\n  // Additional utility methods\n  async getPublicTrainings(): Promise<ApiResponse<Training[]>> {\n    return this.getTrainings({\n      isPublic: true,\n      status: TrainingStatus.PUBLISHED\n    });\n  }\n\n  async getTrainingsByInstructor(instructorId: string): Promise<ApiResponse<Training[]>> {\n    if (!instructorId) {\n      throw new Error('Instructor ID is required');\n    }\n\n    return this.getTrainings({\n      instructorId\n    });\n  }\n\n  async getTrainingsByCategory(categoryId: string): Promise<ApiResponse<Training[]>> {\n    if (!categoryId) {\n      throw new Error('Category ID is required');\n    }\n\n    return this.getTrainings({\n      categoryId\n    });\n  }\n\n  async getTrainingsByDifficulty(difficulty: TrainingDifficulty): Promise<ApiResponse<Training[]>> {\n    return this.getTrainings({\n      difficulty\n    });\n  }\n\n  async searchTrainings(query: string, limit = 10): Promise<ApiResponse<Training[]>> {\n    if (!query || query.trim().length === 0) {\n      throw new Error('Search query is required');\n    }\n\n    return this.getTrainings({\n      search: query.trim(),\n      limit\n    });\n  }\n\n  async getUpcomingTrainings(): Promise<ApiResponse<Training[]>> {\n    const now = new Date().toISOString();\n    \n    return this.getTrainings({\n      status: TrainingStatus.SCHEDULED,\n      startAfter: now\n    });\n  }\n\n  async getCompletedTrainings(): Promise<ApiResponse<Training[]>> {\n    return this.getTrainings({\n      status: TrainingStatus.COMPLETED\n    });\n  }\n\n  async publishTraining(id: string): Promise<ApiResponse<Training>> {\n    return this.updateTraining(id, { status: TrainingStatus.PUBLISHED });\n  }\n\n  async scheduleTraining(id: string, startDate: Date, endDate?: Date): Promise<ApiResponse<Training>> {\n    const updateData: UpdateTrainingDto = {\n      status: TrainingStatus.SCHEDULED,\n      startDate\n    };\n\n    if (endDate) {\n      updateData.endDate = endDate;\n    }\n\n    return this.updateTraining(id, updateData);\n  }\n\n  async startTraining(id: string): Promise<ApiResponse<Training>> {\n    return this.updateTraining(id, { status: TrainingStatus.IN_PROGRESS });\n  }\n\n  async completeTraining(id: string): Promise<ApiResponse<Training>> {\n    return this.updateTraining(id, { status: TrainingStatus.COMPLETED });\n  }\n\n  async cancelTraining(id: string): Promise<ApiResponse<Training>> {\n    return this.updateTraining(id, { status: TrainingStatus.CANCELLED });\n  }\n\n  async archiveTraining(id: string): Promise<ApiResponse<Training>> {\n    return this.updateTraining(id, { status: TrainingStatus.ARCHIVED });\n  }\n\n  private validateCreateTrainingDto(training: CreateTrainingDto): void {\n    if (!training.title || training.title.trim().length === 0) {\n      throw new Error('Training title is required');\n    }\n\n    if (!training.description || training.description.trim().length === 0) {\n      throw new Error('Training description is required');\n    }\n\n    if (!training.categoryId) {\n      throw new Error('Training category is required');\n    }\n\n    if (!training.instructorId) {\n      throw new Error('Training instructor is required');\n    }\n\n    if (!training.duration || training.duration <= 0) {\n      throw new Error('Training duration must be greater than 0');\n    }\n\n    if (!Object.values(TrainingDifficulty).includes(training.difficulty)) {\n      throw new Error('Invalid training difficulty');\n    }\n\n    if (training.maxParticipants && training.maxParticipants <= 0) {\n      throw new Error('Max participants must be greater than 0');\n    }\n\n    if (training.startDate && training.endDate) {\n      if (new Date(training.startDate) >= new Date(training.endDate)) {\n        throw new Error('End date must be after start date');\n      }\n    }\n\n    if (training.tags && !Array.isArray(training.tags)) {\n      throw new Error('Tags must be an array');\n    }\n  }\n}\n\n// Factory function to create training service\nexport function createTrainingService(apiClient: LuminarAPIClient): TrainingService {\n  return new TrainingServiceImpl(apiClient);\n}", "/**\n * Vendors Service - Vendor management operations\n */\n\nimport { LuminarAPIClient } from '../api/types';\nimport {\n  VendorsService,\n  Vendor,\n  CreateVendorDto,\n  UpdateVendorDto,\n  VendorQueryParams\n} from './types';\nimport { ApiResponse } from '../api/types';\n\nexport class VendorsServiceImpl implements VendorsService {\n  private apiClient: LuminarAPIClient;\n  private basePath = '/api/vendors';\n\n  constructor(apiClient: LuminarAPIClient) {\n    this.apiClient = apiClient;\n  }\n\n  async getVendors(params?: VendorQueryParams): Promise<ApiResponse<Vendor[]>> {\n    const queryParams = new URLSearchParams();\n    \n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined && value !== null) {\n          queryParams.append(key, value.toString());\n        }\n      });\n    }\n\n    const url = queryParams.toString() \n      ? `${this.basePath}?${queryParams.toString()}`\n      : this.basePath;\n\n    return this.apiClient.get<Vendor[]>(url);\n  }\n\n  async getVendor(id: string): Promise<ApiResponse<Vendor>> {\n    if (!id) {\n      throw new Error('Vendor ID is required');\n    }\n\n    return this.apiClient.get<Vendor>(`${this.basePath}/${id}`);\n  }\n\n  async createVendor(vendor: CreateVendorDto): Promise<ApiResponse<Vendor>> {\n    this.validateCreateVendorDto(vendor);\n    return this.apiClient.post<Vendor>(this.basePath, vendor);\n  }\n\n  async updateVendor(id: string, vendor: UpdateVendorDto): Promise<ApiResponse<Vendor>> {\n    if (!id) {\n      throw new Error('Vendor ID is required');\n    }\n\n    return this.apiClient.put<Vendor>(`${this.basePath}/${id}`, vendor);\n  }\n\n  async deleteVendor(id: string): Promise<ApiResponse<void>> {\n    if (!id) {\n      throw new Error('Vendor ID is required');\n    }\n\n    return this.apiClient.delete<void>(`${this.basePath}/${id}`);\n  }\n\n  async rateVendor(id: string, rating: number): Promise<ApiResponse<Vendor>> {\n    if (!id) {\n      throw new Error('Vendor ID is required');\n    }\n\n    if (rating < 1 || rating > 5) {\n      throw new Error('Rating must be between 1 and 5');\n    }\n\n    return this.apiClient.post<Vendor>(`${this.basePath}/${id}/rate`, { rating });\n  }\n\n  async verifyVendor(id: string): Promise<ApiResponse<Vendor>> {\n    if (!id) {\n      throw new Error('Vendor ID is required');\n    }\n\n    return this.apiClient.post<Vendor>(`${this.basePath}/${id}/verify`);\n  }\n\n  // Additional utility methods\n  async searchVendors(query: string, limit = 10): Promise<ApiResponse<Vendor[]>> {\n    if (!query || query.trim().length === 0) {\n      throw new Error('Search query is required');\n    }\n\n    return this.getVendors({\n      search: query.trim(),\n      limit\n    });\n  }\n\n  async getActiveVendors(): Promise<ApiResponse<Vendor[]>> {\n    return this.getVendors({\n      isActive: true\n    });\n  }\n\n  async getVerifiedVendors(): Promise<ApiResponse<Vendor[]>> {\n    return this.getVendors({\n      isVerified: true,\n      isActive: true\n    });\n  }\n\n  async getVendorsByCategory(category: string): Promise<ApiResponse<Vendor[]>> {\n    if (!category) {\n      throw new Error('Category is required');\n    }\n\n    return this.getVendors({\n      category\n    });\n  }\n\n  async getTopRatedVendors(minRating = 4): Promise<ApiResponse<Vendor[]>> {\n    return this.getVendors({\n      minRating,\n      isActive: true,\n      sort: 'rating',\n      order: 'desc'\n    });\n  }\n\n  async getVendorsByLocation(city?: string, country?: string): Promise<ApiResponse<Vendor[]>> {\n    const params: VendorQueryParams = {\n      isActive: true\n    };\n\n    if (city) params.city = city;\n    if (country) params.country = country;\n\n    return this.getVendors(params);\n  }\n\n  async deactivateVendor(id: string): Promise<ApiResponse<Vendor>> {\n    return this.updateVendor(id, { isActive: false });\n  }\n\n  async activateVendor(id: string): Promise<ApiResponse<Vendor>> {\n    return this.updateVendor(id, { isActive: true });\n  }\n\n  private validateCreateVendorDto(vendor: CreateVendorDto): void {\n    if (!vendor.name || vendor.name.trim().length === 0) {\n      throw new Error('Vendor name is required');\n    }\n\n    if (vendor.email && !this.isValidEmail(vendor.email)) {\n      throw new Error('Valid email is required');\n    }\n\n    if (vendor.website && !this.isValidUrl(vendor.website)) {\n      throw new Error('Valid website URL is required');\n    }\n\n    if (vendor.contactPerson) {\n      if (!vendor.contactPerson.name || vendor.contactPerson.name.trim().length === 0) {\n        throw new Error('Contact person name is required');\n      }\n\n      if (!vendor.contactPerson.email || !this.isValidEmail(vendor.contactPerson.email)) {\n        throw new Error('Valid contact person email is required');\n      }\n    }\n\n    if (vendor.address) {\n      const requiredFields = ['street', 'city', 'country'];\n      for (const field of requiredFields) {\n        if (!vendor.address[field as keyof typeof vendor.address] || \n            vendor.address[field as keyof typeof vendor.address]?.trim().length === 0) {\n          throw new Error(`Address ${field} is required`);\n        }\n      }\n    }\n\n    if (vendor.services && Array.isArray(vendor.services)) {\n      vendor.services.forEach((service, index) => {\n        if (!service.name || service.name.trim().length === 0) {\n          throw new Error(`Service ${index + 1} name is required`);\n        }\n        if (!service.category || service.category.trim().length === 0) {\n          throw new Error(`Service ${index + 1} category is required`);\n        }\n      });\n    }\n  }\n\n  private isValidEmail(email: string): boolean {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  }\n\n  private isValidUrl(url: string): boolean {\n    try {\n      new URL(url);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n}\n\n// Factory function to create vendors service\nexport function createVendorsService(apiClient: LuminarAPIClient): VendorsService {\n  return new VendorsServiceImpl(apiClient);\n}", "/**\n * Email Service - Email management and sending operations\n */\n\nimport { LuminarAPIClient } from '../api/types';\nimport {\n  EmailService,\n  Email,\n  SendEmailDto,\n  EmailQueryParams,\n  EmailStatus\n} from './types';\nimport { ApiResponse } from '../api/types';\n\nexport class EmailServiceImpl implements EmailService {\n  private apiClient: LuminarAPIClient;\n  private basePath = '/api/emails';\n\n  constructor(apiClient: LuminarAPIClient) {\n    this.apiClient = apiClient;\n  }\n\n  async getEmails(params?: EmailQueryParams): Promise<ApiResponse<Email[]>> {\n    const queryParams = new URLSearchParams();\n    \n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined && value !== null) {\n          queryParams.append(key, value.toString());\n        }\n      });\n    }\n\n    const url = queryParams.toString() \n      ? `${this.basePath}?${queryParams.toString()}`\n      : this.basePath;\n\n    return this.apiClient.get<Email[]>(url);\n  }\n\n  async getEmail(id: string): Promise<ApiResponse<Email>> {\n    if (!id) {\n      throw new Error('Email ID is required');\n    }\n\n    return this.apiClient.get<Email>(`${this.basePath}/${id}`);\n  }\n\n  async sendEmail(email: SendEmailDto): Promise<ApiResponse<Email>> {\n    this.validateSendEmailDto(email);\n    return this.apiClient.post<Email>(`${this.basePath}/send`, email);\n  }\n\n  async scheduleEmail(email: SendEmailDto): Promise<ApiResponse<Email>> {\n    this.validateSendEmailDto(email);\n    \n    if (!email.scheduledAt) {\n      throw new Error('Scheduled date is required for scheduled emails');\n    }\n\n    if (new Date(email.scheduledAt) <= new Date()) {\n      throw new Error('Scheduled date must be in the future');\n    }\n\n    return this.apiClient.post<Email>(`${this.basePath}/schedule`, email);\n  }\n\n  async cancelScheduledEmail(id: string): Promise<ApiResponse<void>> {\n    if (!id) {\n      throw new Error('Email ID is required');\n    }\n\n    return this.apiClient.delete<void>(`${this.basePath}/${id}/cancel`);\n  }\n\n  async getEmailTemplates(): Promise<ApiResponse<any[]>> {\n    return this.apiClient.get<any[]>('/api/email-templates');\n  }\n\n  async getEmailStats(id: string): Promise<ApiResponse<any>> {\n    if (!id) {\n      throw new Error('Email ID is required');\n    }\n\n    return this.apiClient.get<any>(`${this.basePath}/${id}/stats`);\n  }\n\n  // Additional utility methods\n  async getSentEmails(): Promise<ApiResponse<Email[]>> {\n    return this.getEmails({\n      status: EmailStatus.SENT\n    });\n  }\n\n  async getFailedEmails(): Promise<ApiResponse<Email[]>> {\n    return this.getEmails({\n      status: EmailStatus.FAILED\n    });\n  }\n\n  async getScheduledEmails(): Promise<ApiResponse<Email[]>> {\n    return this.getEmails({\n      status: EmailStatus.QUEUED\n    });\n  }\n\n  async getEmailsByTemplate(templateId: string): Promise<ApiResponse<Email[]>> {\n    if (!templateId) {\n      throw new Error('Template ID is required');\n    }\n\n    return this.getEmails({\n      templateId\n    });\n  }\n\n  async searchEmails(query: string, limit = 10): Promise<ApiResponse<Email[]>> {\n    if (!query || query.trim().length === 0) {\n      throw new Error('Search query is required');\n    }\n\n    return this.getEmails({\n      search: query.trim(),\n      limit\n    });\n  }\n\n  async getEmailsByRecipient(recipient: string): Promise<ApiResponse<Email[]>> {\n    if (!recipient) {\n      throw new Error('Recipient email is required');\n    }\n\n    return this.getEmails({\n      to: recipient\n    });\n  }\n\n  async getEmailsBySender(sender: string): Promise<ApiResponse<Email[]>> {\n    if (!sender) {\n      throw new Error('Sender email is required');\n    }\n\n    return this.getEmails({\n      from: sender\n    });\n  }\n\n  async resendEmail(id: string): Promise<ApiResponse<Email>> {\n    if (!id) {\n      throw new Error('Email ID is required');\n    }\n\n    return this.apiClient.post<Email>(`${this.basePath}/${id}/resend`);\n  }\n\n  async sendBulkEmail(emails: SendEmailDto[]): Promise<ApiResponse<Email[]>> {\n    if (!Array.isArray(emails) || emails.length === 0) {\n      throw new Error('Emails array is required and must not be empty');\n    }\n\n    emails.forEach((email, index) => {\n      try {\n        this.validateSendEmailDto(email);\n      } catch (error) {\n        throw new Error(`Email ${index + 1}: ${(error as Error).message}`);\n      }\n    });\n\n    return this.apiClient.post<Email[]>(`${this.basePath}/bulk-send`, { emails });\n  }\n\n  async sendTemplateEmail(\n    templateId: string, \n    to: string[], \n    templateData: Record<string, any>,\n    options?: Partial<SendEmailDto>\n  ): Promise<ApiResponse<Email>> {\n    if (!templateId) {\n      throw new Error('Template ID is required');\n    }\n\n    if (!Array.isArray(to) || to.length === 0) {\n      throw new Error('Recipients are required');\n    }\n\n    const emailData: SendEmailDto = {\n      to,\n      subject: '', // Will be filled by template\n      body: '', // Will be filled by template\n      templateId,\n      templateData,\n      ...options\n    };\n\n    return this.sendEmail(emailData);\n  }\n\n  private validateSendEmailDto(email: SendEmailDto): void {\n    if (!Array.isArray(email.to) || email.to.length === 0) {\n      throw new Error('At least one recipient is required');\n    }\n\n    // Validate all email addresses\n    const allEmails = [\n      ...email.to,\n      ...(email.cc || []),\n      ...(email.bcc || [])\n    ];\n\n    allEmails.forEach(emailAddr => {\n      if (!this.isValidEmail(emailAddr)) {\n        throw new Error(`Invalid email address: ${emailAddr}`);\n      }\n    });\n\n    if (!email.templateId) {\n      if (!email.subject || email.subject.trim().length === 0) {\n        throw new Error('Email subject is required');\n      }\n\n      if (!email.body || email.body.trim().length === 0) {\n        throw new Error('Email body is required');\n      }\n    }\n\n    // Validate attachments\n    if (email.attachments && Array.isArray(email.attachments)) {\n      email.attachments.forEach((attachment, index) => {\n        if (!attachment.filename || attachment.filename.trim().length === 0) {\n          throw new Error(`Attachment ${index + 1} filename is required`);\n        }\n        if (!attachment.contentType || attachment.contentType.trim().length === 0) {\n          throw new Error(`Attachment ${index + 1} content type is required`);\n        }\n        if (!attachment.data && !attachment.url) {\n          throw new Error(`Attachment ${index + 1} must have either data or URL`);\n        }\n      });\n    }\n\n    // Validate template data if template is used\n    if (email.templateId && (!email.templateData || typeof email.templateData !== 'object')) {\n      throw new Error('Template data is required when using email templates');\n    }\n  }\n\n  private isValidEmail(email: string): boolean {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  }\n}\n\n// Factory function to create email service\nexport function createEmailService(apiClient: LuminarAPIClient): EmailService {\n  return new EmailServiceImpl(apiClient);\n}", "/**\n * Wins Service - Wins tracking and management operations\n */\n\nimport { LuminarAPIClient } from '../api/types';\nimport {\n  WinsService,\n  Win,\n  CreateWinDto,\n  UpdateWinDto,\n  WinQueryParams,\n  WinCategory,\n  WinImpact\n} from './types';\nimport { ApiResponse } from '../api/types';\n\nexport class WinsServiceImpl implements WinsService {\n  private apiClient: LuminarAPIClient;\n  private basePath = '/api/wins';\n\n  constructor(apiClient: LuminarAPIClient) {\n    this.apiClient = apiClient;\n  }\n\n  async getWins(params?: WinQueryParams): Promise<ApiResponse<Win[]>> {\n    const queryParams = new URLSearchParams();\n    \n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined && value !== null) {\n          if (Array.isArray(value)) {\n            value.forEach(v => queryParams.append(key, v.toString()));\n          } else {\n            queryParams.append(key, value.toString());\n          }\n        }\n      });\n    }\n\n    const url = queryParams.toString() \n      ? `${this.basePath}?${queryParams.toString()}`\n      : this.basePath;\n\n    return this.apiClient.get<Win[]>(url);\n  }\n\n  async getWin(id: string): Promise<ApiResponse<Win>> {\n    if (!id) {\n      throw new Error('Win ID is required');\n    }\n\n    return this.apiClient.get<Win>(`${this.basePath}/${id}`);\n  }\n\n  async createWin(win: CreateWinDto): Promise<ApiResponse<Win>> {\n    this.validateCreateWinDto(win);\n    return this.apiClient.post<Win>(this.basePath, win);\n  }\n\n  async updateWin(id: string, win: UpdateWinDto): Promise<ApiResponse<Win>> {\n    if (!id) {\n      throw new Error('Win ID is required');\n    }\n\n    return this.apiClient.put<Win>(`${this.basePath}/${id}`, win);\n  }\n\n  async deleteWin(id: string): Promise<ApiResponse<void>> {\n    if (!id) {\n      throw new Error('Win ID is required');\n    }\n\n    return this.apiClient.delete<void>(`${this.basePath}/${id}`);\n  }\n\n  async likeWin(id: string): Promise<ApiResponse<Win>> {\n    if (!id) {\n      throw new Error('Win ID is required');\n    }\n\n    return this.apiClient.post<Win>(`${this.basePath}/${id}/like`);\n  }\n\n  async unlikeWin(id: string): Promise<ApiResponse<Win>> {\n    if (!id) {\n      throw new Error('Win ID is required');\n    }\n\n    return this.apiClient.delete<Win>(`${this.basePath}/${id}/like`);\n  }\n\n  async featureWin(id: string): Promise<ApiResponse<Win>> {\n    if (!id) {\n      throw new Error('Win ID is required');\n    }\n\n    return this.apiClient.post<Win>(`${this.basePath}/${id}/feature`);\n  }\n\n  async unfeatureWin(id: string): Promise<ApiResponse<Win>> {\n    if (!id) {\n      throw new Error('Win ID is required');\n    }\n\n    return this.apiClient.delete<Win>(`${this.basePath}/${id}/feature`);\n  }\n\n  // Additional utility methods\n  async getPublicWins(): Promise<ApiResponse<Win[]>> {\n    return this.getWins({\n      isPublic: true\n    });\n  }\n\n  async getFeaturedWins(): Promise<ApiResponse<Win[]>> {\n    return this.getWins({\n      isFeatured: true,\n      isPublic: true\n    });\n  }\n\n  async getWinsByCategory(category: WinCategory): Promise<ApiResponse<Win[]>> {\n    return this.getWins({\n      category\n    });\n  }\n\n  async getWinsByImpact(impact: WinImpact): Promise<ApiResponse<Win[]>> {\n    return this.getWins({\n      impact\n    });\n  }\n\n  async getWinsByAuthor(authorId: string): Promise<ApiResponse<Win[]>> {\n    if (!authorId) {\n      throw new Error('Author ID is required');\n    }\n\n    return this.getWins({\n      authorId\n    });\n  }\n\n  async getWinsByParticipant(participantId: string): Promise<ApiResponse<Win[]>> {\n    if (!participantId) {\n      throw new Error('Participant ID is required');\n    }\n\n    return this.getWins({\n      participantId\n    });\n  }\n\n  async searchWins(query: string, limit = 10): Promise<ApiResponse<Win[]>> {\n    if (!query || query.trim().length === 0) {\n      throw new Error('Search query is required');\n    }\n\n    return this.getWins({\n      search: query.trim(),\n      limit\n    });\n  }\n\n  async getRecentWins(days = 30): Promise<ApiResponse<Win[]>> {\n    const date = new Date();\n    date.setDate(date.getDate() - days);\n    \n    return this.getWins({\n      achievedAfter: date.toISOString(),\n      sort: 'achievedAt',\n      order: 'desc'\n    });\n  }\n\n  async getTopWins(limit = 10): Promise<ApiResponse<Win[]>> {\n    return this.getWins({\n      sort: 'likes',\n      order: 'desc',\n      limit\n    });\n  }\n\n  async getWinsByTags(tags: string[]): Promise<ApiResponse<Win[]>> {\n    if (!Array.isArray(tags) || tags.length === 0) {\n      throw new Error('Tags array is required and must not be empty');\n    }\n\n    return this.getWins({\n      tags\n    });\n  }\n\n  async getHighImpactWins(): Promise<ApiResponse<Win[]>> {\n    return this.getWins({\n      impact: WinImpact.HIGH\n    });\n  }\n\n  async getCriticalImpactWins(): Promise<ApiResponse<Win[]>> {\n    return this.getWins({\n      impact: WinImpact.CRITICAL\n    });\n  }\n\n  async getTeamWins(): Promise<ApiResponse<Win[]>> {\n    return this.getWins({\n      category: WinCategory.TEAM\n    });\n  }\n\n  async getPersonalWins(): Promise<ApiResponse<Win[]>> {\n    return this.getWins({\n      category: WinCategory.PERSONAL\n    });\n  }\n\n  async getProjectWins(): Promise<ApiResponse<Win[]>> {\n    return this.getWins({\n      category: WinCategory.PROJECT\n    });\n  }\n\n  async getInnovationWins(): Promise<ApiResponse<Win[]>> {\n    return this.getWins({\n      category: WinCategory.INNOVATION\n    });\n  }\n\n  async incrementWinViews(id: string): Promise<ApiResponse<Win>> {\n    if (!id) {\n      throw new Error('Win ID is required');\n    }\n\n    return this.apiClient.post<Win>(`${this.basePath}/${id}/view`);\n  }\n\n  private validateCreateWinDto(win: CreateWinDto): void {\n    if (!win.title || win.title.trim().length === 0) {\n      throw new Error('Win title is required');\n    }\n\n    if (!win.description || win.description.trim().length === 0) {\n      throw new Error('Win description is required');\n    }\n\n    if (!Object.values(WinCategory).includes(win.category)) {\n      throw new Error('Invalid win category');\n    }\n\n    if (!Object.values(WinImpact).includes(win.impact)) {\n      throw new Error('Invalid win impact level');\n    }\n\n    if (win.participantIds && !Array.isArray(win.participantIds)) {\n      throw new Error('Participant IDs must be an array');\n    }\n\n    if (win.tags && !Array.isArray(win.tags)) {\n      throw new Error('Tags must be an array');\n    }\n\n    if (win.metrics && Array.isArray(win.metrics)) {\n      win.metrics.forEach((metric, index) => {\n        if (!metric.name || metric.name.trim().length === 0) {\n          throw new Error(`Metric ${index + 1} name is required`);\n        }\n        if (typeof metric.value !== 'number') {\n          throw new Error(`Metric ${index + 1} value must be a number`);\n        }\n        if (!metric.unit || metric.unit.trim().length === 0) {\n          throw new Error(`Metric ${index + 1} unit is required`);\n        }\n      });\n    }\n\n    if (win.attachments && Array.isArray(win.attachments)) {\n      win.attachments.forEach((attachment, index) => {\n        if (!attachment.filename || attachment.filename.trim().length === 0) {\n          throw new Error(`Attachment ${index + 1} filename is required`);\n        }\n        if (!attachment.contentType || attachment.contentType.trim().length === 0) {\n          throw new Error(`Attachment ${index + 1} content type is required`);\n        }\n        if (!attachment.url || attachment.url.trim().length === 0) {\n          throw new Error(`Attachment ${index + 1} URL is required`);\n        }\n      });\n    }\n\n    if (win.achievedAt && new Date(win.achievedAt) > new Date()) {\n      throw new Error('Achievement date cannot be in the future');\n    }\n  }\n}\n\n// Factory function to create wins service\nexport function createWinsService(apiClient: LuminarAPIClient): WinsService {\n  return new WinsServiceImpl(apiClient);\n}", "/**\n * Services Module - Domain-specific API services\n */\n\nimport type { LuminarAPIClient } from '../api/types';\nimport { createUsersService } from './users.service';\nimport { createTrainingService } from './training.service';\nimport { createVendorsService } from './vendors.service';\nimport { createEmailService } from './email.service';\nimport { createWinsService } from './wins.service';\n\n// Service implementations\nexport {\n  UsersServiceImpl,\n  createUsersService\n} from './users.service';\n\nexport {\n  TrainingServiceImpl,\n  createTrainingService\n} from './training.service';\n\nexport {\n  VendorsServiceImpl,\n  createVendorsService\n} from './vendors.service';\n\nexport {\n  EmailServiceImpl,\n  createEmailService\n} from './email.service';\n\nexport {\n  WinsServiceImpl,\n  createWinsService\n} from './wins.service';\n\n// Service interfaces and types\nexport type {\n  UsersService,\n  TrainingService,\n  VendorsService,\n  EmailService,\n  WinsService,\n  User,\n  Role,\n  Permission,\n  Training,\n  TrainingCategory,\n  Vendor,\n  Email,\n  Win,\n  CreateUserDto,\n  UpdateUserDto,\n  UserQueryParams,\n  CreateTrainingDto,\n  UpdateTrainingDto,\n  TrainingQueryParams,\n  CreateVendorDto,\n  UpdateVendorDto,\n  VendorQueryParams,\n  SendEmailDto,\n  EmailQueryParams,\n  CreateWinDto,\n  UpdateWinDto,\n  WinQueryParams,\n  BaseQueryParams,\n  Address,\n  ContactPerson,\n  VendorService,\n  EmailAttachment,\n  WinMetric,\n  WinAttachment\n} from './types';\n\nexport {\n  TrainingDifficulty,\n  TrainingStatus,\n  EmailStatus,\n  WinCategory,\n  WinImpact\n} from './types';\n\n// Utility function to create all services with a single API client\nexport function createAllServices(apiClient: LuminarAPIClient) {\n  return {\n    users: createUsersService(apiClient),\n    training: createTrainingService(apiClient),\n    vendors: createVendorsService(apiClient),\n    email: createEmailService(apiClient),\n    wins: createWinsService(apiClient)\n  };\n}\n\n// Service factory with configuration\nexport interface ServiceConfig {\n  apiClient: LuminarAPIClient;\n  enableLogging?: boolean;\n  enableCaching?: boolean;\n}\n\nexport function createConfiguredServices(config: ServiceConfig) {\n  const services = createAllServices(config.apiClient);\n\n  // Add logging wrapper if enabled\n  if (config.enableLogging) {\n    Object.keys(services).forEach(serviceName => {\n      const service = services[serviceName as keyof typeof services];\n      wrapServiceWithLogging(service, serviceName);\n    });\n  }\n\n  return services;\n}\n\n// Logging wrapper for services\nfunction wrapServiceWithLogging(service: any, serviceName: string) {\n  const originalMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(service))\n    .filter(name => name !== 'constructor' && typeof service[name] === 'function');\n\n  originalMethods.forEach(methodName => {\n    const originalMethod = service[methodName];\n    service[methodName] = async function(...args: any[]) {\n      console.log(`[${serviceName}Service] ${methodName}`, args);\n      try {\n        const result = await originalMethod.apply(this, args);\n        console.log(`[${serviceName}Service] ${methodName} - Success`);\n        return result;\n      } catch (error) {\n        console.error(`[${serviceName}Service] ${methodName} - Error:`, error);\n        throw error;\n      }\n    };\n  });\n}"]}