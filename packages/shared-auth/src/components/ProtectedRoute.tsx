import React from 'react'
import { Navigate, useLocation } from '@tanstack/react-router'
import { useAuth } from '../hooks'

interface ProtectedRouteProps {
  children: React.ReactNode
  roles?: string[]
  permissions?: string[]
  redirectTo?: string
  fallback?: React.ReactNode
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  roles,
  permissions,
  redirectTo = '/login',
  fallback = <div>Loading...</div>
}) => {
  const { isAuthenticated, isLoading, hasAllRoles, hasAllPermissions } = useAuth()
  const location = useLocation()

  if (isLoading) {
    return <>{fallback}</>
  }

  if (!isAuthenticated) {
    return <Navigate to={redirectTo} replace />
  }

  if (roles && !hasAllRoles(roles)) {
    return <Navigate to="/unauthorized" replace />
  }

  if (permissions && !hasAllPermissions(permissions)) {
    return <Navigate to="/unauthorized" replace />
  }

  return <>{children}</>
}