{"version": 3, "sources": ["../src/components/ProtectedRoute.tsx", "../src/hooks.ts", "../src/auth-context.tsx", "../src/components/PermissionGate.tsx", "../src/components/UnifiedLoginPage.tsx", "../src/utils.ts", "../src/components/CrossAppNavigation.tsx", "../src/components/OAuthButtons.tsx", "../src/oauth-client.ts", "../src/types.ts"], "sourcesContent": ["import React from 'react'\nimport { Navigate, useLocation } from '@tanstack/react-router'\nimport { useAuth } from '../hooks'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  roles?: string[]\n  permissions?: string[]\n  redirectTo?: string\n  fallback?: React.ReactNode\n}\n\nexport const ProtectedRoute: React.FC<ProtectedRouteProps> = ({\n  children,\n  roles,\n  permissions,\n  redirectTo = '/login',\n  fallback = <div>Loading...</div>\n}) => {\n  const { isAuthenticated, isLoading, hasAllRoles, hasAllPermissions } = useAuth()\n  const location = useLocation()\n\n  if (isLoading) {\n    return <>{fallback}</>\n  }\n\n  if (!isAuthenticated) {\n    return <Navigate to={redirectTo} replace />\n  }\n\n  if (roles && !hasAllRoles(roles)) {\n    return <Navigate to=\"/unauthorized\" replace />\n  }\n\n  if (permissions && !hasAllPermissions(permissions)) {\n    return <Navigate to=\"/unauthorized\" replace />\n  }\n\n  return <>{children}</>\n}", "import { useEffect } from 'react'\nimport { useNavigate, useLocation } from '@tanstack/react-router'\nimport { useAuthContext } from './auth-context'\n\nexport { useAuthContext as useAuth } from './auth-context'\n\n// Hook to protect routes\nexport function useRequireAuth(options?: {\n  redirectTo?: string\n  roles?: string[]\n  permissions?: string[]\n}) {\n  const { \n    isAuthenticated, \n    isLoading, \n    user,\n    hasAllRoles,\n    hasAllPermissions \n  } = useAuthContext()\n  const navigate = useNavigate()\n  const location = useLocation()\n\n  useEffect(() => {\n    if (isLoading) return\n\n    const redirectTo = options?.redirectTo || '/login'\n    \n    if (!isAuthenticated) {\n      navigate({ to: redirectTo })\n      return\n    }\n\n    // Check role requirements\n    if (options?.roles && !hasAllRoles(options.roles)) {\n      navigate({ to: '/unauthorized' })\n      return\n    }\n\n    // Check permission requirements\n    if (options?.permissions && !hasAllPermissions(options.permissions)) {\n      navigate({ to: '/unauthorized' })\n      return\n    }\n  }, [\n    isAuthenticated,\n    isLoading,\n    user,\n    navigate,\n    location,\n    options,\n    hasAllRoles,\n    hasAllPermissions\n  ])\n\n  return {\n    isAuthenticated,\n    isLoading,\n    user,\n    isAuthorized: true\n  }\n}\n\n// Hook to redirect if already authenticated\nexport function useRedirectIfAuthenticated(redirectTo = '/dashboard') {\n  const { isAuthenticated, isLoading } = useAuthContext()\n  const navigate = useNavigate()\n  const location = useLocation()\n\n  useEffect(() => {\n    if (!isLoading && isAuthenticated) {\n      const from = (location.state as any)?.from || redirectTo\n      navigate({ to: from })\n    }\n  }, [isAuthenticated, isLoading, navigate, location, redirectTo])\n}\n\n// Hook to check permissions\nexport function usePermissions() {\n  const {\n    user,\n    hasRole,\n    hasPermission,\n    hasAnyRole,\n    hasAllRoles,\n    hasAnyPermission,\n    hasAllPermissions\n  } = useAuthContext()\n\n  return {\n    user,\n    roles: user?.roles || [],\n    permissions: user?.permissions || [],\n    hasRole,\n    hasPermission,\n    hasAnyRole,\n    hasAllRoles,\n    hasAnyPermission,\n    hasAllPermissions,\n    isAdmin: hasRole('admin'),\n    isManager: hasAnyRole(['admin', 'manager']),\n    isUser: hasRole('user')\n  }\n}\n\n// Hook for conditional rendering based on auth\nexport function useAuthGuard(\n  condition: 'authenticated' | 'unauthenticated' | 'loading'\n): boolean {\n  const { isAuthenticated, isLoading } = useAuthContext()\n\n  switch (condition) {\n    case 'authenticated':\n      return isAuthenticated && !isLoading\n    case 'unauthenticated':\n      return !isAuthenticated && !isLoading\n    case 'loading':\n      return isLoading\n    default:\n      return false\n  }\n}\n\n// Hook to handle auth errors\nexport function useAuthError(handler?: (error: Error) => void) {\n  const { error } = useAuthContext()\n\n  useEffect(() => {\n    if (error && handler) {\n      handler(error)\n    }\n  }, [error, handler])\n\n  return error\n}", "import React, { createContext, useContext } from 'react'\nimport { AuthContextValue } from './types'\n\nconst AuthContext = createContext<AuthContextValue | undefined>(undefined)\n\nexport const useAuthContext = (): AuthContextValue => {\n  const context = useContext(AuthContext)\n  if (!context) {\n    throw new Error('useAuthContext must be used within an AuthProvider')\n  }\n  return context\n}\n\nexport { AuthContext }", "import React from 'react'\nimport { usePermissions } from '../hooks'\n\ninterface PermissionGateProps {\n  children: React.ReactNode\n  roles?: string[]\n  permissions?: string[]\n  requireAll?: boolean\n  fallback?: React.ReactNode\n}\n\nexport const PermissionGate: React.FC<PermissionGateProps> = ({\n  children,\n  roles,\n  permissions,\n  requireAll = true,\n  fallback = null\n}) => {\n  const { hasAllRoles, hasAnyRole, hasAllPermissions, hasAnyPermission } = usePermissions()\n\n  let hasAccess = true\n\n  if (roles) {\n    hasAccess = requireAll ? hasAllRoles(roles) : hasAnyRole(roles)\n  }\n\n  if (hasAccess && permissions) {\n    const hasPerms = requireAll ? hasAllPermissions(permissions) : hasAnyPermission(permissions)\n    hasAccess = hasAccess && hasPerms\n  }\n\n  return hasAccess ? <>{children}</> : <>{fallback}</>\n}", "import React, { useState, useEffect } from 'react'\nimport { useAuth, useRedirectIfAuthenticated } from '../hooks'\nimport { getAuthRedirectUrl } from '../utils'\n\ninterface UnifiedLoginPageProps {\n  appName: string\n  appLogo?: string\n  appDescription?: string\n  showRegisterLink?: boolean\n  registerUrl?: string\n  forgotPasswordUrl?: string\n  customStyles?: {\n    primaryColor?: string\n    backgroundColor?: string\n    cardBackground?: string\n  }\n  onLoginSuccess?: () => void\n}\n\nexport const UnifiedLoginPage: React.FC<UnifiedLoginPageProps> = ({\n  appName,\n  appLogo,\n  appDescription,\n  showRegisterLink = true,\n  registerUrl = '/register',\n  forgotPasswordUrl = '/forgot-password',\n  customStyles = {},\n  onLoginSuccess\n}) => {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [rememberMe, setRememberMe] = useState(false)\n  const [showPassword, setShowPassword] = useState(false)\n  \n  const { login, isLoading, error } = useAuth()\n  \n  // Redirect if already authenticated\n  useRedirectIfAuthenticated(getAuthRedirectUrl())\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    try {\n      await login({ email, password, rememberMe })\n      onLoginSuccess?.()\n    } catch (err) {\n      // Error is handled by the auth context\n    }\n  }\n\n  const styles = {\n    primaryColor: customStyles.primaryColor || '#3b82f6',\n    backgroundColor: customStyles.backgroundColor || '#f3f4f6',\n    cardBackground: customStyles.cardBackground || '#ffffff'\n  }\n\n  return (\n    <div \n      className=\"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\"\n      style={{ backgroundColor: styles.backgroundColor }}\n    >\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          {appLogo && (\n            <img\n              className=\"mx-auto h-16 w-auto mb-4\"\n              src={appLogo}\n              alt={appName}\n            />\n          )}\n          <h2 className=\"text-3xl font-extrabold text-gray-900\">\n            Sign in to {appName}\n          </h2>\n          {appDescription && (\n            <p className=\"mt-2 text-sm text-gray-600\">\n              {appDescription}\n            </p>\n          )}\n        </div>\n\n        <div \n          className=\"bg-white shadow-xl rounded-lg p-8\"\n          style={{ backgroundColor: styles.cardBackground }}\n        >\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative\">\n                <span className=\"block sm:inline\">{error.message}</span>\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                required\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm\"\n                placeholder=\"Enter your email\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"current-password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm\"\n                    placeholder=\"Enter your password\"\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600\"\n                >\n                  {showPassword ? (\n                    <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                    </svg>\n                  ) : (\n                    <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21\" />\n                    </svg>\n                  )}\n                </button>\n              </div>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <input\n                  id=\"remember-me\"\n                  name=\"remember-me\"\n                  type=\"checkbox\"\n                  checked={rememberMe}\n                  onChange={(e) => setRememberMe(e.target.checked)}\n                  className=\"h-4 w-4 rounded border-gray-300\"\n                  style={{ color: styles.primaryColor }}\n                />\n                <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-900\">\n                  Remember me\n                </label>\n              </div>\n\n              <div className=\"text-sm\">\n                <a \n                  href={forgotPasswordUrl} \n                  className=\"font-medium hover:opacity-80\"\n                  style={{ color: styles.primaryColor }}\n                >\n                  Forgot your password?\n                </a>\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n                style={{ \n                  backgroundColor: styles.primaryColor\n                }}\n              >\n                {isLoading ? (\n                  <>\n                    <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    Signing in...\n                  </>\n                ) : (\n                  'Sign in'\n                )}\n              </button>\n            </div>\n          </form>\n\n          {showRegisterLink && (\n            <div className=\"mt-6\">\n              <div className=\"relative\">\n                <div className=\"absolute inset-0 flex items-center\">\n                  <div className=\"w-full border-t border-gray-300\" />\n                </div>\n                <div className=\"relative flex justify-center text-sm\">\n                  <span className=\"px-2 bg-white text-gray-500\">New to {appName}?</span>\n                </div>\n              </div>\n\n              <div className=\"mt-6 text-center\">\n                <a \n                  href={registerUrl}\n                  className=\"font-medium hover:opacity-80\"\n                  style={{ color: styles.primaryColor }}\n                >\n                  Create an account\n                </a>\n              </div>\n            </div>\n          )}\n\n          {/* SSO Options */}\n          <div className=\"mt-6 space-y-3\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">Or continue with</span>\n              </div>\n            </div>\n\n            <button\n              type=\"button\"\n              className=\"w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n              onClick={() => window.location.href = '/auth/sso/microsoft'}\n            >\n              <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n                <path fill=\"#f25022\" d=\"M0 0h11.377v11.372H0zm12.623 0H24v11.372H12.623z\"/>\n                <path fill=\"#00a4ef\" d=\"M0 12.623h11.377V24H0z\"/>\n                <path fill=\"#7fba00\" d=\"M12.623 12.623H24V24H12.623z\"/>\n              </svg>\n              Microsoft\n            </button>\n\n            <button\n              type=\"button\"\n              className=\"w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n              onClick={() => window.location.href = '/auth/sso/google'}\n            >\n              <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n                <path fill=\"#4285f4\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n                <path fill=\"#34a853\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n                <path fill=\"#fbbc05\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n                <path fill=\"#ea4335\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n              </svg>\n              Google\n            </button>\n          </div>\n        </div>\n\n        <p className=\"mt-4 text-center text-xs text-gray-600\">\n          By signing in, you agree to our{' '}\n          <a href=\"/terms\" className=\"underline hover:text-gray-900\">Terms of Service</a>\n          {' '}and{' '}\n          <a href=\"/privacy\" className=\"underline hover:text-gray-900\">Privacy Policy</a>\n        </p>\n      </div>\n    </div>\n  )\n}", "import { jwtDecode } from 'jwt-decode'\n\nexport interface TokenPayload {\n  sub: string\n  email: string\n  roles: string[]\n  permissions: string[]\n  exp: number\n  iat: number\n  [key: string]: any\n}\n\nexport function decodeToken(token: string): TokenPayload | null {\n  try {\n    return jwtDecode<TokenPayload>(token)\n  } catch {\n    return null\n  }\n}\n\nexport function isTokenExpired(token: string): boolean {\n  const decoded = decodeToken(token)\n  if (!decoded) return true\n  \n  return decoded.exp * 1000 < Date.now()\n}\n\nexport function getTokenExpirationTime(token: string): Date | null {\n  const decoded = decodeToken(token)\n  if (!decoded) return null\n  \n  return new Date(decoded.exp * 1000)\n}\n\nexport function getTokenRemainingTime(token: string): number {\n  const expirationTime = getTokenExpirationTime(token)\n  if (!expirationTime) return 0\n  \n  const remaining = expirationTime.getTime() - Date.now()\n  return Math.max(0, remaining)\n}\n\n// Role hierarchy helper\nconst ROLE_HIERARCHY: Record<string, string[]> = {\n  'super_admin': ['admin', 'manager', 'user'],\n  'admin': ['manager', 'user'],\n  'manager': ['user'],\n  'user': []\n}\n\nexport function hasRoleWithHierarchy(userRoles: string[], requiredRole: string): boolean {\n  // Direct role check\n  if (userRoles.includes(requiredRole)) {\n    return true\n  }\n\n  // Check if user has a higher role\n  for (const userRole of userRoles) {\n    const inheritedRoles = ROLE_HIERARCHY[userRole] || []\n    if (inheritedRoles.includes(requiredRole)) {\n      return true\n    }\n  }\n\n  return false\n}\n\n// Permission pattern matching\nexport function hasPermissionPattern(\n  userPermissions: string[],\n  pattern: string\n): boolean {\n  // Convert pattern to regex (e.g., \"users:*\" -> \"users:.*\")\n  const regexPattern = pattern.replace(/\\*/g, '.*')\n  const regex = new RegExp(`^${regexPattern}$`)\n  \n  return userPermissions.some(permission => regex.test(permission))\n}\n\n// Storage helpers\nexport function getStorageItem(key: string, storage: Storage = localStorage): string | null {\n  try {\n    return storage.getItem(key)\n  } catch {\n    return null\n  }\n}\n\nexport function setStorageItem(\n  key: string,\n  value: string,\n  storage: Storage = localStorage\n): boolean {\n  try {\n    storage.setItem(key, value)\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport function removeStorageItem(key: string, storage: Storage = localStorage): boolean {\n  try {\n    storage.removeItem(key)\n    return true\n  } catch {\n    return false\n  }\n}\n\n// Session timeout helpers\nexport class SessionTimer {\n  private timer: NodeJS.Timeout | null = null\n  private warningTimer: NodeJS.Timeout | null = null\n\n  start(\n    expirationTime: Date,\n    onExpire: () => void,\n    onWarning?: () => void,\n    warningMinutes = 5\n  ) {\n    this.stop()\n\n    const now = Date.now()\n    const expirationMs = expirationTime.getTime()\n    const timeUntilExpiration = expirationMs - now\n    const timeUntilWarning = timeUntilExpiration - (warningMinutes * 60 * 1000)\n\n    if (timeUntilExpiration > 0) {\n      this.timer = setTimeout(onExpire, timeUntilExpiration)\n\n      if (onWarning && timeUntilWarning > 0) {\n        this.warningTimer = setTimeout(onWarning, timeUntilWarning)\n      }\n    }\n  }\n\n  stop() {\n    if (this.timer) {\n      clearTimeout(this.timer)\n      this.timer = null\n    }\n    if (this.warningTimer) {\n      clearTimeout(this.warningTimer)\n      this.warningTimer = null\n    }\n  }\n\n  getRemainingTime(expirationTime: Date): number {\n    const remaining = expirationTime.getTime() - Date.now()\n    return Math.max(0, remaining)\n  }\n}\n\n// URL helpers for auth redirects\nexport function getAuthRedirectUrl(\n  defaultUrl = '/dashboard',\n  locationState?: any\n): string {\n  // Check if there's a redirect URL in location state\n  if (locationState?.from) {\n    return locationState.from\n  }\n\n  // Check URL params\n  const params = new URLSearchParams(window.location.search)\n  const redirectParam = params.get('redirect') || params.get('returnUrl')\n  \n  if (redirectParam) {\n    // Validate it's a safe redirect (same origin)\n    try {\n      const url = new URL(redirectParam, window.location.origin)\n      if (url.origin === window.location.origin) {\n        return url.pathname + url.search + url.hash\n      }\n    } catch {\n      // Invalid URL, ignore\n    }\n  }\n\n  return defaultUrl\n}\n\n// Cross-tab communication for auth sync\nexport class AuthSyncManager {\n  private channel: BroadcastChannel | null = null\n  private storageKey = 'auth_sync'\n\n  constructor(private onAuthChange: (event: 'login' | 'logout' | 'refresh') => void) {\n    if (typeof window !== 'undefined') {\n      if ('BroadcastChannel' in window) {\n        this.channel = new BroadcastChannel('auth_sync')\n        this.channel.onmessage = (event) => {\n          this.onAuthChange(event.data.type)\n        }\n      } else {\n        // Fallback to storage events\n        (window as any).addEventListener('storage', this.handleStorageChange)\n      }\n    }\n  }\n\n  private handleStorageChange = (event: StorageEvent) => {\n    if (event.key === this.storageKey && event.newValue) {\n      const data = JSON.parse(event.newValue)\n      this.onAuthChange(data.type)\n    }\n  }\n\n  broadcast(type: 'login' | 'logout' | 'refresh') {\n    if (this.channel) {\n      this.channel.postMessage({ type })\n    } else {\n      // Fallback to storage\n      localStorage.setItem(\n        this.storageKey,\n        JSON.stringify({ type, timestamp: Date.now() })\n      )\n    }\n  }\n\n  destroy() {\n    if (this.channel) {\n      this.channel.close()\n    } else {\n      window.removeEventListener('storage', this.handleStorageChange)\n    }\n  }\n}", "import React, { useState } from 'react'\nimport { useAuth } from '../hooks'\n\ninterface AppLink {\n  id: string\n  name: string\n  url: string\n  icon?: React.ReactNode\n  description?: string\n  roles?: string[]\n  permissions?: string[]\n}\n\ninterface CrossAppNavigationProps {\n  currentApp: string\n  apps: AppLink[]\n  position?: 'top' | 'left' | 'right'\n  theme?: 'light' | 'dark'\n  onAppSwitch?: (app: AppLink) => void\n}\n\nexport const CrossAppNavigation: React.FC<CrossAppNavigationProps> = ({\n  currentApp,\n  apps,\n  position = 'top',\n  theme = 'light',\n  onAppSwitch\n}) => {\n  const [isOpen, setIsOpen] = useState(false)\n  const { user, hasAnyRole, hasAnyPermission } = useAuth()\n\n  // Filter apps based on user permissions\n  const accessibleApps = apps.filter(app => {\n    if (app.roles && !hasAnyRole(app.roles)) {\n      return false\n    }\n    if (app.permissions && !hasAnyPermission(app.permissions)) {\n      return false\n    }\n    return true\n  })\n\n  const handleAppClick = (app: AppLink) => {\n    if (app.id === currentApp) {\n      setIsOpen(false)\n      return\n    }\n\n    onAppSwitch?.(app)\n    \n    // Navigate to the app\n    if (app.url.startsWith('http')) {\n      // External URL - open in same tab to maintain auth\n      window.location.href = app.url\n    } else {\n      // Relative URL\n      window.location.href = app.url\n    }\n  }\n\n  const themeClasses = {\n    light: {\n      background: 'bg-white',\n      text: 'text-gray-700',\n      hover: 'hover:bg-gray-100',\n      border: 'border-gray-200',\n      activeApp: 'bg-blue-50 text-blue-700'\n    },\n    dark: {\n      background: 'bg-gray-800',\n      text: 'text-gray-200',\n      hover: 'hover:bg-gray-700',\n      border: 'border-gray-600',\n      activeApp: 'bg-gray-700 text-white'\n    }\n  }[theme]\n\n  if (position === 'top') {\n    return (\n      <div className={`${themeClasses.background} border-b ${themeClasses.border}`}>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-12\">\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => setIsOpen(!isOpen)}\n                className={`flex items-center space-x-2 px-3 py-1 rounded-md ${themeClasses.hover} ${themeClasses.text}`}\n              >\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n                <span className=\"font-medium\">Apps</span>\n              </button>\n\n              <span className={`text-sm ${themeClasses.text}`}>\n                Current: <span className=\"font-medium\">{currentApp}</span>\n              </span>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              <span className={`text-sm ${themeClasses.text}`}>\n                {user?.firstName} {user?.lastName}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Dropdown */}\n        {isOpen && (\n          <div className=\"absolute z-50 mt-1 w-64 rounded-md shadow-lg\">\n            <div className={`rounded-md ${themeClasses.background} ring-1 ring-black ring-opacity-5`}>\n              <div className=\"py-1\">\n                {accessibleApps.map(app => (\n                  <button\n                    key={app.id}\n                    onClick={() => handleAppClick(app)}\n                    className={`\n                      w-full text-left px-4 py-2 text-sm\n                      ${app.id === currentApp ? themeClasses.activeApp : themeClasses.text}\n                      ${themeClasses.hover}\n                    `}\n                  >\n                    <div className=\"flex items-center\">\n                      {app.icon && <div className=\"mr-3\">{app.icon}</div>}\n                      <div>\n                        <div className=\"font-medium\">{app.name}</div>\n                        {app.description && (\n                          <div className=\"text-xs opacity-75\">{app.description}</div>\n                        )}\n                      </div>\n                    </div>\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    )\n  }\n\n  // Side navigation for 'left' or 'right' position\n  const sideClasses = position === 'left' ? 'left-0' : 'right-0'\n  \n  return (\n    <>\n      {/* Toggle button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className={`\n          fixed ${position}-4 top-4 z-50\n          ${themeClasses.background} ${themeClasses.text}\n          p-2 rounded-md shadow-lg ${themeClasses.hover}\n        `}\n      >\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16m-7 6h7\" />\n        </svg>\n      </button>\n\n      {/* Side panel */}\n      {isOpen && (\n        <>\n          {/* Backdrop */}\n          <div \n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40\"\n            onClick={() => setIsOpen(false)}\n          />\n\n          {/* Panel */}\n          <div className={`\n            fixed ${sideClasses} top-0 h-full w-64 z-50\n            ${themeClasses.background} shadow-xl\n            transform transition-transform duration-300\n            ${isOpen ? 'translate-x-0' : position === 'left' ? '-translate-x-full' : 'translate-x-full'}\n          `}>\n            <div className=\"p-4\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className={`text-lg font-semibold ${themeClasses.text}`}>\n                  Luminar Apps\n                </h2>\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className={`${themeClasses.text} ${themeClasses.hover} p-1 rounded`}\n                >\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n\n              <nav className=\"space-y-1\">\n                {accessibleApps.map(app => (\n                  <button\n                    key={app.id}\n                    onClick={() => handleAppClick(app)}\n                    className={`\n                      w-full text-left px-3 py-2 rounded-md text-sm\n                      ${app.id === currentApp ? themeClasses.activeApp : themeClasses.text}\n                      ${themeClasses.hover}\n                    `}\n                  >\n                    <div className=\"flex items-center\">\n                      {app.icon && <div className=\"mr-3\">{app.icon}</div>}\n                      <div>\n                        <div className=\"font-medium\">{app.name}</div>\n                        {app.description && (\n                          <div className=\"text-xs opacity-75\">{app.description}</div>\n                        )}\n                      </div>\n                    </div>\n                  </button>\n                ))}\n              </nav>\n\n              <div className={`mt-8 pt-8 border-t ${themeClasses.border}`}>\n                <div className={`text-sm ${themeClasses.text}`}>\n                  <div className=\"font-medium\">{user?.firstName} {user?.lastName}</div>\n                  <div className=\"text-xs opacity-75\">{user?.email}</div>\n                  <div className=\"mt-2\">\n                    <span className=\"text-xs px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded\">\n                      {user?.roles[0]}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </>\n      )}\n    </>\n  )\n}", "import React from 'react';\nimport { OAuthClient, defaultOAuthProviders } from '../oauth-client';\nimport { AuthConfig } from '../types';\n\ninterface OAuthButtonsProps {\n  providers?: ('google' | 'github' | 'microsoft')[];\n  authConfig: AuthConfig;\n  className?: string;\n  buttonClassName?: string;\n  showDivider?: boolean;\n  dividerText?: string;\n}\n\nconst providerIcons = {\n  google: (\n    <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n      <path fill=\"#4285f4\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n      <path fill=\"#34a853\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n      <path fill=\"#fbbc05\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n      <path fill=\"#ea4335\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n    </svg>\n  ),\n  github: (\n    <svg className=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n      <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n    </svg>\n  ),\n  microsoft: (\n    <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n      <path fill=\"#f25022\" d=\"M0 0h11.377v11.372H0zm12.623 0H24v11.372H12.623z\"/>\n      <path fill=\"#00a4ef\" d=\"M0 12.623h11.377V24H0z\"/>\n      <path fill=\"#7fba00\" d=\"M12.623 12.623H24V24H12.623z\"/>\n    </svg>\n  )\n};\n\nconst providerNames = {\n  google: 'Google',\n  github: 'GitHub',\n  microsoft: 'Microsoft'\n};\n\nexport const OAuthButtons: React.FC<OAuthButtonsProps> = ({\n  providers = ['google', 'github', 'microsoft'],\n  authConfig,\n  className = '',\n  buttonClassName = '',\n  showDivider = true,\n  dividerText = 'Or continue with'\n}) => {\n  const handleOAuthLogin = async (provider: string) => {\n    try {\n      const oauthClient = new OAuthClient(authConfig, {\n        providers: defaultOAuthProviders\n      });\n      \n      await oauthClient.initiateOAuth(provider);\n    } catch (error) {\n      console.error(`OAuth login failed for ${provider}:`, error);\n    }\n  };\n\n  return (\n    <div className={className}>\n      {showDivider && (\n        <div className=\"relative mb-6\">\n          <div className=\"absolute inset-0 flex items-center\">\n            <div className=\"w-full border-t border-gray-300\" />\n          </div>\n          <div className=\"relative flex justify-center text-sm\">\n            <span className=\"px-2 bg-white text-gray-500\">{dividerText}</span>\n          </div>\n        </div>\n      )}\n\n      <div className=\"space-y-3\">\n        {providers.map((provider) => (\n          <button\n            key={provider}\n            type=\"button\"\n            onClick={() => handleOAuthLogin(provider)}\n            className={`w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary ${buttonClassName}`}\n          >\n            {providerIcons[provider]}\n            Sign in with {providerNames[provider]}\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n};", "import axios, { AxiosInstance } from 'axios';\nimport { AuthTokens, User, AuthError, AuthConfig } from './types';\n\nexport interface OAuthProvider {\n  name: string;\n  authUrl: string;\n  tokenUrl: string;\n  userInfoUrl: string;\n  clientId: string;\n  clientSecret?: string;\n  scope: string;\n  redirectUri: string;\n}\n\nexport interface OAuthConfig {\n  providers: {\n    google?: OAuthProvider;\n    github?: OAuthProvider;\n    microsoft?: OAuthProvider;\n    [key: string]: OAuthProvider | undefined;\n  };\n  onSuccess?: (user: User, tokens: AuthTokens) => void;\n  onError?: (error: AuthError) => void;\n}\n\nexport interface OAuthState {\n  state: string;\n  codeVerifier?: string;\n  provider: string;\n  timestamp: number;\n}\n\nexport class OAuthClient {\n  private api: AxiosInstance;\n  private config: OAuthConfig;\n  private authConfig: AuthConfig;\n  private stateKey = 'oauth_state';\n\n  constructor(authConfig: AuthConfig, oauthConfig: OAuthConfig) {\n    this.authConfig = authConfig;\n    this.config = oauthConfig;\n    this.api = axios.create({\n      baseURL: authConfig.apiBaseUrl,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n  }\n\n  /**\n   * Initiate OAuth flow\n   */\n  async initiateOAuth(provider: string): Promise<void> {\n    const providerConfig = this.config.providers[provider];\n    if (!providerConfig) {\n      throw new AuthError(`OAuth provider ${provider} not configured`, 'PROVIDER_NOT_CONFIGURED');\n    }\n\n    // Generate state for CSRF protection\n    const state = this.generateState();\n    \n    // Generate PKCE code verifier and challenge for supported providers\n    const codeVerifier = this.generateCodeVerifier();\n    const codeChallenge = await this.generateCodeChallenge(codeVerifier);\n\n    // Store state for verification\n    const oauthState: OAuthState = {\n      state,\n      codeVerifier,\n      provider,\n      timestamp: Date.now()\n    };\n    \n    localStorage.setItem(this.stateKey, JSON.stringify(oauthState));\n\n    // Build authorization URL\n    const params = new URLSearchParams({\n      client_id: providerConfig.clientId,\n      redirect_uri: providerConfig.redirectUri,\n      response_type: 'code',\n      scope: providerConfig.scope,\n      state,\n      access_type: 'offline', // For refresh tokens\n      prompt: 'consent'\n    });\n\n    // Add PKCE parameters if supported\n    if (provider === 'google' || provider === 'github') {\n      params.append('code_challenge', codeChallenge);\n      params.append('code_challenge_method', 'S256');\n    }\n\n    // Redirect to provider\n    window.location.href = `${providerConfig.authUrl}?${params.toString()}`;\n  }\n\n  /**\n   * Handle OAuth callback\n   */\n  async handleCallback(code: string, state: string): Promise<{ user: User; tokens: AuthTokens }> {\n    // Verify state\n    const storedStateStr = localStorage.getItem(this.stateKey);\n    if (!storedStateStr) {\n      throw new AuthError('No OAuth state found', 'INVALID_STATE');\n    }\n\n    const storedState: OAuthState = JSON.parse(storedStateStr);\n    \n    // Clean up state\n    localStorage.removeItem(this.stateKey);\n\n    // Verify state matches and not expired (5 minutes)\n    if (storedState.state !== state) {\n      throw new AuthError('Invalid OAuth state', 'INVALID_STATE');\n    }\n\n    if (Date.now() - storedState.timestamp > 5 * 60 * 1000) {\n      throw new AuthError('OAuth state expired', 'STATE_EXPIRED');\n    }\n\n    const providerConfig = this.config.providers[storedState.provider];\n    if (!providerConfig) {\n      throw new AuthError('OAuth provider not configured', 'PROVIDER_NOT_CONFIGURED');\n    }\n\n    try {\n      // Exchange code for tokens\n      const tokenResponse = await this.exchangeCodeForTokens(\n        code,\n        storedState.provider,\n        providerConfig,\n        storedState.codeVerifier\n      );\n\n      // Get user info from provider\n      const userInfo = await this.getUserInfo(\n        tokenResponse.access_token,\n        providerConfig\n      );\n\n      // Send to backend to create/update user\n      const response = await this.api.post(`${this.authConfig.authEndpoint}/oauth/callback`, {\n        provider: storedState.provider,\n        accessToken: tokenResponse.access_token,\n        refreshToken: tokenResponse.refresh_token,\n        idToken: tokenResponse.id_token,\n        userInfo,\n        expiresIn: tokenResponse.expires_in\n      });\n\n      const { user, accessToken, refreshToken, expiresIn } = response.data;\n\n      const tokens: AuthTokens = {\n        accessToken,\n        refreshToken,\n        expiresIn,\n        tokenType: 'Bearer'\n      };\n\n      // Call success callback\n      this.config.onSuccess?.(user, tokens);\n\n      return { user, tokens };\n    } catch (error: any) {\n      const authError = new AuthError(\n        error.response?.data?.message || 'OAuth authentication failed',\n        error.response?.data?.code || 'OAUTH_ERROR',\n        error.response?.status\n      );\n      \n      this.config.onError?.(authError);\n      throw authError;\n    }\n  }\n\n  /**\n   * Exchange authorization code for tokens\n   */\n  private async exchangeCodeForTokens(\n    code: string,\n    provider: string,\n    providerConfig: OAuthProvider,\n    codeVerifier?: string\n  ): Promise<any> {\n    const params: any = {\n      grant_type: 'authorization_code',\n      code,\n      client_id: providerConfig.clientId,\n      redirect_uri: providerConfig.redirectUri\n    };\n\n    // Add client secret if configured (for confidential clients)\n    if (providerConfig.clientSecret) {\n      params.client_secret = providerConfig.clientSecret;\n    }\n\n    // Add PKCE verifier if used\n    if (codeVerifier && (provider === 'google' || provider === 'github')) {\n      params.code_verifier = codeVerifier;\n    }\n\n    const response = await axios.post(providerConfig.tokenUrl, params, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      }\n    });\n\n    return response.data;\n  }\n\n  /**\n   * Get user info from OAuth provider\n   */\n  private async getUserInfo(accessToken: string, providerConfig: OAuthProvider): Promise<any> {\n    const response = await axios.get(providerConfig.userInfoUrl, {\n      headers: {\n        'Authorization': `Bearer ${accessToken}`\n      }\n    });\n\n    return response.data;\n  }\n\n  /**\n   * Link OAuth account to existing user\n   */\n  async linkAccount(provider: string, userId: string): Promise<void> {\n    // Store linking state\n    const linkingState = {\n      userId,\n      action: 'link',\n      timestamp: Date.now()\n    };\n    \n    localStorage.setItem('oauth_linking', JSON.stringify(linkingState));\n    \n    // Initiate OAuth flow\n    await this.initiateOAuth(provider);\n  }\n\n  /**\n   * Unlink OAuth account\n   */\n  async unlinkAccount(provider: string, userId: string): Promise<void> {\n    await this.api.post(`${this.authConfig.authEndpoint}/oauth/unlink`, {\n      provider,\n      userId\n    });\n  }\n\n  /**\n   * Get linked accounts for user\n   */\n  async getLinkedAccounts(userId: string): Promise<string[]> {\n    const response = await this.api.get(`${this.authConfig.authEndpoint}/oauth/linked/${userId}`);\n    return response.data.providers;\n  }\n\n  /**\n   * Refresh OAuth tokens\n   */\n  async refreshOAuthTokens(provider: string, refreshToken: string): Promise<AuthTokens> {\n    const providerConfig = this.config.providers[provider];\n    if (!providerConfig) {\n      throw new AuthError('OAuth provider not configured', 'PROVIDER_NOT_CONFIGURED');\n    }\n\n    const params: any = {\n      grant_type: 'refresh_token',\n      refresh_token: refreshToken,\n      client_id: providerConfig.clientId\n    };\n\n    if (providerConfig.clientSecret) {\n      params.client_secret = providerConfig.clientSecret;\n    }\n\n    const response = await axios.post(providerConfig.tokenUrl, params, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded'\n      }\n    });\n\n    return {\n      accessToken: response.data.access_token,\n      refreshToken: response.data.refresh_token || refreshToken,\n      expiresIn: response.data.expires_in,\n      tokenType: 'Bearer'\n    };\n  }\n\n  /**\n   * Generate random state for CSRF protection\n   */\n  private generateState(): string {\n    const array = new Uint8Array(32);\n    crypto.getRandomValues(array);\n    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');\n  }\n\n  /**\n   * Generate PKCE code verifier\n   */\n  private generateCodeVerifier(): string {\n    const array = new Uint8Array(32);\n    crypto.getRandomValues(array);\n    return this.base64UrlEncode(array);\n  }\n\n  /**\n   * Generate PKCE code challenge\n   */\n  private async generateCodeChallenge(verifier: string): Promise<string> {\n    const encoder = new TextEncoder();\n    const data = encoder.encode(verifier);\n    const digest = await crypto.subtle.digest('SHA-256', data);\n    return this.base64UrlEncode(new Uint8Array(digest));\n  }\n\n  /**\n   * Base64 URL encode\n   */\n  private base64UrlEncode(buffer: Uint8Array): string {\n    const base64 = btoa(String.fromCharCode(...buffer));\n    return base64\n      .replace(/\\+/g, '-')\n      .replace(/\\//g, '_')\n      .replace(/=/g, '');\n  }\n}\n\n// OAuth provider configurations\nexport const defaultOAuthProviders: Partial<OAuthConfig['providers']> = {\n  google: {\n    name: 'Google',\n    authUrl: 'https://accounts.google.com/o/oauth2/v2/auth',\n    tokenUrl: 'https://oauth2.googleapis.com/token',\n    userInfoUrl: 'https://www.googleapis.com/oauth2/v2/userinfo',\n    clientId: process.env.VITE_GOOGLE_CLIENT_ID || '',\n    clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n    scope: 'openid email profile',\n    redirectUri: `${window.location.origin}/auth/callback/google`\n  },\n  github: {\n    name: 'GitHub',\n    authUrl: 'https://github.com/login/oauth/authorize',\n    tokenUrl: 'https://github.com/login/oauth/access_token',\n    userInfoUrl: 'https://api.github.com/user',\n    clientId: process.env.VITE_GITHUB_CLIENT_ID || '',\n    clientSecret: process.env.GITHUB_CLIENT_SECRET,\n    scope: 'read:user user:email',\n    redirectUri: `${window.location.origin}/auth/callback/github`\n  },\n  microsoft: {\n    name: 'Microsoft',\n    authUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',\n    tokenUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',\n    userInfoUrl: 'https://graph.microsoft.com/v1.0/me',\n    clientId: process.env.VITE_MICROSOFT_CLIENT_ID || '',\n    clientSecret: process.env.MICROSOFT_CLIENT_SECRET,\n    scope: 'openid email profile User.Read',\n    redirectUri: `${window.location.origin}/auth/callback/microsoft`\n  }\n};", "export interface User {\n  id: string\n  email: string\n  firstName: string\n  lastName: string\n  roles: string[]\n  permissions: string[]\n  department?: string\n  organizationId?: string\n  avatar?: string\n  metadata?: Record<string, any>\n}\n\nexport interface AuthTokens {\n  accessToken: string\n  refreshToken: string\n  expiresIn?: number\n  tokenType?: string\n}\n\nexport interface LoginCredentials {\n  email: string\n  password: string\n  rememberMe?: boolean\n}\n\nexport interface RegisterData {\n  email: string\n  password: string\n  firstName: string\n  lastName: string\n  organizationId?: string\n  department?: string\n}\n\nexport interface AuthConfig {\n  apiBaseUrl: string\n  authEndpoint?: string\n  tokenKey?: string\n  refreshTokenKey?: string\n  userKey?: string\n  enableAutoRefresh?: boolean\n  refreshInterval?: number\n  onAuthError?: (error: AuthError) => void\n  onTokenRefresh?: (tokens: AuthTokens) => void\n  storage?: 'localStorage' | 'sessionStorage' | 'cookie'\n  cookieOptions?: {\n    domain?: string\n    secure?: boolean\n    sameSite?: 'strict' | 'lax' | 'none'\n    httpOnly?: boolean\n  }\n}\n\nexport interface AuthState {\n  user: User | null\n  tokens: AuthTokens | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  error: AuthError | null\n}\n\nexport class AuthError extends Error {\n  constructor(\n    message: string,\n    public code: string,\n    public statusCode?: number,\n    public details?: any\n  ) {\n    super(message)\n    this.name = 'AuthError'\n  }\n}\n\nexport interface AuthContextValue {\n  user: User | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  error: AuthError | null\n  login: (credentials: LoginCredentials) => Promise<User>\n  logout: () => Promise<void>\n  register: (data: RegisterData) => Promise<User>\n  refreshAuth: () => Promise<void>\n  updateUser: (data: Partial<User>) => Promise<User>\n  hasRole: (role: string) => boolean\n  hasPermission: (permission: string) => boolean\n  hasAnyRole: (roles: string[]) => boolean\n  hasAllRoles: (roles: string[]) => boolean\n  hasAnyPermission: (permissions: string[]) => boolean\n  hasAllPermissions: (permissions: string[]) => boolean\n}\n\nexport interface AuthProviderProps {\n  children: React.ReactNode\n  config: AuthConfig\n  initialState?: Partial<AuthState>\n  onAuthStateChange?: (state: AuthState) => void\n}"], "mappings": ";AAAA,OAAOA,YAAW;AAClB,SAAS,UAAU,eAAAC,oBAAmB;;;ACDtC,SAAS,iBAAiB;AAC1B,SAAS,aAAa,mBAAmB;;;ACDzC,SAAgB,eAAe,kBAAkB;AAGjD,IAAM,cAAc,cAA4C,MAAS;AAElE,IAAM,iBAAiB,MAAwB;AACpD,QAAM,UAAU,WAAW,WAAW;AACtC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACtE;AACA,SAAO;AACT;;;ADoDO,SAAS,2BAA2B,aAAa,cAAc;AACpE,QAAM,EAAE,iBAAiB,UAAU,IAAI,eAAe;AACtD,QAAM,WAAW,YAAY;AAC7B,QAAM,WAAW,YAAY;AAE7B,YAAU,MAAM;AACd,QAAI,CAAC,aAAa,iBAAiB;AACjC,YAAM,OAAQ,SAAS,OAAe,QAAQ;AAC9C,eAAS,EAAE,IAAI,KAAK,CAAC;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,iBAAiB,WAAW,UAAU,UAAU,UAAU,CAAC;AACjE;AAGO,SAAS,iBAAiB;AAC/B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AAEnB,SAAO;AAAA,IACL;AAAA,IACA,OAAO,MAAM,SAAS,CAAC;AAAA,IACvB,aAAa,MAAM,eAAe,CAAC;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,QAAQ,OAAO;AAAA,IACxB,WAAW,WAAW,CAAC,SAAS,SAAS,CAAC;AAAA,IAC1C,QAAQ,QAAQ,MAAM;AAAA,EACxB;AACF;;;AD1FO,IAAM,iBAAgD,CAAC;AAAA,EAC5D;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,WAAW,gBAAAC,OAAA,cAAC,aAAI,YAAU;AAC5B,MAAM;AACJ,QAAM,EAAE,iBAAiB,WAAW,aAAa,kBAAkB,IAAI,eAAQ;AAC/E,QAAM,WAAWC,aAAY;AAE7B,MAAI,WAAW;AACb,WAAO,gBAAAD,OAAA,cAAAA,OAAA,gBAAG,QAAS;AAAA,EACrB;AAEA,MAAI,CAAC,iBAAiB;AACpB,WAAO,gBAAAA,OAAA,cAAC,YAAS,IAAI,YAAY,SAAO,MAAC;AAAA,EAC3C;AAEA,MAAI,SAAS,CAAC,YAAY,KAAK,GAAG;AAChC,WAAO,gBAAAA,OAAA,cAAC,YAAS,IAAG,iBAAgB,SAAO,MAAC;AAAA,EAC9C;AAEA,MAAI,eAAe,CAAC,kBAAkB,WAAW,GAAG;AAClD,WAAO,gBAAAA,OAAA,cAAC,YAAS,IAAG,iBAAgB,SAAO,MAAC;AAAA,EAC9C;AAEA,SAAO,gBAAAA,OAAA,cAAAA,OAAA,gBAAG,QAAS;AACrB;;;AGvCA,OAAOE,YAAW;AAWX,IAAM,iBAAgD,CAAC;AAAA,EAC5D;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,WAAW;AACb,MAAM;AACJ,QAAM,EAAE,aAAa,YAAY,mBAAmB,iBAAiB,IAAI,eAAe;AAExF,MAAI,YAAY;AAEhB,MAAI,OAAO;AACT,gBAAY,aAAa,YAAY,KAAK,IAAI,WAAW,KAAK;AAAA,EAChE;AAEA,MAAI,aAAa,aAAa;AAC5B,UAAM,WAAW,aAAa,kBAAkB,WAAW,IAAI,iBAAiB,WAAW;AAC3F,gBAAY,aAAa;AAAA,EAC3B;AAEA,SAAO,YAAY,gBAAAC,OAAA,cAAAA,OAAA,gBAAG,QAAS,IAAM,gBAAAA,OAAA,cAAAA,OAAA,gBAAG,QAAS;AACnD;;;AChCA,OAAOC,UAAS,gBAA2B;;;ACA3C,SAAS,iBAAiB;AA2JnB,SAAS,mBACd,aAAa,cACb,eACQ;AAER,MAAI,eAAe,MAAM;AACvB,WAAO,cAAc;AAAA,EACvB;AAGA,QAAM,SAAS,IAAI,gBAAgB,OAAO,SAAS,MAAM;AACzD,QAAM,gBAAgB,OAAO,IAAI,UAAU,KAAK,OAAO,IAAI,WAAW;AAEtE,MAAI,eAAe;AAEjB,QAAI;AACF,YAAM,MAAM,IAAI,IAAI,eAAe,OAAO,SAAS,MAAM;AACzD,UAAI,IAAI,WAAW,OAAO,SAAS,QAAQ;AACzC,eAAO,IAAI,WAAW,IAAI,SAAS,IAAI;AAAA,MACzC;AAAA,IACF,QAAQ;AAAA,IAER;AAAA,EACF;AAEA,SAAO;AACT;;;ADlKO,IAAM,mBAAoD,CAAC;AAAA,EAChE;AAAA,EACA;AAAA,EACA;AAAA,EACA,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,eAAe,CAAC;AAAA,EAChB;AACF,MAAM;AACJ,QAAM,CAAC,OAAO,QAAQ,IAAI,SAAS,EAAE;AACrC,QAAM,CAAC,UAAU,WAAW,IAAI,SAAS,EAAE;AAC3C,QAAM,CAAC,YAAY,aAAa,IAAI,SAAS,KAAK;AAClD,QAAM,CAAC,cAAc,eAAe,IAAI,SAAS,KAAK;AAEtD,QAAM,EAAE,OAAO,WAAW,MAAM,IAAI,eAAQ;AAG5C,6BAA2B,mBAAmB,CAAC;AAE/C,QAAM,eAAe,OAAO,MAAuB;AACjD,MAAE,eAAe;AAEjB,QAAI;AACF,YAAM,MAAM,EAAE,OAAO,UAAU,WAAW,CAAC;AAC3C,uBAAiB;AAAA,IACnB,SAAS,KAAK;AAAA,IAEd;AAAA,EACF;AAEA,QAAM,SAAS;AAAA,IACb,cAAc,aAAa,gBAAgB;AAAA,IAC3C,iBAAiB,aAAa,mBAAmB;AAAA,IACjD,gBAAgB,aAAa,kBAAkB;AAAA,EACjD;AAEA,SACE,gBAAAC,OAAA;AAAA,IAAC;AAAA;AAAA,MACC,WAAU;AAAA,MACV,OAAO,EAAE,iBAAiB,OAAO,gBAAgB;AAAA;AAAA,IAEjD,gBAAAA,OAAA,cAAC,SAAI,WAAU,+BACb,gBAAAA,OAAA,cAAC,SAAI,WAAU,iBACZ,WACC,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC,WAAU;AAAA,QACV,KAAK;AAAA,QACL,KAAK;AAAA;AAAA,IACP,GAEF,gBAAAA,OAAA,cAAC,QAAG,WAAU,2CAAwC,eACxC,OACd,GACC,kBACC,gBAAAA,OAAA,cAAC,OAAE,WAAU,gCACV,cACH,CAEJ,GAEA,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC,WAAU;AAAA,QACV,OAAO,EAAE,iBAAiB,OAAO,eAAe;AAAA;AAAA,MAEhD,gBAAAA,OAAA,cAAC,UAAK,WAAU,aAAY,UAAU,gBACnC,SACC,gBAAAA,OAAA,cAAC,SAAI,WAAU,6EACb,gBAAAA,OAAA,cAAC,UAAK,WAAU,qBAAmB,MAAM,OAAQ,CACnD,GAGF,gBAAAA,OAAA,cAAC,aACC,gBAAAA,OAAA,cAAC,WAAM,SAAQ,SAAQ,WAAU,6CAA0C,eAE3E,GACA,gBAAAA,OAAA;AAAA,QAAC;AAAA;AAAA,UACC,IAAG;AAAA,UACH,MAAK;AAAA,UACL,MAAK;AAAA,UACL,cAAa;AAAA,UACb,UAAQ;AAAA,UACR,OAAO;AAAA,UACP,UAAU,CAAC,MAAM,SAAS,EAAE,OAAO,KAAK;AAAA,UACxC,WAAU;AAAA,UACV,aAAY;AAAA;AAAA,MACd,CACF,GAEA,gBAAAA,OAAA,cAAC,aACC,gBAAAA,OAAA,cAAC,WAAM,SAAQ,YAAW,WAAU,6CAA0C,UAE9E,GACA,gBAAAA,OAAA,cAAC,SAAI,WAAU,mBACb,gBAAAA,OAAA;AAAA,QAAC;AAAA;AAAA,UACC,IAAG;AAAA,UACH,MAAK;AAAA,UACL,MAAM,eAAe,SAAS;AAAA,UAC9B,cAAa;AAAA,UACb,UAAQ;AAAA,UACR,OAAO;AAAA,UACP,UAAU,CAAC,MAAM,YAAY,EAAE,OAAO,KAAK;AAAA,UAC3C,WAAU;AAAA,UACR,aAAY;AAAA;AAAA,MAChB,GACA,gBAAAA,OAAA;AAAA,QAAC;AAAA;AAAA,UACC,MAAK;AAAA,UACL,SAAS,MAAM,gBAAgB,CAAC,YAAY;AAAA,UAC5C,WAAU;AAAA;AAAA,QAET,eACC,gBAAAA,OAAA,cAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,eACjE,gBAAAA,OAAA,cAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,oCAAmC,GACxG,gBAAAA,OAAA,cAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,2HAA0H,CACjM,IAEA,gBAAAA,OAAA,cAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,eACjE,gBAAAA,OAAA,cAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,4SAA2S,CAClX;AAAA,MAEJ,CACF,CACF,GAEA,gBAAAA,OAAA,cAAC,SAAI,WAAU,uCACb,gBAAAA,OAAA,cAAC,SAAI,WAAU,uBACb,gBAAAA,OAAA;AAAA,QAAC;AAAA;AAAA,UACC,IAAG;AAAA,UACH,MAAK;AAAA,UACL,MAAK;AAAA,UACL,SAAS;AAAA,UACT,UAAU,CAAC,MAAM,cAAc,EAAE,OAAO,OAAO;AAAA,UAC/C,WAAU;AAAA,UACV,OAAO,EAAE,OAAO,OAAO,aAAa;AAAA;AAAA,MACtC,GACA,gBAAAA,OAAA,cAAC,WAAM,SAAQ,eAAc,WAAU,sCAAmC,aAE1E,CACF,GAEA,gBAAAA,OAAA,cAAC,SAAI,WAAU,aACb,gBAAAA,OAAA;AAAA,QAAC;AAAA;AAAA,UACC,MAAM;AAAA,UACN,WAAU;AAAA,UACV,OAAO,EAAE,OAAO,OAAO,aAAa;AAAA;AAAA,QACrC;AAAA,MAED,CACF,CACF,GAEA,gBAAAA,OAAA,cAAC,aACC,gBAAAA,OAAA;AAAA,QAAC;AAAA;AAAA,UACC,MAAK;AAAA,UACL,UAAU;AAAA,UACV,WAAU;AAAA,UACV,OAAO;AAAA,YACL,iBAAiB,OAAO;AAAA,UAC1B;AAAA;AAAA,QAEC,YACC,gBAAAA,OAAA,cAAAA,OAAA,gBACE,gBAAAA,OAAA,cAAC,SAAI,WAAU,8CAA6C,MAAK,QAAO,SAAQ,eAC9E,gBAAAA,OAAA,cAAC,YAAO,WAAU,cAAa,IAAG,MAAK,IAAG,MAAK,GAAE,MAAK,QAAO,gBAAe,aAAY,KAAI,GAC5F,gBAAAA,OAAA,cAAC,UAAK,WAAU,cAAa,MAAK,gBAAe,GAAE,mHAAkH,CACvK,GAAM,eAER,IAEA;AAAA,MAEJ,CACF,CACF;AAAA,MAEC,oBACC,gBAAAA,OAAA,cAAC,SAAI,WAAU,UACb,gBAAAA,OAAA,cAAC,SAAI,WAAU,cACb,gBAAAA,OAAA,cAAC,SAAI,WAAU,wCACb,gBAAAA,OAAA,cAAC,SAAI,WAAU,mCAAkC,CACnD,GACA,gBAAAA,OAAA,cAAC,SAAI,WAAU,0CACb,gBAAAA,OAAA,cAAC,UAAK,WAAU,iCAA8B,WAAQ,SAAQ,GAAC,CACjE,CACF,GAEA,gBAAAA,OAAA,cAAC,SAAI,WAAU,sBACb,gBAAAA,OAAA;AAAA,QAAC;AAAA;AAAA,UACC,MAAM;AAAA,UACN,WAAU;AAAA,UACV,OAAO,EAAE,OAAO,OAAO,aAAa;AAAA;AAAA,QACrC;AAAA,MAED,CACF,CACF;AAAA,MAIF,gBAAAA,OAAA,cAAC,SAAI,WAAU,oBACb,gBAAAA,OAAA,cAAC,SAAI,WAAU,cACb,gBAAAA,OAAA,cAAC,SAAI,WAAU,wCACb,gBAAAA,OAAA,cAAC,SAAI,WAAU,mCAAkC,CACnD,GACA,gBAAAA,OAAA,cAAC,SAAI,WAAU,0CACb,gBAAAA,OAAA,cAAC,UAAK,WAAU,iCAA8B,kBAAgB,CAChE,CACF,GAEA,gBAAAA,OAAA;AAAA,QAAC;AAAA;AAAA,UACC,MAAK;AAAA,UACL,WAAU;AAAA,UACV,SAAS,MAAM,OAAO,SAAS,OAAO;AAAA;AAAA,QAEtC,gBAAAA,OAAA,cAAC,SAAI,WAAU,gBAAe,SAAQ,eACpC,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,oDAAkD,GACzE,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,0BAAwB,GAC/C,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,gCAA8B,CACvD;AAAA,QAAM;AAAA,MAER,GAEA,gBAAAA,OAAA;AAAA,QAAC;AAAA;AAAA,UACC,MAAK;AAAA,UACL,WAAU;AAAA,UACV,SAAS,MAAM,OAAO,SAAS,OAAO;AAAA;AAAA,QAEtC,gBAAAA,OAAA,cAAC,SAAI,WAAU,gBAAe,SAAQ,eACpC,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,2HAAyH,GAChJ,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,yIAAuI,GAC9J,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,iIAA+H,GACtJ,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,uIAAqI,CAC9J;AAAA,QAAM;AAAA,MAER,CACF;AAAA,IACF,GAEA,gBAAAA,OAAA,cAAC,OAAE,WAAU,4CAAyC,mCACpB,KAChC,gBAAAA,OAAA,cAAC,OAAE,MAAK,UAAS,WAAU,mCAAgC,kBAAgB,GAC1E,KAAI,OAAI,KACT,gBAAAA,OAAA,cAAC,OAAE,MAAK,YAAW,WAAU,mCAAgC,gBAAc,CAC7E,CACF;AAAA,EACF;AAEJ;;;AE1QA,OAAOC,UAAS,YAAAC,iBAAgB;AAqBzB,IAAM,qBAAwD,CAAC;AAAA,EACpE;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,QAAQ;AAAA,EACR;AACF,MAAM;AACJ,QAAM,CAAC,QAAQ,SAAS,IAAIC,UAAS,KAAK;AAC1C,QAAM,EAAE,MAAM,YAAY,iBAAiB,IAAI,eAAQ;AAGvD,QAAM,iBAAiB,KAAK,OAAO,SAAO;AACxC,QAAI,IAAI,SAAS,CAAC,WAAW,IAAI,KAAK,GAAG;AACvC,aAAO;AAAA,IACT;AACA,QAAI,IAAI,eAAe,CAAC,iBAAiB,IAAI,WAAW,GAAG;AACzD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,CAAC;AAED,QAAM,iBAAiB,CAAC,QAAiB;AACvC,QAAI,IAAI,OAAO,YAAY;AACzB,gBAAU,KAAK;AACf;AAAA,IACF;AAEA,kBAAc,GAAG;AAGjB,QAAI,IAAI,IAAI,WAAW,MAAM,GAAG;AAE9B,aAAO,SAAS,OAAO,IAAI;AAAA,IAC7B,OAAO;AAEL,aAAO,SAAS,OAAO,IAAI;AAAA,IAC7B;AAAA,EACF;AAEA,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,WAAW;AAAA,IACb;AAAA,IACA,MAAM;AAAA,MACJ,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,WAAW;AAAA,IACb;AAAA,EACF,EAAE,KAAK;AAEP,MAAI,aAAa,OAAO;AACtB,WACE,gBAAAC,OAAA,cAAC,SAAI,WAAW,GAAG,aAAa,UAAU,aAAa,aAAa,MAAM,MACxE,gBAAAA,OAAA,cAAC,SAAI,WAAU,4CACb,gBAAAA,OAAA,cAAC,SAAI,WAAU,4CACb,gBAAAA,OAAA,cAAC,SAAI,WAAU,iCACb,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC,SAAS,MAAM,UAAU,CAAC,MAAM;AAAA,QAChC,WAAW,oDAAoD,aAAa,KAAK,IAAI,aAAa,IAAI;AAAA;AAAA,MAEtG,gBAAAA,OAAA,cAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,eACjE,gBAAAA,OAAA,cAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,2BAA0B,CACjG;AAAA,MACA,gBAAAA,OAAA,cAAC,UAAK,WAAU,iBAAc,MAAI;AAAA,IACpC,GAEA,gBAAAA,OAAA,cAAC,UAAK,WAAW,WAAW,aAAa,IAAI,MAAI,aACtC,gBAAAA,OAAA,cAAC,UAAK,WAAU,iBAAe,UAAW,CACrD,CACF,GAEA,gBAAAA,OAAA,cAAC,SAAI,WAAU,iCACb,gBAAAA,OAAA,cAAC,UAAK,WAAW,WAAW,aAAa,IAAI,MAC1C,MAAM,WAAU,KAAE,MAAM,QAC3B,CACF,CACF,CACF,GAGC,UACC,gBAAAA,OAAA,cAAC,SAAI,WAAU,kDACb,gBAAAA,OAAA,cAAC,SAAI,WAAW,cAAc,aAAa,UAAU,uCACnD,gBAAAA,OAAA,cAAC,SAAI,WAAU,UACZ,eAAe,IAAI,SAClB,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC,KAAK,IAAI;AAAA,QACT,SAAS,MAAM,eAAe,GAAG;AAAA,QACjC,WAAW;AAAA;AAAA,wBAEP,IAAI,OAAO,aAAa,aAAa,YAAY,aAAa,IAAI;AAAA,wBAClE,aAAa,KAAK;AAAA;AAAA;AAAA,MAGtB,gBAAAA,OAAA,cAAC,SAAI,WAAU,uBACZ,IAAI,QAAQ,gBAAAA,OAAA,cAAC,SAAI,WAAU,UAAQ,IAAI,IAAK,GAC7C,gBAAAA,OAAA,cAAC,aACC,gBAAAA,OAAA,cAAC,SAAI,WAAU,iBAAe,IAAI,IAAK,GACtC,IAAI,eACH,gBAAAA,OAAA,cAAC,SAAI,WAAU,wBAAsB,IAAI,WAAY,CAEzD,CACF;AAAA,IACF,CACD,CACH,CACF,CACF,CAEJ;AAAA,EAEJ;AAGA,QAAM,cAAc,aAAa,SAAS,WAAW;AAErD,SACE,gBAAAA,OAAA,cAAAA,OAAA,gBAEE,gBAAAA,OAAA;AAAA,IAAC;AAAA;AAAA,MACC,SAAS,MAAM,UAAU,CAAC,MAAM;AAAA,MAChC,WAAW;AAAA,kBACD,QAAQ;AAAA,YACd,aAAa,UAAU,IAAI,aAAa,IAAI;AAAA,qCACnB,aAAa,KAAK;AAAA;AAAA;AAAA,IAG/C,gBAAAA,OAAA,cAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,eACjE,gBAAAA,OAAA,cAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,0BAAyB,CAChG;AAAA,EACF,GAGC,UACC,gBAAAA,OAAA,cAAAA,OAAA,gBAEE,gBAAAA,OAAA;AAAA,IAAC;AAAA;AAAA,MACC,WAAU;AAAA,MACV,SAAS,MAAM,UAAU,KAAK;AAAA;AAAA,EAChC,GAGA,gBAAAA,OAAA,cAAC,SAAI,WAAW;AAAA,oBACN,WAAW;AAAA,cACjB,aAAa,UAAU;AAAA;AAAA,cAEvB,SAAS,kBAAkB,aAAa,SAAS,sBAAsB,kBAAkB;AAAA,eAE3F,gBAAAA,OAAA,cAAC,SAAI,WAAU,SACb,gBAAAA,OAAA,cAAC,SAAI,WAAU,4CACb,gBAAAA,OAAA,cAAC,QAAG,WAAW,yBAAyB,aAAa,IAAI,MAAI,cAE7D,GACA,gBAAAA,OAAA;AAAA,IAAC;AAAA;AAAA,MACC,SAAS,MAAM,UAAU,KAAK;AAAA,MAC9B,WAAW,GAAG,aAAa,IAAI,IAAI,aAAa,KAAK;AAAA;AAAA,IAErD,gBAAAA,OAAA,cAAC,SAAI,WAAU,WAAU,MAAK,QAAO,QAAO,gBAAe,SAAQ,eACjE,gBAAAA,OAAA,cAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,aAAa,GAAG,GAAE,wBAAuB,CAC9F;AAAA,EACF,CACF,GAEA,gBAAAA,OAAA,cAAC,SAAI,WAAU,eACZ,eAAe,IAAI,SAClB,gBAAAA,OAAA;AAAA,IAAC;AAAA;AAAA,MACC,KAAK,IAAI;AAAA,MACT,SAAS,MAAM,eAAe,GAAG;AAAA,MACjC,WAAW;AAAA;AAAA,wBAEP,IAAI,OAAO,aAAa,aAAa,YAAY,aAAa,IAAI;AAAA,wBAClE,aAAa,KAAK;AAAA;AAAA;AAAA,IAGtB,gBAAAA,OAAA,cAAC,SAAI,WAAU,uBACZ,IAAI,QAAQ,gBAAAA,OAAA,cAAC,SAAI,WAAU,UAAQ,IAAI,IAAK,GAC7C,gBAAAA,OAAA,cAAC,aACC,gBAAAA,OAAA,cAAC,SAAI,WAAU,iBAAe,IAAI,IAAK,GACtC,IAAI,eACH,gBAAAA,OAAA,cAAC,SAAI,WAAU,wBAAsB,IAAI,WAAY,CAEzD,CACF;AAAA,EACF,CACD,CACH,GAEA,gBAAAA,OAAA,cAAC,SAAI,WAAW,sBAAsB,aAAa,MAAM,MACvD,gBAAAA,OAAA,cAAC,SAAI,WAAW,WAAW,aAAa,IAAI,MAC1C,gBAAAA,OAAA,cAAC,SAAI,WAAU,iBAAe,MAAM,WAAU,KAAE,MAAM,QAAS,GAC/D,gBAAAA,OAAA,cAAC,SAAI,WAAU,wBAAsB,MAAM,KAAM,GACjD,gBAAAA,OAAA,cAAC,SAAI,WAAU,UACb,gBAAAA,OAAA,cAAC,UAAK,WAAU,4DACb,MAAM,MAAM,CAAC,CAChB,CACF,CACF,CACF,CACF,CACF,CACF,CAEJ;AAEJ;;;ACvOA,OAAOC,YAAW;;;ACAlB,OAAO,WAA8B;;;AC8D9B,IAAM,YAAN,cAAwB,MAAM;AAAA,EACnC,YACE,SACO,MACA,YACA,SACP;AACA,UAAM,OAAO;AAJN;AACA;AACA;AAGP,SAAK,OAAO;AAAA,EACd;AACF;;;ADxCO,IAAM,cAAN,MAAkB;AAAA,EAMvB,YAAY,YAAwB,aAA0B;AAF9D,SAAQ,WAAW;AAGjB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,MAAM,MAAM,OAAO;AAAA,MACtB,SAAS,WAAW;AAAA,MACpB,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc,UAAiC;AACnD,UAAM,iBAAiB,KAAK,OAAO,UAAU,QAAQ;AACrD,QAAI,CAAC,gBAAgB;AACnB,YAAM,IAAI,UAAU,kBAAkB,QAAQ,mBAAmB,yBAAyB;AAAA,IAC5F;AAGA,UAAM,QAAQ,KAAK,cAAc;AAGjC,UAAM,eAAe,KAAK,qBAAqB;AAC/C,UAAM,gBAAgB,MAAM,KAAK,sBAAsB,YAAY;AAGnE,UAAM,aAAyB;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,KAAK,IAAI;AAAA,IACtB;AAEA,iBAAa,QAAQ,KAAK,UAAU,KAAK,UAAU,UAAU,CAAC;AAG9D,UAAM,SAAS,IAAI,gBAAgB;AAAA,MACjC,WAAW,eAAe;AAAA,MAC1B,cAAc,eAAe;AAAA,MAC7B,eAAe;AAAA,MACf,OAAO,eAAe;AAAA,MACtB;AAAA,MACA,aAAa;AAAA;AAAA,MACb,QAAQ;AAAA,IACV,CAAC;AAGD,QAAI,aAAa,YAAY,aAAa,UAAU;AAClD,aAAO,OAAO,kBAAkB,aAAa;AAC7C,aAAO,OAAO,yBAAyB,MAAM;AAAA,IAC/C;AAGA,WAAO,SAAS,OAAO,GAAG,eAAe,OAAO,IAAI,OAAO,SAAS,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,MAAc,OAA4D;AAE7F,UAAM,iBAAiB,aAAa,QAAQ,KAAK,QAAQ;AACzD,QAAI,CAAC,gBAAgB;AACnB,YAAM,IAAI,UAAU,wBAAwB,eAAe;AAAA,IAC7D;AAEA,UAAM,cAA0B,KAAK,MAAM,cAAc;AAGzD,iBAAa,WAAW,KAAK,QAAQ;AAGrC,QAAI,YAAY,UAAU,OAAO;AAC/B,YAAM,IAAI,UAAU,uBAAuB,eAAe;AAAA,IAC5D;AAEA,QAAI,KAAK,IAAI,IAAI,YAAY,YAAY,IAAI,KAAK,KAAM;AACtD,YAAM,IAAI,UAAU,uBAAuB,eAAe;AAAA,IAC5D;AAEA,UAAM,iBAAiB,KAAK,OAAO,UAAU,YAAY,QAAQ;AACjE,QAAI,CAAC,gBAAgB;AACnB,YAAM,IAAI,UAAU,iCAAiC,yBAAyB;AAAA,IAChF;AAEA,QAAI;AAEF,YAAM,gBAAgB,MAAM,KAAK;AAAA,QAC/B;AAAA,QACA,YAAY;AAAA,QACZ;AAAA,QACA,YAAY;AAAA,MACd;AAGA,YAAM,WAAW,MAAM,KAAK;AAAA,QAC1B,cAAc;AAAA,QACd;AAAA,MACF;AAGA,YAAM,WAAW,MAAM,KAAK,IAAI,KAAK,GAAG,KAAK,WAAW,YAAY,mBAAmB;AAAA,QACrF,UAAU,YAAY;AAAA,QACtB,aAAa,cAAc;AAAA,QAC3B,cAAc,cAAc;AAAA,QAC5B,SAAS,cAAc;AAAA,QACvB;AAAA,QACA,WAAW,cAAc;AAAA,MAC3B,CAAC;AAED,YAAM,EAAE,MAAM,aAAa,cAAc,UAAU,IAAI,SAAS;AAEhE,YAAM,SAAqB;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,MACb;AAGA,WAAK,OAAO,YAAY,MAAM,MAAM;AAEpC,aAAO,EAAE,MAAM,OAAO;AAAA,IACxB,SAAS,OAAY;AACnB,YAAM,YAAY,IAAI;AAAA,QACpB,MAAM,UAAU,MAAM,WAAW;AAAA,QACjC,MAAM,UAAU,MAAM,QAAQ;AAAA,QAC9B,MAAM,UAAU;AAAA,MAClB;AAEA,WAAK,OAAO,UAAU,SAAS;AAC/B,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,sBACZ,MACA,UACA,gBACA,cACc;AACd,UAAM,SAAc;AAAA,MAClB,YAAY;AAAA,MACZ;AAAA,MACA,WAAW,eAAe;AAAA,MAC1B,cAAc,eAAe;AAAA,IAC/B;AAGA,QAAI,eAAe,cAAc;AAC/B,aAAO,gBAAgB,eAAe;AAAA,IACxC;AAGA,QAAI,iBAAiB,aAAa,YAAY,aAAa,WAAW;AACpE,aAAO,gBAAgB;AAAA,IACzB;AAEA,UAAM,WAAW,MAAM,MAAM,KAAK,eAAe,UAAU,QAAQ;AAAA,MACjE,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAED,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,YAAY,aAAqB,gBAA6C;AAC1F,UAAM,WAAW,MAAM,MAAM,IAAI,eAAe,aAAa;AAAA,MAC3D,SAAS;AAAA,QACP,iBAAiB,UAAU,WAAW;AAAA,MACxC;AAAA,IACF,CAAC;AAED,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,YAAY,UAAkB,QAA+B;AAEjE,UAAM,eAAe;AAAA,MACnB;AAAA,MACA,QAAQ;AAAA,MACR,WAAW,KAAK,IAAI;AAAA,IACtB;AAEA,iBAAa,QAAQ,iBAAiB,KAAK,UAAU,YAAY,CAAC;AAGlE,UAAM,KAAK,cAAc,QAAQ;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc,UAAkB,QAA+B;AACnE,UAAM,KAAK,IAAI,KAAK,GAAG,KAAK,WAAW,YAAY,iBAAiB;AAAA,MAClE;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,kBAAkB,QAAmC;AACzD,UAAM,WAAW,MAAM,KAAK,IAAI,IAAI,GAAG,KAAK,WAAW,YAAY,iBAAiB,MAAM,EAAE;AAC5F,WAAO,SAAS,KAAK;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,mBAAmB,UAAkB,cAA2C;AACpF,UAAM,iBAAiB,KAAK,OAAO,UAAU,QAAQ;AACrD,QAAI,CAAC,gBAAgB;AACnB,YAAM,IAAI,UAAU,iCAAiC,yBAAyB;AAAA,IAChF;AAEA,UAAM,SAAc;AAAA,MAClB,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,WAAW,eAAe;AAAA,IAC5B;AAEA,QAAI,eAAe,cAAc;AAC/B,aAAO,gBAAgB,eAAe;AAAA,IACxC;AAEA,UAAM,WAAW,MAAM,MAAM,KAAK,eAAe,UAAU,QAAQ;AAAA,MACjE,SAAS;AAAA,QACP,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAED,WAAO;AAAA,MACL,aAAa,SAAS,KAAK;AAAA,MAC3B,cAAc,SAAS,KAAK,iBAAiB;AAAA,MAC7C,WAAW,SAAS,KAAK;AAAA,MACzB,WAAW;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKQ,gBAAwB;AAC9B,UAAM,QAAQ,IAAI,WAAW,EAAE;AAC/B,WAAO,gBAAgB,KAAK;AAC5B,WAAO,MAAM,KAAK,OAAO,UAAQ,KAAK,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA,EAKQ,uBAA+B;AACrC,UAAM,QAAQ,IAAI,WAAW,EAAE;AAC/B,WAAO,gBAAgB,KAAK;AAC5B,WAAO,KAAK,gBAAgB,KAAK;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,sBAAsB,UAAmC;AACrE,UAAM,UAAU,IAAI,YAAY;AAChC,UAAM,OAAO,QAAQ,OAAO,QAAQ;AACpC,UAAM,SAAS,MAAM,OAAO,OAAO,OAAO,WAAW,IAAI;AACzD,WAAO,KAAK,gBAAgB,IAAI,WAAW,MAAM,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAKQ,gBAAgB,QAA4B;AAClD,UAAM,SAAS,KAAK,OAAO,aAAa,GAAG,MAAM,CAAC;AAClD,WAAO,OACJ,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,GAAG,EAClB,QAAQ,MAAM,EAAE;AAAA,EACrB;AACF;AAGO,IAAM,wBAA2D;AAAA,EACtE,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,UAAU,QAAQ,IAAI,yBAAyB;AAAA,IAC/C,cAAc,QAAQ,IAAI;AAAA,IAC1B,OAAO;AAAA,IACP,aAAa,GAAG,OAAO,SAAS,MAAM;AAAA,EACxC;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,UAAU,QAAQ,IAAI,yBAAyB;AAAA,IAC/C,cAAc,QAAQ,IAAI;AAAA,IAC1B,OAAO;AAAA,IACP,aAAa,GAAG,OAAO,SAAS,MAAM;AAAA,EACxC;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,UAAU,QAAQ,IAAI,4BAA4B;AAAA,IAClD,cAAc,QAAQ,IAAI;AAAA,IAC1B,OAAO;AAAA,IACP,aAAa,GAAG,OAAO,SAAS,MAAM;AAAA,EACxC;AACF;;;AD9VA,IAAM,gBAAgB;AAAA,EACpB,QACE,gBAAAC,OAAA,cAAC,SAAI,WAAU,gBAAe,SAAQ,eACpC,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,2HAAyH,GAChJ,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,yIAAuI,GAC9J,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,iIAA+H,GACtJ,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,uIAAqI,CAC9J;AAAA,EAEF,QACE,gBAAAA,OAAA,cAAC,SAAI,WAAU,gBAAe,MAAK,gBAAe,SAAQ,eACxD,gBAAAA,OAAA,cAAC,UAAK,GAAE,6sBAA2sB,CACrtB;AAAA,EAEF,WACE,gBAAAA,OAAA,cAAC,SAAI,WAAU,gBAAe,SAAQ,eACpC,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,oDAAkD,GACzE,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,0BAAwB,GAC/C,gBAAAA,OAAA,cAAC,UAAK,MAAK,WAAU,GAAE,gCAA8B,CACvD;AAEJ;AAEA,IAAM,gBAAgB;AAAA,EACpB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,WAAW;AACb;AAEO,IAAM,eAA4C,CAAC;AAAA,EACxD,YAAY,CAAC,UAAU,UAAU,WAAW;AAAA,EAC5C;AAAA,EACA,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,cAAc;AAChB,MAAM;AACJ,QAAM,mBAAmB,OAAO,aAAqB;AACnD,QAAI;AACF,YAAM,cAAc,IAAI,YAAY,YAAY;AAAA,QAC9C,WAAW;AAAA,MACb,CAAC;AAED,YAAM,YAAY,cAAc,QAAQ;AAAA,IAC1C,SAAS,OAAO;AACd,cAAQ,MAAM,0BAA0B,QAAQ,KAAK,KAAK;AAAA,IAC5D;AAAA,EACF;AAEA,SACE,gBAAAA,OAAA,cAAC,SAAI,aACF,eACC,gBAAAA,OAAA,cAAC,SAAI,WAAU,mBACb,gBAAAA,OAAA,cAAC,SAAI,WAAU,wCACb,gBAAAA,OAAA,cAAC,SAAI,WAAU,mCAAkC,CACnD,GACA,gBAAAA,OAAA,cAAC,SAAI,WAAU,0CACb,gBAAAA,OAAA,cAAC,UAAK,WAAU,iCAA+B,WAAY,CAC7D,CACF,GAGF,gBAAAA,OAAA,cAAC,SAAI,WAAU,eACZ,UAAU,IAAI,CAAC,aACd,gBAAAA,OAAA;AAAA,IAAC;AAAA;AAAA,MACC,KAAK;AAAA,MACL,MAAK;AAAA,MACL,SAAS,MAAM,iBAAiB,QAAQ;AAAA,MACxC,WAAW,oOAAoO,eAAe;AAAA;AAAA,IAE7P,cAAc,QAAQ;AAAA,IAAE;AAAA,IACX,cAAc,QAAQ;AAAA,EACtC,CACD,CACH,CACF;AAEJ;", "names": ["React", "useLocation", "React", "useLocation", "React", "React", "React", "React", "React", "useState", "useState", "React", "React", "React"]}