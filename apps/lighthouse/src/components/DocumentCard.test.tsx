import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, waitFor } from '@testing-library/react'
import { render } from '../test/test-utils'
import { DocumentCard } from './DocumentCard'
import type { ProcessedDocument } from '../types'

// Mock fetch globally
global.fetch = vi.fn()

describe('DocumentCard', () => {
  const mockDocument: ProcessedDocument = {
    id: 'doc-1',
    name: 'Research Paper.pdf',
    type: 'pdf',
    size: 2097152, // 2 MB
    createdAt: '2024-01-15T10:00:00Z',
    tags: ['research', 'ai', 'machine-learning'],
    content: 'Document content here',
    metadata: {},
    url: 'https://example.com/doc.pdf',
    processedAt: '2024-01-15T10:05:00Z',
  }

  const mockSummaryResponse = {
    summary: 'This research paper explores advanced machine learning techniques.',
    keyTakeaways: [
      'Neural networks show significant improvement',
      'Data preprocessing is crucial',
      'Model interpretability remains challenging'
    ],
    topics: ['Neural Networks', 'Data Science', 'AI Ethics'],
    readingTime: 15
  }

  const defaultProps = {
    document: mockDocument,
    onDelete: vi.fn(),
    onSelect: vi.fn(),
    selected: false,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(global.fetch as any).mockReset()
  })

  it('renders document information correctly', () => {
    render(<DocumentCard {...defaultProps} />)

    expect(screen.getByText('Research Paper.pdf')).toBeInTheDocument()
    expect(screen.getByText('Type: PDF')).toBeInTheDocument()
    expect(screen.getByText('Size: 2.00 MB')).toBeInTheDocument()
    expect(screen.getByText('Added: 1/15/2024')).toBeInTheDocument()
  })

  it('renders document tags', () => {
    render(<DocumentCard {...defaultProps} />)

    expect(screen.getByText('research')).toBeInTheDocument()
    expect(screen.getByText('ai')).toBeInTheDocument()
    expect(screen.getByText('machine-learning')).toBeInTheDocument()
  })

  it('handles document selection on click', async () => {
    const { user } = render(<DocumentCard {...defaultProps} />)

    const card = screen.getByRole('article')
    await user.click(card)

    expect(defaultProps.onSelect).toHaveBeenCalledWith('doc-1')
  })

  it('shows selected state with ring styling', () => {
    render(<DocumentCard {...defaultProps} selected={true} />)

    const card = screen.getByRole('article')
    expect(card).toHaveClass('ring-2', 'ring-blue-500', 'shadow-lg')
  })

  it('generates and displays summary on button click', async () => {
    ;(global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockSummaryResponse
    })

    const { user } = render(<DocumentCard {...defaultProps} />)

    const summaryButton = screen.getByRole('button', { name: /view summary/i })
    await user.click(summaryButton)

    expect(screen.getByText(/generating/i)).toBeInTheDocument()

    await waitFor(() => {
      expect(screen.getByText(mockSummaryResponse.summary)).toBeInTheDocument()
    })

    expect(global.fetch).toHaveBeenCalledWith('/api/ai/summarize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ documentId: 'doc-1' })
    })
  })

  it('toggles summary visibility', async () => {
    ;(global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockSummaryResponse
    })

    const { user } = render(<DocumentCard {...defaultProps} />)

    // First click to generate and show
    const summaryButton = screen.getByRole('button', { name: /view summary/i })
    await user.click(summaryButton)

    await waitFor(() => {
      expect(screen.getByText(mockSummaryResponse.summary)).toBeInTheDocument()
    })

    // Button text should change
    expect(screen.getByRole('button', { name: /hide summary/i })).toBeInTheDocument()

    // Second click to hide
    await user.click(screen.getByRole('button', { name: /hide summary/i }))

    expect(screen.queryByText(mockSummaryResponse.summary)).not.toBeInTheDocument()
  })

  it('displays key takeaways in summary', async () => {
    ;(global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockSummaryResponse
    })

    const { user } = render(<DocumentCard {...defaultProps} />)

    const summaryButton = screen.getByRole('button', { name: /view summary/i })
    await user.click(summaryButton)

    await waitFor(() => {
      mockSummaryResponse.keyTakeaways.forEach(takeaway => {
        expect(screen.getByText(takeaway)).toBeInTheDocument()
      })
    })
  })

  it('displays topics in summary', async () => {
    ;(global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockSummaryResponse
    })

    const { user } = render(<DocumentCard {...defaultProps} />)

    const summaryButton = screen.getByRole('button', { name: /view summary/i })
    await user.click(summaryButton)

    await waitFor(() => {
      mockSummaryResponse.topics.forEach(topic => {
        expect(screen.getByText(topic)).toBeInTheDocument()
      })
    })
  })

  it('displays reading time in summary', async () => {
    ;(global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockSummaryResponse
    })

    const { user } = render(<DocumentCard {...defaultProps} />)

    const summaryButton = screen.getByRole('button', { name: /view summary/i })
    await user.click(summaryButton)

    await waitFor(() => {
      expect(screen.getByText(/estimated reading time: 15 minutes/i)).toBeInTheDocument()
    })
  })

  it('handles summary generation error', async () => {
    ;(global.fetch as any).mockRejectedValueOnce(new Error('Network error'))
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    const { user } = render(<DocumentCard {...defaultProps} />)

    const summaryButton = screen.getByRole('button', { name: /view summary/i })
    await user.click(summaryButton)

    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to generate summary:', expect.any(Error))
    })

    // Button should go back to normal state
    expect(screen.getByRole('button', { name: /view summary/i })).toBeInTheDocument()

    consoleErrorSpy.mockRestore()
  })

  it('handles delete button click', async () => {
    const { user } = render(<DocumentCard {...defaultProps} />)

    const deleteButton = screen.getByRole('button', { name: /delete/i })
    await user.click(deleteButton)

    expect(defaultProps.onDelete).toHaveBeenCalledWith('doc-1')
  })

  it('prevents event propagation on delete', async () => {
    const { user } = render(<DocumentCard {...defaultProps} />)

    const deleteButton = screen.getByRole('button', { name: /delete/i })
    await user.click(deleteButton)

    // Should not trigger selection
    expect(defaultProps.onSelect).not.toHaveBeenCalled()
  })

  it('prevents event propagation on summary generation', async () => {
    const { user } = render(<DocumentCard {...defaultProps} />)

    const summaryButton = screen.getByRole('button', { name: /view summary/i })
    await user.click(summaryButton)

    // Should not trigger selection
    expect(defaultProps.onSelect).not.toHaveBeenCalled()
  })

  it('does not render delete button when onDelete is not provided', () => {
    render(<DocumentCard document={mockDocument} />)

    expect(screen.queryByRole('button', { name: /delete/i })).not.toBeInTheDocument()
  })

  it('handles document with missing type', () => {
    const docWithoutType = { ...mockDocument, type: undefined }
    render(<DocumentCard {...defaultProps} document={docWithoutType} />)

    expect(screen.getByText('Type: N/A')).toBeInTheDocument()
  })

  it('handles document with no tags', () => {
    const docWithoutTags = { ...mockDocument, tags: [] }
    render(<DocumentCard {...defaultProps} document={docWithoutTags} />)

    // Should render without errors
    expect(screen.getByText('Research Paper.pdf')).toBeInTheDocument()
  })

  it('formats large file sizes correctly', () => {
    const largeDoc = { ...mockDocument, size: 157286400 } // 150 MB
    render(<DocumentCard {...defaultProps} document={largeDoc} />)

    expect(screen.getByText('Size: 150.00 MB')).toBeInTheDocument()
  })

  it('formats small file sizes correctly', () => {
    const smallDoc = { ...mockDocument, size: 524288 } // 0.5 MB
    render(<DocumentCard {...defaultProps} document={smallDoc} />)

    expect(screen.getByText('Size: 0.50 MB')).toBeInTheDocument()
  })

  it('caches summary after first generation', async () => {
    ;(global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: async () => mockSummaryResponse
    })

    const { user } = render(<DocumentCard {...defaultProps} />)

    // First click to generate
    const summaryButton = screen.getByRole('button', { name: /view summary/i })
    await user.click(summaryButton)

    await waitFor(() => {
      expect(screen.getByText(mockSummaryResponse.summary)).toBeInTheDocument()
    })

    // Hide summary
    await user.click(screen.getByRole('button', { name: /hide summary/i }))

    // Show again - should not fetch
    await user.click(screen.getByRole('button', { name: /view summary/i }))

    // Fetch should only be called once
    expect(global.fetch).toHaveBeenCalledTimes(1)
    expect(screen.getByText(mockSummaryResponse.summary)).toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(<DocumentCard {...defaultProps} className="custom-class" />)

    const card = screen.getByRole('article')
    expect(card).toHaveClass('custom-class')
  })

  it('links to document detail page', () => {
    render(<DocumentCard {...defaultProps} />)

    const link = screen.getByRole('link')
    expect(link).toHaveAttribute('href', '/documents/doc-1')
  })

  it('handles HTTP error response for summary', async () => {
    ;(global.fetch as any).mockResolvedValueOnce({
      ok: false,
      status: 500
    })

    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    const { user } = render(<DocumentCard {...defaultProps} />)

    const summaryButton = screen.getByRole('button', { name: /view summary/i })
    await user.click(summaryButton)

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /view summary/i })).toBeInTheDocument()
    })

    // Summary should not be displayed
    expect(screen.queryByText(/summary/i)).not.toBeInTheDocument()

    consoleErrorSpy.mockRestore()
  })
})

