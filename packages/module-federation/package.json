{"name": "@luminar/module-federation", "version": "1.0.0", "private": true, "description": "Module Federation configuration and utilities for Luminar micro-frontends", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit"}, "dependencies": {"@originjs/vite-plugin-federation": "^1.3.5"}, "devDependencies": {"@types/node": "^22.5.4", "typescript": "^5.7.2", "vite": "^5.4.15"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}}