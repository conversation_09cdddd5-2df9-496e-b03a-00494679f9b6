"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/index.ts
var components_exports = {};
__export(components_exports, {
  CrossAppNavigation: () => CrossAppNavigation,
  OAuthButtons: () => OAuthButtons,
  PermissionGate: () => PermissionGate,
  ProtectedRoute: () => ProtectedRoute,
  UnifiedLoginPage: () => UnifiedLoginPage
});
module.exports = __toCommonJS(components_exports);

// src/components/ProtectedRoute.tsx
var import_react3 = __toESM(require("react"));
var import_react_router2 = require("@tanstack/react-router");

// src/hooks.ts
var import_react2 = require("react");
var import_react_router = require("@tanstack/react-router");

// src/auth-context.tsx
var import_react = require("react");
var AuthContext = (0, import_react.createContext)(void 0);
var useAuthContext = () => {
  const context = (0, import_react.useContext)(AuthContext);
  if (!context) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
};

// src/hooks.ts
function useRedirectIfAuthenticated(redirectTo = "/dashboard") {
  const { isAuthenticated, isLoading } = useAuthContext();
  const navigate = (0, import_react_router.useNavigate)();
  const location = (0, import_react_router.useLocation)();
  (0, import_react2.useEffect)(() => {
    if (!isLoading && isAuthenticated) {
      const from = location.state?.from || redirectTo;
      navigate({ to: from });
    }
  }, [isAuthenticated, isLoading, navigate, location, redirectTo]);
}
function usePermissions() {
  const {
    user,
    hasRole,
    hasPermission,
    hasAnyRole,
    hasAllRoles,
    hasAnyPermission,
    hasAllPermissions
  } = useAuthContext();
  return {
    user,
    roles: user?.roles || [],
    permissions: user?.permissions || [],
    hasRole,
    hasPermission,
    hasAnyRole,
    hasAllRoles,
    hasAnyPermission,
    hasAllPermissions,
    isAdmin: hasRole("admin"),
    isManager: hasAnyRole(["admin", "manager"]),
    isUser: hasRole("user")
  };
}

// src/components/ProtectedRoute.tsx
var ProtectedRoute = ({
  children,
  roles,
  permissions,
  redirectTo = "/login",
  fallback = /* @__PURE__ */ import_react3.default.createElement("div", null, "Loading...")
}) => {
  const { isAuthenticated, isLoading, hasAllRoles, hasAllPermissions } = useAuthContext();
  const location = (0, import_react_router2.useLocation)();
  if (isLoading) {
    return /* @__PURE__ */ import_react3.default.createElement(import_react3.default.Fragment, null, fallback);
  }
  if (!isAuthenticated) {
    return /* @__PURE__ */ import_react3.default.createElement(import_react_router2.Navigate, { to: redirectTo, replace: true });
  }
  if (roles && !hasAllRoles(roles)) {
    return /* @__PURE__ */ import_react3.default.createElement(import_react_router2.Navigate, { to: "/unauthorized", replace: true });
  }
  if (permissions && !hasAllPermissions(permissions)) {
    return /* @__PURE__ */ import_react3.default.createElement(import_react_router2.Navigate, { to: "/unauthorized", replace: true });
  }
  return /* @__PURE__ */ import_react3.default.createElement(import_react3.default.Fragment, null, children);
};

// src/components/PermissionGate.tsx
var import_react4 = __toESM(require("react"));
var PermissionGate = ({
  children,
  roles,
  permissions,
  requireAll = true,
  fallback = null
}) => {
  const { hasAllRoles, hasAnyRole, hasAllPermissions, hasAnyPermission } = usePermissions();
  let hasAccess = true;
  if (roles) {
    hasAccess = requireAll ? hasAllRoles(roles) : hasAnyRole(roles);
  }
  if (hasAccess && permissions) {
    const hasPerms = requireAll ? hasAllPermissions(permissions) : hasAnyPermission(permissions);
    hasAccess = hasAccess && hasPerms;
  }
  return hasAccess ? /* @__PURE__ */ import_react4.default.createElement(import_react4.default.Fragment, null, children) : /* @__PURE__ */ import_react4.default.createElement(import_react4.default.Fragment, null, fallback);
};

// src/components/UnifiedLoginPage.tsx
var import_react5 = __toESM(require("react"));

// src/utils.ts
var import_jwt_decode = require("jwt-decode");
function getAuthRedirectUrl(defaultUrl = "/dashboard", locationState) {
  if (locationState?.from) {
    return locationState.from;
  }
  const params = new URLSearchParams(window.location.search);
  const redirectParam = params.get("redirect") || params.get("returnUrl");
  if (redirectParam) {
    try {
      const url = new URL(redirectParam, window.location.origin);
      if (url.origin === window.location.origin) {
        return url.pathname + url.search + url.hash;
      }
    } catch {
    }
  }
  return defaultUrl;
}

// src/components/UnifiedLoginPage.tsx
var UnifiedLoginPage = ({
  appName,
  appLogo,
  appDescription,
  showRegisterLink = true,
  registerUrl = "/register",
  forgotPasswordUrl = "/forgot-password",
  customStyles = {},
  onLoginSuccess
}) => {
  const [email, setEmail] = (0, import_react5.useState)("");
  const [password, setPassword] = (0, import_react5.useState)("");
  const [rememberMe, setRememberMe] = (0, import_react5.useState)(false);
  const [showPassword, setShowPassword] = (0, import_react5.useState)(false);
  const { login, isLoading, error } = useAuthContext();
  useRedirectIfAuthenticated(getAuthRedirectUrl());
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await login({ email, password, rememberMe });
      onLoginSuccess?.();
    } catch (err) {
    }
  };
  const styles = {
    primaryColor: customStyles.primaryColor || "#3b82f6",
    backgroundColor: customStyles.backgroundColor || "#f3f4f6",
    cardBackground: customStyles.cardBackground || "#ffffff"
  };
  return /* @__PURE__ */ import_react5.default.createElement(
    "div",
    {
      className: "min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",
      style: { backgroundColor: styles.backgroundColor }
    },
    /* @__PURE__ */ import_react5.default.createElement("div", { className: "max-w-md w-full space-y-8" }, /* @__PURE__ */ import_react5.default.createElement("div", { className: "text-center" }, appLogo && /* @__PURE__ */ import_react5.default.createElement(
      "img",
      {
        className: "mx-auto h-16 w-auto mb-4",
        src: appLogo,
        alt: appName
      }
    ), /* @__PURE__ */ import_react5.default.createElement("h2", { className: "text-3xl font-extrabold text-gray-900" }, "Sign in to ", appName), appDescription && /* @__PURE__ */ import_react5.default.createElement("p", { className: "mt-2 text-sm text-gray-600" }, appDescription)), /* @__PURE__ */ import_react5.default.createElement(
      "div",
      {
        className: "bg-white shadow-xl rounded-lg p-8",
        style: { backgroundColor: styles.cardBackground }
      },
      /* @__PURE__ */ import_react5.default.createElement("form", { className: "space-y-6", onSubmit: handleSubmit }, error && /* @__PURE__ */ import_react5.default.createElement("div", { className: "bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" }, /* @__PURE__ */ import_react5.default.createElement("span", { className: "block sm:inline" }, error.message)), /* @__PURE__ */ import_react5.default.createElement("div", null, /* @__PURE__ */ import_react5.default.createElement("label", { htmlFor: "email", className: "block text-sm font-medium text-gray-700" }, "Email address"), /* @__PURE__ */ import_react5.default.createElement(
        "input",
        {
          id: "email",
          name: "email",
          type: "email",
          autoComplete: "email",
          required: true,
          value: email,
          onChange: (e) => setEmail(e.target.value),
          className: "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm",
          placeholder: "Enter your email"
        }
      )), /* @__PURE__ */ import_react5.default.createElement("div", null, /* @__PURE__ */ import_react5.default.createElement("label", { htmlFor: "password", className: "block text-sm font-medium text-gray-700" }, "Password"), /* @__PURE__ */ import_react5.default.createElement("div", { className: "mt-1 relative" }, /* @__PURE__ */ import_react5.default.createElement(
        "input",
        {
          id: "password",
          name: "password",
          type: showPassword ? "text" : "password",
          autoComplete: "current-password",
          required: true,
          value: password,
          onChange: (e) => setPassword(e.target.value),
          className: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 sm:text-sm",
          placeholder: "Enter your password"
        }
      ), /* @__PURE__ */ import_react5.default.createElement(
        "button",
        {
          type: "button",
          onClick: () => setShowPassword(!showPassword),
          className: "absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
        },
        showPassword ? /* @__PURE__ */ import_react5.default.createElement("svg", { className: "h-5 w-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, /* @__PURE__ */ import_react5.default.createElement("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M15 12a3 3 0 11-6 0 3 3 0 016 0z" }), /* @__PURE__ */ import_react5.default.createElement("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" })) : /* @__PURE__ */ import_react5.default.createElement("svg", { className: "h-5 w-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, /* @__PURE__ */ import_react5.default.createElement("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" }))
      ))), /* @__PURE__ */ import_react5.default.createElement("div", { className: "flex items-center justify-between" }, /* @__PURE__ */ import_react5.default.createElement("div", { className: "flex items-center" }, /* @__PURE__ */ import_react5.default.createElement(
        "input",
        {
          id: "remember-me",
          name: "remember-me",
          type: "checkbox",
          checked: rememberMe,
          onChange: (e) => setRememberMe(e.target.checked),
          className: "h-4 w-4 rounded border-gray-300",
          style: { color: styles.primaryColor }
        }
      ), /* @__PURE__ */ import_react5.default.createElement("label", { htmlFor: "remember-me", className: "ml-2 block text-sm text-gray-900" }, "Remember me")), /* @__PURE__ */ import_react5.default.createElement("div", { className: "text-sm" }, /* @__PURE__ */ import_react5.default.createElement(
        "a",
        {
          href: forgotPasswordUrl,
          className: "font-medium hover:opacity-80",
          style: { color: styles.primaryColor }
        },
        "Forgot your password?"
      ))), /* @__PURE__ */ import_react5.default.createElement("div", null, /* @__PURE__ */ import_react5.default.createElement(
        "button",
        {
          type: "submit",
          disabled: isLoading,
          className: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",
          style: {
            backgroundColor: styles.primaryColor
          }
        },
        isLoading ? /* @__PURE__ */ import_react5.default.createElement(import_react5.default.Fragment, null, /* @__PURE__ */ import_react5.default.createElement("svg", { className: "animate-spin -ml-1 mr-3 h-5 w-5 text-white", fill: "none", viewBox: "0 0 24 24" }, /* @__PURE__ */ import_react5.default.createElement("circle", { className: "opacity-25", cx: "12", cy: "12", r: "10", stroke: "currentColor", strokeWidth: "4" }), /* @__PURE__ */ import_react5.default.createElement("path", { className: "opacity-75", fill: "currentColor", d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" })), "Signing in...") : "Sign in"
      ))),
      showRegisterLink && /* @__PURE__ */ import_react5.default.createElement("div", { className: "mt-6" }, /* @__PURE__ */ import_react5.default.createElement("div", { className: "relative" }, /* @__PURE__ */ import_react5.default.createElement("div", { className: "absolute inset-0 flex items-center" }, /* @__PURE__ */ import_react5.default.createElement("div", { className: "w-full border-t border-gray-300" })), /* @__PURE__ */ import_react5.default.createElement("div", { className: "relative flex justify-center text-sm" }, /* @__PURE__ */ import_react5.default.createElement("span", { className: "px-2 bg-white text-gray-500" }, "New to ", appName, "?"))), /* @__PURE__ */ import_react5.default.createElement("div", { className: "mt-6 text-center" }, /* @__PURE__ */ import_react5.default.createElement(
        "a",
        {
          href: registerUrl,
          className: "font-medium hover:opacity-80",
          style: { color: styles.primaryColor }
        },
        "Create an account"
      ))),
      /* @__PURE__ */ import_react5.default.createElement("div", { className: "mt-6 space-y-3" }, /* @__PURE__ */ import_react5.default.createElement("div", { className: "relative" }, /* @__PURE__ */ import_react5.default.createElement("div", { className: "absolute inset-0 flex items-center" }, /* @__PURE__ */ import_react5.default.createElement("div", { className: "w-full border-t border-gray-300" })), /* @__PURE__ */ import_react5.default.createElement("div", { className: "relative flex justify-center text-sm" }, /* @__PURE__ */ import_react5.default.createElement("span", { className: "px-2 bg-white text-gray-500" }, "Or continue with"))), /* @__PURE__ */ import_react5.default.createElement(
        "button",
        {
          type: "button",
          className: "w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",
          onClick: () => window.location.href = "/auth/sso/microsoft"
        },
        /* @__PURE__ */ import_react5.default.createElement("svg", { className: "w-5 h-5 mr-2", viewBox: "0 0 24 24" }, /* @__PURE__ */ import_react5.default.createElement("path", { fill: "#f25022", d: "M0 0h11.377v11.372H0zm12.623 0H24v11.372H12.623z" }), /* @__PURE__ */ import_react5.default.createElement("path", { fill: "#00a4ef", d: "M0 12.623h11.377V24H0z" }), /* @__PURE__ */ import_react5.default.createElement("path", { fill: "#7fba00", d: "M12.623 12.623H24V24H12.623z" })),
        "Microsoft"
      ), /* @__PURE__ */ import_react5.default.createElement(
        "button",
        {
          type: "button",
          className: "w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",
          onClick: () => window.location.href = "/auth/sso/google"
        },
        /* @__PURE__ */ import_react5.default.createElement("svg", { className: "w-5 h-5 mr-2", viewBox: "0 0 24 24" }, /* @__PURE__ */ import_react5.default.createElement("path", { fill: "#4285f4", d: "M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" }), /* @__PURE__ */ import_react5.default.createElement("path", { fill: "#34a853", d: "M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" }), /* @__PURE__ */ import_react5.default.createElement("path", { fill: "#fbbc05", d: "M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" }), /* @__PURE__ */ import_react5.default.createElement("path", { fill: "#ea4335", d: "M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" })),
        "Google"
      ))
    ), /* @__PURE__ */ import_react5.default.createElement("p", { className: "mt-4 text-center text-xs text-gray-600" }, "By signing in, you agree to our", " ", /* @__PURE__ */ import_react5.default.createElement("a", { href: "/terms", className: "underline hover:text-gray-900" }, "Terms of Service"), " ", "and", " ", /* @__PURE__ */ import_react5.default.createElement("a", { href: "/privacy", className: "underline hover:text-gray-900" }, "Privacy Policy")))
  );
};

// src/components/CrossAppNavigation.tsx
var import_react6 = __toESM(require("react"));
var CrossAppNavigation = ({
  currentApp,
  apps,
  position = "top",
  theme = "light",
  onAppSwitch
}) => {
  const [isOpen, setIsOpen] = (0, import_react6.useState)(false);
  const { user, hasAnyRole, hasAnyPermission } = useAuthContext();
  const accessibleApps = apps.filter((app) => {
    if (app.roles && !hasAnyRole(app.roles)) {
      return false;
    }
    if (app.permissions && !hasAnyPermission(app.permissions)) {
      return false;
    }
    return true;
  });
  const handleAppClick = (app) => {
    if (app.id === currentApp) {
      setIsOpen(false);
      return;
    }
    onAppSwitch?.(app);
    if (app.url.startsWith("http")) {
      window.location.href = app.url;
    } else {
      window.location.href = app.url;
    }
  };
  const themeClasses = {
    light: {
      background: "bg-white",
      text: "text-gray-700",
      hover: "hover:bg-gray-100",
      border: "border-gray-200",
      activeApp: "bg-blue-50 text-blue-700"
    },
    dark: {
      background: "bg-gray-800",
      text: "text-gray-200",
      hover: "hover:bg-gray-700",
      border: "border-gray-600",
      activeApp: "bg-gray-700 text-white"
    }
  }[theme];
  if (position === "top") {
    return /* @__PURE__ */ import_react6.default.createElement("div", { className: `${themeClasses.background} border-b ${themeClasses.border}` }, /* @__PURE__ */ import_react6.default.createElement("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" }, /* @__PURE__ */ import_react6.default.createElement("div", { className: "flex justify-between items-center h-12" }, /* @__PURE__ */ import_react6.default.createElement("div", { className: "flex items-center space-x-4" }, /* @__PURE__ */ import_react6.default.createElement(
      "button",
      {
        onClick: () => setIsOpen(!isOpen),
        className: `flex items-center space-x-2 px-3 py-1 rounded-md ${themeClasses.hover} ${themeClasses.text}`
      },
      /* @__PURE__ */ import_react6.default.createElement("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, /* @__PURE__ */ import_react6.default.createElement("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M4 6h16M4 12h16M4 18h16" })),
      /* @__PURE__ */ import_react6.default.createElement("span", { className: "font-medium" }, "Apps")
    ), /* @__PURE__ */ import_react6.default.createElement("span", { className: `text-sm ${themeClasses.text}` }, "Current: ", /* @__PURE__ */ import_react6.default.createElement("span", { className: "font-medium" }, currentApp))), /* @__PURE__ */ import_react6.default.createElement("div", { className: "flex items-center space-x-4" }, /* @__PURE__ */ import_react6.default.createElement("span", { className: `text-sm ${themeClasses.text}` }, user?.firstName, " ", user?.lastName)))), isOpen && /* @__PURE__ */ import_react6.default.createElement("div", { className: "absolute z-50 mt-1 w-64 rounded-md shadow-lg" }, /* @__PURE__ */ import_react6.default.createElement("div", { className: `rounded-md ${themeClasses.background} ring-1 ring-black ring-opacity-5` }, /* @__PURE__ */ import_react6.default.createElement("div", { className: "py-1" }, accessibleApps.map((app) => /* @__PURE__ */ import_react6.default.createElement(
      "button",
      {
        key: app.id,
        onClick: () => handleAppClick(app),
        className: `
                      w-full text-left px-4 py-2 text-sm
                      ${app.id === currentApp ? themeClasses.activeApp : themeClasses.text}
                      ${themeClasses.hover}
                    `
      },
      /* @__PURE__ */ import_react6.default.createElement("div", { className: "flex items-center" }, app.icon && /* @__PURE__ */ import_react6.default.createElement("div", { className: "mr-3" }, app.icon), /* @__PURE__ */ import_react6.default.createElement("div", null, /* @__PURE__ */ import_react6.default.createElement("div", { className: "font-medium" }, app.name), app.description && /* @__PURE__ */ import_react6.default.createElement("div", { className: "text-xs opacity-75" }, app.description)))
    ))))));
  }
  const sideClasses = position === "left" ? "left-0" : "right-0";
  return /* @__PURE__ */ import_react6.default.createElement(import_react6.default.Fragment, null, /* @__PURE__ */ import_react6.default.createElement(
    "button",
    {
      onClick: () => setIsOpen(!isOpen),
      className: `
          fixed ${position}-4 top-4 z-50
          ${themeClasses.background} ${themeClasses.text}
          p-2 rounded-md shadow-lg ${themeClasses.hover}
        `
    },
    /* @__PURE__ */ import_react6.default.createElement("svg", { className: "w-6 h-6", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, /* @__PURE__ */ import_react6.default.createElement("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M4 6h16M4 12h16m-7 6h7" }))
  ), isOpen && /* @__PURE__ */ import_react6.default.createElement(import_react6.default.Fragment, null, /* @__PURE__ */ import_react6.default.createElement(
    "div",
    {
      className: "fixed inset-0 bg-black bg-opacity-50 z-40",
      onClick: () => setIsOpen(false)
    }
  ), /* @__PURE__ */ import_react6.default.createElement("div", { className: `
            fixed ${sideClasses} top-0 h-full w-64 z-50
            ${themeClasses.background} shadow-xl
            transform transition-transform duration-300
            ${isOpen ? "translate-x-0" : position === "left" ? "-translate-x-full" : "translate-x-full"}
          ` }, /* @__PURE__ */ import_react6.default.createElement("div", { className: "p-4" }, /* @__PURE__ */ import_react6.default.createElement("div", { className: "flex items-center justify-between mb-6" }, /* @__PURE__ */ import_react6.default.createElement("h2", { className: `text-lg font-semibold ${themeClasses.text}` }, "Luminar Apps"), /* @__PURE__ */ import_react6.default.createElement(
    "button",
    {
      onClick: () => setIsOpen(false),
      className: `${themeClasses.text} ${themeClasses.hover} p-1 rounded`
    },
    /* @__PURE__ */ import_react6.default.createElement("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24" }, /* @__PURE__ */ import_react6.default.createElement("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M6 18L18 6M6 6l12 12" }))
  )), /* @__PURE__ */ import_react6.default.createElement("nav", { className: "space-y-1" }, accessibleApps.map((app) => /* @__PURE__ */ import_react6.default.createElement(
    "button",
    {
      key: app.id,
      onClick: () => handleAppClick(app),
      className: `
                      w-full text-left px-3 py-2 rounded-md text-sm
                      ${app.id === currentApp ? themeClasses.activeApp : themeClasses.text}
                      ${themeClasses.hover}
                    `
    },
    /* @__PURE__ */ import_react6.default.createElement("div", { className: "flex items-center" }, app.icon && /* @__PURE__ */ import_react6.default.createElement("div", { className: "mr-3" }, app.icon), /* @__PURE__ */ import_react6.default.createElement("div", null, /* @__PURE__ */ import_react6.default.createElement("div", { className: "font-medium" }, app.name), app.description && /* @__PURE__ */ import_react6.default.createElement("div", { className: "text-xs opacity-75" }, app.description)))
  ))), /* @__PURE__ */ import_react6.default.createElement("div", { className: `mt-8 pt-8 border-t ${themeClasses.border}` }, /* @__PURE__ */ import_react6.default.createElement("div", { className: `text-sm ${themeClasses.text}` }, /* @__PURE__ */ import_react6.default.createElement("div", { className: "font-medium" }, user?.firstName, " ", user?.lastName), /* @__PURE__ */ import_react6.default.createElement("div", { className: "text-xs opacity-75" }, user?.email), /* @__PURE__ */ import_react6.default.createElement("div", { className: "mt-2" }, /* @__PURE__ */ import_react6.default.createElement("span", { className: "text-xs px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded" }, user?.roles[0]))))))));
};

// src/components/OAuthButtons.tsx
var import_react7 = __toESM(require("react"));

// src/oauth-client.ts
var import_axios = __toESM(require("axios"));

// src/types.ts
var AuthError = class extends Error {
  constructor(message, code, statusCode, details) {
    super(message);
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
    this.name = "AuthError";
  }
};

// src/oauth-client.ts
var OAuthClient = class {
  constructor(authConfig, oauthConfig) {
    this.stateKey = "oauth_state";
    this.authConfig = authConfig;
    this.config = oauthConfig;
    this.api = import_axios.default.create({
      baseURL: authConfig.apiBaseUrl,
      headers: {
        "Content-Type": "application/json"
      }
    });
  }
  /**
   * Initiate OAuth flow
   */
  async initiateOAuth(provider) {
    const providerConfig = this.config.providers[provider];
    if (!providerConfig) {
      throw new AuthError(`OAuth provider ${provider} not configured`, "PROVIDER_NOT_CONFIGURED");
    }
    const state = this.generateState();
    const codeVerifier = this.generateCodeVerifier();
    const codeChallenge = await this.generateCodeChallenge(codeVerifier);
    const oauthState = {
      state,
      codeVerifier,
      provider,
      timestamp: Date.now()
    };
    localStorage.setItem(this.stateKey, JSON.stringify(oauthState));
    const params = new URLSearchParams({
      client_id: providerConfig.clientId,
      redirect_uri: providerConfig.redirectUri,
      response_type: "code",
      scope: providerConfig.scope,
      state,
      access_type: "offline",
      // For refresh tokens
      prompt: "consent"
    });
    if (provider === "google" || provider === "github") {
      params.append("code_challenge", codeChallenge);
      params.append("code_challenge_method", "S256");
    }
    window.location.href = `${providerConfig.authUrl}?${params.toString()}`;
  }
  /**
   * Handle OAuth callback
   */
  async handleCallback(code, state) {
    const storedStateStr = localStorage.getItem(this.stateKey);
    if (!storedStateStr) {
      throw new AuthError("No OAuth state found", "INVALID_STATE");
    }
    const storedState = JSON.parse(storedStateStr);
    localStorage.removeItem(this.stateKey);
    if (storedState.state !== state) {
      throw new AuthError("Invalid OAuth state", "INVALID_STATE");
    }
    if (Date.now() - storedState.timestamp > 5 * 60 * 1e3) {
      throw new AuthError("OAuth state expired", "STATE_EXPIRED");
    }
    const providerConfig = this.config.providers[storedState.provider];
    if (!providerConfig) {
      throw new AuthError("OAuth provider not configured", "PROVIDER_NOT_CONFIGURED");
    }
    try {
      const tokenResponse = await this.exchangeCodeForTokens(
        code,
        storedState.provider,
        providerConfig,
        storedState.codeVerifier
      );
      const userInfo = await this.getUserInfo(
        tokenResponse.access_token,
        providerConfig
      );
      const response = await this.api.post(`${this.authConfig.authEndpoint}/oauth/callback`, {
        provider: storedState.provider,
        accessToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token,
        idToken: tokenResponse.id_token,
        userInfo,
        expiresIn: tokenResponse.expires_in
      });
      const { user, accessToken, refreshToken, expiresIn } = response.data;
      const tokens = {
        accessToken,
        refreshToken,
        expiresIn,
        tokenType: "Bearer"
      };
      this.config.onSuccess?.(user, tokens);
      return { user, tokens };
    } catch (error) {
      const authError = new AuthError(
        error.response?.data?.message || "OAuth authentication failed",
        error.response?.data?.code || "OAUTH_ERROR",
        error.response?.status
      );
      this.config.onError?.(authError);
      throw authError;
    }
  }
  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(code, provider, providerConfig, codeVerifier) {
    const params = {
      grant_type: "authorization_code",
      code,
      client_id: providerConfig.clientId,
      redirect_uri: providerConfig.redirectUri
    };
    if (providerConfig.clientSecret) {
      params.client_secret = providerConfig.clientSecret;
    }
    if (codeVerifier && (provider === "google" || provider === "github")) {
      params.code_verifier = codeVerifier;
    }
    const response = await import_axios.default.post(providerConfig.tokenUrl, params, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      }
    });
    return response.data;
  }
  /**
   * Get user info from OAuth provider
   */
  async getUserInfo(accessToken, providerConfig) {
    const response = await import_axios.default.get(providerConfig.userInfoUrl, {
      headers: {
        "Authorization": `Bearer ${accessToken}`
      }
    });
    return response.data;
  }
  /**
   * Link OAuth account to existing user
   */
  async linkAccount(provider, userId) {
    const linkingState = {
      userId,
      action: "link",
      timestamp: Date.now()
    };
    localStorage.setItem("oauth_linking", JSON.stringify(linkingState));
    await this.initiateOAuth(provider);
  }
  /**
   * Unlink OAuth account
   */
  async unlinkAccount(provider, userId) {
    await this.api.post(`${this.authConfig.authEndpoint}/oauth/unlink`, {
      provider,
      userId
    });
  }
  /**
   * Get linked accounts for user
   */
  async getLinkedAccounts(userId) {
    const response = await this.api.get(`${this.authConfig.authEndpoint}/oauth/linked/${userId}`);
    return response.data.providers;
  }
  /**
   * Refresh OAuth tokens
   */
  async refreshOAuthTokens(provider, refreshToken) {
    const providerConfig = this.config.providers[provider];
    if (!providerConfig) {
      throw new AuthError("OAuth provider not configured", "PROVIDER_NOT_CONFIGURED");
    }
    const params = {
      grant_type: "refresh_token",
      refresh_token: refreshToken,
      client_id: providerConfig.clientId
    };
    if (providerConfig.clientSecret) {
      params.client_secret = providerConfig.clientSecret;
    }
    const response = await import_axios.default.post(providerConfig.tokenUrl, params, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      }
    });
    return {
      accessToken: response.data.access_token,
      refreshToken: response.data.refresh_token || refreshToken,
      expiresIn: response.data.expires_in,
      tokenType: "Bearer"
    };
  }
  /**
   * Generate random state for CSRF protection
   */
  generateState() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join("");
  }
  /**
   * Generate PKCE code verifier
   */
  generateCodeVerifier() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return this.base64UrlEncode(array);
  }
  /**
   * Generate PKCE code challenge
   */
  async generateCodeChallenge(verifier) {
    const encoder = new TextEncoder();
    const data = encoder.encode(verifier);
    const digest = await crypto.subtle.digest("SHA-256", data);
    return this.base64UrlEncode(new Uint8Array(digest));
  }
  /**
   * Base64 URL encode
   */
  base64UrlEncode(buffer) {
    const base64 = btoa(String.fromCharCode(...buffer));
    return base64.replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
  }
};
var defaultOAuthProviders = {
  google: {
    name: "Google",
    authUrl: "https://accounts.google.com/o/oauth2/v2/auth",
    tokenUrl: "https://oauth2.googleapis.com/token",
    userInfoUrl: "https://www.googleapis.com/oauth2/v2/userinfo",
    clientId: process.env.VITE_GOOGLE_CLIENT_ID || "",
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    scope: "openid email profile",
    redirectUri: `${window.location.origin}/auth/callback/google`
  },
  github: {
    name: "GitHub",
    authUrl: "https://github.com/login/oauth/authorize",
    tokenUrl: "https://github.com/login/oauth/access_token",
    userInfoUrl: "https://api.github.com/user",
    clientId: process.env.VITE_GITHUB_CLIENT_ID || "",
    clientSecret: process.env.GITHUB_CLIENT_SECRET,
    scope: "read:user user:email",
    redirectUri: `${window.location.origin}/auth/callback/github`
  },
  microsoft: {
    name: "Microsoft",
    authUrl: "https://login.microsoftonline.com/common/oauth2/v2.0/authorize",
    tokenUrl: "https://login.microsoftonline.com/common/oauth2/v2.0/token",
    userInfoUrl: "https://graph.microsoft.com/v1.0/me",
    clientId: process.env.VITE_MICROSOFT_CLIENT_ID || "",
    clientSecret: process.env.MICROSOFT_CLIENT_SECRET,
    scope: "openid email profile User.Read",
    redirectUri: `${window.location.origin}/auth/callback/microsoft`
  }
};

// src/components/OAuthButtons.tsx
var providerIcons = {
  google: /* @__PURE__ */ import_react7.default.createElement("svg", { className: "w-5 h-5 mr-2", viewBox: "0 0 24 24" }, /* @__PURE__ */ import_react7.default.createElement("path", { fill: "#4285f4", d: "M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" }), /* @__PURE__ */ import_react7.default.createElement("path", { fill: "#34a853", d: "M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" }), /* @__PURE__ */ import_react7.default.createElement("path", { fill: "#fbbc05", d: "M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" }), /* @__PURE__ */ import_react7.default.createElement("path", { fill: "#ea4335", d: "M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" })),
  github: /* @__PURE__ */ import_react7.default.createElement("svg", { className: "w-5 h-5 mr-2", fill: "currentColor", viewBox: "0 0 24 24" }, /* @__PURE__ */ import_react7.default.createElement("path", { d: "M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" })),
  microsoft: /* @__PURE__ */ import_react7.default.createElement("svg", { className: "w-5 h-5 mr-2", viewBox: "0 0 24 24" }, /* @__PURE__ */ import_react7.default.createElement("path", { fill: "#f25022", d: "M0 0h11.377v11.372H0zm12.623 0H24v11.372H12.623z" }), /* @__PURE__ */ import_react7.default.createElement("path", { fill: "#00a4ef", d: "M0 12.623h11.377V24H0z" }), /* @__PURE__ */ import_react7.default.createElement("path", { fill: "#7fba00", d: "M12.623 12.623H24V24H12.623z" }))
};
var providerNames = {
  google: "Google",
  github: "GitHub",
  microsoft: "Microsoft"
};
var OAuthButtons = ({
  providers = ["google", "github", "microsoft"],
  authConfig,
  className = "",
  buttonClassName = "",
  showDivider = true,
  dividerText = "Or continue with"
}) => {
  const handleOAuthLogin = async (provider) => {
    try {
      const oauthClient = new OAuthClient(authConfig, {
        providers: defaultOAuthProviders
      });
      await oauthClient.initiateOAuth(provider);
    } catch (error) {
      console.error(`OAuth login failed for ${provider}:`, error);
    }
  };
  return /* @__PURE__ */ import_react7.default.createElement("div", { className }, showDivider && /* @__PURE__ */ import_react7.default.createElement("div", { className: "relative mb-6" }, /* @__PURE__ */ import_react7.default.createElement("div", { className: "absolute inset-0 flex items-center" }, /* @__PURE__ */ import_react7.default.createElement("div", { className: "w-full border-t border-gray-300" })), /* @__PURE__ */ import_react7.default.createElement("div", { className: "relative flex justify-center text-sm" }, /* @__PURE__ */ import_react7.default.createElement("span", { className: "px-2 bg-white text-gray-500" }, dividerText))), /* @__PURE__ */ import_react7.default.createElement("div", { className: "space-y-3" }, providers.map((provider) => /* @__PURE__ */ import_react7.default.createElement(
    "button",
    {
      key: provider,
      type: "button",
      onClick: () => handleOAuthLogin(provider),
      className: `w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary ${buttonClassName}`
    },
    providerIcons[provider],
    "Sign in with ",
    providerNames[provider]
  ))));
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CrossAppNavigation,
  OAuthButtons,
  PermissionGate,
  ProtectedRoute,
  UnifiedLoginPage
});
//# sourceMappingURL=components.js.map