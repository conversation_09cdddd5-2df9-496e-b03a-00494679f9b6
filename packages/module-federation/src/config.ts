import federation from '@originjs/vite-plugin-federation';
import type { Plugin } from 'vite';
import type { LuminarModuleFederationConfig } from './types';

/**
 * Creates a Module Federation configuration for Luminar apps
 */
export function createModuleFederationConfig(
  config: LuminarModuleFederationConfig
): Plugin {
  const defaultShared = [
    'react',
    'react-dom',
    'react-router-dom',
    '@tanstack/react-router',
    'zustand',
    'axios',
    'clsx',
    'tailwind-merge',
    '@radix-ui/react-slot',
    'lucide-react',
  ];

  const mergedConfig = {
    ...config,
    shared: config.shared || defaultShared,
    filename: config.filename || 'remoteEntry.js',
  };

  // Remove Luminar-specific properties before passing to federation plugin
  const { luminar, ...federationConfig } = mergedConfig;

  // Add Luminar-specific enhancements
  if (luminar?.runtimeConfig) {
    // Runtime config will be handled by our custom plugin
  }

  return federation(federationConfig as any);
}

/**
 * Configuration for the shell/host application
 */
export function createShellConfig(
  remotes: Record<string, string>
): LuminarModuleFederationConfig {
  return {
    name: 'luminar-shell',
    remotes: Object.entries(remotes).reduce((acc, [name, url]) => {
      acc[name] = {
        external: url,
        format: 'esm',
        from: 'vite',
      };
      return acc;
    }, {} as any),
    luminar: {
      runtimeConfig: true,
      featureFlags: true,
      errorTracking: true,
      healthCheck: '/api/health',
    },
  };
}

/**
 * Configuration for remote applications
 */
export function createRemoteConfig(
  name: string,
  exposes: Record<string, string>
): LuminarModuleFederationConfig {
  return {
    name,
    filename: 'remoteEntry.js',
    exposes,
    luminar: {
      healthCheck: `/api/apps/${name}/health`,
    },
  };
}

/**
 * Get environment-specific remote URLs
 */
export function getRemoteUrls(env: 'development' | 'staging' | 'production') {
  const baseUrls = {
    development: {
      amna: 'http://localhost:5001',
      commandCenter: 'http://localhost:5002',
      eConnect: 'http://localhost:5003',
      lighthouse: 'http://localhost:5004',
      trainingAnalysis: 'http://localhost:5005',
      vendors: 'http://localhost:5006',
      winsOfWeek: 'http://localhost:5007',
    },
    staging: {
      amna: 'https://amna-staging.luminar.com',
      commandCenter: 'https://command-center-staging.luminar.com',
      eConnect: 'https://e-connect-staging.luminar.com',
      lighthouse: 'https://lighthouse-staging.luminar.com',
      trainingAnalysis: 'https://training-analysis-staging.luminar.com',
      vendors: 'https://vendors-staging.luminar.com',
      winsOfWeek: 'https://wins-staging.luminar.com',
    },
    production: {
      amna: 'https://amna.luminar.com',
      commandCenter: 'https://command-center.luminar.com',
      eConnect: 'https://e-connect.luminar.com',
      lighthouse: 'https://lighthouse.luminar.com',
      trainingAnalysis: 'https://training-analysis.luminar.com',
      vendors: 'https://vendors.luminar.com',
      winsOfWeek: 'https://wins.luminar.com',
    },
  };

  const urls = baseUrls[env];
  return Object.entries(urls).reduce((acc, [name, baseUrl]) => {
    acc[name] = `${baseUrl}/remoteEntry.js`;
    return acc;
  }, {} as Record<string, string>);
}