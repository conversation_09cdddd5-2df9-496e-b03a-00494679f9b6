import { faker } from '@faker-js/faker'

// User fixtures
export const createMockUser = (overrides: Partial<any> = {}) => ({
  id: faker.string.uuid(),
  email: faker.internet.email(),
  name: faker.person.fullName(),
  avatar: faker.image.avatar(),
  role: 'user',
  createdAt: faker.date.past().toISOString(),
  updatedAt: faker.date.recent().toISOString(),
  ...overrides,
})

export const createMockUsers = (count: number, overrides: Partial<any> = {}) => {
  return Array.from({ length: count }, () => createMockUser(overrides))
}

// Auth fixtures
export const createMockAuthToken = () => ({
  access_token: faker.string.alphanumeric(32),
  refresh_token: faker.string.alphanumeric(32),
  expires_in: 3600,
  token_type: 'Bearer',
})

export const createMockSession = (overrides: Partial<any> = {}) => ({
  user: createMockUser(),
  token: createMockAuthToken(),
  expiresAt: faker.date.future().toISOString(),
  ...overrides,
})

// API response fixtures
export const createMockApiResponse = <T = any>(data: T, overrides: Partial<any> = {}) => ({
  data,
  status: 'success',
  message: 'Operation successful',
  timestamp: new Date().toISOString(),
  ...overrides,
})

export const createMockApiError = (overrides: Partial<any> = {}) => ({
  status: 'error',
  message: faker.lorem.sentence(),
  code: faker.string.alphanumeric(6).toUpperCase(),
  timestamp: new Date().toISOString(),
  ...overrides,
})

export const createMockPaginatedResponse = <T = any>(
  items: T[],
  overrides: Partial<any> = {}
) => ({
  data: items,
  pagination: {
    page: 1,
    pageSize: 10,
    total: items.length,
    totalPages: Math.ceil(items.length / 10),
  },
  ...overrides,
})

// Form data fixtures
export const createMockFormData = () => ({
  name: faker.person.fullName(),
  email: faker.internet.email(),
  phone: faker.phone.number(),
  message: faker.lorem.paragraph(),
  company: faker.company.name(),
  website: faker.internet.url(),
})

// File upload fixtures
export const createMockFile = (overrides: Partial<File> = {}): File => {
  const content = faker.lorem.paragraphs()
  const blob = new Blob([content], { type: 'text/plain' })
  return new File([blob], faker.system.fileName(), {
    type: 'text/plain',
    lastModified: Date.now(),
    ...overrides,
  })
}

export const createMockImageFile = (overrides: Partial<File> = {}): File => {
  const canvas = document.createElement('canvas')
  canvas.width = 100
  canvas.height = 100
  const ctx = canvas.getContext('2d')!
  ctx.fillStyle = faker.color.rgb()
  ctx.fillRect(0, 0, 100, 100)
  
  return new Promise<File>((resolve) => {
    canvas.toBlob((blob) => {
      const file = new File([blob!], faker.system.fileName() + '.png', {
        type: 'image/png',
        lastModified: Date.now(),
        ...overrides,
      })
      resolve(file)
    })
  }) as any // Type assertion for simplicity
}

// Date fixtures
export const createMockDateRange = () => ({
  start: faker.date.past(),
  end: faker.date.future(),
})

export const createMockTimeSlots = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: faker.string.uuid(),
    time: `${9 + i}:00`,
    available: faker.datatype.boolean(),
  }))
}

// Product/Item fixtures
export const createMockProduct = (overrides: Partial<any> = {}) => ({
  id: faker.string.uuid(),
  name: faker.commerce.productName(),
  description: faker.commerce.productDescription(),
  price: parseFloat(faker.commerce.price()),
  image: faker.image.url(),
  category: faker.commerce.department(),
  inStock: faker.datatype.boolean(),
  rating: faker.number.float({ min: 1, max: 5, fractionDigits: 1 }),
  ...overrides,
})

export const createMockProducts = (count: number, overrides: Partial<any> = {}) => {
  return Array.from({ length: count }, () => createMockProduct(overrides))
}

// Notification fixtures
export const createMockNotification = (overrides: Partial<any> = {}) => ({
  id: faker.string.uuid(),
  type: faker.helpers.arrayElement(['info', 'success', 'warning', 'error']),
  title: faker.lorem.sentence(),
  message: faker.lorem.paragraph(),
  read: faker.datatype.boolean(),
  createdAt: faker.date.recent().toISOString(),
  ...overrides,
})

export const createMockNotifications = (count: number, overrides: Partial<any> = {}) => {
  return Array.from({ length: count }, () => createMockNotification(overrides))
}

// Settings fixtures
export const createMockSettings = (overrides: Partial<any> = {}) => ({
  theme: faker.helpers.arrayElement(['light', 'dark', 'auto']),
  language: faker.helpers.arrayElement(['en', 'es', 'fr', 'de']),
  notifications: {
    email: faker.datatype.boolean(),
    push: faker.datatype.boolean(),
    sms: faker.datatype.boolean(),
  },
  privacy: {
    profileVisible: faker.datatype.boolean(),
    showEmail: faker.datatype.boolean(),
    showPhone: faker.datatype.boolean(),
  },
  ...overrides,
})

// Chart data fixtures
export const createMockChartData = (points: number = 7) => ({
  labels: Array.from({ length: points }, (_, i) => 
    faker.date.recent({ days: points - i }).toLocaleDateString()
  ),
  datasets: [
    {
      label: faker.lorem.word(),
      data: Array.from({ length: points }, () => 
        faker.number.int({ min: 10, max: 100 })
      ),
    },
  ],
})

// Table data fixtures
export const createMockTableData = (rows: number = 10, columns: string[] = ['id', 'name', 'email', 'status']) => {
  return Array.from({ length: rows }, () => {
    const row: Record<string, any> = {}
    columns.forEach(col => {
      switch (col) {
        case 'id':
          row[col] = faker.string.uuid()
          break
        case 'name':
          row[col] = faker.person.fullName()
          break
        case 'email':
          row[col] = faker.internet.email()
          break
        case 'status':
          row[col] = faker.helpers.arrayElement(['active', 'inactive', 'pending'])
          break
        default:
          row[col] = faker.lorem.word()
      }
    })
    return row
  })
}

// Fixture factory
export const createFixture = <T>(
  generator: () => T,
  count?: number
): T | T[] => {
  if (count === undefined) {
    return generator()
  }
  return Array.from({ length: count }, generator)
}