/**
 * Shared Testing Utilities - Common testing helpers and utilities
 */

import React, { ReactElement, ReactNode } from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { createRouter, createRootRoute, createRoute, RouterProvider } from '@tanstack/react-router';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

// Test providers interface
interface TestProvidersProps {
  children: ReactNode;
  queryClient?: QueryClient;
  initialEntries?: string[];
  routeConfig?: any;
}

// Create test query client
export function createTestQueryClient(): QueryClient {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
}

// Test providers wrapper
export function TestProviders({ 
  children, 
  queryClient = createTestQueryClient()
}: TestProvidersProps) {
  // Create memory router for testing
  const rootRoute = createRootRoute({
    component: () => <div>{children}</div>,
  });

  const indexRoute = createRoute({
    getParentRoute: () => rootRoute,
    path: '/',
    component: () => <>{children}</>,
  });

  const routeTree = rootRoute.addChildren([indexRoute]);

  const router = createRouter({
    routeTree,
    defaultPreload: 'intent',
  });

  return (
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router} />
    </QueryClientProvider>
  );
}

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient;
  initialEntries?: string[];
  routeConfig?: any;
  withProviders?: boolean;
}

export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult & { user: ReturnType<typeof userEvent.setup> } {
  const {
    queryClient,
    initialEntries,
    routeConfig,
    withProviders = true,
    ...renderOptions
  } = options;

  const user = userEvent.setup();

  const Wrapper = withProviders
    ? ({ children }: { children: ReactNode }) => (
        <TestProviders
          queryClient={queryClient || createTestQueryClient()}
          initialEntries={initialEntries || []}
          routeConfig={routeConfig}
        >
          {children}
        </TestProviders>
      )
    : undefined;

  const result = render(ui, {
    wrapper: Wrapper,
    ...renderOptions,
  });

  return {
    ...result,
    user,
  };
}

// Mock API client for testing
export interface MockApiClient {
  get: ReturnType<typeof vi.fn>;
  post: ReturnType<typeof vi.fn>;
  put: ReturnType<typeof vi.fn>;
  patch: ReturnType<typeof vi.fn>;
  delete: ReturnType<typeof vi.fn>;
  addRequestInterceptor: ReturnType<typeof vi.fn>;
  addResponseInterceptor: ReturnType<typeof vi.fn>;
  removeRequestInterceptor: ReturnType<typeof vi.fn>;
  removeResponseInterceptor: ReturnType<typeof vi.fn>;
  setBaseURL: ReturnType<typeof vi.fn>;
  setDefaultHeaders: ReturnType<typeof vi.fn>;
  getConfig: ReturnType<typeof vi.fn>;
}

export function createMockApiClient(): MockApiClient {
  return {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
    addRequestInterceptor: vi.fn(),
    addResponseInterceptor: vi.fn(),
    removeRequestInterceptor: vi.fn(),
    removeResponseInterceptor: vi.fn(),
    setBaseURL: vi.fn(),
    setDefaultHeaders: vi.fn(),
    getConfig: vi.fn(),
  };
}

// Mock auth provider
export interface MockAuthState {
  isAuthenticated: boolean;
  user?: any;
  permissions?: string[];
  roles?: string[];
}

export function createMockAuthProvider(initialState: MockAuthState = { isAuthenticated: false }) {
  const AuthContext = React.createContext(initialState);
  
  const MockAuthProvider = ({ children, state = initialState }: { 
    children: ReactNode; 
    state?: MockAuthState;
  }) => (
    <AuthContext.Provider value={state}>
      {children}
    </AuthContext.Provider>
  );

  const useAuth = () => React.useContext(AuthContext);

  return {
    MockAuthProvider,
    useAuth,
    AuthContext,
  };
}

// Test data factories
export const testDataFactory = {
  // User factory
  createUser: (overrides: Partial<any> = {}) => ({
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    roles: ['user'],
    permissions: ['read'],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  }),

  // Training factory
  createTraining: (overrides: Partial<any> = {}) => ({
    id: '1',
    title: 'Test Training',
    description: 'Test training description',
    category: { id: '1', name: 'Test Category' },
    duration: 60,
    difficulty: 'beginner',
    instructor: testDataFactory.createUser({ id: '2', name: 'Instructor' }),
    participants: [],
    status: 'published',
    isPublic: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  }),

  // API response factory
  createApiResponse: <T,>(data: T, overrides: Partial<any> = {}) => ({
    data,
    status: 200,
    statusText: 'OK',
    message: 'Success',
    ...overrides,
  }),

  // Error factory
  createApiError: (overrides: Partial<any> = {}) => ({
    message: 'Test error',
    code: 'TEST_ERROR',
    details: {
      type: 'CLIENT_ERROR',
      status: 400,
      timestamp: new Date(),
    },
    ...overrides,
  }),
};

// Mock handlers for MSW
export const mockHandlers = {
  // Success response handler
  success: <T,>(data: T) => ({
    status: 200,
    json: testDataFactory.createApiResponse(data),
  }),

  // Error response handler
  error: (status = 400, message = 'Test error') => ({
    status,
    json: testDataFactory.createApiError({ message }),
  }),

  // Loading delay handler
  delay: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),
};

// Component testing utilities
export const componentTestUtils = {
  // Wait for component to be loaded (for lazy components)
  waitForComponent: async (getByTestId: (id: string) => HTMLElement, testId: string) => {
    return new Promise<HTMLElement>((resolve) => {
      const checkForComponent = () => {
        try {
          const element = getByTestId(testId);
          resolve(element);
        } catch {
          setTimeout(checkForComponent, 10);
        }
      };
      checkForComponent();
    });
  },

  // Simulate network delay
  simulateNetworkDelay: (ms = 100) => 
    new Promise(resolve => setTimeout(resolve, ms)),

  // Mock intersection observer for lazy loading tests
  mockIntersectionObserver: () => {
    const mockIntersectionObserver = vi.fn();
    mockIntersectionObserver.mockReturnValue({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    });
    
    Object.defineProperty(window, 'IntersectionObserver', {
      writable: true,
      configurable: true,
      value: mockIntersectionObserver,
    });

    return mockIntersectionObserver;
  },

  // Mock ResizeObserver
  mockResizeObserver: () => {
    const mockResizeObserver = vi.fn();
    mockResizeObserver.mockReturnValue({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    });
    
    Object.defineProperty(window, 'ResizeObserver', {
      writable: true,
      configurable: true,
      value: mockResizeObserver,
    });

    return mockResizeObserver;
  },
};

// Accessibility testing utilities
export const a11yTestUtils = {
  // Check for required ARIA attributes
  checkAriaAttributes: (element: HTMLElement, requiredAttributes: string[]) => {
    const missingAttributes = requiredAttributes.filter(
      attr => !element.hasAttribute(attr)
    );
    
    if (missingAttributes.length > 0) {
      throw new Error(`Missing ARIA attributes: ${missingAttributes.join(', ')}`);
    }
  },

  // Check for keyboard navigation
  checkKeyboardNavigation: async (
    user: ReturnType<typeof userEvent.setup>,
    element: HTMLElement
  ) => {
    element.focus();
    expect(element).toHaveFocus();
    
    await user.keyboard('{Tab}');
    // Additional keyboard navigation checks would go here
  },

  // Check color contrast (simplified)
  checkColorContrast: (element: HTMLElement) => {
    const styles = window.getComputedStyle(element);
    const backgroundColor = styles.backgroundColor;
    const color = styles.color;
    
    // This is a simplified check - in reality you'd use a proper contrast calculation
    if (backgroundColor === color) {
      throw new Error('Insufficient color contrast');
    }
  },
};

// Performance testing utilities
export const performanceTestUtils = {
  // Measure render time
  measureRenderTime: async (renderFn: () => Promise<void> | void) => {
    const start = performance.now();
    await renderFn();
    const end = performance.now();
    return end - start;
  },

  // Check for memory leaks (simplified)
  checkMemoryLeaks: (cleanup: () => void) => {
    const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
    
    return () => {
      cleanup();
      
      // Force garbage collection if available
      if ((global as any).gc) {
        (global as any).gc();
      }
      
      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
      const memoryDiff = finalMemory - initialMemory;
      
      if (memoryDiff > 1000000) { // 1MB threshold
        console.warn(`Potential memory leak detected: ${memoryDiff} bytes`);
      }
    };
  },
};

// Custom matchers for testing
export const customMatchers = {
  // Check if element is accessible
  toBeAccessible: (element: HTMLElement) => {
    try {
      // Basic accessibility checks
      const hasAriaLabel = element.hasAttribute('aria-label') || 
                          element.hasAttribute('aria-labelledby');
      const hasRole = element.hasAttribute('role') || 
                     ['button', 'link', 'input'].includes(element.tagName.toLowerCase());
      
      if (!hasAriaLabel && !hasRole) {
        return {
          message: () => 'Element is not accessible - missing aria-label or role',
          pass: false,
        };
      }
      
      return {
        message: () => 'Element is accessible',
        pass: true,
      };
    } catch (error) {
      return {
        message: () => `Accessibility check failed: ${error}`,
        pass: false,
      };
    }
  },
};

// Export everything
export * from '@testing-library/react';
export * from '@testing-library/user-event';
export { vi } from 'vitest';
export { default as userEvent } from '@testing-library/user-event';