/**
 * Luminar Debug Utilities
 * Comprehensive debugging tools for development
 */

import { performance } from 'perf_hooks';
import * as React from 'react';

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Debug levels
export enum DebugLevel {
  TRACE = 0,
  DEBUG = 1,
  INFO = 2,
  WARN = 3,
  ERROR = 4,
  FATAL = 5,
}

// Global debug configuration
interface DebugConfig {
  enabled: boolean;
  level: DebugLevel;
  showTimestamp: boolean;
  showStackTrace: boolean;
  enablePerformance: boolean;
  enableStateSnapshot: boolean;
  filters: string[];
}

const defaultConfig: DebugConfig = {
  enabled: process.env.NODE_ENV === 'development',
  level: DebugLevel.DEBUG,
  showTimestamp: true,
  showStackTrace: false,
  enablePerformance: true,
  enableStateSnapshot: true,
  filters: [],
};

let globalConfig = { ...defaultConfig };

/**
 * Configure debug utilities
 */
export function configureDebug(config: Partial<DebugConfig>): void {
  globalConfig = { ...globalConfig, ...config };
}

/**
 * Create a namespaced debugger
 */
export function createDebugger(namespace: string) {
  const performanceMarks = new Map<string, number>();
  const stateSnapshots = new Map<string, any>();

  function shouldLog(level: DebugLevel): boolean {
    if (!globalConfig.enabled) return false;
    if (level < globalConfig.level) return false;
    if (globalConfig.filters.length > 0) {
      return globalConfig.filters.some(filter => namespace.includes(filter));
    }
    return true;
  }

  function formatMessage(level: DebugLevel, message: string, data?: any): string {
    const levelColors = {
      [DebugLevel.TRACE]: colors.dim,
      [DebugLevel.DEBUG]: colors.cyan,
      [DebugLevel.INFO]: colors.blue,
      [DebugLevel.WARN]: colors.yellow,
      [DebugLevel.ERROR]: colors.red,
      [DebugLevel.FATAL]: colors.bright + colors.red,
    };

    const levelNames = {
      [DebugLevel.TRACE]: 'TRACE',
      [DebugLevel.DEBUG]: 'DEBUG',
      [DebugLevel.INFO]: 'INFO ',
      [DebugLevel.WARN]: 'WARN ',
      [DebugLevel.ERROR]: 'ERROR',
      [DebugLevel.FATAL]: 'FATAL',
    };

    let output = '';

    if (globalConfig.showTimestamp) {
      output += `${colors.dim}[${new Date().toISOString()}]${colors.reset} `;
    }

    output += `${levelColors[level]}[${levelNames[level]}]${colors.reset} `;
    output += `${colors.magenta}[${namespace}]${colors.reset} `;
    output += message;

    if (data !== undefined) {
      output += '\n' + JSON.stringify(data, null, 2);
    }

    return output;
  }

  return {
    trace(message: string, data?: any): void {
      if (shouldLog(DebugLevel.TRACE)) {
        console.log(formatMessage(DebugLevel.TRACE, message, data));
      }
    },

    debug(message: string, data?: any): void {
      if (shouldLog(DebugLevel.DEBUG)) {
        console.log(formatMessage(DebugLevel.DEBUG, message, data));
      }
    },

    info(message: string, data?: any): void {
      if (shouldLog(DebugLevel.INFO)) {
        console.info(formatMessage(DebugLevel.INFO, message, data));
      }
    },

    warn(message: string, data?: any): void {
      if (shouldLog(DebugLevel.WARN)) {
        console.warn(formatMessage(DebugLevel.WARN, message, data));
        if (globalConfig.showStackTrace) {
          console.trace();
        }
      }
    },

    error(message: string, error?: Error | any): void {
      if (shouldLog(DebugLevel.ERROR)) {
        console.error(formatMessage(DebugLevel.ERROR, message, error));
        if (error instanceof Error && globalConfig.showStackTrace) {
          console.error(error.stack);
        }
      }
    },

    fatal(message: string, error?: Error | any): void {
      if (shouldLog(DebugLevel.FATAL)) {
        console.error(formatMessage(DebugLevel.FATAL, message, error));
        if (error instanceof Error) {
          console.error(error.stack);
        }
        // In production, you might want to send this to a logging service
      }
    },

    // Performance measurement
    startTimer(label: string): void {
      if (globalConfig.enablePerformance) {
        performanceMarks.set(label, performance.now());
        this.trace(`Timer started: ${label}`);
      }
    },

    endTimer(label: string): number | null {
      if (!globalConfig.enablePerformance) return null;
      
      const start = performanceMarks.get(label);
      if (start === undefined) {
        this.warn(`Timer '${label}' was not started`);
        return null;
      }

      const duration = performance.now() - start;
      performanceMarks.delete(label);
      
      this.debug(`Timer '${label}' completed in ${duration.toFixed(2)}ms`);
      return duration;
    },

    // State snapshots for debugging
    saveSnapshot(label: string, state: any): void {
      if (globalConfig.enableStateSnapshot) {
        stateSnapshots.set(label, JSON.parse(JSON.stringify(state)));
        this.trace(`State snapshot saved: ${label}`);
      }
    },

    compareSnapshot(label: string, currentState: any): void {
      if (!globalConfig.enableStateSnapshot) return;
      
      const savedState = stateSnapshots.get(label);
      if (!savedState) {
        this.warn(`No snapshot found for: ${label}`);
        return;
      }

      const diff = deepDiff(savedState, currentState);
      if (diff.length > 0) {
        this.info(`State differences for '${label}':`, diff);
      } else {
        this.debug(`No state differences for '${label}'`);
      }
    },

    // Assert with custom message
    assert(condition: boolean, message: string): void {
      if (!condition) {
        this.error(`Assertion failed: ${message}`);
        if (process.env.NODE_ENV === 'development') {
          throw new Error(`Assertion failed: ${message}`);
        }
      }
    },

    // Group related logs
    group(label: string): void {
      if (globalConfig.enabled && console.group) {
        console.group(`${colors.bright}${label}${colors.reset}`);
      }
    },

    groupEnd(): void {
      if (globalConfig.enabled && console.groupEnd) {
        console.groupEnd();
      }
    },

    // Table output for structured data
    table(data: any[], columns?: string[]): void {
      if (shouldLog(DebugLevel.DEBUG) && console.table) {
        console.table(data, columns);
      }
    },
  };
}

/**
 * Deep diff utility for comparing objects
 */
function deepDiff(obj1: any, obj2: any, path: string = ''): any[] {
  const differences: any[] = [];

  // Handle different types
  if (typeof obj1 !== typeof obj2) {
    differences.push({
      path,
      type: 'type',
      oldValue: obj1,
      newValue: obj2,
    });
    return differences;
  }

  // Handle arrays
  if (Array.isArray(obj1) && Array.isArray(obj2)) {
    const maxLength = Math.max(obj1.length, obj2.length);
    for (let i = 0; i < maxLength; i++) {
      if (i >= obj1.length) {
        differences.push({
          path: `${path}[${i}]`,
          type: 'added',
          value: obj2[i],
        });
      } else if (i >= obj2.length) {
        differences.push({
          path: `${path}[${i}]`,
          type: 'removed',
          value: obj1[i],
        });
      } else {
        differences.push(...deepDiff(obj1[i], obj2[i], `${path}[${i}]`));
      }
    }
    return differences;
  }

  // Handle objects
  if (typeof obj1 === 'object' && obj1 !== null && obj2 !== null) {
    const allKeys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);
    
    for (const key of allKeys) {
      const newPath = path ? `${path}.${key}` : key;
      
      if (!(key in obj1)) {
        differences.push({
          path: newPath,
          type: 'added',
          value: obj2[key],
        });
      } else if (!(key in obj2)) {
        differences.push({
          path: newPath,
          type: 'removed',
          value: obj1[key],
        });
      } else {
        differences.push(...deepDiff(obj1[key], obj2[key], newPath));
      }
    }
    return differences;
  }

  // Handle primitive values
  if (obj1 !== obj2) {
    differences.push({
      path,
      type: 'changed',
      oldValue: obj1,
      newValue: obj2,
    });
  }

  return differences;
}

/**
 * Performance profiler
 */
export class PerformanceProfiler {
  private marks: Map<string, number> = new Map();
  private measures: Map<string, number[]> = new Map();
  private debug = createDebugger('PerformanceProfiler');

  mark(name: string): void {
    this.marks.set(name, performance.now());
  }

  measure(name: string, startMark: string, endMark?: string): void {
    const start = this.marks.get(startMark);
    const end = endMark ? this.marks.get(endMark) : performance.now();

    if (!start) {
      this.debug.warn(`Start mark '${startMark}' not found`);
      return;
    }

    if (endMark && !this.marks.has(endMark)) {
      this.debug.warn(`End mark '${endMark}' not found`);
      return;
    }

    const duration = (end || performance.now()) - start;
    
    if (!this.measures.has(name)) {
      this.measures.set(name, []);
    }
    
    this.measures.get(name)!.push(duration);
    this.debug.trace(`Measure '${name}': ${duration.toFixed(2)}ms`);
  }

  getStats(measureName: string): {
    count: number;
    total: number;
    average: number;
    min: number;
    max: number;
  } | null {
    const measures = this.measures.get(measureName);
    if (!measures || measures.length === 0) {
      return null;
    }

    const total = measures.reduce((sum, val) => sum + val, 0);
    
    return {
      count: measures.length,
      total,
      average: total / measures.length,
      min: Math.min(...measures),
      max: Math.max(...measures),
    };
  }

  report(): void {
    this.debug.group('Performance Report');
    
    for (const [name, _measures] of this.measures) {
      const stats = this.getStats(name);
      if (stats) {
        this.debug.info(`${name}:`, {
          ...stats,
          total: `${stats.total.toFixed(2)}ms`,
          average: `${stats.average.toFixed(2)}ms`,
          min: `${stats.min.toFixed(2)}ms`,
          max: `${stats.max.toFixed(2)}ms`,
        });
      }
    }
    
    this.debug.groupEnd();
  }

  clear(): void {
    this.marks.clear();
    this.measures.clear();
  }
}

/**
 * Memory usage tracker
 */
export class MemoryTracker {
  private debug = createDebugger('MemoryTracker');
  private baseline: NodeJS.MemoryUsage | null = null;

  captureBaseline(): void {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      this.baseline = process.memoryUsage();
      this.debug.debug('Memory baseline captured', this.formatMemory(this.baseline));
    }
  }

  getCurrentUsage(): NodeJS.MemoryUsage | null {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage();
    }
    return null;
  }

  logUsage(label: string = 'Current'): void {
    const usage = this.getCurrentUsage();
    if (usage) {
      this.debug.info(`${label} memory usage:`, this.formatMemory(usage));
      
      if (this.baseline) {
        const diff = {
          rss: usage.rss - this.baseline.rss,
          heapTotal: usage.heapTotal - this.baseline.heapTotal,
          heapUsed: usage.heapUsed - this.baseline.heapUsed,
          external: usage.external - this.baseline.external,
        };
        this.debug.debug('Memory diff from baseline:', this.formatMemory(diff as any));
      }
    }
  }

  private formatMemory(usage: NodeJS.MemoryUsage): any {
    return {
      rss: `${(usage.rss / 1024 / 1024).toFixed(2)} MB`,
      heapTotal: `${(usage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
      heapUsed: `${(usage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
      external: `${(usage.external / 1024 / 1024).toFixed(2)} MB`,
    };
  }
}

/**
 * React component debugging utilities
 */
export const ReactDebugUtils = {
  /**
   * Log component renders with props
   */
  logRender(componentName: string, props: any): void {
    const debug = createDebugger(`React:${componentName}`);
    debug.trace('Render', { props });
  },

  /**
   * Log effect executions
   */
  logEffect(componentName: string, effectName: string, deps?: any[]): void {
    const debug = createDebugger(`React:${componentName}`);
    debug.trace(`Effect: ${effectName}`, { dependencies: deps });
  },

  /**
   * Log state updates
   */
  logStateUpdate(componentName: string, stateName: string, oldValue: any, newValue: any): void {
    const debug = createDebugger(`React:${componentName}`);
    debug.debug(`State update: ${stateName}`, { oldValue, newValue });
  },

  /**
   * Create a HOC for debugging components
   */
  withDebug<P extends object>(Component: React.ComponentType<P>, name?: string) {
    const componentName = name || Component.displayName || Component.name || 'Component';
    const debug = createDebugger(`React:${componentName}`);

    return React.forwardRef<any, P>((props, ref) => {
      debug.trace('Render', props);
      
      React.useEffect(() => {
        debug.trace('Mounted');
        return () => debug.trace('Unmounted');
      }, []);

      return React.createElement(Component, { ...props, ref } as any);
    });
  },
};

// Export convenience functions
export const debug = createDebugger('app');
export const profiler = new PerformanceProfiler();
export const memoryTracker = new MemoryTracker();