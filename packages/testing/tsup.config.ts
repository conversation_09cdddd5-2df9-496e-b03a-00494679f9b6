import { defineConfig } from 'tsup'

export default defineConfig({
  entry: {
    index: 'src/index.ts',
    'react/index': 'src/react/index.ts',
    'mocks/index': 'src/mocks/index.ts',
  },
  format: ['esm', 'cjs'],
  dts: true,
  sourcemap: true,
  clean: true,
  external: ['react', 'react-dom', 'vitest'],
  treeshake: true,
  splitting: false,
  minify: false,
  cjsInterop: true,
  legacyOutput: false,
})