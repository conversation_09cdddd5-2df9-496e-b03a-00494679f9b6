"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const c=require("react"),r=require("react/jsx-runtime"),T=require("@tanstack/react-query"),Ja=require("@tanstack/react-query-devtools"),v=require("framer-motion"),F=require("lucide-react"),Nn=require("react-dom"),Te=require("@mui/material"),Ot=require("@mui/icons-material"),Xa=require("@luminar/shared-core"),Ce=require("@tanstack/react-router");function Za(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const s in e)if(s!=="default"){const n=Object.getOwnPropertyDescriptor(e,s);Object.defineProperty(t,s,n.get?n:{enumerable:!0,get:()=>e[s]})}}return t.default=e,Object.freeze(t)}const me=Za(c);function Sn(e){var t,s,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(s=Sn(e[t]))&&(n&&(n+=" "),n+=s)}else for(s in e)e[s]&&(n&&(n+=" "),n+=s);return n}function Bs(){for(var e,t,s=0,n="",a=arguments.length;s<a;s++)(e=arguments[s])&&(t=Sn(e))&&(n&&(n+=" "),n+=t);return n}const Vs="-",ei=e=>{const t=si(e),{conflictingClassGroups:s,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:o=>{const l=o.split(Vs);return l[0]===""&&l.length!==1&&l.shift(),Tn(l,t)||ti(o)},getConflictingClassGroupIds:(o,l)=>{const u=s[o]||[];return l&&n[o]?[...u,...n[o]]:u}}},Tn=(e,t)=>{if(e.length===0)return t.classGroupId;const s=e[0],n=t.nextPart.get(s),a=n?Tn(e.slice(1),n):void 0;if(a)return a;if(t.validators.length===0)return;const i=e.join(Vs);return t.validators.find(({validator:o})=>o(i))?.classGroupId},qr=/^\[(.+)\]$/,ti=e=>{if(qr.test(e)){const t=qr.exec(e)[1],s=t?.substring(0,t.indexOf(":"));if(s)return"arbitrary.."+s}},si=e=>{const{theme:t,classGroups:s}=e,n={nextPart:new Map,validators:[]};for(const a in s)Is(s[a],n,a,t);return n},Is=(e,t,s,n)=>{e.forEach(a=>{if(typeof a=="string"){const i=a===""?t:Br(t,a);i.classGroupId=s;return}if(typeof a=="function"){if(ri(a)){Is(a(n),t,s,n);return}t.validators.push({validator:a,classGroupId:s});return}Object.entries(a).forEach(([i,o])=>{Is(o,Br(t,i),s,n)})})},Br=(e,t)=>{let s=e;return t.split(Vs).forEach(n=>{s.nextPart.has(n)||s.nextPart.set(n,{nextPart:new Map,validators:[]}),s=s.nextPart.get(n)}),s},ri=e=>e.isThemeGetter,ni=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,s=new Map,n=new Map;const a=(i,o)=>{s.set(i,o),t++,t>e&&(t=0,n=s,s=new Map)};return{get(i){let o=s.get(i);if(o!==void 0)return o;if((o=n.get(i))!==void 0)return a(i,o),o},set(i,o){s.has(i)?s.set(i,o):a(i,o)}}},Os="!",Ds=":",ai=Ds.length,ii=e=>{const{prefix:t,experimentalParseClassName:s}=e;let n=a=>{const i=[];let o=0,l=0,u=0,d;for(let g=0;g<a.length;g++){let f=a[g];if(o===0&&l===0){if(f===Ds){i.push(a.slice(u,g)),u=g+ai;continue}if(f==="/"){d=g;continue}}f==="["?o++:f==="]"?o--:f==="("?l++:f===")"&&l--}const m=i.length===0?a:a.substring(u),p=oi(m),y=p!==m,w=d&&d>u?d-u:void 0;return{modifiers:i,hasImportantModifier:y,baseClassName:p,maybePostfixModifierPosition:w}};if(t){const a=t+Ds,i=n;n=o=>o.startsWith(a)?i(o.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:o,maybePostfixModifierPosition:void 0}}if(s){const a=n;n=i=>s({className:i,parseClassName:a})}return n},oi=e=>e.endsWith(Os)?e.substring(0,e.length-1):e.startsWith(Os)?e.substring(1):e,li=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const a=[];let i=[];return n.forEach(o=>{o[0]==="["||t[o]?(a.push(...i.sort(),o),i=[]):i.push(o)}),a.push(...i.sort()),a}},ci=e=>({cache:ni(e.cacheSize),parseClassName:ii(e),sortModifiers:li(e),...ei(e)}),ui=/\s+/,di=(e,t)=>{const{parseClassName:s,getClassGroupId:n,getConflictingClassGroupIds:a,sortModifiers:i}=t,o=[],l=e.trim().split(ui);let u="";for(let d=l.length-1;d>=0;d-=1){const m=l[d],{isExternal:p,modifiers:y,hasImportantModifier:w,baseClassName:g,maybePostfixModifierPosition:f}=s(m);if(p){u=m+(u.length>0?" "+u:u);continue}let h=!!f,b=n(h?g.substring(0,f):g);if(!b){if(!h){u=m+(u.length>0?" "+u:u);continue}if(b=n(g),!b){u=m+(u.length>0?" "+u:u);continue}h=!1}const x=i(y).join(":"),N=w?x+Os:x,R=N+b;if(o.includes(R))continue;o.push(R);const I=a(b,h);for(let j=0;j<I.length;++j){const E=I[j];o.push(N+E)}u=m+(u.length>0?" "+u:u)}return u};function mi(){let e=0,t,s,n="";for(;e<arguments.length;)(t=arguments[e++])&&(s=Rn(t))&&(n&&(n+=" "),n+=s);return n}const Rn=e=>{if(typeof e=="string")return e;let t,s="";for(let n=0;n<e.length;n++)e[n]&&(t=Rn(e[n]))&&(s&&(s+=" "),s+=t);return s};function pi(e,...t){let s,n,a,i=o;function o(u){const d=t.reduce((m,p)=>p(m),e());return s=ci(d),n=s.cache.get,a=s.cache.set,i=l,l(u)}function l(u){const d=n(u);if(d)return d;const m=di(u,s);return a(u,m),m}return function(){return i(mi.apply(null,arguments))}}const Re=e=>{const t=s=>s[e]||[];return t.isThemeGetter=!0,t},En=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,An=/^\((?:(\w[\w-]*):)?(.+)\)$/i,fi=/^\d+\/\d+$/,hi=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,gi=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,yi=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,bi=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,xi=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,nt=e=>fi.test(e),ye=e=>!!e&&!Number.isNaN(Number(e)),Ye=e=>!!e&&Number.isInteger(Number(e)),gs=e=>e.endsWith("%")&&ye(e.slice(0,-1)),qe=e=>hi.test(e),wi=()=>!0,vi=e=>gi.test(e)&&!yi.test(e),Pn=()=>!1,ji=e=>bi.test(e),Ci=e=>xi.test(e),ki=e=>!ne(e)&&!ae(e),Ni=e=>ut(e,On,Pn),ne=e=>En.test(e),Xe=e=>ut(e,Dn,vi),ys=e=>ut(e,Ai,ye),Vr=e=>ut(e,Ln,Pn),Si=e=>ut(e,In,Ci),Dt=e=>ut(e,Mn,ji),ae=e=>An.test(e),ft=e=>dt(e,Dn),Ti=e=>dt(e,Pi),Hr=e=>dt(e,Ln),Ri=e=>dt(e,On),Ei=e=>dt(e,In),Mt=e=>dt(e,Mn,!0),ut=(e,t,s)=>{const n=En.exec(e);return n?n[1]?t(n[1]):s(n[2]):!1},dt=(e,t,s=!1)=>{const n=An.exec(e);return n?n[1]?t(n[1]):s:!1},Ln=e=>e==="position"||e==="percentage",In=e=>e==="image"||e==="url",On=e=>e==="length"||e==="size"||e==="bg-size",Dn=e=>e==="length",Ai=e=>e==="number",Pi=e=>e==="family-name",Mn=e=>e==="shadow",Li=()=>{const e=Re("color"),t=Re("font"),s=Re("text"),n=Re("font-weight"),a=Re("tracking"),i=Re("leading"),o=Re("breakpoint"),l=Re("container"),u=Re("spacing"),d=Re("radius"),m=Re("shadow"),p=Re("inset-shadow"),y=Re("text-shadow"),w=Re("drop-shadow"),g=Re("blur"),f=Re("perspective"),h=Re("aspect"),b=Re("ease"),x=Re("animate"),N=()=>["auto","avoid","all","avoid-page","page","left","right","column"],R=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],I=()=>[...R(),ae,ne],j=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto","contain","none"],k=()=>[ae,ne,u],L=()=>[nt,"full","auto",...k()],$=()=>[Ye,"none","subgrid",ae,ne],W=()=>["auto",{span:["full",Ye,ae,ne]},Ye,ae,ne],D=()=>[Ye,"auto",ae,ne],V=()=>["auto","min","max","fr",ae,ne],z=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],O=()=>["start","end","center","stretch","center-safe","end-safe"],M=()=>["auto",...k()],K=()=>[nt,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...k()],U=()=>[e,ae,ne],ee=()=>[...R(),Hr,Vr,{position:[ae,ne]}],Y=()=>["no-repeat",{repeat:["","x","y","space","round"]}],se=()=>["auto","cover","contain",Ri,Ni,{size:[ae,ne]}],G=()=>[gs,ft,Xe],P=()=>["","none","full",d,ae,ne],B=()=>["",ye,ft,Xe],_=()=>["solid","dashed","dotted","double"],te=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],H=()=>[ye,gs,Hr,Vr],Z=()=>["","none",g,ae,ne],ue=()=>["none",ye,ae,ne],J=()=>["none",ye,ae,ne],oe=()=>[ye,ae,ne],pe=()=>[nt,"full",...k()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[qe],breakpoint:[qe],color:[wi],container:[qe],"drop-shadow":[qe],ease:["in","out","in-out"],font:[ki],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[qe],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[qe],shadow:[qe],spacing:["px",ye],text:[qe],"text-shadow":[qe],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",nt,ne,ae,h]}],container:["container"],columns:[{columns:[ye,ne,ae,l]}],"break-after":[{"break-after":N()}],"break-before":[{"break-before":N()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:I()}],overflow:[{overflow:j()}],"overflow-x":[{"overflow-x":j()}],"overflow-y":[{"overflow-y":j()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:L()}],"inset-x":[{"inset-x":L()}],"inset-y":[{"inset-y":L()}],start:[{start:L()}],end:[{end:L()}],top:[{top:L()}],right:[{right:L()}],bottom:[{bottom:L()}],left:[{left:L()}],visibility:["visible","invisible","collapse"],z:[{z:[Ye,"auto",ae,ne]}],basis:[{basis:[nt,"full","auto",l,...k()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ye,nt,"auto","initial","none",ne]}],grow:[{grow:["",ye,ae,ne]}],shrink:[{shrink:["",ye,ae,ne]}],order:[{order:[Ye,"first","last","none",ae,ne]}],"grid-cols":[{"grid-cols":$()}],"col-start-end":[{col:W()}],"col-start":[{"col-start":D()}],"col-end":[{"col-end":D()}],"grid-rows":[{"grid-rows":$()}],"row-start-end":[{row:W()}],"row-start":[{"row-start":D()}],"row-end":[{"row-end":D()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":V()}],"auto-rows":[{"auto-rows":V()}],gap:[{gap:k()}],"gap-x":[{"gap-x":k()}],"gap-y":[{"gap-y":k()}],"justify-content":[{justify:[...z(),"normal"]}],"justify-items":[{"justify-items":[...O(),"normal"]}],"justify-self":[{"justify-self":["auto",...O()]}],"align-content":[{content:["normal",...z()]}],"align-items":[{items:[...O(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...O(),{baseline:["","last"]}]}],"place-content":[{"place-content":z()}],"place-items":[{"place-items":[...O(),"baseline"]}],"place-self":[{"place-self":["auto",...O()]}],p:[{p:k()}],px:[{px:k()}],py:[{py:k()}],ps:[{ps:k()}],pe:[{pe:k()}],pt:[{pt:k()}],pr:[{pr:k()}],pb:[{pb:k()}],pl:[{pl:k()}],m:[{m:M()}],mx:[{mx:M()}],my:[{my:M()}],ms:[{ms:M()}],me:[{me:M()}],mt:[{mt:M()}],mr:[{mr:M()}],mb:[{mb:M()}],ml:[{ml:M()}],"space-x":[{"space-x":k()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":k()}],"space-y-reverse":["space-y-reverse"],size:[{size:K()}],w:[{w:[l,"screen",...K()]}],"min-w":[{"min-w":[l,"screen","none",...K()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[o]},...K()]}],h:[{h:["screen","lh",...K()]}],"min-h":[{"min-h":["screen","lh","none",...K()]}],"max-h":[{"max-h":["screen","lh",...K()]}],"font-size":[{text:["base",s,ft,Xe]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,ae,ys]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",gs,ne]}],"font-family":[{font:[Ti,ne,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,ae,ne]}],"line-clamp":[{"line-clamp":[ye,"none",ae,ys]}],leading:[{leading:[i,...k()]}],"list-image":[{"list-image":["none",ae,ne]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ae,ne]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:U()}],"text-color":[{text:U()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[..._(),"wavy"]}],"text-decoration-thickness":[{decoration:[ye,"from-font","auto",ae,Xe]}],"text-decoration-color":[{decoration:U()}],"underline-offset":[{"underline-offset":[ye,"auto",ae,ne]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:k()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ae,ne]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ae,ne]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ee()}],"bg-repeat":[{bg:Y()}],"bg-size":[{bg:se()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Ye,ae,ne],radial:["",ae,ne],conic:[Ye,ae,ne]},Ei,Si]}],"bg-color":[{bg:U()}],"gradient-from-pos":[{from:G()}],"gradient-via-pos":[{via:G()}],"gradient-to-pos":[{to:G()}],"gradient-from":[{from:U()}],"gradient-via":[{via:U()}],"gradient-to":[{to:U()}],rounded:[{rounded:P()}],"rounded-s":[{"rounded-s":P()}],"rounded-e":[{"rounded-e":P()}],"rounded-t":[{"rounded-t":P()}],"rounded-r":[{"rounded-r":P()}],"rounded-b":[{"rounded-b":P()}],"rounded-l":[{"rounded-l":P()}],"rounded-ss":[{"rounded-ss":P()}],"rounded-se":[{"rounded-se":P()}],"rounded-ee":[{"rounded-ee":P()}],"rounded-es":[{"rounded-es":P()}],"rounded-tl":[{"rounded-tl":P()}],"rounded-tr":[{"rounded-tr":P()}],"rounded-br":[{"rounded-br":P()}],"rounded-bl":[{"rounded-bl":P()}],"border-w":[{border:B()}],"border-w-x":[{"border-x":B()}],"border-w-y":[{"border-y":B()}],"border-w-s":[{"border-s":B()}],"border-w-e":[{"border-e":B()}],"border-w-t":[{"border-t":B()}],"border-w-r":[{"border-r":B()}],"border-w-b":[{"border-b":B()}],"border-w-l":[{"border-l":B()}],"divide-x":[{"divide-x":B()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":B()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[..._(),"hidden","none"]}],"divide-style":[{divide:[..._(),"hidden","none"]}],"border-color":[{border:U()}],"border-color-x":[{"border-x":U()}],"border-color-y":[{"border-y":U()}],"border-color-s":[{"border-s":U()}],"border-color-e":[{"border-e":U()}],"border-color-t":[{"border-t":U()}],"border-color-r":[{"border-r":U()}],"border-color-b":[{"border-b":U()}],"border-color-l":[{"border-l":U()}],"divide-color":[{divide:U()}],"outline-style":[{outline:[..._(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ye,ae,ne]}],"outline-w":[{outline:["",ye,ft,Xe]}],"outline-color":[{outline:U()}],shadow:[{shadow:["","none",m,Mt,Dt]}],"shadow-color":[{shadow:U()}],"inset-shadow":[{"inset-shadow":["none",p,Mt,Dt]}],"inset-shadow-color":[{"inset-shadow":U()}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:U()}],"ring-offset-w":[{"ring-offset":[ye,Xe]}],"ring-offset-color":[{"ring-offset":U()}],"inset-ring-w":[{"inset-ring":B()}],"inset-ring-color":[{"inset-ring":U()}],"text-shadow":[{"text-shadow":["none",y,Mt,Dt]}],"text-shadow-color":[{"text-shadow":U()}],opacity:[{opacity:[ye,ae,ne]}],"mix-blend":[{"mix-blend":[...te(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":te()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ye]}],"mask-image-linear-from-pos":[{"mask-linear-from":H()}],"mask-image-linear-to-pos":[{"mask-linear-to":H()}],"mask-image-linear-from-color":[{"mask-linear-from":U()}],"mask-image-linear-to-color":[{"mask-linear-to":U()}],"mask-image-t-from-pos":[{"mask-t-from":H()}],"mask-image-t-to-pos":[{"mask-t-to":H()}],"mask-image-t-from-color":[{"mask-t-from":U()}],"mask-image-t-to-color":[{"mask-t-to":U()}],"mask-image-r-from-pos":[{"mask-r-from":H()}],"mask-image-r-to-pos":[{"mask-r-to":H()}],"mask-image-r-from-color":[{"mask-r-from":U()}],"mask-image-r-to-color":[{"mask-r-to":U()}],"mask-image-b-from-pos":[{"mask-b-from":H()}],"mask-image-b-to-pos":[{"mask-b-to":H()}],"mask-image-b-from-color":[{"mask-b-from":U()}],"mask-image-b-to-color":[{"mask-b-to":U()}],"mask-image-l-from-pos":[{"mask-l-from":H()}],"mask-image-l-to-pos":[{"mask-l-to":H()}],"mask-image-l-from-color":[{"mask-l-from":U()}],"mask-image-l-to-color":[{"mask-l-to":U()}],"mask-image-x-from-pos":[{"mask-x-from":H()}],"mask-image-x-to-pos":[{"mask-x-to":H()}],"mask-image-x-from-color":[{"mask-x-from":U()}],"mask-image-x-to-color":[{"mask-x-to":U()}],"mask-image-y-from-pos":[{"mask-y-from":H()}],"mask-image-y-to-pos":[{"mask-y-to":H()}],"mask-image-y-from-color":[{"mask-y-from":U()}],"mask-image-y-to-color":[{"mask-y-to":U()}],"mask-image-radial":[{"mask-radial":[ae,ne]}],"mask-image-radial-from-pos":[{"mask-radial-from":H()}],"mask-image-radial-to-pos":[{"mask-radial-to":H()}],"mask-image-radial-from-color":[{"mask-radial-from":U()}],"mask-image-radial-to-color":[{"mask-radial-to":U()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":R()}],"mask-image-conic-pos":[{"mask-conic":[ye]}],"mask-image-conic-from-pos":[{"mask-conic-from":H()}],"mask-image-conic-to-pos":[{"mask-conic-to":H()}],"mask-image-conic-from-color":[{"mask-conic-from":U()}],"mask-image-conic-to-color":[{"mask-conic-to":U()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ee()}],"mask-repeat":[{mask:Y()}],"mask-size":[{mask:se()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ae,ne]}],filter:[{filter:["","none",ae,ne]}],blur:[{blur:Z()}],brightness:[{brightness:[ye,ae,ne]}],contrast:[{contrast:[ye,ae,ne]}],"drop-shadow":[{"drop-shadow":["","none",w,Mt,Dt]}],"drop-shadow-color":[{"drop-shadow":U()}],grayscale:[{grayscale:["",ye,ae,ne]}],"hue-rotate":[{"hue-rotate":[ye,ae,ne]}],invert:[{invert:["",ye,ae,ne]}],saturate:[{saturate:[ye,ae,ne]}],sepia:[{sepia:["",ye,ae,ne]}],"backdrop-filter":[{"backdrop-filter":["","none",ae,ne]}],"backdrop-blur":[{"backdrop-blur":Z()}],"backdrop-brightness":[{"backdrop-brightness":[ye,ae,ne]}],"backdrop-contrast":[{"backdrop-contrast":[ye,ae,ne]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ye,ae,ne]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ye,ae,ne]}],"backdrop-invert":[{"backdrop-invert":["",ye,ae,ne]}],"backdrop-opacity":[{"backdrop-opacity":[ye,ae,ne]}],"backdrop-saturate":[{"backdrop-saturate":[ye,ae,ne]}],"backdrop-sepia":[{"backdrop-sepia":["",ye,ae,ne]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":k()}],"border-spacing-x":[{"border-spacing-x":k()}],"border-spacing-y":[{"border-spacing-y":k()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ae,ne]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ye,"initial",ae,ne]}],ease:[{ease:["linear","initial",b,ae,ne]}],delay:[{delay:[ye,ae,ne]}],animate:[{animate:["none",x,ae,ne]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,ae,ne]}],"perspective-origin":[{"perspective-origin":I()}],rotate:[{rotate:ue()}],"rotate-x":[{"rotate-x":ue()}],"rotate-y":[{"rotate-y":ue()}],"rotate-z":[{"rotate-z":ue()}],scale:[{scale:J()}],"scale-x":[{"scale-x":J()}],"scale-y":[{"scale-y":J()}],"scale-z":[{"scale-z":J()}],"scale-3d":["scale-3d"],skew:[{skew:oe()}],"skew-x":[{"skew-x":oe()}],"skew-y":[{"skew-y":oe()}],transform:[{transform:[ae,ne,"","none","gpu","cpu"]}],"transform-origin":[{origin:I()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:pe()}],"translate-x":[{"translate-x":pe()}],"translate-y":[{"translate-y":pe()}],"translate-z":[{"translate-z":pe()}],"translate-none":["translate-none"],accent:[{accent:U()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:U()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ae,ne]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":k()}],"scroll-mx":[{"scroll-mx":k()}],"scroll-my":[{"scroll-my":k()}],"scroll-ms":[{"scroll-ms":k()}],"scroll-me":[{"scroll-me":k()}],"scroll-mt":[{"scroll-mt":k()}],"scroll-mr":[{"scroll-mr":k()}],"scroll-mb":[{"scroll-mb":k()}],"scroll-ml":[{"scroll-ml":k()}],"scroll-p":[{"scroll-p":k()}],"scroll-px":[{"scroll-px":k()}],"scroll-py":[{"scroll-py":k()}],"scroll-ps":[{"scroll-ps":k()}],"scroll-pe":[{"scroll-pe":k()}],"scroll-pt":[{"scroll-pt":k()}],"scroll-pr":[{"scroll-pr":k()}],"scroll-pb":[{"scroll-pb":k()}],"scroll-pl":[{"scroll-pl":k()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ae,ne]}],fill:[{fill:["none",...U()]}],"stroke-w":[{stroke:[ye,ft,Xe,ys]}],stroke:[{stroke:["none",...U()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Fn=pi(Li);function S(...e){return Fn(Bs(e))}const Hs=e=>typeof e=="string"&&["xs","sm","md","lg","xl"].includes(e),Gs=e=>typeof e=="string"&&["default","primary","secondary","outline","ghost","glass","gradient","destructive","success","warning","info"].includes(e),_n=e=>typeof e=="string"&&["subtle","light","medium","strong","intense"].includes(e),Ii=e=>typeof e=="string"&&["surface","elevated","floating","modal"].includes(e),Un=e=>typeof e=="string"&&["fadeIn","slideUp","slideDown","slideLeft","slideRight","scale","rotate","bounce","pulse","wiggle","none"].includes(e),Oi=e=>typeof e=="string"&&["idle","loading","success","error"].includes(e),Di=e=>typeof e=="string"&&["neutral","primary","secondary","accent","success","warning","error","info"].includes(e),$n=e=>{const t=[];return e.className!==void 0&&typeof e.className!="string"&&t.push("className must be a string"),e.id!==void 0&&typeof e.id!="string"&&t.push("id must be a string"),e["data-testid"]!==void 0&&typeof e["data-testid"]!="string"&&t.push("data-testid must be a string"),e["aria-label"]!==void 0&&typeof e["aria-label"]!="string"&&t.push("aria-label must be a string"),e["aria-describedby"]!==void 0&&typeof e["aria-describedby"]!="string"&&t.push("aria-describedby must be a string"),t.length>0?(console.warn("BaseComponentProps validation errors:",t),!1):!0},zn=e=>{const t=[];return e.variant!==void 0&&!Gs(e.variant)&&t.push("variant must be one of: default, primary, secondary, outline, ghost, glass, gradient, destructive, success, warning, info"),e.size!==void 0&&!Hs(e.size)&&t.push("size must be one of: xs, sm, md, lg, xl"),t.length>0?(console.warn("VariantProps validation errors:",t),!1):!0},Qn=e=>{const t=[];return e.name!==void 0&&typeof e.name!="string"&&t.push("name must be a string"),e.required!==void 0&&typeof e.required!="boolean"&&t.push("required must be a boolean"),e.placeholder!==void 0&&typeof e.placeholder!="string"&&t.push("placeholder must be a string"),e.label!==void 0&&typeof e.label!="string"&&t.push("label must be a string"),e.helperText!==void 0&&typeof e.helperText!="string"&&t.push("helperText must be a string"),e.error!==void 0&&typeof e.error!="string"&&t.push("error must be a string"),e.showError!==void 0&&typeof e.showError!="boolean"&&t.push("showError must be a boolean"),t.length>0?(console.warn("FormComponentProps validation errors:",t),!1):!0},Ks=e=>$n(e)&&zn(e),Mi=e=>Ks(e)&&Qn(e),qn=e=>{const t={...e};return t.variant&&!Gs(t.variant)&&(console.warn(`Invalid variant "${t.variant}", falling back to "default"`),t.variant="default"),t.size&&!Hs(t.size)&&(console.warn(`Invalid size "${t.size}", falling back to "md"`),t.size="md"),t.glassIntensity&&!_n(t.glassIntensity)&&(console.warn(`Invalid glassIntensity "${t.glassIntensity}", falling back to "medium"`),t.glassIntensity="medium"),t.animation&&!Un(t.animation)&&(console.warn(`Invalid animation "${t.animation}", falling back to "fadeIn"`),t.animation="fadeIn"),t},Wt=process.env.NODE_ENV==="development",Fi=(e,t,s)=>{Wt&&(e(t)||console.warn(`${s}: Invalid props detected. Check the console for details.`))},_i=(e,t)=>{const s=n=>(Wt&&t(n),me.createElement(e,n));return s.displayName=`WithPropValidation(${e.displayName||e.name})`,s},Ui=(e,t)=>{const s={};return Object.entries(e).forEach(([n,a])=>{const i=t[n]||n;s[i]=a}),s},$i={button:{color:"variant",isLoading:"loading",isDisabled:"disabled",leftIcon:"icon",rightIcon:"icon",iconPosition:"iconPosition"},input:{isInvalid:"error",isRequired:"required",isDisabled:"disabled",helperText:"helperText",errorMessage:"error"},card:{shadow:"elevation",isClickable:"clickable",borderRadius:"variant"}},Bn=e=>{e.glass&&e.variant==="glass"&&console.warn("Both glass prop and glass variant are set. Consider using only one."),e.disableAnimation&&e.animation&&e.animation!=="none"&&console.warn("Animation is disabled but animation preset is set. Animation will be ignored."),e.interactive===!1&&e.hoverable===!0&&console.warn("Component is not interactive but hoverable is enabled. Hover effects may not work as expected.")},zi=(e,t)=>{const s={...e,...t};return Wt&&(Ks(s),Bn(s)),qn(s)};function Ws(...e){return Fn(Bs(e))}const Vn=e=>{const t={default:"bg-primary text-primary-foreground",primary:"bg-blue-600 text-white",secondary:"bg-secondary text-secondary-foreground",outline:"border border-input bg-background",ghost:"hover:bg-accent hover:text-accent-foreground",glass:"backdrop-blur-md bg-white/10 dark:bg-white/5 border border-white/20",gradient:"bg-gradient-to-r from-purple-600 to-pink-600 text-white",destructive:"bg-destructive text-destructive-foreground",success:"bg-green-600 text-white",warning:"bg-yellow-600 text-white",info:"bg-blue-600 text-white"};return t[e]||t.default},Je=(e,t="button")=>{const s={button:{xs:"h-7 px-2 py-1 text-xs",sm:"h-9 px-3 py-2 text-sm",md:"h-10 px-4 py-2 text-sm",lg:"h-11 px-6 py-3 text-base",xl:"h-12 px-8 py-4 text-lg"},input:{xs:"h-7 px-2 py-1 text-xs",sm:"h-9 px-3 py-2 text-sm",md:"h-10 px-3 py-2 text-sm",lg:"h-11 px-4 py-3 text-base",xl:"h-12 px-4 py-3 text-lg"},card:{xs:"p-2 rounded-md",sm:"p-3 rounded-md",md:"p-4 rounded-lg",lg:"p-6 rounded-lg",xl:"p-8 rounded-xl"},icon:{xs:"w-4 h-4",sm:"w-5 h-5",md:"w-6 h-6",lg:"w-7 h-7",xl:"w-8 h-8"}};return s[t]?.[e]||s.button[e]},Hn=(e,t=!1)=>{const s="backdrop-blur-md",n={subtle:"bg-white/5 dark:bg-white/5",light:"bg-white/10 dark:bg-white/10",medium:"bg-white/15 dark:bg-white/15",strong:"bg-white/20 dark:bg-white/20",intense:"bg-white/25 dark:bg-white/25"},a="border border-white/20 dark:border-white/10",i=t?"hover:bg-white/20 dark:hover:bg-white/20":"";return Ws(s,n[e],a,i)},Qi=e=>({fadeIn:"animate-fadeIn",slideUp:"animate-slideUp",slideDown:"animate-slideDown",slideLeft:"animate-slideLeft",slideRight:"animate-slideRight",scale:"animate-scale",rotate:"animate-rotate",bounce:"animate-bounce",pulse:"animate-pulse",wiggle:"animate-wiggle",none:""})[e]||"",qi=e=>{const{className:t,id:s,style:n,"data-testid":a,"aria-label":i,"aria-describedby":o,variant:l,size:u,animation:d,disableAnimation:m,animationDuration:p,animationDelay:y,disabled:w,loading:g,interactive:f,hoverable:h,glass:b,glassIntensity:x,glassDepth:N,glassConfig:R,...I}=e;return{baseProps:{className:t,id:s,style:n,"data-testid":a,"aria-label":i,"aria-describedby":o},variantProps:{variant:l,size:u},animationProps:{animation:d,disableAnimation:m,animationDuration:p,animationDelay:y},interactiveProps:{disabled:w,loading:g,interactive:f,hoverable:h},glassProps:{glass:b,glassIntensity:x,glassDepth:N,glassConfig:R},remainingProps:I}},Ys=e=>{const t={};return Object.entries(e).forEach(([s,n])=>{n!==void 0&&(t[s]=n)}),t},Gn=(...e)=>{const t={};return e.forEach(s=>{Object.assign(t,Ys(s))}),t},Kn=(e,t)=>{const s=[e];return Object.entries(t).forEach(([n,a])=>{a&&s.push(`${n}:${a}`)}),Ws(...s)},Bi=(e,t)=>{const{base:s,sm:n,md:a,lg:i,xl:o,"2xl":l}=t,u=s?Je(s,e):"",d={sm:n?Je(n,e):void 0,md:a?Je(a,e):void 0,lg:i?Je(i,e):void 0,xl:o?Je(o,e):void 0,"2xl":l?Je(l,e):void 0};return Kn(u,d)},Vi=e=>{const{disabled:t,loading:s,error:n,required:a,expanded:i,pressed:o,selected:l}=e;return Ys({"aria-disabled":t,"aria-busy":s,"aria-invalid":!!n,"aria-required":a,"aria-expanded":i,"aria-pressed":o,"aria-selected":l})},Hi=(e="default")=>Ws("focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",{default:"focus-visible:ring-primary",primary:"focus-visible:ring-blue-500",secondary:"focus-visible:ring-gray-500",outline:"focus-visible:ring-primary",ghost:"focus-visible:ring-primary",glass:"focus-visible:ring-white/50",gradient:"focus-visible:ring-purple-500",destructive:"focus-visible:ring-red-500",success:"focus-visible:ring-green-500",warning:"focus-visible:ring-yellow-500",info:"focus-visible:ring-blue-500"}[e]),Yt=e=>{const t=new Map;return(...s)=>{const n=JSON.stringify(s);if(t.has(n))return t.get(n);const a=e(...s);return t.set(n,a),a}},Gi=Yt(Vn),Ki=Yt(Je),Wi=Yt(Hn),Yi=e=>t=>Gn(e,t),Ji=e=>t=>e(t),Xi=c.createContext(void 0);function Zi({children:e,defaultTheme:t="system",storageKey:s="luminar-ui-theme"}){const[n,a]=c.useState(()=>typeof window<"u"&&localStorage.getItem(s)||t),[i,o]=c.useState("light"),[l,u]=c.useState(!1);c.useEffect(()=>{u(!0)},[]),c.useEffect(()=>{if(!l)return;const w=window.document.documentElement,g=window.document.body,f=h=>{let b;h==="system"?b=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":b=h==="dark"?"dark":"light",w.classList.remove("light","dark"),g.classList.remove("light","dark"),w.classList.add(b),g.classList.add(b),w.setAttribute("data-theme",b),g.setAttribute("data-theme",b),b==="dark"?(w.style.setProperty("--theme-bg","15 23 42"),w.style.setProperty("--theme-fg","248 250 252"),w.style.setProperty("--theme-glass-bg","15 23 42"),w.style.setProperty("--theme-glass-border","71 85 105"),w.style.setProperty("--theme-accent","59 130 246"),w.style.setProperty("--nav-text","248 250 252"),w.style.setProperty("--nav-text-hover","147 197 253"),w.style.setProperty("--nav-text-active","96 165 250")):(w.style.setProperty("--theme-bg","248 250 252"),w.style.setProperty("--theme-fg","0 0 0"),w.style.setProperty("--theme-glass-bg","255 255 255"),w.style.setProperty("--theme-glass-border","203 213 225"),w.style.setProperty("--theme-accent","59 130 246"),w.style.setProperty("--nav-text","55 65 81"),w.style.setProperty("--nav-text-hover","37 99 235"),w.style.setProperty("--nav-text-active","29 78 216")),o(b)};if(f(n),localStorage.setItem(s,n),n==="system"){const h=window.matchMedia("(prefers-color-scheme: dark)"),b=()=>f("system");return h.addEventListener("change",b),()=>h.removeEventListener("change",b)}},[n,l,s]);const y=l?{theme:n,resolvedTheme:i,setTheme:a,toggleTheme:()=>{a(i==="dark"?"light":"dark")}}:{theme:t,resolvedTheme:"light",setTheme:()=>{},toggleTheme:()=>{}};return r.jsx(Xi.Provider,{value:y,children:e})}const eo={},Gr=e=>{let t;const s=new Set,n=(m,p)=>{const y=typeof m=="function"?m(t):m;if(!Object.is(y,t)){const w=t;t=p??(typeof y!="object"||y===null)?y:Object.assign({},t,y),s.forEach(g=>g(t,w))}},a=()=>t,u={setState:n,getState:a,getInitialState:()=>d,subscribe:m=>(s.add(m),()=>s.delete(m)),destroy:()=>{(eo?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),s.clear()}},d=t=e(n,a,u);return u},to=e=>e?Gr(e):Gr;function so(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ft={exports:{}},bs={},_t={exports:{}},xs={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kr;function ro(){if(Kr)return xs;Kr=1;var e=c;function t(p,y){return p===y&&(p!==0||1/p===1/y)||p!==p&&y!==y}var s=typeof Object.is=="function"?Object.is:t,n=e.useState,a=e.useEffect,i=e.useLayoutEffect,o=e.useDebugValue;function l(p,y){var w=y(),g=n({inst:{value:w,getSnapshot:y}}),f=g[0].inst,h=g[1];return i(function(){f.value=w,f.getSnapshot=y,u(f)&&h({inst:f})},[p,w,y]),a(function(){return u(f)&&h({inst:f}),p(function(){u(f)&&h({inst:f})})},[p]),o(w),w}function u(p){var y=p.getSnapshot;p=p.value;try{var w=y();return!s(p,w)}catch{return!0}}function d(p,y){return y()}var m=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?d:l;return xs.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:m,xs}var ws={};/**
 * @license React
 * use-sync-external-store-shim.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wr;function no(){return Wr||(Wr=1,process.env.NODE_ENV!=="production"&&function(){function e(w,g){return w===g&&(w!==0||1/w===1/g)||w!==w&&g!==g}function t(w,g){m||a.startTransition===void 0||(m=!0,console.error("You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release."));var f=g();if(!p){var h=g();i(f,h)||(console.error("The result of getSnapshot should be cached to avoid an infinite loop"),p=!0)}h=o({inst:{value:f,getSnapshot:g}});var b=h[0].inst,x=h[1];return u(function(){b.value=f,b.getSnapshot=g,s(b)&&x({inst:b})},[w,f,g]),l(function(){return s(b)&&x({inst:b}),w(function(){s(b)&&x({inst:b})})},[w]),d(f),f}function s(w){var g=w.getSnapshot;w=w.value;try{var f=g();return!i(w,f)}catch{return!0}}function n(w,g){return g()}typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());var a=c,i=typeof Object.is=="function"?Object.is:e,o=a.useState,l=a.useEffect,u=a.useLayoutEffect,d=a.useDebugValue,m=!1,p=!1,y=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?n:t;ws.useSyncExternalStore=a.useSyncExternalStore!==void 0?a.useSyncExternalStore:y,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error())}()),ws}var Yr;function Wn(){return Yr||(Yr=1,process.env.NODE_ENV==="production"?_t.exports=ro():_t.exports=no()),_t.exports}/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jr;function ao(){if(Jr)return bs;Jr=1;var e=c,t=Wn();function s(d,m){return d===m&&(d!==0||1/d===1/m)||d!==d&&m!==m}var n=typeof Object.is=="function"?Object.is:s,a=t.useSyncExternalStore,i=e.useRef,o=e.useEffect,l=e.useMemo,u=e.useDebugValue;return bs.useSyncExternalStoreWithSelector=function(d,m,p,y,w){var g=i(null);if(g.current===null){var f={hasValue:!1,value:null};g.current=f}else f=g.current;g=l(function(){function b(j){if(!x){if(x=!0,N=j,j=y(j),w!==void 0&&f.hasValue){var E=f.value;if(w(E,j))return R=E}return R=j}if(E=R,n(N,j))return E;var k=y(j);return w!==void 0&&w(E,k)?(N=j,E):(N=j,R=k)}var x=!1,N,R,I=p===void 0?null:p;return[function(){return b(m())},I===null?void 0:function(){return b(I())}]},[m,p,y,w]);var h=a(d,g[0],g[1]);return o(function(){f.hasValue=!0,f.value=h},[h]),u(h),h},bs}var vs={};/**
 * @license React
 * use-sync-external-store-shim/with-selector.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xr;function io(){return Xr||(Xr=1,process.env.NODE_ENV!=="production"&&function(){function e(d,m){return d===m&&(d!==0||1/d===1/m)||d!==d&&m!==m}typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());var t=c,s=Wn(),n=typeof Object.is=="function"?Object.is:e,a=s.useSyncExternalStore,i=t.useRef,o=t.useEffect,l=t.useMemo,u=t.useDebugValue;vs.useSyncExternalStoreWithSelector=function(d,m,p,y,w){var g=i(null);if(g.current===null){var f={hasValue:!1,value:null};g.current=f}else f=g.current;g=l(function(){function b(j){if(!x){if(x=!0,N=j,j=y(j),w!==void 0&&f.hasValue){var E=f.value;if(w(E,j))return R=E}return R=j}if(E=R,n(N,j))return E;var k=y(j);return w!==void 0&&w(E,k)?(N=j,E):(N=j,R=k)}var x=!1,N,R,I=p===void 0?null:p;return[function(){return b(m())},I===null?void 0:function(){return b(I())}]},[m,p,y,w]);var h=a(d,g[0],g[1]);return o(function(){f.hasValue=!0,f.value=h},[h]),u(h),h},typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error())}()),vs}var Zr;function oo(){return Zr||(Zr=1,process.env.NODE_ENV==="production"?Ft.exports=ao():Ft.exports=io()),Ft.exports}var lo=oo();const co=so(lo),Yn={},{useDebugValue:uo}=c,{useSyncExternalStoreWithSelector:mo}=co;let en=!1;const po=e=>e;function fo(e,t=po,s){(Yn?"production":void 0)!=="production"&&s&&!en&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),en=!0);const n=mo(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,s);return uo(n),n}const tn=e=>{(Yn?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?to(e):e,s=(n,a)=>fo(t,n,a);return Object.assign(s,t),s},ho=e=>e?tn(e):tn,go={};function Jn(e,t){let s;try{s=e()}catch{return}return{getItem:a=>{var i;const o=u=>u===null?null:JSON.parse(u,void 0),l=(i=s.getItem(a))!=null?i:null;return l instanceof Promise?l.then(o):o(l)},setItem:(a,i)=>s.setItem(a,JSON.stringify(i,void 0)),removeItem:a=>s.removeItem(a)}}const yt=e=>t=>{try{const s=e(t);return s instanceof Promise?s:{then(n){return yt(n)(s)},catch(n){return this}}}catch(s){return{then(n){return this},catch(n){return yt(n)(s)}}}},yo=(e,t)=>(s,n,a)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:h=>h,version:0,merge:(h,b)=>({...b,...h}),...t},o=!1;const l=new Set,u=new Set;let d;try{d=i.getStorage()}catch{}if(!d)return e((...h)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),s(...h)},n,a);const m=yt(i.serialize),p=()=>{const h=i.partialize({...n()});let b;const x=m({state:h,version:i.version}).then(N=>d.setItem(i.name,N)).catch(N=>{b=N});if(b)throw b;return x},y=a.setState;a.setState=(h,b)=>{y(h,b),p()};const w=e((...h)=>{s(...h),p()},n,a);let g;const f=()=>{var h;if(!d)return;o=!1,l.forEach(x=>x(n()));const b=((h=i.onRehydrateStorage)==null?void 0:h.call(i,n()))||void 0;return yt(d.getItem.bind(d))(i.name).then(x=>{if(x)return i.deserialize(x)}).then(x=>{if(x)if(typeof x.version=="number"&&x.version!==i.version){if(i.migrate)return i.migrate(x.state,x.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return x.state}).then(x=>{var N;return g=i.merge(x,(N=n())!=null?N:w),s(g,!0),p()}).then(()=>{b?.(g,void 0),o=!0,u.forEach(x=>x(g))}).catch(x=>{b?.(void 0,x)})};return a.persist={setOptions:h=>{i={...i,...h},h.getStorage&&(d=h.getStorage())},clearStorage:()=>{d?.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>f(),hasHydrated:()=>o,onHydrate:h=>(l.add(h),()=>{l.delete(h)}),onFinishHydration:h=>(u.add(h),()=>{u.delete(h)})},f(),g||w},bo=(e,t)=>(s,n,a)=>{let i={storage:Jn(()=>localStorage),partialize:f=>f,version:0,merge:(f,h)=>({...h,...f}),...t},o=!1;const l=new Set,u=new Set;let d=i.storage;if(!d)return e((...f)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),s(...f)},n,a);const m=()=>{const f=i.partialize({...n()});return d.setItem(i.name,{state:f,version:i.version})},p=a.setState;a.setState=(f,h)=>{p(f,h),m()};const y=e((...f)=>{s(...f),m()},n,a);a.getInitialState=()=>y;let w;const g=()=>{var f,h;if(!d)return;o=!1,l.forEach(x=>{var N;return x((N=n())!=null?N:y)});const b=((h=i.onRehydrateStorage)==null?void 0:h.call(i,(f=n())!=null?f:y))||void 0;return yt(d.getItem.bind(d))(i.name).then(x=>{if(x)if(typeof x.version=="number"&&x.version!==i.version){if(i.migrate)return[!0,i.migrate(x.state,x.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,x.state];return[!1,void 0]}).then(x=>{var N;const[R,I]=x;if(w=i.merge(I,(N=n())!=null?N:y),s(w,!0),R)return m()}).then(()=>{b?.(w,void 0),w=n(),o=!0,u.forEach(x=>x(w))}).catch(x=>{b?.(void 0,x)})};return a.persist={setOptions:f=>{i={...i,...f},f.storage&&(d=f.storage)},clearStorage:()=>{d?.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>g(),hasHydrated:()=>o,onHydrate:f=>(l.add(f),()=>{l.delete(f)}),onFinishHydration:f=>(u.add(f),()=>{u.delete(f)})},i.skipHydration||g(),w||y},xo=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((go?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),yo(e,t)):bo(e,t),wo=xo;function Xn(e,t){return function(){return e.apply(t,arguments)}}const{toString:vo}=Object.prototype,{getPrototypeOf:Js}=Object,{iterator:Jt,toStringTag:Zn}=Symbol,Xt=(e=>t=>{const s=vo.call(t);return e[s]||(e[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Me=e=>(e=e.toLowerCase(),t=>Xt(t)===e),Zt=e=>t=>typeof t===e,{isArray:mt}=Array,bt=Zt("undefined");function jo(e){return e!==null&&!bt(e)&&e.constructor!==null&&!bt(e.constructor)&&Ie(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const ea=Me("ArrayBuffer");function Co(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&ea(e.buffer),t}const ko=Zt("string"),Ie=Zt("function"),ta=Zt("number"),es=e=>e!==null&&typeof e=="object",No=e=>e===!0||e===!1,$t=e=>{if(Xt(e)!=="object")return!1;const t=Js(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Zn in e)&&!(Jt in e)},So=Me("Date"),To=Me("File"),Ro=Me("Blob"),Eo=Me("FileList"),Ao=e=>es(e)&&Ie(e.pipe),Po=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Ie(e.append)&&((t=Xt(e))==="formdata"||t==="object"&&Ie(e.toString)&&e.toString()==="[object FormData]"))},Lo=Me("URLSearchParams"),[Io,Oo,Do,Mo]=["ReadableStream","Request","Response","Headers"].map(Me),Fo=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function wt(e,t,{allOwnKeys:s=!1}={}){if(e===null||typeof e>"u")return;let n,a;if(typeof e!="object"&&(e=[e]),mt(e))for(n=0,a=e.length;n<a;n++)t.call(null,e[n],n,e);else{const i=s?Object.getOwnPropertyNames(e):Object.keys(e),o=i.length;let l;for(n=0;n<o;n++)l=i[n],t.call(null,e[l],l,e)}}function sa(e,t){t=t.toLowerCase();const s=Object.keys(e);let n=s.length,a;for(;n-- >0;)if(a=s[n],t===a.toLowerCase())return a;return null}const Ze=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,ra=e=>!bt(e)&&e!==Ze;function Ms(){const{caseless:e}=ra(this)&&this||{},t={},s=(n,a)=>{const i=e&&sa(t,a)||a;$t(t[i])&&$t(n)?t[i]=Ms(t[i],n):$t(n)?t[i]=Ms({},n):mt(n)?t[i]=n.slice():t[i]=n};for(let n=0,a=arguments.length;n<a;n++)arguments[n]&&wt(arguments[n],s);return t}const _o=(e,t,s,{allOwnKeys:n}={})=>(wt(t,(a,i)=>{s&&Ie(a)?e[i]=Xn(a,s):e[i]=a},{allOwnKeys:n}),e),Uo=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),$o=(e,t,s,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),s&&Object.assign(e.prototype,s)},zo=(e,t,s,n)=>{let a,i,o;const l={};if(t=t||{},e==null)return t;do{for(a=Object.getOwnPropertyNames(e),i=a.length;i-- >0;)o=a[i],(!n||n(o,e,t))&&!l[o]&&(t[o]=e[o],l[o]=!0);e=s!==!1&&Js(e)}while(e&&(!s||s(e,t))&&e!==Object.prototype);return t},Qo=(e,t,s)=>{e=String(e),(s===void 0||s>e.length)&&(s=e.length),s-=t.length;const n=e.indexOf(t,s);return n!==-1&&n===s},qo=e=>{if(!e)return null;if(mt(e))return e;let t=e.length;if(!ta(t))return null;const s=new Array(t);for(;t-- >0;)s[t]=e[t];return s},Bo=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Js(Uint8Array)),Vo=(e,t)=>{const n=(e&&e[Jt]).call(e);let a;for(;(a=n.next())&&!a.done;){const i=a.value;t.call(e,i[0],i[1])}},Ho=(e,t)=>{let s;const n=[];for(;(s=e.exec(t))!==null;)n.push(s);return n},Go=Me("HTMLFormElement"),Ko=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,n,a){return n.toUpperCase()+a}),sn=(({hasOwnProperty:e})=>(t,s)=>e.call(t,s))(Object.prototype),Wo=Me("RegExp"),na=(e,t)=>{const s=Object.getOwnPropertyDescriptors(e),n={};wt(s,(a,i)=>{let o;(o=t(a,i,e))!==!1&&(n[i]=o||a)}),Object.defineProperties(e,n)},Yo=e=>{na(e,(t,s)=>{if(Ie(e)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const n=e[s];if(Ie(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},Jo=(e,t)=>{const s={},n=a=>{a.forEach(i=>{s[i]=!0})};return mt(e)?n(e):n(String(e).split(t)),s},Xo=()=>{},Zo=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function el(e){return!!(e&&Ie(e.append)&&e[Zn]==="FormData"&&e[Jt])}const tl=e=>{const t=new Array(10),s=(n,a)=>{if(es(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[a]=n;const i=mt(n)?[]:{};return wt(n,(o,l)=>{const u=s(o,a+1);!bt(u)&&(i[l]=u)}),t[a]=void 0,i}}return n};return s(e,0)},sl=Me("AsyncFunction"),rl=e=>e&&(es(e)||Ie(e))&&Ie(e.then)&&Ie(e.catch),aa=((e,t)=>e?setImmediate:t?((s,n)=>(Ze.addEventListener("message",({source:a,data:i})=>{a===Ze&&i===s&&n.length&&n.shift()()},!1),a=>{n.push(a),Ze.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",Ie(Ze.postMessage)),nl=typeof queueMicrotask<"u"?queueMicrotask.bind(Ze):typeof process<"u"&&process.nextTick||aa,al=e=>e!=null&&Ie(e[Jt]),Q={isArray:mt,isArrayBuffer:ea,isBuffer:jo,isFormData:Po,isArrayBufferView:Co,isString:ko,isNumber:ta,isBoolean:No,isObject:es,isPlainObject:$t,isReadableStream:Io,isRequest:Oo,isResponse:Do,isHeaders:Mo,isUndefined:bt,isDate:So,isFile:To,isBlob:Ro,isRegExp:Wo,isFunction:Ie,isStream:Ao,isURLSearchParams:Lo,isTypedArray:Bo,isFileList:Eo,forEach:wt,merge:Ms,extend:_o,trim:Fo,stripBOM:Uo,inherits:$o,toFlatObject:zo,kindOf:Xt,kindOfTest:Me,endsWith:Qo,toArray:qo,forEachEntry:Vo,matchAll:Ho,isHTMLForm:Go,hasOwnProperty:sn,hasOwnProp:sn,reduceDescriptors:na,freezeMethods:Yo,toObjectSet:Jo,toCamelCase:Ko,noop:Xo,toFiniteNumber:Zo,findKey:sa,global:Ze,isContextDefined:ra,isSpecCompliantForm:el,toJSONObject:tl,isAsyncFn:sl,isThenable:rl,setImmediate:aa,asap:nl,isIterable:al};function ge(e,t,s,n,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),s&&(this.config=s),n&&(this.request=n),a&&(this.response=a,this.status=a.status?a.status:null)}Q.inherits(ge,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Q.toJSONObject(this.config),code:this.code,status:this.status}}});const ia=ge.prototype,oa={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{oa[e]={value:e}});Object.defineProperties(ge,oa);Object.defineProperty(ia,"isAxiosError",{value:!0});ge.from=(e,t,s,n,a,i)=>{const o=Object.create(ia);return Q.toFlatObject(e,o,function(u){return u!==Error.prototype},l=>l!=="isAxiosError"),ge.call(o,e.message,t,s,n,a),o.cause=e,o.name=e.name,i&&Object.assign(o,i),o};const il=null;function Fs(e){return Q.isPlainObject(e)||Q.isArray(e)}function la(e){return Q.endsWith(e,"[]")?e.slice(0,-2):e}function rn(e,t,s){return e?e.concat(t).map(function(a,i){return a=la(a),!s&&i?"["+a+"]":a}).join(s?".":""):t}function ol(e){return Q.isArray(e)&&!e.some(Fs)}const ll=Q.toFlatObject(Q,{},null,function(t){return/^is[A-Z]/.test(t)});function ts(e,t,s){if(!Q.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,s=Q.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(f,h){return!Q.isUndefined(h[f])});const n=s.metaTokens,a=s.visitor||m,i=s.dots,o=s.indexes,u=(s.Blob||typeof Blob<"u"&&Blob)&&Q.isSpecCompliantForm(t);if(!Q.isFunction(a))throw new TypeError("visitor must be a function");function d(g){if(g===null)return"";if(Q.isDate(g))return g.toISOString();if(Q.isBoolean(g))return g.toString();if(!u&&Q.isBlob(g))throw new ge("Blob is not supported. Use a Buffer instead.");return Q.isArrayBuffer(g)||Q.isTypedArray(g)?u&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function m(g,f,h){let b=g;if(g&&!h&&typeof g=="object"){if(Q.endsWith(f,"{}"))f=n?f:f.slice(0,-2),g=JSON.stringify(g);else if(Q.isArray(g)&&ol(g)||(Q.isFileList(g)||Q.endsWith(f,"[]"))&&(b=Q.toArray(g)))return f=la(f),b.forEach(function(N,R){!(Q.isUndefined(N)||N===null)&&t.append(o===!0?rn([f],R,i):o===null?f:f+"[]",d(N))}),!1}return Fs(g)?!0:(t.append(rn(h,f,i),d(g)),!1)}const p=[],y=Object.assign(ll,{defaultVisitor:m,convertValue:d,isVisitable:Fs});function w(g,f){if(!Q.isUndefined(g)){if(p.indexOf(g)!==-1)throw Error("Circular reference detected in "+f.join("."));p.push(g),Q.forEach(g,function(b,x){(!(Q.isUndefined(b)||b===null)&&a.call(t,b,Q.isString(x)?x.trim():x,f,y))===!0&&w(b,f?f.concat(x):[x])}),p.pop()}}if(!Q.isObject(e))throw new TypeError("data must be an object");return w(e),t}function nn(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function Xs(e,t){this._pairs=[],e&&ts(e,this,t)}const ca=Xs.prototype;ca.append=function(t,s){this._pairs.push([t,s])};ca.toString=function(t){const s=t?function(n){return t.call(this,n,nn)}:nn;return this._pairs.map(function(a){return s(a[0])+"="+s(a[1])},"").join("&")};function cl(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ua(e,t,s){if(!t)return e;const n=s&&s.encode||cl;Q.isFunction(s)&&(s={serialize:s});const a=s&&s.serialize;let i;if(a?i=a(t,s):i=Q.isURLSearchParams(t)?t.toString():new Xs(t,s).toString(n),i){const o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class an{constructor(){this.handlers=[]}use(t,s,n){return this.handlers.push({fulfilled:t,rejected:s,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){Q.forEach(this.handlers,function(n){n!==null&&t(n)})}}const da={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ul=typeof URLSearchParams<"u"?URLSearchParams:Xs,dl=typeof FormData<"u"?FormData:null,ml=typeof Blob<"u"?Blob:null,pl={isBrowser:!0,classes:{URLSearchParams:ul,FormData:dl,Blob:ml},protocols:["http","https","file","blob","url","data"]},Zs=typeof window<"u"&&typeof document<"u",_s=typeof navigator=="object"&&navigator||void 0,fl=Zs&&(!_s||["ReactNative","NativeScript","NS"].indexOf(_s.product)<0),hl=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",gl=Zs&&window.location.href||"http://localhost",yl=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Zs,hasStandardBrowserEnv:fl,hasStandardBrowserWebWorkerEnv:hl,navigator:_s,origin:gl},Symbol.toStringTag,{value:"Module"})),Pe={...yl,...pl};function bl(e,t){return ts(e,new Pe.classes.URLSearchParams,Object.assign({visitor:function(s,n,a,i){return Pe.isNode&&Q.isBuffer(s)?(this.append(n,s.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function xl(e){return Q.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function wl(e){const t={},s=Object.keys(e);let n;const a=s.length;let i;for(n=0;n<a;n++)i=s[n],t[i]=e[i];return t}function ma(e){function t(s,n,a,i){let o=s[i++];if(o==="__proto__")return!0;const l=Number.isFinite(+o),u=i>=s.length;return o=!o&&Q.isArray(a)?a.length:o,u?(Q.hasOwnProp(a,o)?a[o]=[a[o],n]:a[o]=n,!l):((!a[o]||!Q.isObject(a[o]))&&(a[o]=[]),t(s,n,a[o],i)&&Q.isArray(a[o])&&(a[o]=wl(a[o])),!l)}if(Q.isFormData(e)&&Q.isFunction(e.entries)){const s={};return Q.forEachEntry(e,(n,a)=>{t(xl(n),a,s,0)}),s}return null}function vl(e,t,s){if(Q.isString(e))try{return(t||JSON.parse)(e),Q.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(s||JSON.stringify)(e)}const vt={transitional:da,adapter:["xhr","http","fetch"],transformRequest:[function(t,s){const n=s.getContentType()||"",a=n.indexOf("application/json")>-1,i=Q.isObject(t);if(i&&Q.isHTMLForm(t)&&(t=new FormData(t)),Q.isFormData(t))return a?JSON.stringify(ma(t)):t;if(Q.isArrayBuffer(t)||Q.isBuffer(t)||Q.isStream(t)||Q.isFile(t)||Q.isBlob(t)||Q.isReadableStream(t))return t;if(Q.isArrayBufferView(t))return t.buffer;if(Q.isURLSearchParams(t))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return bl(t,this.formSerializer).toString();if((l=Q.isFileList(t))||n.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return ts(l?{"files[]":t}:t,u&&new u,this.formSerializer)}}return i||a?(s.setContentType("application/json",!1),vl(t)):t}],transformResponse:[function(t){const s=this.transitional||vt.transitional,n=s&&s.forcedJSONParsing,a=this.responseType==="json";if(Q.isResponse(t)||Q.isReadableStream(t))return t;if(t&&Q.isString(t)&&(n&&!this.responseType||a)){const o=!(s&&s.silentJSONParsing)&&a;try{return JSON.parse(t)}catch(l){if(o)throw l.name==="SyntaxError"?ge.from(l,ge.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Pe.classes.FormData,Blob:Pe.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Q.forEach(["delete","get","head","post","put","patch"],e=>{vt.headers[e]={}});const jl=Q.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Cl=e=>{const t={};let s,n,a;return e&&e.split(`
`).forEach(function(o){a=o.indexOf(":"),s=o.substring(0,a).trim().toLowerCase(),n=o.substring(a+1).trim(),!(!s||t[s]&&jl[s])&&(s==="set-cookie"?t[s]?t[s].push(n):t[s]=[n]:t[s]=t[s]?t[s]+", "+n:n)}),t},on=Symbol("internals");function ht(e){return e&&String(e).trim().toLowerCase()}function zt(e){return e===!1||e==null?e:Q.isArray(e)?e.map(zt):String(e)}function kl(e){const t=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=s.exec(e);)t[n[1]]=n[2];return t}const Nl=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function js(e,t,s,n,a){if(Q.isFunction(n))return n.call(this,t,s);if(a&&(t=s),!!Q.isString(t)){if(Q.isString(n))return t.indexOf(n)!==-1;if(Q.isRegExp(n))return n.test(t)}}function Sl(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,s,n)=>s.toUpperCase()+n)}function Tl(e,t){const s=Q.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+s,{value:function(a,i,o){return this[n].call(this,t,a,i,o)},configurable:!0})})}let Oe=class{constructor(t){t&&this.set(t)}set(t,s,n){const a=this;function i(l,u,d){const m=ht(u);if(!m)throw new Error("header name must be a non-empty string");const p=Q.findKey(a,m);(!p||a[p]===void 0||d===!0||d===void 0&&a[p]!==!1)&&(a[p||u]=zt(l))}const o=(l,u)=>Q.forEach(l,(d,m)=>i(d,m,u));if(Q.isPlainObject(t)||t instanceof this.constructor)o(t,s);else if(Q.isString(t)&&(t=t.trim())&&!Nl(t))o(Cl(t),s);else if(Q.isObject(t)&&Q.isIterable(t)){let l={},u,d;for(const m of t){if(!Q.isArray(m))throw TypeError("Object iterator must return a key-value pair");l[d=m[0]]=(u=l[d])?Q.isArray(u)?[...u,m[1]]:[u,m[1]]:m[1]}o(l,s)}else t!=null&&i(s,t,n);return this}get(t,s){if(t=ht(t),t){const n=Q.findKey(this,t);if(n){const a=this[n];if(!s)return a;if(s===!0)return kl(a);if(Q.isFunction(s))return s.call(this,a,n);if(Q.isRegExp(s))return s.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,s){if(t=ht(t),t){const n=Q.findKey(this,t);return!!(n&&this[n]!==void 0&&(!s||js(this,this[n],n,s)))}return!1}delete(t,s){const n=this;let a=!1;function i(o){if(o=ht(o),o){const l=Q.findKey(n,o);l&&(!s||js(n,n[l],l,s))&&(delete n[l],a=!0)}}return Q.isArray(t)?t.forEach(i):i(t),a}clear(t){const s=Object.keys(this);let n=s.length,a=!1;for(;n--;){const i=s[n];(!t||js(this,this[i],i,t,!0))&&(delete this[i],a=!0)}return a}normalize(t){const s=this,n={};return Q.forEach(this,(a,i)=>{const o=Q.findKey(n,i);if(o){s[o]=zt(a),delete s[i];return}const l=t?Sl(i):String(i).trim();l!==i&&delete s[i],s[l]=zt(a),n[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const s=Object.create(null);return Q.forEach(this,(n,a)=>{n!=null&&n!==!1&&(s[a]=t&&Q.isArray(n)?n.join(", "):n)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,s])=>t+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...s){const n=new this(t);return s.forEach(a=>n.set(a)),n}static accessor(t){const n=(this[on]=this[on]={accessors:{}}).accessors,a=this.prototype;function i(o){const l=ht(o);n[l]||(Tl(a,o),n[l]=!0)}return Q.isArray(t)?t.forEach(i):i(t),this}};Oe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);Q.reduceDescriptors(Oe.prototype,({value:e},t)=>{let s=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[s]=n}}});Q.freezeMethods(Oe);function Cs(e,t){const s=this||vt,n=t||s,a=Oe.from(n.headers);let i=n.data;return Q.forEach(e,function(l){i=l.call(s,i,a.normalize(),t?t.status:void 0)}),a.normalize(),i}function pa(e){return!!(e&&e.__CANCEL__)}function pt(e,t,s){ge.call(this,e??"canceled",ge.ERR_CANCELED,t,s),this.name="CanceledError"}Q.inherits(pt,ge,{__CANCEL__:!0});function fa(e,t,s){const n=s.config.validateStatus;!s.status||!n||n(s.status)?e(s):t(new ge("Request failed with status code "+s.status,[ge.ERR_BAD_REQUEST,ge.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function Rl(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function El(e,t){e=e||10;const s=new Array(e),n=new Array(e);let a=0,i=0,o;return t=t!==void 0?t:1e3,function(u){const d=Date.now(),m=n[i];o||(o=d),s[a]=u,n[a]=d;let p=i,y=0;for(;p!==a;)y+=s[p++],p=p%e;if(a=(a+1)%e,a===i&&(i=(i+1)%e),d-o<t)return;const w=m&&d-m;return w?Math.round(y*1e3/w):void 0}}function Al(e,t){let s=0,n=1e3/t,a,i;const o=(d,m=Date.now())=>{s=m,a=null,i&&(clearTimeout(i),i=null),e.apply(null,d)};return[(...d)=>{const m=Date.now(),p=m-s;p>=n?o(d,m):(a=d,i||(i=setTimeout(()=>{i=null,o(a)},n-p)))},()=>a&&o(a)]}const Ht=(e,t,s=3)=>{let n=0;const a=El(50,250);return Al(i=>{const o=i.loaded,l=i.lengthComputable?i.total:void 0,u=o-n,d=a(u),m=o<=l;n=o;const p={loaded:o,total:l,progress:l?o/l:void 0,bytes:u,rate:d||void 0,estimated:d&&l&&m?(l-o)/d:void 0,event:i,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(p)},s)},ln=(e,t)=>{const s=e!=null;return[n=>t[0]({lengthComputable:s,total:e,loaded:n}),t[1]]},cn=e=>(...t)=>Q.asap(()=>e(...t)),Pl=Pe.hasStandardBrowserEnv?((e,t)=>s=>(s=new URL(s,Pe.origin),e.protocol===s.protocol&&e.host===s.host&&(t||e.port===s.port)))(new URL(Pe.origin),Pe.navigator&&/(msie|trident)/i.test(Pe.navigator.userAgent)):()=>!0,Ll=Pe.hasStandardBrowserEnv?{write(e,t,s,n,a,i){const o=[e+"="+encodeURIComponent(t)];Q.isNumber(s)&&o.push("expires="+new Date(s).toGMTString()),Q.isString(n)&&o.push("path="+n),Q.isString(a)&&o.push("domain="+a),i===!0&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Il(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Ol(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function ha(e,t,s){let n=!Il(t);return e&&(n||s==!1)?Ol(e,t):t}const un=e=>e instanceof Oe?{...e}:e;function st(e,t){t=t||{};const s={};function n(d,m,p,y){return Q.isPlainObject(d)&&Q.isPlainObject(m)?Q.merge.call({caseless:y},d,m):Q.isPlainObject(m)?Q.merge({},m):Q.isArray(m)?m.slice():m}function a(d,m,p,y){if(Q.isUndefined(m)){if(!Q.isUndefined(d))return n(void 0,d,p,y)}else return n(d,m,p,y)}function i(d,m){if(!Q.isUndefined(m))return n(void 0,m)}function o(d,m){if(Q.isUndefined(m)){if(!Q.isUndefined(d))return n(void 0,d)}else return n(void 0,m)}function l(d,m,p){if(p in t)return n(d,m);if(p in e)return n(void 0,d)}const u={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:l,headers:(d,m,p)=>a(un(d),un(m),p,!0)};return Q.forEach(Object.keys(Object.assign({},e,t)),function(m){const p=u[m]||a,y=p(e[m],t[m],m);Q.isUndefined(y)&&p!==l||(s[m]=y)}),s}const ga=e=>{const t=st({},e);let{data:s,withXSRFToken:n,xsrfHeaderName:a,xsrfCookieName:i,headers:o,auth:l}=t;t.headers=o=Oe.from(o),t.url=ua(ha(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&o.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let u;if(Q.isFormData(s)){if(Pe.hasStandardBrowserEnv||Pe.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((u=o.getContentType())!==!1){const[d,...m]=u?u.split(";").map(p=>p.trim()).filter(Boolean):[];o.setContentType([d||"multipart/form-data",...m].join("; "))}}if(Pe.hasStandardBrowserEnv&&(n&&Q.isFunction(n)&&(n=n(t)),n||n!==!1&&Pl(t.url))){const d=a&&i&&Ll.read(i);d&&o.set(a,d)}return t},Dl=typeof XMLHttpRequest<"u",Ml=Dl&&function(e){return new Promise(function(s,n){const a=ga(e);let i=a.data;const o=Oe.from(a.headers).normalize();let{responseType:l,onUploadProgress:u,onDownloadProgress:d}=a,m,p,y,w,g;function f(){w&&w(),g&&g(),a.cancelToken&&a.cancelToken.unsubscribe(m),a.signal&&a.signal.removeEventListener("abort",m)}let h=new XMLHttpRequest;h.open(a.method.toUpperCase(),a.url,!0),h.timeout=a.timeout;function b(){if(!h)return;const N=Oe.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),I={data:!l||l==="text"||l==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:N,config:e,request:h};fa(function(E){s(E),f()},function(E){n(E),f()},I),h=null}"onloadend"in h?h.onloadend=b:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(b)},h.onabort=function(){h&&(n(new ge("Request aborted",ge.ECONNABORTED,e,h)),h=null)},h.onerror=function(){n(new ge("Network Error",ge.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let R=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const I=a.transitional||da;a.timeoutErrorMessage&&(R=a.timeoutErrorMessage),n(new ge(R,I.clarifyTimeoutError?ge.ETIMEDOUT:ge.ECONNABORTED,e,h)),h=null},i===void 0&&o.setContentType(null),"setRequestHeader"in h&&Q.forEach(o.toJSON(),function(R,I){h.setRequestHeader(I,R)}),Q.isUndefined(a.withCredentials)||(h.withCredentials=!!a.withCredentials),l&&l!=="json"&&(h.responseType=a.responseType),d&&([y,g]=Ht(d,!0),h.addEventListener("progress",y)),u&&h.upload&&([p,w]=Ht(u),h.upload.addEventListener("progress",p),h.upload.addEventListener("loadend",w)),(a.cancelToken||a.signal)&&(m=N=>{h&&(n(!N||N.type?new pt(null,e,h):N),h.abort(),h=null)},a.cancelToken&&a.cancelToken.subscribe(m),a.signal&&(a.signal.aborted?m():a.signal.addEventListener("abort",m)));const x=Rl(a.url);if(x&&Pe.protocols.indexOf(x)===-1){n(new ge("Unsupported protocol "+x+":",ge.ERR_BAD_REQUEST,e));return}h.send(i||null)})},Fl=(e,t)=>{const{length:s}=e=e?e.filter(Boolean):[];if(t||s){let n=new AbortController,a;const i=function(d){if(!a){a=!0,l();const m=d instanceof Error?d:this.reason;n.abort(m instanceof ge?m:new pt(m instanceof Error?m.message:m))}};let o=t&&setTimeout(()=>{o=null,i(new ge(`timeout ${t} of ms exceeded`,ge.ETIMEDOUT))},t);const l=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(d=>{d.unsubscribe?d.unsubscribe(i):d.removeEventListener("abort",i)}),e=null)};e.forEach(d=>d.addEventListener("abort",i));const{signal:u}=n;return u.unsubscribe=()=>Q.asap(l),u}},_l=function*(e,t){let s=e.byteLength;if(s<t){yield e;return}let n=0,a;for(;n<s;)a=n+t,yield e.slice(n,a),n=a},Ul=async function*(e,t){for await(const s of $l(e))yield*_l(s,t)},$l=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:s,value:n}=await t.read();if(s)break;yield n}}finally{await t.cancel()}},dn=(e,t,s,n)=>{const a=Ul(e,t);let i=0,o,l=u=>{o||(o=!0,n&&n(u))};return new ReadableStream({async pull(u){try{const{done:d,value:m}=await a.next();if(d){l(),u.close();return}let p=m.byteLength;if(s){let y=i+=p;s(y)}u.enqueue(new Uint8Array(m))}catch(d){throw l(d),d}},cancel(u){return l(u),a.return()}},{highWaterMark:2})},ss=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ya=ss&&typeof ReadableStream=="function",zl=ss&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ba=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Ql=ya&&ba(()=>{let e=!1;const t=new Request(Pe.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),mn=64*1024,Us=ya&&ba(()=>Q.isReadableStream(new Response("").body)),Gt={stream:Us&&(e=>e.body)};ss&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Gt[t]&&(Gt[t]=Q.isFunction(e[t])?s=>s[t]():(s,n)=>{throw new ge(`Response type '${t}' is not supported`,ge.ERR_NOT_SUPPORT,n)})})})(new Response);const ql=async e=>{if(e==null)return 0;if(Q.isBlob(e))return e.size;if(Q.isSpecCompliantForm(e))return(await new Request(Pe.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(Q.isArrayBufferView(e)||Q.isArrayBuffer(e))return e.byteLength;if(Q.isURLSearchParams(e)&&(e=e+""),Q.isString(e))return(await zl(e)).byteLength},Bl=async(e,t)=>{const s=Q.toFiniteNumber(e.getContentLength());return s??ql(t)},Vl=ss&&(async e=>{let{url:t,method:s,data:n,signal:a,cancelToken:i,timeout:o,onDownloadProgress:l,onUploadProgress:u,responseType:d,headers:m,withCredentials:p="same-origin",fetchOptions:y}=ga(e);d=d?(d+"").toLowerCase():"text";let w=Fl([a,i&&i.toAbortSignal()],o),g;const f=w&&w.unsubscribe&&(()=>{w.unsubscribe()});let h;try{if(u&&Ql&&s!=="get"&&s!=="head"&&(h=await Bl(m,n))!==0){let I=new Request(t,{method:"POST",body:n,duplex:"half"}),j;if(Q.isFormData(n)&&(j=I.headers.get("content-type"))&&m.setContentType(j),I.body){const[E,k]=ln(h,Ht(cn(u)));n=dn(I.body,mn,E,k)}}Q.isString(p)||(p=p?"include":"omit");const b="credentials"in Request.prototype;g=new Request(t,{...y,signal:w,method:s.toUpperCase(),headers:m.normalize().toJSON(),body:n,duplex:"half",credentials:b?p:void 0});let x=await fetch(g,y);const N=Us&&(d==="stream"||d==="response");if(Us&&(l||N&&f)){const I={};["status","statusText","headers"].forEach(L=>{I[L]=x[L]});const j=Q.toFiniteNumber(x.headers.get("content-length")),[E,k]=l&&ln(j,Ht(cn(l),!0))||[];x=new Response(dn(x.body,mn,E,()=>{k&&k(),f&&f()}),I)}d=d||"text";let R=await Gt[Q.findKey(Gt,d)||"text"](x,e);return!N&&f&&f(),await new Promise((I,j)=>{fa(I,j,{data:R,headers:Oe.from(x.headers),status:x.status,statusText:x.statusText,config:e,request:g})})}catch(b){throw f&&f(),b&&b.name==="TypeError"&&/Load failed|fetch/i.test(b.message)?Object.assign(new ge("Network Error",ge.ERR_NETWORK,e,g),{cause:b.cause||b}):ge.from(b,b&&b.code,e,g)}}),$s={http:il,xhr:Ml,fetch:Vl};Q.forEach($s,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const pn=e=>`- ${e}`,Hl=e=>Q.isFunction(e)||e===null||e===!1,xa={getAdapter:e=>{e=Q.isArray(e)?e:[e];const{length:t}=e;let s,n;const a={};for(let i=0;i<t;i++){s=e[i];let o;if(n=s,!Hl(s)&&(n=$s[(o=String(s)).toLowerCase()],n===void 0))throw new ge(`Unknown adapter '${o}'`);if(n)break;a[o||"#"+i]=n}if(!n){const i=Object.entries(a).map(([l,u])=>`adapter ${l} `+(u===!1?"is not supported by the environment":"is not available in the build"));let o=t?i.length>1?`since :
`+i.map(pn).join(`
`):" "+pn(i[0]):"as no adapter specified";throw new ge("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return n},adapters:$s};function ks(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new pt(null,e)}function fn(e){return ks(e),e.headers=Oe.from(e.headers),e.data=Cs.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),xa.getAdapter(e.adapter||vt.adapter)(e).then(function(n){return ks(e),n.data=Cs.call(e,e.transformResponse,n),n.headers=Oe.from(n.headers),n},function(n){return pa(n)||(ks(e),n&&n.response&&(n.response.data=Cs.call(e,e.transformResponse,n.response),n.response.headers=Oe.from(n.response.headers))),Promise.reject(n)})}const wa="1.10.0",rs={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{rs[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const hn={};rs.transitional=function(t,s,n){function a(i,o){return"[Axios v"+wa+"] Transitional option '"+i+"'"+o+(n?". "+n:"")}return(i,o,l)=>{if(t===!1)throw new ge(a(o," has been removed"+(s?" in "+s:"")),ge.ERR_DEPRECATED);return s&&!hn[o]&&(hn[o]=!0,console.warn(a(o," has been deprecated since v"+s+" and will be removed in the near future"))),t?t(i,o,l):!0}};rs.spelling=function(t){return(s,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function Gl(e,t,s){if(typeof e!="object")throw new ge("options must be an object",ge.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let a=n.length;for(;a-- >0;){const i=n[a],o=t[i];if(o){const l=e[i],u=l===void 0||o(l,i,e);if(u!==!0)throw new ge("option "+i+" must be "+u,ge.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new ge("Unknown option "+i,ge.ERR_BAD_OPTION)}}const Qt={assertOptions:Gl,validators:rs},Fe=Qt.validators;let tt=class{constructor(t){this.defaults=t||{},this.interceptors={request:new an,response:new an}}async request(t,s){try{return await this._request(t,s)}catch(n){if(n instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=new Error;const i=a.stack?a.stack.replace(/^.+\n/,""):"";try{n.stack?i&&!String(n.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+i):n.stack=i}catch{}}throw n}}_request(t,s){typeof t=="string"?(s=s||{},s.url=t):s=t||{},s=st(this.defaults,s);const{transitional:n,paramsSerializer:a,headers:i}=s;n!==void 0&&Qt.assertOptions(n,{silentJSONParsing:Fe.transitional(Fe.boolean),forcedJSONParsing:Fe.transitional(Fe.boolean),clarifyTimeoutError:Fe.transitional(Fe.boolean)},!1),a!=null&&(Q.isFunction(a)?s.paramsSerializer={serialize:a}:Qt.assertOptions(a,{encode:Fe.function,serialize:Fe.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),Qt.assertOptions(s,{baseUrl:Fe.spelling("baseURL"),withXsrfToken:Fe.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let o=i&&Q.merge(i.common,i[s.method]);i&&Q.forEach(["delete","get","head","post","put","patch","common"],g=>{delete i[g]}),s.headers=Oe.concat(o,i);const l=[];let u=!0;this.interceptors.request.forEach(function(f){typeof f.runWhen=="function"&&f.runWhen(s)===!1||(u=u&&f.synchronous,l.unshift(f.fulfilled,f.rejected))});const d=[];this.interceptors.response.forEach(function(f){d.push(f.fulfilled,f.rejected)});let m,p=0,y;if(!u){const g=[fn.bind(this),void 0];for(g.unshift.apply(g,l),g.push.apply(g,d),y=g.length,m=Promise.resolve(s);p<y;)m=m.then(g[p++],g[p++]);return m}y=l.length;let w=s;for(p=0;p<y;){const g=l[p++],f=l[p++];try{w=g(w)}catch(h){f.call(this,h);break}}try{m=fn.call(this,w)}catch(g){return Promise.reject(g)}for(p=0,y=d.length;p<y;)m=m.then(d[p++],d[p++]);return m}getUri(t){t=st(this.defaults,t);const s=ha(t.baseURL,t.url,t.allowAbsoluteUrls);return ua(s,t.params,t.paramsSerializer)}};Q.forEach(["delete","get","head","options"],function(t){tt.prototype[t]=function(s,n){return this.request(st(n||{},{method:t,url:s,data:(n||{}).data}))}});Q.forEach(["post","put","patch"],function(t){function s(n){return function(i,o,l){return this.request(st(l||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:i,data:o}))}}tt.prototype[t]=s(),tt.prototype[t+"Form"]=s(!0)});let Kl=class va{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(i){s=i});const n=this;this.promise.then(a=>{if(!n._listeners)return;let i=n._listeners.length;for(;i-- >0;)n._listeners[i](a);n._listeners=null}),this.promise.then=a=>{let i;const o=new Promise(l=>{n.subscribe(l),i=l}).then(a);return o.cancel=function(){n.unsubscribe(i)},o},t(function(i,o,l){n.reason||(n.reason=new pt(i,o,l),s(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const s=this._listeners.indexOf(t);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const t=new AbortController,s=n=>{t.abort(n)};return this.subscribe(s),t.signal.unsubscribe=()=>this.unsubscribe(s),t.signal}static source(){let t;return{token:new va(function(a){t=a}),cancel:t}}};function Wl(e){return function(s){return e.apply(null,s)}}function Yl(e){return Q.isObject(e)&&e.isAxiosError===!0}const zs={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(zs).forEach(([e,t])=>{zs[t]=e});function ja(e){const t=new tt(e),s=Xn(tt.prototype.request,t);return Q.extend(s,tt.prototype,t,{allOwnKeys:!0}),Q.extend(s,t,null,{allOwnKeys:!0}),s.create=function(a){return ja(st(e,a))},s}const Se=ja(vt);Se.Axios=tt;Se.CanceledError=pt;Se.CancelToken=Kl;Se.isCancel=pa;Se.VERSION=wa;Se.toFormData=ts;Se.AxiosError=ge;Se.Cancel=Se.CanceledError;Se.all=function(t){return Promise.all(t)};Se.spread=Wl;Se.isAxiosError=Yl;Se.mergeConfig=st;Se.AxiosHeaders=Oe;Se.formToJSON=e=>ma(Q.isHTMLForm(e)?new FormData(e):e);Se.getAdapter=xa.getAdapter;Se.HttpStatusCode=zs;Se.default=Se;const{Axios:lh,AxiosError:ch,CanceledError:uh,isCancel:dh,CancelToken:mh,VERSION:ph,all:fh,Cancel:hh,isAxiosError:gh,spread:yh,toFormData:bh,AxiosHeaders:xh,HttpStatusCode:wh,formToJSON:vh,getAdapter:jh,mergeConfig:Ch}=Se;class Jl{listeners=[];subscribe(t){return this.listeners.push(t),()=>{this.listeners=this.listeners.filter(s=>s!==t)}}emit(t,s){this.listeners.forEach(n=>n(t,s)),console.log(`[${s.toUpperCase()}] ${t}`)}success(t){this.emit(t,"success")}error(t){this.emit(t,"error")}warning(t){this.emit(t,"warning")}info(t){this.emit(t,"info")}}const re=new Jl,gn=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3000/api",Xl=3e4,Zl=3,ec=1e3,Ns="ld_access_token",Ss="ld_refresh_token",Ts="ld_user";class tc{client;refreshPromise=null;defaultRetryConfig;constructor(){this.client=Se.create({baseURL:gn,timeout:Xl,headers:{"Content-Type":"application/json","X-Client-Version":"1.0.0"}}),this.defaultRetryConfig={maxRetries:Zl,baseDelay:ec,maxDelay:1e4,retryCondition:t=>!t.response||t.code==="ECONNABORTED"||t.response.status>=500&&t.response.status<600||t.response.status===429},this.setupInterceptors()}setupInterceptors(){this.client.interceptors.request.use(t=>{const s=this.getAccessToken();s&&(t.headers.Authorization=`Bearer ${s}`);const n=this.generateRequestId();t.headers["X-Request-ID"]=n;const a={startTime:Date.now(),retryCount:0,endpoint:t.url||"",method:t.method?.toUpperCase()||"GET"};return t.metadata=a,this.logRequest(t),t},t=>(console.error("Request setup error:",t),Promise.reject(t))),this.client.interceptors.response.use(t=>(this.logResponse(t),t),async t=>{const s=t.config;if(t.response?.status===401&&!s._retry){s._retry=!0;try{this.refreshPromise||(this.refreshPromise=this.refreshAccessToken());const i=await this.refreshPromise;return this.refreshPromise=null,s.headers=s.headers||{},s.headers.Authorization=`Bearer ${i.accessToken}`,this.client(s)}catch(i){return this.logout(),typeof window<"u"&&(window.location.href="/auth/signin"),Promise.reject(i)}}const n=s._retryCount||0;if(n<this.defaultRetryConfig.maxRetries&&this.defaultRetryConfig.retryCondition(t)){s._retryCount=n+1;const i=Math.min(this.defaultRetryConfig.baseDelay*Math.pow(2,n),this.defaultRetryConfig.maxDelay);return await this.delay(i),this.client(s)}const a=this.categorizeError(t);return this.handleApiError(a),this.logError(t),Promise.reject(a)})}categorizeError(t){let s,n=!1,a;if(t.response){const{status:i,data:o}=t.response,l=o?.message||"An error occurred";switch(i){case 400:s="VALIDATION",a=`Invalid request: ${l}`;break;case 401:s="AUTHENTICATION",a="Please sign in to continue";break;case 403:s="AUTHORIZATION",a="You don't have permission to perform this action";break;case 404:s="VALIDATION",a="The requested resource was not found";break;case 429:s="SERVER",a="Too many requests. Please try again later.",n=!0;break;case 500:case 502:case 503:case 504:s="SERVER",a="Server error. Please try again later.",n=!0;break;default:s="UNKNOWN",a=l}}else t.request?(s="NETWORK",a="Network error. Please check your connection.",n=!0):t.code==="ECONNABORTED"?(s="TIMEOUT",a="Request timeout. Please try again.",n=!0):(s="UNKNOWN",a="An unexpected error occurred.");return{...t.response?.data,message:t.response?.data?.message||t.message,statusCode:t.response?.status||0,category:s,retryable:n,userMessage:a,timestamp:new Date().toISOString(),path:t.config?.url,traceId:t.config?.headers?.["X-Request-ID"]}}handleApiError(t){t.category!=="AUTHENTICATION"&&re.error(t.userMessage)}generateRequestId(){return`req_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}delay(t){return new Promise(s=>setTimeout(s,t))}logRequest(t){process.env.NODE_ENV==="development"&&console.log(`[API] ${t.method?.toUpperCase()} ${t.url}`,{headers:t.headers,data:t.data})}logResponse(t){if(process.env.NODE_ENV==="development"){const s=Date.now()-(t.config.metadata?.startTime||0);console.log(`[API] ${t.status} ${t.config.method?.toUpperCase()} ${t.config.url} (${s}ms)`)}}logError(t){process.env.NODE_ENV==="development"&&console.error(`[API] Error ${t.config?.method?.toUpperCase()} ${t.config?.url}`,{status:t.response?.status,message:t.message,data:t.response?.data})}getAccessToken(){return typeof window<"u"?localStorage.getItem(Ns):null}getRefreshToken(){return typeof window<"u"?localStorage.getItem(Ss):null}setTokens(t){typeof window<"u"&&(localStorage.setItem(Ns,t.accessToken),localStorage.setItem(Ss,t.refreshToken))}clearTokens(){typeof window<"u"&&(localStorage.removeItem(Ns),localStorage.removeItem(Ss),localStorage.removeItem(Ts))}getUser(){if(typeof window<"u"){const t=localStorage.getItem(Ts);return t?JSON.parse(t):null}return null}setUser(t){typeof window<"u"&&localStorage.setItem(Ts,JSON.stringify(t))}async refreshAccessToken(){const t=this.getRefreshToken();if(!t)throw new Error("No refresh token available");const n=(await Se.post(`${gn}/auth/refresh`,{refreshToken:t})).data;return this.setTokens(n),n}logout(){this.clearTokens()}async get(t,s){try{return await this.client.get(t,s)}catch(n){throw this.enhanceError(n,"GET",t)}}async post(t,s,n){try{return await this.client.post(t,s,n)}catch(a){throw this.enhanceError(a,"POST",t)}}async put(t,s,n){try{return await this.client.put(t,s,n)}catch(a){throw this.enhanceError(a,"PUT",t)}}async patch(t,s,n){try{return await this.client.patch(t,s,n)}catch(a){throw this.enhanceError(a,"PATCH",t)}}async delete(t,s){try{return await this.client.delete(t,s)}catch(n){throw this.enhanceError(n,"DELETE",t)}}enhanceError(t,s,n){return t.name==="CategorizedError"?t:this.categorizeError(t)}async upload(t,s,n,a){try{return await this.client.post(t,s,{...a,headers:{"Content-Type":"multipart/form-data",...a?.headers},timeout:12e4,onUploadProgress:i=>{if(n&&i.total){const o=Math.round(i.loaded*100/i.total);n(o)}}})}catch(i){throw this.enhanceError(i,"POST",t)}}async healthCheck(){try{return(await this.client.get("/health")).status===200}catch(t){return console.warn("Health check failed:",t),!1}}setTimeout(t){this.client.defaults.timeout=t}setRetryConfig(t){this.defaultRetryConfig={...this.defaultRetryConfig,...t}}getBaseURL(){return this.client.defaults.baseURL||""}setBaseURL(t){this.client.defaults.baseURL=t}}const A=new tc,sc={async login(e){const s=(await A.post("/auth/login",e)).data;return A.setTokens(s.tokens),A.setUser(s.user),s},async register(e){return(await A.post("/auth/register",e)).data},async logout(e,t){try{await A.post("/auth/logout",{refreshToken:e,sessionId:t})}finally{A.logout()}},async refreshToken(e){const s=(await A.post("/auth/refresh",{refreshToken:e})).data;return A.setTokens(s),s},async getProfile(){return(await A.get("/auth/profile")).data},async changePassword(e){return(await A.put("/auth/change-password",e)).data},async verifyEmail(e){return(await A.post("/auth/verify-email",e)).data},async resendVerificationEmail(){return(await A.post("/auth/resend-verification")).data},async requestPasswordReset(e){return(await A.post("/auth/password-reset/request",e)).data},async confirmPasswordReset(e){return(await A.post("/auth/password-reset/confirm",e)).data},async getSessions(){return(await A.get("/auth/sessions")).data},async terminateSession(e){return(await A.delete(`/auth/sessions/${e}`)).data},async terminateAllSessions(){return(await A.delete("/auth/sessions")).data},async getRoles(){return(await A.get("/auth/roles")).data},async createRole(e){return(await A.post("/auth/roles",e)).data},async updateRole(e,t){return(await A.put(`/auth/roles/${e}`,t)).data},async deleteRole(e){return(await A.delete(`/auth/roles/${e}`)).data},async assignRole(e,t){return(await A.post(`/auth/users/${e}/roles`,{roleId:t})).data},async removeRole(e,t){return(await A.delete(`/auth/users/${e}/roles/${t}`)).data},async getSecurityStats(){return(await A.get("/auth/admin/security-stats")).data},async getAuthAttempts(e=1,t=50){return(await A.get("/auth/admin/auth-attempts",{params:{page:e,limit:t}})).data},async getActiveSessions(){return(await A.get("/auth/admin/active-sessions")).data}},ve={async createCourse(e){return(await A.post("/training/courses",e)).data},async getCourses(e={}){return(await A.get("/training/courses",{params:e})).data},async getCourseById(e){return(await A.get(`/training/courses/${e}`)).data},async updateCourse(e,t){return(await A.put(`/training/courses/${e}`,t)).data},async deleteCourse(e){await A.delete(`/training/courses/${e}`)},async enrollInCourse(e){return(await A.post("/training/enrollments",e)).data},async getEnrollments(e,t){return(await A.get("/training/enrollments",{params:{userId:e,courseId:t}})).data},async updateEnrollment(e,t){return(await A.put(`/training/enrollments/${e}`,t)).data},async getRecommendations(){return(await A.get("/training/recommendations")).data},async generateRecommendations(){return(await A.post("/training/recommendations/generate")).data},async updateRecommendationStatus(e,t){return(await A.put(`/training/recommendations/${e}/status`,{status:t})).data},async getSkills(e){return(await A.get("/training/skills",{params:{category:e}})).data},async getSkillById(e){return(await A.get(`/training/skills/${e}`)).data},async getUserSkills(e){return(await A.get("/training/user-skills",{params:{userId:e}})).data},async updateUserSkill(e,t){return(await A.put(`/training/user-skills/${e}`,{level:t})).data},async getAssessments(e={}){return(await A.get("/training/assessments",{params:e})).data},async getAssessmentById(e){return(await A.get(`/training/assessments/${e}`)).data},async createAssessment(e){return(await A.post("/training/assessments",e)).data},async submitAssessmentResponse(e,t){return(await A.post(`/training/assessments/${e}/responses`,t)).data},async getSkillGaps(e){return(await A.get("/training/skill-gaps",{params:{userId:e}})).data},async analyzeSkillGaps(e){return(await A.post("/training/skill-gaps/analyze",{userId:e})).data},async getLearningPaths(e){return(await A.get("/training/learning-paths",{params:{roleId:e}})).data},async getLearningPathById(e){return(await A.get(`/training/learning-paths/${e}`)).data},async getTrainingMetrics(e){return(await A.get("/training/metrics",{params:{departmentId:e}})).data},async getCourseAnalytics(e){return(await A.get(`/training/courses/${e}/analytics`)).data},async getUserProgress(e){return(await A.get(`/training/users/${e||"me"}/progress`)).data},async getROIAnalysis(e){return(await A.get("/training/roi-analysis",{params:{departmentId:e}})).data}},rc={async createVendor(e){return(await A.post("/vendors",e)).data},async getVendors(e={}){return(await A.get("/vendors",{params:e})).data},async getVendorById(e){return(await A.get(`/vendors/${e}`)).data},async updateVendor(e,t){return(await A.put(`/vendors/${e}`,t)).data},async deleteVendor(e){await A.delete(`/vendors/${e}`)},async getVendorPerformance(e){return(await A.get(`/vendors/${e}/performance`)).data},async getVendorCategories(){return(await A.get("/vendors/categories")).data},async getVendorStatistics(){return(await A.get("/vendors/statistics")).data},async initiateVendorOnboarding(e){return(await A.post(`/vendors/${e}/onboard`)).data},async approveVendor(e,t){return(await A.post(`/vendors/${e}/approve`,{approverId:t})).data},async suspendVendor(e,t,s){return(await A.post(`/vendors/${e}/suspend`,{reason:t,suspendedBy:s})).data},async createProposal(e){return(await A.post("/proposals",e)).data},async getProposals(e={}){return(await A.get("/proposals",{params:e})).data},async getProposalById(e){return(await A.get(`/proposals/${e}`)).data},async updateProposal(e,t){return(await A.put(`/proposals/${e}`,t)).data},async deleteProposal(e){await A.delete(`/proposals/${e}`)},async submitProposal(e){return(await A.post(`/proposals/${e}/submit`)).data},async approveProposal(e,t){return(await A.post(`/proposals/${e}/approve`,{approverId:t})).data},async rejectProposal(e,t){return(await A.post(`/proposals/${e}/reject`,{reason:t})).data},async negotiateProposal(e,t){return(await A.post(`/proposals/${e}/negotiate`,{notes:t})).data},async createReview(e){return(await A.post("/reviews",e)).data},async getReviews(e,t){return(await A.get("/reviews",{params:{vendorId:e,proposalId:t}})).data},async getReviewById(e){return(await A.get(`/reviews/${e}`)).data},async updateReview(e,t){return(await A.put(`/reviews/${e}`,t)).data},async deleteReview(e){await A.delete(`/reviews/${e}`)},async getDashboardStats(){return(await A.get("/vendors/dashboard-stats")).data},async getVendorAnalytics(e){return(await A.get("/vendors/analytics",{params:{timeRange:e}})).data},async getTopPerformingVendors(e=10){return(await A.get("/vendors/top-performing",{params:{limit:e}})).data},async getVendorTrends(e,t){return(await A.get(`/vendors/${e}/trends`,{params:{timeRange:t}})).data},async uploadProposalAttachment(e,t,s){const n=new FormData;return n.append("file",t),(await A.upload(`/proposals/${e}/attachments`,n,s)).data},async deleteProposalAttachment(e,t){await A.delete(`/proposals/${e}/attachments/${t}`)}},nc={async createSubmission(e){return(await A.post("/wins/submissions",e)).data},async getSubmissions(e={}){return(await A.get("/wins/submissions",{params:e})).data},async getSubmissionById(e){return(await A.get(`/wins/submissions/${e}`)).data},async updateSubmission(e,t){return(await A.put(`/wins/submissions/${e}`,t)).data},async deleteSubmission(e){await A.delete(`/wins/submissions/${e}`)},async submitForReview(e){return(await A.post(`/wins/submissions/${e}/submit`)).data},async approveSubmission(e,t){return(await A.post(`/wins/submissions/${e}/approve`,{reviewerId:t})).data},async rejectSubmission(e,t){return(await A.post(`/wins/submissions/${e}/reject`,{reason:t})).data},async getDashboardStats(e,t){return(await A.get("/wins/dashboard-stats",{params:{userId:e,departmentId:t}})).data},async getSubmissionSummary(e){return(await A.get("/wins/submission-summary",{params:{timeRange:e}})).data},async getTopPerformers(e=10,t){return(await A.get("/wins/top-performers",{params:{limit:e,timeRange:t}})).data},async getTeamProgress(e,t){return(await A.get(`/wins/team-progress/${e}`,{params:{timeRange:t}})).data},async getCostSavingsTrends(e){return(await A.get("/wins/cost-savings-trends",{params:{timeRange:e}})).data},async generateReport(e,t){return(await A.post("/wins/reports/generate",{type:e,parameters:t})).data},async getReports(e={}){return(await A.get("/wins/reports",{params:e})).data},async downloadReport(e,t="pdf"){return(await A.get(`/wins/reports/${e}/download`,{params:{format:t},responseType:"blob"})).data},async getAchievementTemplates(){return(await A.get("/wins/achievement-templates")).data},async getAchievementLeaderboard(e){return(await A.get("/wins/achievement-leaderboard",{params:{timeRange:e}})).data},async uploadAttachment(e,t,s){const n=new FormData;return n.append("file",t),(await A.upload(`/wins/submissions/${e}/attachments`,n,s)).data},async deleteAttachment(e,t){await A.delete(`/wins/submissions/${e}/attachments/${t}`)},async exportSubmissions(e,t="excel"){return(await A.get("/wins/submissions/export",{params:{...e,format:t},responseType:"blob"})).data},async exportDashboard(e,t="pdf"){return(await A.get("/wins/dashboard/export",{params:{timeRange:e,format:t},responseType:"blob"})).data}},be={async getUsers(e){return(await A.get("/users",{params:e})).data},async getUser(e){return(await A.get(`/users/${e}`)).data},async getCurrentUser(){return(await A.get("/users/me")).data},async createUser(e){return(await A.post("/users",e)).data},async updateUser(e,t){return(await A.put(`/users/${e}`,t)).data},async updateCurrentUser(e){return(await A.put("/users/me",e)).data},async deleteUser(e){return(await A.delete(`/users/${e}`)).data},async activateUser(e){return(await A.post(`/users/${e}/activate`)).data},async deactivateUser(e){return(await A.post(`/users/${e}/deactivate`)).data},async bulkUpdateUsers(e){return(await A.post("/users/bulk",e)).data},async updateUserPreferences(e){return(await A.put("/users/me/preferences",e)).data},async uploadAvatar(e){const t=new FormData;return t.append("avatar",e),(await A.upload("/users/me/avatar",t)).data},async removeAvatar(){return(await A.delete("/users/me/avatar")).data},async addCertification(e){return(await A.post("/users/me/certifications",e)).data},async removeCertification(e){return(await A.delete(`/users/me/certifications/${e}`)).data},async changePassword(e){return(await A.post("/users/me/change-password",e)).data},async requestPasswordReset(e){return(await A.post("/users/request-password-reset",{email:e})).data},async resetPassword(e){return(await A.post("/users/reset-password",e)).data},async getUserSessions(){return(await A.get("/users/me/sessions")).data},async revokeSession(e){return(await A.delete(`/users/me/sessions/${e}`)).data},async revokeAllSessions(){return(await A.delete("/users/me/sessions")).data},async getUserActivity(e,t){const s=e?`/users/${e}/activity`:"/users/me/activity";return(await A.get(s,{params:t})).data},async getRoles(){return(await A.get("/users/roles")).data},async getRole(e){return(await A.get(`/users/roles/${e}`)).data},async createRole(e){return(await A.post("/users/roles",e)).data},async updateRole(e,t){return(await A.put(`/users/roles/${e}`,t)).data},async deleteRole(e){return(await A.delete(`/users/roles/${e}`)).data},async getPermissions(){return(await A.get("/users/permissions")).data},async getUserPermissions(e){const t=e?`/users/${e}/permissions`:"/users/me/permissions";return(await A.get(t)).data},async assignRole(e,t){return(await A.post(`/users/${e}/roles`,{roleId:t})).data},async removeRole(e,t){return(await A.delete(`/users/${e}/roles/${t}`)).data},async getDepartments(){return(await A.get("/users/departments")).data},async getDepartment(e){return(await A.get(`/users/departments/${e}`)).data},async createDepartment(e){return(await A.post("/users/departments",e)).data},async updateDepartment(e,t){return(await A.put(`/users/departments/${e}`,t)).data},async deleteDepartment(e){return(await A.delete(`/users/departments/${e}`)).data},async getDepartmentMembers(e){return(await A.get(`/users/departments/${e}/members`)).data},async addDepartmentMember(e,t){return(await A.post(`/users/departments/${e}/members`,{userId:t})).data},async removeDepartmentMember(e,t){return(await A.delete(`/users/departments/${e}/members/${t}`)).data},async getTeamMembers(e){const t=e?`/users/${e}/team`:"/users/me/team";return(await A.get(t)).data},async assignManager(e,t){return(await A.post(`/users/${e}/manager`,{managerId:t})).data},async removeManager(e){return(await A.delete(`/users/${e}/manager`)).data},async exportUsers(e){return(await A.get("/users/export",{params:e})).data},async importUsers(e){const t=new FormData;return t.append("file",e),(await A.upload("/users/import",t)).data},async downloadImportTemplate(){return(await A.get("/users/import-template")).data},async getUserStats(e){return(await A.get("/users/stats",{params:e})).data},async getUserGrowth(e){return(await A.get("/users/growth",{params:e})).data},async searchUsers(e,t){return(await A.get("/users/search",{params:{query:e,...t}})).data},async getUserSuggestions(e){return(await A.get("/users/suggestions",{params:e})).data},async getUserNotifications(){return(await A.get("/users/me/notifications")).data},async markNotificationAsRead(e){return(await A.put(`/users/me/notifications/${e}/read`)).data},async markAllNotificationsAsRead(){return(await A.put("/users/me/notifications/read-all")).data},async deleteNotification(e){return(await A.delete(`/users/me/notifications/${e}`)).data}},xe={async getTemplates(e){return(await A.get("/email/templates",{params:e})).data},async getTemplate(e){return(await A.get(`/email/templates/${e}`)).data},async createTemplate(e){return(await A.post("/email/templates",e)).data},async updateTemplate(e,t){return(await A.put(`/email/templates/${e}`,t)).data},async deleteTemplate(e){return(await A.delete(`/email/templates/${e}`)).data},async previewTemplate(e,t){return(await A.post(`/email/templates/${e}/preview`,{variables:t})).data},async testTemplate(e,t,s){return(await A.post(`/email/templates/${e}/test`,{email:t,variables:s})).data},async getCampaigns(e){return(await A.get("/email/campaigns",{params:e})).data},async getCampaign(e){return(await A.get(`/email/campaigns/${e}`)).data},async createCampaign(e){return(await A.post("/email/campaigns",e)).data},async updateCampaign(e,t){return(await A.put(`/email/campaigns/${e}`,t)).data},async deleteCampaign(e){return(await A.delete(`/email/campaigns/${e}`)).data},async sendCampaign(e){return(await A.post(`/email/campaigns/${e}/send`)).data},async scheduleCampaign(e,t){return(await A.post(`/email/campaigns/${e}/schedule`,{scheduledAt:t})).data},async pauseCampaign(e){return(await A.post(`/email/campaigns/${e}/pause`)).data},async resumeCampaign(e){return(await A.post(`/email/campaigns/${e}/resume`)).data},async getCampaignMetrics(e){return(await A.get(`/email/campaigns/${e}/metrics`)).data},async sendEmail(e){return(await A.post("/email/send",e)).data},async sendBulkEmail(e){return(await A.post("/email/send/bulk",e)).data},async sendTransactionalEmail(e){return(await A.post("/email/send/transactional",e)).data},async getEmailLogs(e){return(await A.get("/email/logs",{params:e})).data},async getEmailLog(e){return(await A.get(`/email/logs/${e}`)).data},async retryFailedEmail(e){return(await A.post(`/email/logs/${e}/retry`)).data},async getRecipients(e){return(await A.get("/email/recipients",{params:e})).data},async addRecipient(e){return(await A.post("/email/recipients",e)).data},async updateRecipient(e,t){return(await A.put(`/email/recipients/${e}`,t)).data},async deleteRecipient(e){return(await A.delete(`/email/recipients/${e}`)).data},async importRecipients(e){const t=new FormData;return t.append("file",e),(await A.upload("/email/recipients/import",t)).data},async getSettings(){return(await A.get("/email/settings")).data},async updateSettings(e){return(await A.put("/email/settings",e)).data},async testSettings(){return(await A.post("/email/settings/test")).data},async getAnalytics(e){return(await A.get("/email/analytics",{params:e})).data},async getQueueStatus(){return(await A.get("/email/queue/status")).data},async clearQueue(){return(await A.post("/email/queue/clear")).data},async retryFailedJobs(){return(await A.post("/email/queue/retry-failed")).data}},Be={auth:authApi,training:trainingApi,vendor:vendorApi,wins:winsApi,users:usersApi,email:emailApi},ac=ho()(wo((e,t)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,sessionId:null,oauthProvider:null,login:async s=>{e({isLoading:!0,error:null});try{const n=await Be.auth.login(s);return e({user:n.user,isAuthenticated:!0,sessionId:n.sessionId,oauthProvider:null,isLoading:!1,error:null}),re.success("Login successful!"),n}catch(n){const a=n.response?.data?.message||"Login failed";return e({user:null,isAuthenticated:!1,isLoading:!1,error:a}),re.error(a),null}},register:async s=>{e({isLoading:!0,error:null});try{const n=await Be.auth.register(s);return e({isLoading:!1,error:null}),n.requiresEmailVerification?re.info("Please check your email to verify your account."):re.success("Registration successful! You can now login."),!0}catch(n){const a=n.response?.data?.message||"Registration failed";return e({isLoading:!1,error:a}),re.error(a),!1}},logout:async()=>{const{sessionId:s}=t(),n=Be.auth.getRefreshToken();e({isLoading:!0});try{await Be.auth.logout(n||void 0,s||void 0)}catch(a){console.error("Logout error:",a)}finally{e({user:null,isAuthenticated:!1,sessionId:null,oauthProvider:null,isLoading:!1,error:null}),re.info("You have been logged out.")}},refreshUser:async()=>{const{isAuthenticated:s}=t();if(s)try{const n=await Be.auth.getProfile();e({user:n})}catch(n){console.error("Failed to refresh user:",n),n.response?.status===401&&t().logout()}},updateUser:s=>{const{user:n}=t();n&&e({user:{...n,...s}})},clearError:()=>e({error:null}),checkAuth:async()=>{if(!Be.auth.getAccessToken())return e({isAuthenticated:!1,user:null}),!1;e({isLoading:!0});try{const n=await Be.auth.getProfile();return e({user:n,isAuthenticated:!0,isLoading:!1}),!0}catch(n){return console.error("Auth check failed:",n),e({user:null,isAuthenticated:!1,isLoading:!1}),!1}},oauthLogin:async s=>{const n=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3000",a=encodeURIComponent(`${window.location.origin}/auth/callback`),i=Math.random().toString(36).substring(2,15);sessionStorage.setItem("oauth_state",i),sessionStorage.setItem("oauth_provider",s),window.location.href=`${n}/auth/oauth/${s}?redirect_uri=${a}&state=${i}`},handleOAuthCallback:async(s,n,a)=>{e({isLoading:!0,error:null});try{const i=sessionStorage.getItem("oauth_state"),o=sessionStorage.getItem("oauth_provider");if(i!==n||o!==a)throw new Error("Invalid OAuth state or provider");const l=process.env.NEXT_PUBLIC_API_URL||"http://localhost:3000",u=await fetch(`${l}/auth/oauth/callback`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({code:s,state:n,provider:a,redirect_uri:`${window.location.origin}/auth/callback`})});if(!u.ok)throw new Error("OAuth callback failed");const d=await u.json();return d.tokens&&Be.auth.setTokens(d.tokens),e({user:d.user,isAuthenticated:!0,sessionId:d.sessionId,oauthProvider:a,isLoading:!1,error:null}),sessionStorage.removeItem("oauth_state"),sessionStorage.removeItem("oauth_provider"),re.success(`Successfully authenticated with ${a}!`),!0}catch(i){const o=i.message||"OAuth authentication failed";return e({user:null,isAuthenticated:!1,sessionId:null,oauthProvider:null,isLoading:!1,error:o}),sessionStorage.removeItem("oauth_state"),sessionStorage.removeItem("oauth_provider"),re.error(o),!1}}}),{name:"auth-storage",storage:Jn(()=>localStorage),partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated,sessionId:e.sessionId,oauthProvider:e.oauthProvider})})),Ca=c.createContext(void 0),Ge=()=>{const e=c.useContext(Ca);if(e===void 0)throw new Error("useAuth must be used within an AuthProvider");return e},ic=({children:e,enableRBAC:t=!0,autoRefreshInterval:s=5*60*1e3})=>{const{user:n,isAuthenticated:a,isLoading:i,error:o,login:l,logout:u,register:d,refreshUser:m,clearError:p,checkAuth:y}=ac(),[w,g]=c.useState(!1);c.useEffect(()=>{(async()=>{try{await y()}catch(O){console.error("Auth initialization failed:",O)}finally{g(!0)}})()},[y]),c.useEffect(()=>{if(!a||!s)return;const z=setInterval(async()=>{try{await m()}catch(O){console.error("Auto-refresh failed:",O)}},s);return()=>clearInterval(z)},[a,s,m]);const f=c.useCallback(async z=>{try{return await l(z)!==null}catch(O){return console.error("Login failed:",O),!1}},[l]),h=c.useCallback(async()=>{try{await u()}catch(z){console.error("Logout failed:",z)}},[u]),b=c.useCallback(async z=>{try{return await d(z)}catch(O){return console.error("Registration failed:",O),!1}},[d]),x=c.useCallback(async()=>{try{await m()}catch(z){console.error("User refresh failed:",z)}},[m]),N=c.useCallback(()=>!t||!n?.roles?[]:n.roles.map(z=>({id:z.id,name:z.name,displayName:z.name,permissions:z.permissions.map(O=>({id:O,name:O,resource:O.split(":")[0]||"",action:O.split(":")[1]||"",description:`${O.split(":")[1]||"access"} permission for ${O.split(":")[0]||"resource"}`}))})),[t,n]),R=c.useCallback(()=>{if(!t||!n?.roles)return[];const z=[];return n.roles.forEach(O=>{O.permissions.forEach(M=>{const K={id:M,name:M,resource:M.split(":")[0]||"",action:M.split(":")[1]||"",description:`${M.split(":")[1]||"access"} permission for ${M.split(":")[0]||"resource"}`};z.some(U=>U.id===K.id)||z.push(K)})}),z},[t,n]),I=c.useCallback((z,O)=>{if(!t||!n?.roles)return!1;const M=O?`${O}:${z}`:z;return n.roles.some(K=>K.permissions.includes(M)||K.permissions.includes("*:*")||K.permissions.includes(`${O}:*`)||K.permissions.includes(`*:${z}`))},[t,n]),j=c.useCallback(z=>!t||!n?.roles?!1:n.roles.some(O=>O.name===z),[t,n]),E=c.useCallback(z=>!t||!n?.roles?!1:n.roles.some(O=>z.includes(O.name)),[t,n]),k=c.useCallback(z=>!t||!n?.roles?!1:z.every(O=>n.roles.some(M=>M.name===O)),[t,n]),L=c.useCallback(z=>I("read",z),[I]),$=c.useCallback(z=>I("write",z)||I("create",z)||I("update",z),[I]),W=c.useCallback(z=>I("delete",z),[I]),D=c.useCallback(z=>I("admin",z)||I("*",z),[I]),V={user:n,isAuthenticated:a,isLoading:i||!w,error:o,login:f,logout:h,register:b,hasPermission:I,hasRole:j,hasAnyRole:E,hasAllRoles:k,canRead:L,canWrite:$,canDelete:W,canAdmin:D,getUserRoles:N,getUserPermissions:R,clearError:p,refreshUser:x};return r.jsx(Ca.Provider,{value:V,children:e})},oc=(e,t,s)=>function(a){const{isAuthenticated:i,hasPermission:o,hasRole:l,isLoading:u}=Ge();return u?r.jsx("div",{children:"Loading..."}):i?t&&t.length>0&&!t.every(m=>o(m))?r.jsx("div",{children:"You don't have permission to access this content."}):s&&s.length>0&&!s.some(m=>l(m))?r.jsx("div",{children:"You don't have the required role to access this content."}):r.jsx(e,{...a}):r.jsx("div",{children:"Please log in to access this content."})},lc={defaultOptions:{queries:{staleTime:5*60*1e3,gcTime:10*60*1e3,retry:(e,t)=>t?.response?.status>=400&&t?.response?.status<500?!1:e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!0,refetchOnReconnect:!0,refetchOnMount:!0,networkMode:"online"},mutations:{retry:(e,t)=>t?.response?.status>=400&&t?.response?.status<500?!1:e<2,onError:e=>{e?.category!=="AUTHENTICATION"&&re.error(e?.userMessage||"An error occurred")},networkMode:"online"}}},Ve=new T.QueryClient(lc),C={auth:{user:()=>["auth","user"],permissions:()=>["auth","permissions"]},users:{all:()=>["users"],lists:()=>[...C.users.all(),"list"],list:e=>[...C.users.lists(),e],details:()=>[...C.users.all(),"detail"],detail:e=>[...C.users.details(),e],current:()=>[...C.users.all(),"current"],activity:e=>[...C.users.all(),"activity",e],sessions:()=>[...C.users.all(),"sessions"],notifications:()=>[...C.users.all(),"notifications"],stats:e=>[...C.users.all(),"stats",e],growth:e=>[...C.users.all(),"growth",e],search:(e,t)=>[...C.users.all(),"search",e,t],suggestions:e=>[...C.users.all(),"suggestions",e]},roles:{all:()=>["roles"],lists:()=>[...C.roles.all(),"list"],list:()=>[...C.roles.lists()],details:()=>[...C.roles.all(),"detail"],detail:e=>[...C.roles.details(),e],permissions:()=>[...C.roles.all(),"permissions"]},departments:{all:()=>["departments"],lists:()=>[...C.departments.all(),"list"],list:()=>[...C.departments.lists()],details:()=>[...C.departments.all(),"detail"],detail:e=>[...C.departments.details(),e],members:e=>[...C.departments.all(),"members",e]},training:{all:()=>["training"],courses:{all:()=>[...C.training.all(),"courses"],lists:()=>[...C.training.courses.all(),"list"],list:e=>[...C.training.courses.lists(),e],details:()=>[...C.training.courses.all(),"detail"],detail:e=>[...C.training.courses.details(),e],analytics:e=>[...C.training.courses.all(),"analytics",e]},enrollments:{all:()=>[...C.training.all(),"enrollments"],lists:()=>[...C.training.enrollments.all(),"list"],list:e=>[...C.training.enrollments.lists(),e],details:()=>[...C.training.enrollments.all(),"detail"],detail:e=>[...C.training.enrollments.details(),e],progress:e=>[...C.training.enrollments.all(),"progress",e]},skills:{all:()=>[...C.training.all(),"skills"],lists:()=>[...C.training.skills.all(),"list"],list:e=>[...C.training.skills.lists(),e],gaps:e=>[...C.training.skills.all(),"gaps",e],ratings:e=>[...C.training.skills.all(),"ratings",e],recommendations:e=>[...C.training.skills.all(),"recommendations",e]},assessments:{all:()=>[...C.training.all(),"assessments"],lists:()=>[...C.training.assessments.all(),"list"],list:e=>[...C.training.assessments.lists(),e],details:()=>[...C.training.assessments.all(),"detail"],detail:e=>[...C.training.assessments.details(),e],responses:e=>[...C.training.assessments.all(),"responses",e]},analytics:{all:()=>[...C.training.all(),"analytics"],dashboard:e=>[...C.training.analytics.all(),"dashboard",e],metrics:e=>[...C.training.analytics.all(),"metrics",e],reports:e=>[...C.training.analytics.all(),"reports",e]}},vendors:{all:()=>["vendors"],lists:()=>[...C.vendors.all(),"list"],list:e=>[...C.vendors.lists(),e],details:()=>[...C.vendors.all(),"detail"],detail:e=>[...C.vendors.details(),e],performance:e=>[...C.vendors.all(),"performance",e],proposals:{all:()=>[...C.vendors.all(),"proposals"],lists:()=>[...C.vendors.proposals.all(),"list"],list:e=>[...C.vendors.proposals.lists(),e],details:()=>[...C.vendors.proposals.all(),"detail"],detail:e=>[...C.vendors.proposals.details(),e]},reviews:{all:()=>[...C.vendors.all(),"reviews"],lists:()=>[...C.vendors.reviews.all(),"list"],list:e=>[...C.vendors.reviews.lists(),e],vendor:e=>[...C.vendors.reviews.all(),"vendor",e]},analytics:{all:()=>[...C.vendors.all(),"analytics"],dashboard:e=>[...C.vendors.analytics.all(),"dashboard",e],metrics:e=>[...C.vendors.analytics.all(),"metrics",e]}},wins:{all:()=>["wins"],submissions:{all:()=>[...C.wins.all(),"submissions"],lists:()=>[...C.wins.submissions.all(),"list"],list:e=>[...C.wins.submissions.lists(),e],details:()=>[...C.wins.submissions.all(),"detail"],detail:e=>[...C.wins.submissions.details(),e],metrics:e=>[...C.wins.submissions.all(),"metrics",e]},analytics:{all:()=>[...C.wins.all(),"analytics"],dashboard:e=>[...C.wins.analytics.all(),"dashboard",e],metrics:e=>[...C.wins.analytics.all(),"metrics",e],team:e=>[...C.wins.analytics.all(),"team",e]}},email:{all:()=>["email"],templates:{all:()=>[...C.email.all(),"templates"],lists:()=>[...C.email.templates.all(),"list"],list:e=>[...C.email.templates.lists(),e],details:()=>[...C.email.templates.all(),"detail"],detail:e=>[...C.email.templates.details(),e],preview:(e,t)=>[...C.email.templates.all(),"preview",e,t]},campaigns:{all:()=>[...C.email.all(),"campaigns"],lists:()=>[...C.email.campaigns.all(),"list"],list:e=>[...C.email.campaigns.lists(),e],details:()=>[...C.email.campaigns.all(),"detail"],detail:e=>[...C.email.campaigns.details(),e],metrics:e=>[...C.email.campaigns.all(),"metrics",e]},logs:{all:()=>[...C.email.all(),"logs"],lists:()=>[...C.email.logs.all(),"list"],list:e=>[...C.email.logs.lists(),e],details:()=>[...C.email.logs.all(),"detail"],detail:e=>[...C.email.logs.details(),e]},recipients:{all:()=>[...C.email.all(),"recipients"],lists:()=>[...C.email.recipients.all(),"list"],list:e=>[...C.email.recipients.lists(),e]},settings:()=>[...C.email.all(),"settings"],analytics:e=>[...C.email.all(),"analytics",e],queue:()=>[...C.email.all(),"queue"]}},ie={invalidateQueries:e=>Ve.invalidateQueries({queryKey:e}),removeQueries:e=>Ve.removeQueries({queryKey:e}),getQueryData:e=>Ve.getQueryData(e),setQueryData:(e,t)=>Ve.setQueryData(e,t),clear:()=>Ve.clear(),cancelQueries:e=>Ve.cancelQueries({queryKey:e}),prefetchQuery:(e,t)=>Ve.prefetchQuery({queryKey:e,queryFn:t})},ka={initialIsOpen:!1,position:"bottom-right",toggleButtonProps:{style:{marginLeft:"5px",transform:"scale(0.8)"}}};function cc({children:e,showDevtools:t=process.env.NODE_ENV==="development"}){return r.jsxs(T.QueryClientProvider,{client:Ve,children:[e,t&&r.jsx(Ja.ReactQueryDevtools,{...ka})]})}function yn(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function uc(...e){return t=>{let s=!1;const n=e.map(a=>{const i=yn(a,t);return!s&&typeof i=="function"&&(s=!0),i});if(s)return()=>{for(let a=0;a<n.length;a++){const i=n[a];typeof i=="function"?i():yn(e[a],null)}}}}function dc(e){const t=pc(e),s=me.forwardRef((n,a)=>{const{children:i,...o}=n,l=me.Children.toArray(i),u=l.find(hc);if(u){const d=u.props.children,m=l.map(p=>p===u?me.Children.count(d)>1?me.Children.only(null):me.isValidElement(d)?d.props.children:null:p);return r.jsx(t,{...o,ref:a,children:me.isValidElement(d)?me.cloneElement(d,void 0,m):null})}return r.jsx(t,{...o,ref:a,children:i})});return s.displayName=`${e}.Slot`,s}var mc=dc("Slot");function pc(e){const t=me.forwardRef((s,n)=>{const{children:a,...i}=s;if(me.isValidElement(a)){const o=yc(a),l=gc(i,a.props);return a.type!==me.Fragment&&(l.ref=n?uc(n,o):o),me.cloneElement(a,l)}return me.Children.count(a)>1?me.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var fc=Symbol("radix.slottable");function hc(e){return me.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===fc}function gc(e,t){const s={...t};for(const n in t){const a=e[n],i=t[n];/^on[A-Z]/.test(n)?a&&i?s[n]=(...l)=>{const u=i(...l);return a(...l),u}:a&&(s[n]=a):n==="style"?s[n]={...a,...i}:n==="className"&&(s[n]=[a,i].filter(Boolean).join(" "))}return{...e,...s}}function yc(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,s=t&&"isReactWarning"in t&&t.isReactWarning;return s?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,s=t&&"isReactWarning"in t&&t.isReactWarning,s?e.props.ref:e.props.ref||e.ref)}const bn=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,xn=Bs,jt=(e,t)=>s=>{var n;if(t?.variants==null)return xn(e,s?.class,s?.className);const{variants:a,defaultVariants:i}=t,o=Object.keys(a).map(d=>{const m=s?.[d],p=i?.[d];if(m===null)return null;const y=bn(m)||bn(p);return a[d][y]}),l=s&&Object.entries(s).reduce((d,m)=>{let[p,y]=m;return y===void 0||(d[p]=y),d},{}),u=t==null||(n=t.compoundVariants)===null||n===void 0?void 0:n.reduce((d,m)=>{let{class:p,className:y,...w}=m;return Object.entries(w).every(g=>{let[f,h]=g;return Array.isArray(h)?h.includes({...i,...l}[f]):{...i,...l}[f]===h})?[...d,p,y]:d},[]);return xn(e,o,u,s?.class,s?.className)},bc={soft:{blur:"backdrop-blur-sm",saturation:"backdrop-saturate-125",opacity:"bg-opacity-30 dark:bg-opacity-35"},standard:{blur:"backdrop-blur-md",saturation:"backdrop-saturate-150",opacity:"bg-opacity-35 dark:bg-opacity-40"},hard:{blur:"backdrop-blur-lg",saturation:"backdrop-saturate-175",opacity:"bg-opacity-40 dark:bg-opacity-45"},intense:{blur:"backdrop-blur-xl",saturation:"backdrop-saturate-200",opacity:"bg-opacity-45 dark:bg-opacity-50"}},xc={card:{base:"rounded-xl",border:"border border-white/30 dark:border-white/20",shadow:"shadow-lg shadow-black/10"},modal:{base:"rounded-2xl",border:"border border-white/35 dark:border-white/25",shadow:"shadow-2xl shadow-black/15"},sidebar:{base:"",border:"border-r border-white/30 dark:border-white/20",shadow:"shadow-2xl shadow-black/20"},button:{base:"rounded-lg",border:"border border-white/40 dark:border-white/25",shadow:"shadow-md shadow-black/15"},input:{base:"rounded-lg",border:"border border-white/35 dark:border-white/20",shadow:"shadow-inner-sm shadow-black/10"},control:{base:"rounded-full",border:"border border-white/40 dark:border-white/25",shadow:"shadow-lg shadow-black/15"}},Ut={interactive:"transition-all duration-200 ease-in-out",glow:"relative after:absolute after:inset-0 after:rounded-[inherit] after:bg-gradient-radial after:from-white/10 after:to-transparent after:opacity-0 after:transition-opacity hover:after:opacity-100",frost:"relative before:absolute before:inset-0 before:rounded-[inherit] before:bg-white/5 before:backdrop-blur-[1px] before:pointer-events-none",noise:"relative before:absolute before:inset-0 before:bg-[url(/noise.svg)] before:opacity-25 before:pointer-events-none"},wc={neutral:{bg:"bg-black",highlight:"ring-white/5"},primary:{bg:"bg-primary",highlight:"ring-primary/10"},secondary:{bg:"bg-secondary",highlight:"ring-secondary/10"},accent:{bg:"bg-accent",highlight:"ring-accent/10"},success:{bg:"bg-green-500",highlight:"ring-green-500/10"},warning:{bg:"bg-yellow-500",highlight:"ring-yellow-500/10"},error:{bg:"bg-red-500",highlight:"ring-red-500/10"},info:{bg:"bg-cyan-500",highlight:"ring-cyan-500/10"}};function vc(e={}){const{profile:t="standard",element:s="card",interactive:n=!0,glow:a=!1,frost:i=!0,noise:o=!1,theme:l="neutral",textSafe:u=!0}=e,d=bc[t],m=xc[s],p=wc[l],w=u||s==="button";return S(m.base,m.border,m.shadow,w&&"glass-text-safe",w&&`glass-${s}-safe`,!w&&d.blur,!w&&d.saturation,!w&&p.bg,!w&&d.opacity,n&&Ut.interactive,a&&Ut.glow,i&&Ut.frost,o&&Ut.noise,`focus-visible:ring-2 ${p.highlight}`)}const jc={card:{profile:"standard",element:"card",interactive:!0,frost:!0,glow:!0},modal:{profile:"hard",element:"modal",interactive:!1,frost:!0,glow:!0},sidebar:{profile:"intense",element:"sidebar",interactive:!1,frost:!0,glow:!1},button:{profile:"soft",element:"button",interactive:!0,frost:!1,glow:!0},input:{profile:"soft",element:"input",interactive:!0,frost:!1,glow:!1},control:{profile:"hard",element:"control",interactive:!0,frost:!1,glow:!0}};function Ke(e="card"){const t=typeof e=="string"?jc[e]:e;return vc(t)}function Cc(e){return S("transition-all duration-200",e&&"ring-2 ring-primary/50 scale-[1.01]")}const er={xs:{padding:"px-2 py-1",text:"text-xs",height:"h-6",gap:"gap-1",radius:"rounded-md",icon:"w-3 h-3"},sm:{padding:"px-3 py-1.5",text:"text-sm",height:"h-8",gap:"gap-1.5",radius:"rounded-md",icon:"w-4 h-4"},md:{padding:"px-4 py-2",text:"text-base",height:"h-10",gap:"gap-2",radius:"rounded-lg",icon:"w-5 h-5"},lg:{padding:"px-6 py-3",text:"text-lg",height:"h-12",gap:"gap-2.5",radius:"rounded-lg",icon:"w-6 h-6"},xl:{padding:"px-8 py-4",text:"text-xl",height:"h-14",gap:"gap-3",radius:"rounded-xl",icon:"w-7 h-7"}},He={fadeIn:{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0}},slideUp:{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20}},slideDown:{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:20}},slideLeft:{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20}},slideRight:{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20}},scale:{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9}},bounce:{initial:{opacity:0,scale:.3},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.3}},glassSlideUp:{initial:{opacity:0,y:20,backdropFilter:"blur(0px)"},animate:{opacity:1,y:0,backdropFilter:"blur(12px)"},exit:{opacity:0,y:-20,backdropFilter:"blur(0px)"}},glassScale:{initial:{opacity:0,scale:.95,backdropFilter:"blur(0px)"},animate:{opacity:1,scale:1,backdropFilter:"blur(12px)"},exit:{opacity:0,scale:.95,backdropFilter:"blur(0px)"}},glassMorph:{initial:{opacity:0,scale:.8,rotateX:-15},animate:{opacity:1,scale:1,rotateX:0},exit:{opacity:0,scale:.8,rotateX:15}},hover:{scale:1.02,y:-2,transition:{duration:.2,ease:"easeOut"}},tap:{scale:.98,transition:{duration:.1,ease:"easeInOut"}},focus:{scale:1.01,boxShadow:"0 0 0 3px rgba(59, 130, 246, 0.1)",transition:{duration:.2}},elastic:{initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},exit:{opacity:0,scale:0},transition:{type:"spring",stiffness:400,damping:10}},magnetic:{initial:{opacity:0,scale:.8,rotate:-180},animate:{opacity:1,scale:1,rotate:0},exit:{opacity:0,scale:.8,rotate:180}},liquid:{initial:{opacity:0,scale:.8,borderRadius:"50%"},animate:{opacity:1,scale:1,borderRadius:"12px"},exit:{opacity:0,scale:.8,borderRadius:"50%"}},swipeLeft:{initial:{x:100,opacity:0},animate:{x:0,opacity:1},exit:{x:-100,opacity:0}},swipeRight:{initial:{x:-100,opacity:0},animate:{x:0,opacity:1},exit:{x:100,opacity:0}},pulse:{animate:{scale:[1,1.05,1],opacity:[.7,1,.7]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}},shimmer:{animate:{x:["-100%","100%"]},transition:{duration:1.5,repeat:1/0,ease:"linear"}}},ze={default:{duration:.3,ease:"easeInOut"},spring:{type:"spring",stiffness:300,damping:30}},wn={default:"backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30",card:"backdrop-blur-md bg-white/15 dark:bg-gray-900/15 border border-white/20 dark:border-gray-700/30",modal:"backdrop-blur-lg bg-white/20 dark:bg-gray-900/20 border border-white/30 dark:border-gray-700/40",input:"backdrop-blur-sm bg-white/5 dark:bg-gray-900/5 border border-white/20 dark:border-gray-700/30",button:"backdrop-blur-sm bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30"};function tr(e,t){const s=wn[e]||wn.default;if(!t)return s;let n=s;if(t.intensity)switch(t.intensity){case"subtle":n=n.replace("backdrop-blur-md","backdrop-blur-sm").replace("bg-white/15","bg-white/5").replace("bg-white/10","bg-white/5");break;case"strong":n=n.replace("backdrop-blur-md","backdrop-blur-lg").replace("bg-white/15","bg-white/25").replace("bg-white/10","bg-white/20");break;case"intense":n=n.replace("backdrop-blur-md","backdrop-blur-xl").replace("bg-white/15","bg-white/30").replace("bg-white/10","bg-white/25");break}return n}function ct(e,t="medium",s){let n=tr(e,{intensity:t});if(s)switch(s){case"success":n+=" border-green-500/30 bg-green-500/5";break;case"warning":n+=" border-yellow-500/30 bg-yellow-500/5";break;case"error":n+=" border-red-500/30 bg-red-500/5";break;case"info":n+=" border-blue-500/30 bg-blue-500/5";break}return n}const kc=jt("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 relative overflow-hidden",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl",primary:"bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",ghost:"hover:bg-accent hover:text-accent-foreground",glass:"backdrop-blur-md bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 text-gray-900 dark:text-white hover:bg-white/20 dark:hover:bg-white/10 shadow-lg hover:shadow-xl",gradient:"bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 shadow-lg hover:shadow-xl",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-lg hover:shadow-xl",success:"bg-green-600 text-white hover:bg-green-700 shadow-lg hover:shadow-xl",warning:"bg-yellow-600 text-white hover:bg-yellow-700 shadow-lg hover:shadow-xl",info:"bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl"},size:{xs:"h-7 px-2 py-1 text-xs",sm:"h-9 px-3 py-2 text-sm",md:"h-10 px-4 py-2 text-sm",lg:"h-11 px-6 py-3 text-base",xl:"h-12 px-8 py-4 text-lg"}},defaultVariants:{variant:"default",size:"md"}}),le=me.forwardRef(({className:e,id:t,style:s,"data-testid":n,"aria-label":a,"aria-describedby":i,variant:o=defaultComponentProps.variant,size:l=defaultComponentProps.size,glass:u=defaultComponentProps.glass,glassIntensity:d=defaultComponentProps.glassIntensity,glassDepth:m,glassConfig:p,animation:y=defaultComponentProps.animation,disableAnimation:w=defaultComponentProps.disableAnimation,animationDuration:g,animationDelay:f,motionProps:h,disabled:b=!1,loading:x=!1,loadingText:N,interactive:R=defaultComponentProps.interactive,hoverable:I=defaultComponentProps.hoverable,icon:j,iconPosition:E="left",iconSize:k,iconAnimation:L,type:$="button",onClick:W,fullWidth:D=!1,asChild:V=!1,shimmer:z=!1,glow:O=!1,loadingSpinner:M="default",children:K,...U},ee)=>{const Y=V?mc:"button",{whileHover:se,whileTap:G,whileFocus:P,whileInView:B,animate:_,initial:te,exit:H,variants:Z,transition:ue,...J}=U,oe=o==="glass",pe=o==="default"||o==="primary",q=k||sizeToPixels[l]*.5,ce=u||oe?Ke({element:"button",profile:d==="subtle"?"soft":d==="intense"?"hard":"standard",interactive:R&&!b&&!x,...p}):"",fe=w||y==="none"?{}:{...He[y]||He.fadeIn,transition:{...ze.default,duration:g||ze.default.duration,delay:f||0}},Ne=()=>r.jsx(v.motion.div,{className:"absolute inset-0 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:M==="default"?r.jsx(F.Loader2,{className:"animate-spin",size:q}):M==="dots"?r.jsx("div",{className:"flex gap-1",children:[0,1,2].map(It=>r.jsx(v.motion.div,{className:"w-2 h-2 bg-current rounded-full",animate:{opacity:[.3,1,.3]},transition:{duration:.8,delay:It*.2,repeat:1/0}},It))}):r.jsx(v.motion.div,{className:"w-6 h-6 border-2 border-current border-t-transparent rounded-full",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}})}),ke=()=>{!b&&!x&&W&&W()};return r.jsx(v.motion.div,{whileHover:I&&!b&&!x?{scale:1.02}:{},whileTap:R&&!b&&!x?{scale:.98}:{},transition:{type:"spring",stiffness:400,damping:17},...fe,...h,children:r.jsxs(Y,{ref:ee,id:t,type:V?void 0:$,className:S(kc({variant:o,size:l}),u&&ce,x&&"cursor-not-allowed opacity-70",D&&"w-full",e),style:s,disabled:b||x,onClick:ke,"data-testid":n,"aria-label":a,"aria-describedby":i,"aria-busy":x,...J,children:[r.jsx(v.AnimatePresence,{children:x&&r.jsx(Ne,{})}),r.jsxs("span",{className:S("relative z-10 flex items-center justify-center gap-2",x&&"opacity-0"),children:[j&&E==="left"&&r.jsx(j,{size:q,className:S("shrink-0",L&&!w&&`animate-${L}`)}),x&&N?N:K,j&&E==="right"&&r.jsx(j,{size:q,className:S("shrink-0",L&&!w&&`animate-${L}`)})]}),z&&!x&&!b&&r.jsx(v.motion.div,{className:"absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/30 to-transparent",initial:{x:"-100%"},animate:{x:"200%"},transition:{duration:1.5,repeat:1/0,repeatDelay:3,ease:"easeInOut"}}),O&&pe&&!b&&r.jsx(v.motion.div,{className:"absolute inset-0 rounded-md bg-gradient-to-r from-blue-400/0 via-blue-400/40 to-blue-400/0",animate:{opacity:[0,.8,0],scale:[.8,1.1,.8]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}})]})})});le.displayName="Button";const $e={buttonPress:{whileTap:{scale:.95,transition:{duration:.1,ease:"easeInOut"}},whileHover:{scale:1.02,y:-1,transition:{duration:.2,ease:"easeOut"}}},glassHover:{whileHover:{backdropFilter:"blur(16px)",backgroundColor:"rgba(255, 255, 255, 0.15)",scale:1.01,transition:{duration:.3,ease:"easeOut"}}},magnetic:{whileHover:{scale:1.05,rotate:[0,1,-1,0],transition:{scale:{duration:.2},rotate:{duration:.6,repeat:1/0}}}},pulse:{animate:{scale:[1,1.05,1],opacity:[.8,1,.8],transition:{duration:2,repeat:1/0,ease:"easeInOut"}}}},We=c.forwardRef(({className:e,variant:t=defaultComponentProps.variant,size:s=defaultComponentProps.size,glassConfig:n={element:"button"},icon:a,iconPosition:i="left",loading:o=!1,ripple:l=!0,glow:u=!1,animation:d="scale",disableAnimation:m=!1,magnetic:p=!1,haptic:y=!1,enablePerformanceMonitoring:w=!1,children:g,disabled:f,onClick:h,...b},x)=>{const[N,R]=c.useState(!1),[I,j]=c.useState(null),E=c.useRef([]);c.useEffect(()=>()=>{E.current.forEach(O=>clearTimeout(O)),E.current=[],j(null),R(!1)},[]);const k=t==="glass"?Ke({...n,glow:u}):"",L=er[s],$=m?{}:He[d],W={default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",glass:k},D=m?{}:{...$e.buttonPress,...p?$e.magnetic:{},onTapStart:()=>R(!0),onTap:()=>R(!1),onTapCancel:()=>R(!1)},V=O=>{if(!(f||o)){if(l&&!m){const M=O.currentTarget.getBoundingClientRect(),K=O.clientX-M.left,U=O.clientY-M.top,ee=Date.now();j({x:K,y:U,id:ee});const Y=setTimeout(()=>{j(se=>se?.id===ee?null:se)},600);E.current.push(Y)}if(y&&"vibrate"in navigator)try{navigator.vibrate(10)}catch{}h?.(O)}};return r.jsxs(v.motion.button,{ref:x,className:S("relative inline-flex items-center justify-center overflow-hidden","whitespace-nowrap font-medium ring-offset-background","transition-colors focus-visible:outline-none focus-visible:ring-2","focus-visible:ring-ring focus-visible:ring-offset-2","disabled:pointer-events-none disabled:opacity-50","text-crisp",t==="glass"&&"glass-text dark:glass-text-dark glass-optimized glass-accessible glass-wcag-compliant",L.padding,L.text,L.height,L.radius,L.gap,W[t],u&&t!=="glass"&&"shadow-lg shadow-current/25",o&&"cursor-wait","will-change-transform backface-hidden",e),disabled:f||o,onClick:V,...$,...D,transition:{...ze.default,duration:.2,type:"tween"},layout:!1,"aria-label":t==="glass"&&!b["aria-label"]?`Glass button: ${g}`:b["aria-label"],role:"button",...b,children:[o&&r.jsx(v.motion.div,{className:"absolute inset-0 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2,type:"tween"},children:r.jsx(v.motion.div,{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear",type:"tween"}})}),r.jsxs(v.motion.div,{className:S("flex items-center gap-2",o&&"opacity-0",t==="glass"&&"glass-text-container"),"data-glass-text":t==="glass"?"true":void 0,style:t==="glass"?{backgroundColor:"var(--glass-effective-bg)",color:"var(--glass-effective-text)",position:"relative",zIndex:1}:void 0,animate:{opacity:o?0:1},transition:{duration:.2,type:"tween"},children:[a&&i==="left"&&r.jsx(a,{className:S(L.icon,"flex-shrink-0")}),g,a&&i==="right"&&r.jsx(a,{className:S(L.icon,"flex-shrink-0")})]}),I&&r.jsx(v.motion.div,{className:"absolute rounded-full bg-white/30 pointer-events-none",style:{left:I.x-10,top:I.y-10,width:20,height:20},initial:{scale:0,opacity:1},animate:{scale:4,opacity:0},transition:{duration:.6,ease:"easeOut",type:"tween"}},I.id),u&&!m&&t!=="glass"&&r.jsx(v.motion.div,{className:"absolute inset-0 rounded-md bg-gradient-to-r from-transparent via-white/10 to-transparent",animate:{x:["-100%","100%"]},transition:{duration:2,repeat:1/0,ease:"linear",type:"tween"}}),N&&r.jsx("div",{className:"absolute inset-0 bg-black/10 rounded-md"})]})});We.displayName="LuminarButton";const Nc={bounce:{animate:{y:[0,-20,0],transition:{duration:.6,repeat:1/0,repeatDelay:1}}},spin:{animate:{rotate:360,transition:{duration:1,repeat:1/0,ease:"linear"}}},pulse:{animate:{scale:[1,1.2,1],opacity:[1,.8,1],transition:{duration:1,repeat:1/0}}},shake:{animate:{x:[0,-10,10,-10,10,0],transition:{duration:.5,repeat:1/0,repeatDelay:1}}},flip:{animate:{rotateY:[0,180,360],transition:{duration:1.2,repeat:1/0,repeatDelay:1}}},swing:{animate:{rotate:[0,15,-15,15,-15,0],transition:{duration:1,repeat:1/0,repeatDelay:.5}}},rubberBand:{animate:{scaleX:[1,1.25,.75,1.15,.95,1],scaleY:[1,.75,1.25,.85,1.05,1],transition:{duration:.8,repeat:1/0,repeatDelay:1}}},heartBeat:{animate:{scale:[1,1.3,1,1.3,1],transition:{duration:1.3,repeat:1/0}}},rotate:{animate:{rotate:360,transition:{duration:2,repeat:1/0,ease:"linear"}}},none:{animate:{}}},Qe=c.forwardRef(({icon:e,animation:t="none",size:s=24,strokeWidth:n=2,trigger:a="auto",duration:i,className:o,...l},u)=>{const m=(()=>{if(t==="none"||a==="none")return{};const p=Nc[t];switch(a){case"hover":return{variants:{initial:{},hover:p.animate},initial:"initial",whileHover:"hover"};case"click":return{variants:{initial:{},tap:p.animate},initial:"initial",whileTap:"tap"};case"auto":default:return{animate:p.animate}}})();return r.jsx(v.motion.div,{ref:u,className:S("inline-flex items-center justify-center",o),...m,...l,children:r.jsx(e,{size:s,strokeWidth:n})})});Qe.displayName="LuminarIcon";const sr=c.forwardRef(({icon:e,variant:t=defaultComponentProps.variant,size:s=defaultComponentProps.size,animation:n="none",iconSize:a,label:i,labelPosition:o="right",className:l,disabled:u,onClick:d},m)=>{const p={sm:"h-8 px-3 text-sm gap-1.5",md:"h-10 px-4 text-base gap-2",lg:"h-12 px-6 text-lg gap-2.5"},y={sm:a||16,md:a||20,lg:a||24},w={default:"bg-primary text-primary-foreground hover:bg-primary/90",ghost:"hover:bg-accent hover:text-accent-foreground",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",glass:Ke({element:"button"})};return r.jsxs(v.motion.button,{ref:m,className:S("inline-flex items-center justify-center rounded-lg font-medium transition-colors","focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2","disabled:pointer-events-none disabled:opacity-50",p[s],w[t],l),whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:17},disabled:u,onClick:d,children:[i&&o==="left"&&r.jsx("span",{children:i}),r.jsx(Qe,{icon:e,size:y[s],animation:n,trigger:"hover"}),i&&o==="right"&&r.jsx("span",{children:i})]})});sr.displayName="LuminarIconButton";const Sc={"bottom-right":"bottom-6 right-6","bottom-left":"bottom-6 left-6","top-right":"top-6 right-6","top-left":"top-6 left-6"},vn={sm:"w-12 h-12",md:"w-14 h-14",lg:"w-16 h-16",xl:"w-18 h-18"},Tc={sm:"h-12 px-4",md:"h-14 px-6",lg:"h-16 px-8",xl:"h-18 px-10"},Rs={sm:"w-5 h-5",md:"w-6 h-6",lg:"w-7 h-7",xl:"w-8 h-8"},Rc={primary:"bg-blue-500 hover:bg-blue-600 text-white",secondary:"bg-gray-500 hover:bg-gray-600 text-white",success:"bg-green-500 hover:bg-green-600 text-white",warning:"bg-yellow-500 hover:bg-yellow-600 text-white",error:"bg-red-500 hover:bg-red-600 text-white",glass:"bg-white/10 hover:bg-white/20 text-white backdrop-blur-md",default:"bg-gray-100 hover:bg-gray-200 text-gray-900",outline:"bg-transparent border-2 border-current hover:bg-current/10"},Ct=c.forwardRef(({icon:e=F.Plus,actions:t=[],position:s="bottom-right",size:n=defaultComponentProps.size,variant:a="primary",extended:i=!1,label:o,glass:l=!0,animated:u=!0,disabled:d=!1,hideOnScroll:m=!1,onClick:p,className:y,...w},g)=>{const[f,h]=c.useState(!1),[b,x]=c.useState(!0),[N,R]=c.useState(0);m&&typeof window<"u"&&window.addEventListener("scroll",()=>{const k=window.scrollY;k>N&&k>100?x(!1):x(!0),R(k)});const I=()=>{t.length>0?h(!f):p?.()},j=k=>{k.onClick(),h(!1)},E=S("relative rounded-full shadow-lg transition-all duration-300 flex items-center justify-center",i?Tc[n]:vn[n],Rc[a],l&&Ke({element:"control"}),d&&"opacity-50 cursor-not-allowed",!b&&m&&"translate-y-24",y);return r.jsxs("div",{ref:g,className:S("fixed z-50",Sc[s]),...w,children:[r.jsx(v.AnimatePresence,{children:f&&t.map((k,L)=>r.jsxs(v.motion.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1,y:s.includes("bottom")?-(L+1)*60:(L+1)*60},exit:{scale:0,opacity:0},transition:{delay:L*.05,type:"spring",stiffness:500,damping:25},className:"absolute right-0",style:{bottom:s.includes("bottom")?0:void 0,top:s.includes("top")?0:void 0},children:[r.jsx(v.motion.button,{whileHover:{scale:1.1},whileTap:{scale:.95},onClick:()=>j(k),className:S("rounded-full shadow-lg flex items-center justify-center",vn.sm,k.color||"bg-gray-700 hover:bg-gray-600 text-white",l&&"backdrop-blur-md bg-opacity-90"),title:k.label,children:c.createElement(k.icon,{className:Rs.sm})}),r.jsx(v.motion.span,{initial:{opacity:0,x:s.includes("right")?10:-10},animate:{opacity:1,x:0},className:S("absolute whitespace-nowrap px-2 py-1 rounded text-sm",s.includes("right")?"right-full mr-3":"left-full ml-3","top-1/2 -translate-y-1/2","bg-gray-800 text-white"),children:k.label})]},k.id))}),r.jsx(v.motion.button,{className:E,onClick:I,disabled:d,whileHover:u&&!d?{scale:1.05}:{},whileTap:u&&!d?{scale:.95}:{},animate:f&&t.length>0?{rotate:45}:{rotate:0},transition:{duration:.2},children:t.length>0&&f?r.jsx(F.X,{className:Rs[n]}):r.jsxs(r.Fragment,{children:[c.createElement(e,{className:S(Rs[n],i&&o&&"mr-2")}),i&&o&&r.jsx("span",{className:"font-medium",children:o})]})}),r.jsx(v.AnimatePresence,{children:f&&t.length>0&&r.jsx(v.motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 -z-10",onClick:()=>h(!1)})})]})});Ct.displayName="LuminarFab";const Ec=c.forwardRef((e,t)=>r.jsx(Ct,{ref:t,...e,size:"sm",extended:!1}));Ec.displayName="LuminarMiniFab";const Ac=c.forwardRef(({label:e="Action",...t},s)=>r.jsx(Ct,{ref:s,...t,extended:!0,label:e}));Ac.displayName="LuminarExtendedFab";const kt=c.forwardRef(({trigger:e,items:t,align:s="left",glass:n=!0,className:a,...i},o)=>{const[l,u]=c.useState(!1),d=c.useRef(null);c.useEffect(()=>{const y=w=>{d.current&&!d.current.contains(w.target)&&u(!1)};return document.addEventListener("mousedown",y),()=>document.removeEventListener("mousedown",y)},[]);const m=y=>{y.disabled||y.separator||(y.onClick?.(),u(!1))},p={left:"left-0",right:"right-0",center:"left-1/2 -translate-x-1/2"};return r.jsxs("div",{ref:d,className:"relative inline-block",...i,children:[r.jsx(v.motion.div,{onClick:()=>u(!l),whileHover:{scale:1.02},whileTap:{scale:.98},className:"cursor-pointer",children:e}),r.jsx(v.AnimatePresence,{children:l&&r.jsx(v.motion.div,{className:S("absolute z-50 mt-2 min-w-[200px] overflow-hidden",n?Ke({element:"control",profile:"hard"}):"bg-popover rounded-lg shadow-xl",p[s],a),initial:{opacity:0,y:-10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-10,scale:.95},transition:{duration:.2},children:r.jsx("div",{className:"py-1",children:t.map((y,w)=>{if(y.separator)return r.jsx("div",{className:"my-1 h-px bg-white/20 dark:bg-gray-700/30"},y.id);const g=y.icon;return r.jsxs(v.motion.div,{className:S("px-4 py-2 flex items-center gap-3 cursor-pointer","transition-colors",y.disabled?"opacity-50 cursor-not-allowed":"hover:bg-white/10 dark:hover:bg-gray-700/20"),onClick:()=>m(y),whileHover:y.disabled?{}:{x:4},initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:w*.05},children:[g&&typeof g=="function"&&r.jsx(g,{className:"w-4 h-4"}),r.jsx("span",{className:"text-sm",children:y.label})]},y.id)})})})})]})});kt.displayName="LuminarDropdown";const rr=c.forwardRef(({items:e=[],categories:t=[],open:s,onOpenChange:n,placeholder:a="Type a command or search...",glass:i=!0,animated:o=!0,showShortcuts:l=!0,maxHeight:u="400px",onItemSelect:d,className:m,...p},y)=>{const[w,g]=c.useState(!1),[f,h]=c.useState(""),[b,x]=c.useState(0),N=c.useRef(null),R=c.useRef([]),I=s??w,j=D=>{n?n(D):g(D),D&&(h(""),x(0))},E=c.useMemo(()=>{const D=[...e];return t.forEach(V=>{D.push(...V.items.map(z=>({...z,category:V.label})))}),D},[e,t]),k=c.useMemo(()=>{if(!f.trim())return E;const D=f.toLowerCase();return E.filter(V=>[V.label,V.description,V.category,...V.keywords||[]].filter(Boolean).join(" ").toLowerCase().includes(D))},[E,f]),L=c.useMemo(()=>{const D={};return k.forEach(V=>{const z=V.category||"Commands";D[z]||(D[z]=[]),D[z].push(V)}),D},[k]);c.useEffect(()=>{if(!I)return;const D=V=>{switch(V.key){case"Escape":j(!1);break;case"ArrowDown":V.preventDefault(),x(O=>O<k.length-1?O+1:0);break;case"ArrowUp":V.preventDefault(),x(O=>O>0?O-1:k.length-1);break;case"Enter":V.preventDefault();const z=k[b];z&&!z.disabled&&$(z);break}};return document.addEventListener("keydown",D),()=>document.removeEventListener("keydown",D)},[I,k,b]),c.useEffect(()=>{I&&N.current&&N.current.focus()},[I]),c.useEffect(()=>{const D=R.current[b];D&&D.scrollIntoView({block:"nearest",behavior:"smooth"})},[b]),c.useEffect(()=>{const D=V=>{(V.metaKey||V.ctrlKey)&&V.key==="k"&&(V.preventDefault(),j(!I))};return document.addEventListener("keydown",D),()=>document.removeEventListener("keydown",D)},[I]);const $=D=>{D.disabled||(d?.(D),D.action?.(),j(!1))},W=D=>{switch(D.toLowerCase()){case"files":return F.File;case"folders":return F.Folder;case"users":return F.User;case"settings":return F.Settings;default:return F.Hash}};return I?r.jsx(v.AnimatePresence,{children:r.jsxs(v.motion.div,{className:"fixed inset-0 z-50 flex items-start justify-center pt-[10vh]",initial:o?{opacity:0}:{},animate:{opacity:1},exit:{opacity:0},children:[r.jsx(v.motion.div,{className:"absolute inset-0 bg-black/20 backdrop-blur-sm",onClick:()=>j(!1),initial:o?{opacity:0}:{},animate:{opacity:1},exit:{opacity:0}}),r.jsxs(v.motion.div,{ref:y,className:S("relative w-full max-w-lg mx-4 overflow-hidden",i?"glass-modal-safe":"bg-popover border-border rounded-xl shadow-2xl",m),initial:o?{opacity:0,scale:.95,y:-20}:{},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:-20},transition:{type:"spring",stiffness:400,damping:30},children:[r.jsxs("div",{className:"flex items-center gap-3 p-4 border-b border-white/10 dark:border-gray-700/20",children:[r.jsx(F.Search,{className:"w-5 h-5 text-muted-foreground"}),r.jsx("input",{ref:N,type:"text",placeholder:a,value:f,onChange:D=>h(D.target.value),className:"flex-1 bg-transparent outline-none placeholder:text-muted-foreground"}),l&&r.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[r.jsx("kbd",{className:"px-1.5 py-0.5 bg-white/10 dark:bg-gray-700/20 rounded border border-white/20 dark:border-gray-600/30",children:r.jsx(F.Command,{className:"w-3 h-3"})}),r.jsx("span",{children:"+"}),r.jsx("kbd",{className:"px-1.5 py-0.5 bg-white/10 dark:bg-gray-700/20 rounded border border-white/20 dark:border-gray-600/30",children:"K"})]})]}),r.jsx("div",{className:"overflow-y-auto",style:{maxHeight:u},children:Object.keys(L).length===0?r.jsxs(v.motion.div,{className:"p-8 text-center text-muted-foreground",initial:o?{opacity:0}:{},animate:{opacity:1},children:[r.jsx(F.Search,{className:"w-8 h-8 mx-auto mb-2 opacity-50"}),r.jsx("p",{children:"No results found"}),r.jsx("p",{className:"text-xs mt-1",children:"Try a different search term"})]}):r.jsx("div",{className:"p-2",children:Object.entries(L).map(([D,V],z)=>{const O=W(D);return r.jsxs(v.motion.div,{initial:o?{opacity:0,y:10}:{},animate:{opacity:1,y:0},transition:{delay:z*.05},children:[r.jsxs("div",{className:"flex items-center gap-2 px-2 py-1 mb-1 text-xs font-medium text-muted-foreground",children:[r.jsx(O,{className:"w-3 h-3"}),r.jsx("span",{children:D})]}),V.map((M,K)=>{const U=k.indexOf(M),ee=U===b,Y=M.icon;return r.jsxs(v.motion.div,{ref:se=>{R.current[U]=se},className:S("flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all duration-200",ee&&"bg-primary/10 border border-primary/20",!ee&&"hover:bg-white/10 dark:hover:bg-gray-700/20",M.disabled&&"opacity-50 cursor-not-allowed"),onClick:()=>$(M),initial:o?{opacity:0,x:-10}:{},animate:{opacity:1,x:0},transition:{delay:z*.05+K*.02},whileHover:M.disabled?{}:{x:2},children:[Y&&r.jsx(Qe,{icon:Y,size:18,animation:ee?"pulse":"none",className:S("flex-shrink-0",ee?"text-primary":"text-muted-foreground")}),r.jsxs("div",{className:"flex-1 min-w-0",children:[r.jsx("div",{className:S("font-medium truncate",ee?"text-primary":"text-foreground"),children:M.label}),M.description&&r.jsx("div",{className:"text-xs text-muted-foreground truncate",children:M.description})]}),l&&M.shortcut&&r.jsx("div",{className:"flex items-center gap-1",children:M.shortcut.map((se,G)=>r.jsx("kbd",{className:"px-1.5 py-0.5 text-xs bg-white/10 dark:bg-gray-700/20 rounded border border-white/20 dark:border-gray-600/30",children:se},G))}),ee&&r.jsx(v.motion.div,{initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{duration:.2},children:r.jsx(F.ArrowRight,{className:"w-4 h-4 text-primary"})})]},M.id)})]},D)})})})]})]})}):null});rr.displayName="LuminarCommandPalette";const Pc=e=>"variant"in e||"size"in e,Lc=e=>"glass"in e||"glassIntensity"in e,Ic=e=>"animation"in e||"disableAnimation"in e,Ae={variant:"default",size:"md",animation:"fadeIn",disableAnimation:!1,glass:!1,glassIntensity:"medium",interactive:!0,hoverable:!0},Oc={...Ae,showError:!0,required:!1},Dc={xs:24,sm:32,md:40,lg:48,xl:56},Mc={xs:"0.25rem",sm:"0.5rem",md:"0.75rem",lg:"1rem",xl:"1.5rem"},Na=c.createContext(void 0),ns=()=>{const e=c.useContext(Na);if(!e)throw new Error("Command components must be used within Command");return e},Fc=(e,t)=>{if(!t)return 1;const s=e.toLowerCase(),n=t.toLowerCase();return s.includes(n)?1:0},nr=c.forwardRef(({className:e,value:t,onValueChange:s,filter:n=Fc,shouldFilter:a=!0,variant:i="card",size:o=Ae.size,glassIntensity:l="medium",colorTheme:u="neutral",animation:d="fadeIn",disableAnimation:m=!1,children:p,...y},w)=>{const[g,f]=c.useState(""),[h,b]=c.useState(t||""),x="bg-white/10 backdrop-blur-md border border-white/20",N={padding:o==="sm"?"p-2":o==="lg"?"p-6":"p-4"},R=m?{}:{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10}},j={search:g,setSearch:f,value:h,setValue:E=>{b(E),s?.(E)},filter:n,shouldFilter:a,onValueChange:s};return r.jsx(Na.Provider,{value:j,children:r.jsx(v.motion.div,{ref:w,className:S("flex h-full w-full flex-col overflow-hidden rounded-md text-popover-foreground",x,N.padding,e),...R,transition:{type:"spring",damping:25,stiffness:500},...y,children:p})})}),_c=c.forwardRef(({className:e,placeholder:t="Type a command or search...",value:s,onValueChange:n,...a},i)=>{const{search:o,setSearch:l}=ns(),u=d=>{const m=d.target.value;l(m),n?.(m)};return r.jsxs("div",{className:"flex items-center border-b px-3 border-white/10 dark:border-gray-700/20",children:[r.jsx(F.Search,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),r.jsx("input",{ref:i,value:s??o,onChange:u,placeholder:t,className:S("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",e),...a})]})}),Uc=c.forwardRef(({className:e,label:t="Suggestions",children:s,...n},a)=>r.jsx("div",{ref:a,className:S("max-h-[300px] overflow-y-auto overflow-x-hidden",e),role:"listbox","aria-label":t,...n,children:s})),$c=c.forwardRef(({className:e,...t},s)=>{const{search:n}=ns();return r.jsx("div",{ref:s,className:S("py-6 text-center text-sm text-muted-foreground",e),...t,children:n?"No results found.":"No commands available."})}),zc=c.forwardRef(({className:e,heading:t,value:s,children:n,...a},i)=>{const{search:o,filter:l,shouldFilter:u}=ns(),d=u?c.Children.toArray(n).filter(m=>c.isValidElement(m)&&m.props.value?l(m.props.value,o)>0:!0):n;return u&&c.Children.count(d)===0?null:r.jsxs("div",{ref:i,className:S("overflow-hidden p-1",e),...a,children:[t&&r.jsx("div",{className:"px-2 py-1.5 text-xs font-medium text-muted-foreground",children:t}),r.jsx("div",{role:"group",children:d})]})}),Qc=c.forwardRef(({className:e,value:t="",onSelect:s,disabled:n=!1,keywords:a=[],children:i},o)=>{const{search:l,filter:u,shouldFilter:d,setValue:m}=ns(),p=[t,...a].join(" "),y=d?u(p,l):1;if(d&&y===0)return null;const w=()=>{n||(m(t),s?.(t))};return r.jsx(v.motion.div,{ref:o,role:"option","aria-selected":!1,className:S("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors","hover:bg-white/10 focus:bg-white/10 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n&&"opacity-50 cursor-not-allowed",e),onClick:w,...!n&&$e.buttonPress,children:i})}),qc=c.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:S("my-1 h-px bg-white/10 dark:bg-gray-700/20",e),...t})),Bc=c.forwardRef(({className:e,...t},s)=>r.jsx("span",{ref:s,className:S("ml-auto text-xs tracking-widest text-muted-foreground",e),...t})),Vc=c.forwardRef(({className:e,open:t=!1,onOpenChange:s,variant:n="modal",size:a=Ae.size,glassIntensity:i="medium",colorTheme:o="neutral",animation:l="glassScale",disableAnimation:u=!1,children:d,...m},p)=>{const y=c.useRef(null),w="bg-white/10 backdrop-blur-md border border-white/20",g=u?{}:{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95}};return c.useEffect(()=>{const f=b=>{b.key==="Escape"&&s?.(!1)},h=b=>{y.current&&!y.current.contains(b.target)&&s?.(!1)};return t&&(document.addEventListener("keydown",f),document.addEventListener("mousedown",h),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",f),document.removeEventListener("mousedown",h),document.body.style.overflow="unset"}},[t,s]),c.useEffect(()=>{const f=h=>{(h.metaKey||h.ctrlKey)&&h.key==="k"&&(h.preventDefault(),s?.(!t))};return document.addEventListener("keydown",f),()=>document.removeEventListener("keydown",f)},[t,s]),r.jsx(v.AnimatePresence,{children:t&&r.jsxs(r.Fragment,{children:[r.jsx(v.motion.div,{className:"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0}}),r.jsx("div",{className:"fixed inset-0 z-50 flex items-start justify-center pt-[10vh] px-4",children:r.jsx(v.motion.div,{ref:y,className:S("relative w-full max-w-lg overflow-hidden rounded-xl shadow-2xl",w,e),...g,transition:{type:"spring",damping:25,stiffness:500},...m,children:d})})]})})});nr.displayName="LuminarCommand";_c.displayName="LuminarCommandInput";Uc.displayName="LuminarCommandList";$c.displayName="LuminarCommandEmpty";zc.displayName="LuminarCommandGroup";Qc.displayName="LuminarCommandItem";qc.displayName="LuminarCommandSeparator";Bc.displayName="LuminarCommandShortcut";Vc.displayName="LuminarCommandDialog";const as=c.forwardRef(({children:e,onClick:t,loadingText:s="Loading...",successText:n="Success!",errorText:a="Error",successDuration:i=2e3,errorDuration:o=3e3,resetOnError:l=!0,className:u,disabled:d,...m},p)=>{const[y,w]=c.useState("idle"),[g,f]=c.useState(!1),h=async()=>{if(!(y!=="idle"||d||!t)){w("loading"),f(!0);try{await t(),w("success"),setTimeout(()=>{w("idle"),f(!1)},i)}catch{w("error"),l?setTimeout(()=>{w("idle"),f(!1)},o):f(!1)}}},b=()=>{switch(y){case"loading":return r.jsxs(v.motion.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"flex items-center space-x-2",children:[r.jsx(F.Loader2,{className:"w-4 h-4 animate-spin"}),r.jsx("span",{children:s})]},"loading");case"success":return r.jsxs(v.motion.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"flex items-center space-x-2",children:[r.jsx(F.Check,{className:"w-4 h-4"}),r.jsx("span",{children:n})]},"success");case"error":return r.jsxs(v.motion.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"flex items-center space-x-2",children:[r.jsx(F.AlertCircle,{className:"w-4 h-4"}),r.jsx("span",{children:a})]},"error");default:return r.jsx(v.motion.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},children:e},"idle")}},x=()=>{switch(y){case"success":return"bg-green-500 hover:bg-green-600 text-white";case"error":return"bg-red-500 hover:bg-red-600 text-white";default:return""}};return r.jsxs(We,{ref:p,onClick:h,disabled:d||g,className:S("relative overflow-hidden transition-all duration-300",y!=="idle"&&x(),u),...m,children:[r.jsx(v.AnimatePresence,{mode:"wait",children:b()}),r.jsxs(v.AnimatePresence,{children:[y==="success"&&r.jsx(v.motion.div,{initial:{scale:0,opacity:.5},animate:{scale:2,opacity:0},exit:{opacity:0},transition:{duration:.6},className:"absolute inset-0 bg-green-400 rounded-full",style:{originX:.5,originY:.5}}),y==="error"&&r.jsx(v.motion.div,{initial:{x:"-100%"},animate:{x:"100%"},exit:{opacity:0},transition:{duration:.5},className:"absolute inset-0 bg-gradient-to-r from-transparent via-red-300/30 to-transparent"})]})]})});as.displayName="StatefulButton";function Hc(){const e=()=>new Promise(t=>{setTimeout(t,4e3)});return r.jsx("div",{className:"flex h-40 w-full items-center justify-center",children:r.jsx(as,{onClick:e,children:"Send message"})})}const Gc=Object.freeze(Object.defineProperty({__proto__:null,Button:le,LuminarButton:We,LuminarCommand:nr,LuminarCommandPalette:rr,LuminarDropdown:kt,LuminarFab:Ct,LuminarIconButton:sr,StatefulButton:as,StatefulButtonDemo:Hc},Symbol.toStringTag,{value:"Module"})),ar=c.forwardRef(({variant:e=defaultComponentProps.variant,size:t=defaultComponentProps.size,color:s,className:n,...a},i)=>{const l={sm:20,md:40,lg:60,xl:80}[t],u={default:r.jsx(v.motion.svg,{width:l,height:l,viewBox:"0 0 50 50",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:r.jsx("circle",{cx:"25",cy:"25",r:"20",stroke:s||"currentColor",strokeWidth:"5",fill:"none",strokeLinecap:"round",strokeDasharray:"80 20"})}),dots:r.jsx("div",{className:"flex gap-1",children:[0,1,2].map(d=>r.jsx(v.motion.div,{className:S("rounded-full",t==="sm"&&"w-2 h-2",t==="md"&&"w-3 h-3",t==="lg"&&"w-4 h-4",t==="xl"&&"w-5 h-5"),style:{backgroundColor:s||"currentColor"},animate:{y:[0,-10,0],opacity:[.3,1,.3]},transition:{duration:.6,repeat:1/0,delay:d*.15}},d))}),pulse:r.jsx(v.motion.div,{className:S("rounded-full border-4",t==="sm"&&"w-5 h-5",t==="md"&&"w-10 h-10",t==="lg"&&"w-15 h-15",t==="xl"&&"w-20 h-20"),style:{borderColor:s||"currentColor"},animate:{scale:[1,1.2,1],opacity:[1,.5,1]},transition:{duration:1.5,repeat:1/0,ease:"easeInOut"}}),orbit:r.jsx("div",{className:"relative",style:{width:l,height:l},children:r.jsx(v.motion.div,{className:"absolute inset-0",animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},children:[0,120,240].map(d=>r.jsx(v.motion.div,{className:S("absolute rounded-full",t==="sm"&&"w-2 h-2",t==="md"&&"w-3 h-3",t==="lg"&&"w-4 h-4",t==="xl"&&"w-5 h-5"),style:{backgroundColor:s||"currentColor",top:"10%",left:"50%",transform:`translateX(-50%) rotate(${d}deg) translateY(-${l/2.5}px)`},animate:{scale:[1,.5,1],opacity:[1,.3,1]},transition:{duration:1.5,repeat:1/0,delay:d/360}},d))})}),bars:r.jsx("div",{className:"flex gap-1",children:[0,1,2,3,4].map(d=>r.jsx(v.motion.div,{className:S("rounded-sm",t==="sm"&&"w-1 h-4",t==="md"&&"w-1.5 h-8",t==="lg"&&"w-2 h-12",t==="xl"&&"w-3 h-16"),style:{backgroundColor:s||"currentColor"},animate:{scaleY:[.5,1,.5],opacity:[.5,1,.5]},transition:{duration:.8,repeat:1/0,delay:d*.1}},d))})};return r.jsx("div",{ref:i,className:S("inline-flex items-center justify-center",n),...a,children:u[e]})});ar.displayName="LoadingSpinner";const ir=c.forwardRef(({items:e,type:t="single",defaultOpenItems:s=[],glass:n=!0,className:a,...i},o)=>{const[l,u]=c.useState(s),d=(m,p)=>{p||u(t==="single"?l.includes(m)?[]:[m]:l.includes(m)?l.filter(y=>y!==m):[...l,m])};return r.jsx("div",{ref:o,className:S("space-y-3",a),...i,children:e.map((m,p)=>{const y=l.includes(m.id);return r.jsxs(v.motion.div,{className:S("rounded-lg overflow-hidden",n&&"backdrop-blur-md bg-white/10 dark:bg-gray-900/10","border border-white/20 dark:border-gray-700/30"),initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:p*.1},children:[r.jsxs(v.motion.button,{className:S("w-full px-6 py-4 flex items-center justify-between text-left","transition-colors",m.disabled?"opacity-50 cursor-not-allowed":"hover:bg-white/5 dark:hover:bg-gray-700/10"),onClick:()=>d(m.id,m.disabled),whileHover:m.disabled?{}:{scale:1.01},whileTap:m.disabled?{}:{scale:.99},children:[r.jsx("h3",{className:"font-medium",children:m.title}),r.jsx(v.motion.div,{animate:{rotate:y?180:0},transition:{duration:.3,ease:"easeInOut"},children:r.jsx(F.ChevronDown,{className:"w-5 h-5 text-muted-foreground"})})]}),r.jsx(v.AnimatePresence,{initial:!1,children:y&&r.jsx(v.motion.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3,ease:"easeInOut"},className:"overflow-hidden",children:r.jsx("div",{className:"px-6 pb-4",children:r.jsx("div",{className:"pt-2 text-muted-foreground",children:m.content})})})})]},m.id)})})});ir.displayName="LuminarAccordion";const is=c.forwardRef(({src:e,alt:t="Avatar",fallback:s,size:n="md",status:a,glass:i=!0,animated:o=!0,className:l,...u},d)=>{const m={sm:"w-8 h-8 text-xs",md:"w-10 h-10 text-sm",lg:"w-12 h-12 text-base",xl:"w-16 h-16 text-lg"},p={sm:"w-2 h-2",md:"w-2.5 h-2.5",lg:"w-3 h-3",xl:"w-4 h-4"},y={online:"bg-green-500",offline:"bg-gray-400",away:"bg-yellow-500",busy:"bg-red-500"},w=g=>g.split(" ").map(f=>f[0]).join("").toUpperCase().slice(0,2);return r.jsxs(v.motion.div,{ref:d,className:S("relative inline-flex items-center justify-center rounded-full overflow-hidden",i&&"backdrop-blur-md bg-white/20 dark:bg-gray-900/20 border border-white/30 dark:border-gray-700/40",!i&&"bg-secondary",m[n],l),initial:o?{scale:0,rotate:-180}:{},animate:o?{scale:1,rotate:0}:{},whileHover:o?{scale:1.05}:{},transition:{type:"spring",stiffness:400,damping:30},children:[e?r.jsx("img",{src:e,alt:t,className:"w-full h-full object-cover",onError:g=>{g.target.style.display="none"}}):null,(!e||s)&&r.jsx("span",{className:"font-medium",children:s?w(s):"?"}),a&&r.jsx(v.motion.span,{className:S("absolute bottom-0 right-0 rounded-full ring-2 ring-background",p[n],y[a]),initial:{scale:0},animate:{scale:1},transition:{delay:.2},children:a==="online"&&r.jsx(v.motion.span,{className:S("absolute inset-0 rounded-full",y[a]),animate:{scale:[1,1.5,1.5],opacity:[1,0,0]},transition:{duration:2,repeat:1/0}})})]})});is.displayName="LuminarAvatar";const Kc=c.forwardRef(({children:e,max:t=3,size:s="md",glass:n=!0,className:a,...i},o)=>{const l=Array.isArray(e)?e:[e],u=l.slice(0,t),d=l.length-t,m={sm:"-ml-2",md:"-ml-3",lg:"-ml-4",xl:"-ml-5"};return r.jsxs("div",{ref:o,className:S("flex items-center",a),...i,children:[u.map((p,y)=>r.jsx(v.motion.div,{className:S(y>0&&m[s],"relative"),initial:{x:-20,opacity:0},animate:{x:0,opacity:1},transition:{delay:y*.1},style:{zIndex:u.length-y},children:p},y)),d>0&&r.jsx(v.motion.div,{className:S(m[s],"relative"),initial:{x:-20,opacity:0},animate:{x:0,opacity:1},transition:{delay:u.length*.1},children:r.jsx(is,{size:s,glass:n,fallback:`+${d}`,animated:!1})})]})});Kc.displayName="LuminarAvatarGroup";const Nt=c.forwardRef(({variant:e=defaultComponentProps.variant,size:t=defaultComponentProps.size,glass:s=!0,removable:n=!1,onRemove:a,pulse:i=!1,className:o,children:l,...u},d)=>{const m={sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-1 text-sm",lg:"px-3 py-1.5 text-base"},p={default:s?"bg-white/20 dark:bg-gray-900/20 text-foreground":"bg-secondary text-secondary-foreground",secondary:s?"bg-gray-500/20 text-gray-600 dark:text-gray-400 border-gray-500/30":"bg-gray-500 text-white",success:s?"bg-green-500/20 text-green-600 dark:text-green-400 border-green-500/30":"bg-green-500 text-white",warning:s?"bg-yellow-500/20 text-yellow-600 dark:text-yellow-400 border-yellow-500/30":"bg-yellow-500 text-white",error:s?"bg-red-500/20 text-red-600 dark:text-red-400 border-red-500/30":"bg-red-500 text-white",info:s?"bg-blue-500/20 text-blue-600 dark:text-blue-400 border-blue-500/30":"bg-blue-500 text-white"};return r.jsxs(v.motion.div,{ref:d,className:S("inline-flex items-center gap-1 rounded-full font-medium",s&&"backdrop-blur-md border border-white/20 dark:border-gray-700/30",m[t],p[e],o),initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},whileHover:{scale:1.05},transition:{type:"spring",stiffness:500,damping:30},children:[i&&r.jsx(v.motion.span,{className:S("absolute -inset-1 rounded-full",e==="default"&&"bg-gray-400",e==="secondary"&&"bg-gray-500",e==="success"&&"bg-green-500",e==="warning"&&"bg-yellow-500",e==="error"&&"bg-red-500",e==="info"&&"bg-blue-500"),animate:{scale:[1,1.5,1.5],opacity:[.5,0,0]},transition:{duration:2,repeat:1/0,repeatDelay:1}}),r.jsx("span",{className:"relative z-10",children:l}),n&&r.jsx(v.motion.button,{className:"relative z-10 ml-1 -mr-1 p-0.5 rounded-full hover:bg-black/10 dark:hover:bg-white/10",onClick:y=>{y.stopPropagation(),a?.()},whileHover:{scale:1.1},whileTap:{scale:.9},children:r.jsx(F.X,{className:S(t==="sm"&&"w-2.5 h-2.5",t==="md"&&"w-3 h-3",t==="lg"&&"w-3.5 h-3.5")})})]})});Nt.displayName="LuminarBadge";class Wc{metrics=[];observers=[];measureRender(t,s){const n=performance.now(),a=s(),i=performance.now();return this.addMetric({name:`${t}.render`,value:i-n,unit:"ms",timestamp:Date.now(),type:"render"}),a}measureAnimation(t,s){this.addMetric({name:`${t}.animation`,value:s,unit:"ms",timestamp:Date.now(),type:"animation"})}trackMemory(t){if("memory"in performance){const s=performance.memory;this.addMetric({name:`${t}.memory`,value:s.usedJSHeapSize/1024/1024,unit:"MB",timestamp:Date.now(),type:"memory"})}}getComponentMetrics(t){return this.metrics.filter(s=>s.name.startsWith(t))}getMetricsByType(t){return this.metrics.filter(s=>s.type===t)}getSummary(){const t=this.getMetricsByType("render"),s=this.getMetricsByType("animation"),n=this.getMetricsByType("memory");return{averageRenderTime:this.average(t.map(a=>a.value)),averageAnimationDuration:this.average(s.map(a=>a.value)),peakMemoryUsage:Math.max(...n.map(a=>a.value)),totalMeasurements:this.metrics.length,components:[...new Set(this.metrics.map(a=>a.name.split(".")[0]))]}}subscribe(t){return this.observers.push(t),()=>{const s=this.observers.indexOf(t);s>-1&&this.observers.splice(s,1)}}clear(){this.metrics=[],this.notifyObservers()}addMetric(t){this.metrics.push(t),this.metrics.length>1e3&&(this.metrics=this.metrics.slice(-1e3)),this.notifyObservers()}notifyObservers(){this.observers.forEach(t=>t([...this.metrics]))}average(t){return t.length===0?0:t.reduce((s,n)=>s+n,0)/t.length}}const Yc=new Wc;class Jc{budgets=new Map;violations=[];setBudget(t,s,n,a="ms"){this.budgets.set(`${t}.${s}`,{limit:n,unit:a})}checkBudget(t){const s=t.name,n=this.budgets.get(s);return n&&t.value>n.limit?(this.violations.push({component:t.name.split(".")[0],metric:t.name,value:t.value,limit:n.limit}),!1):!0}getViolations(){return[...this.violations]}clearViolations(){this.violations=[]}}const or=new Jc;or.setBudget("LuminarCard","render",16);or.setBudget("LuminarModal","render",32);or.setBudget("LuminarInput","render",8);const xt=c.forwardRef(({className:e,id:t,style:s,"data-testid":n,"aria-label":a,"aria-describedby":i,variant:o=defaultComponentProps.variant,size:l=defaultComponentProps.size,glass:u=defaultComponentProps.glass,glassIntensity:d=defaultComponentProps.glassIntensity,glassDepth:m,glassConfig:p={element:"card"},animation:y=defaultComponentProps.animation,disableAnimation:w=defaultComponentProps.disableAnimation,animationDuration:g,animationDelay:f,motionProps:h,interactive:b=defaultComponentProps.interactive,hoverable:x=defaultComponentProps.hoverable,spacing:N,padding:R,margin:I,gap:j,maxWidth:E,maxHeight:k,centered:L,elevation:$=1,clickable:W=!1,onClick:D,bordered:V=!1,enablePerformanceMonitoring:z=!1,children:O,...M},K)=>{const U=sizeToSpacing[l],ee=u||o==="glass"?Ke({...p,element:"card",profile:d==="subtle"?"soft":d==="intense"?"hard":"standard",interactive:b||x||W}):"",Y=w||y==="none"?{}:{...He[y]||He.slideUp,transition:{...ze.default,duration:g||ze.default.duration,delay:f||0}},se=(b||x||W)&&!w?{...$e.glassHover,whileTap:b||W?$e.buttonPress.whileTap:void 0}:{},G=()=>{W&&D&&D()},P={0:"",1:"shadow-sm",2:"shadow-md",3:"shadow-lg",4:"shadow-xl",5:"shadow-2xl"}[$]||"shadow-sm",B={...N&&{padding:typeof N=="number"?`${N}px`:U},...R&&{padding:typeof R=="number"?`${R}px`:sizeToSpacing[R]},...I&&{margin:typeof I=="number"?`${I}px`:sizeToSpacing[I]},...E&&{maxWidth:typeof E=="number"?`${E}px`:E},...k&&{maxHeight:typeof k=="number"?`${k}px`:k},...s},_=r.jsxs(v.motion.div,{ref:K,id:t,className:S("relative overflow-hidden rounded-lg","text-crisp",ee,P,V&&"border border-border",(b||W)&&"cursor-pointer",L&&"flex items-center justify-center",j&&`gap-${j}`,"will-change-transform backface-hidden",e),style:B,onClick:G,"data-testid":n,"aria-label":a,"aria-describedby":i,...Y,...se,...h,transition:ze.default,...M,children:[r.jsx("div",{className:"relative z-10",children:O}),(b||W)&&r.jsx("div",{className:"absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300",children:r.jsx("div",{className:"glass-shimmer"})})]});return z?Yc.measureRender("LuminarCard",()=>_):_});xt.displayName="LuminarCard";const jn=1e4,Xc=(e,t)=>Math.abs(e)*t,lr=c.forwardRef(({children:e,autoPlay:t=!1,interval:s=3e3,loop:n=!0,showIndicators:a=!0,showControls:i=!0,pauseOnHover:o=!0,effect:l="slide",orientation:u="horizontal",glass:d=!0,animated:m=!0,onChange:p,gap:y=16,itemsPerView:w=1,centerMode:g=!1,className:f,...h},b)=>{const[x,N]=c.useState(0),[R,I]=c.useState(t),[j,E]=c.useState(!1),k=c.useRef(void 0),L=c.useRef(null),$=c.Children.toArray(e),W=$.length,D=u==="horizontal",z=Math.max(0,W-(typeof w=="number"?w:1));c.useEffect(()=>(R&&!j&&W>1&&(k.current=setInterval(()=>{M()},s)),()=>{k.current&&clearInterval(k.current)}),[R,j,x,s,W]);const O=c.useCallback(()=>{N(G=>{let P=G-1;return P<0&&(P=n?z:0),p?.(P),P})},[n,z,p]),M=c.useCallback(()=>{N(G=>{let P=G+1;return P>z&&(P=n?0:z),p?.(P),P})},[n,z,p]),K=(G,{offset:P,velocity:B})=>{const _=Xc(D?P.x:P.y,D?B.x:B.y);_<-1e4?M():_>jn&&O()},U=G=>{N(G),p?.(G)},ee=()=>{I(!R)},Y=()=>{switch(l){case"fade":return{enter:{opacity:0},center:{opacity:1},exit:{opacity:0}};case"scale":return{enter:{opacity:0,scale:.8},center:{opacity:1,scale:1},exit:{opacity:0,scale:1.2}};case"flip":return{enter:{rotateY:90,opacity:0},center:{rotateY:0,opacity:1},exit:{rotateY:-90,opacity:0}};default:return{enter:G=>({[D?"x":"y"]:G>0?1e3:-1e3,opacity:0}),center:{[D?"x":"y"]:0,opacity:1},exit:G=>({[D?"x":"y"]:G<0?1e3:-1e3,opacity:0})}}},se=typeof w=="number"?`calc((100% - ${y*(w-1)}px) / ${w})`:"auto";return r.jsxs("div",{ref:b,className:S("relative overflow-hidden rounded-xl",d&&"backdrop-blur-md bg-white/5 border border-white/20",f),onMouseEnter:()=>o&&E(!0),onMouseLeave:()=>o&&E(!1),...h,children:[r.jsx("div",{ref:L,className:S("relative w-full h-full",D?"flex":"flex-col"),children:l==="slide"&&w!=="auto"?r.jsx(v.motion.div,{className:S("flex",D?"flex-row":"flex-col",g&&"items-center"),style:{gap:`${y}px`,[D?"x":"y"]:`calc(-${x} * (${se} + ${y}px))`},animate:{[D?"x":"y"]:`calc(-${x} * (${se} + ${y}px))`},transition:{type:"spring",stiffness:300,damping:30},drag:D?"x":"y",dragConstraints:{left:0,right:0,top:0,bottom:0},dragElastic:1,onDragEnd:K,children:$.map((G,P)=>r.jsx("div",{className:S("flex-shrink-0",g&&P!==x&&"scale-95 opacity-60"),style:{width:se},children:G},P))}):r.jsx(v.AnimatePresence,{initial:!1,custom:x,children:r.jsx(v.motion.div,{custom:x,variants:Y(),initial:"enter",animate:"center",exit:"exit",transition:{opacity:{duration:.5},...D?{x:{type:"spring",stiffness:300,damping:30}}:{y:{type:"spring",stiffness:300,damping:30}}},drag:D?"x":"y",dragConstraints:{left:0,right:0,top:0,bottom:0},dragElastic:1,onDragEnd:K,className:"absolute inset-0",style:{perspective:l==="flip"?1e3:void 0},children:$[x]},x)})}),i&&W>1&&r.jsxs(r.Fragment,{children:[r.jsx(le,{variant:"ghost",size:"sm",className:S("absolute z-10",d&&"backdrop-blur-md bg-white/10",D?"left-4 top-1/2 -translate-y-1/2":"top-4 left-1/2 -translate-x-1/2 rotate-90"),onClick:O,disabled:!n&&x===0,children:r.jsx(F.ChevronLeft,{className:"w-5 h-5"})}),r.jsx(le,{variant:"ghost",size:"sm",className:S("absolute z-10",d&&"backdrop-blur-md bg-white/10",D?"right-4 top-1/2 -translate-y-1/2":"bottom-4 left-1/2 -translate-x-1/2 rotate-90"),onClick:M,disabled:!n&&x===z,children:r.jsx(F.ChevronRight,{className:"w-5 h-5"})})]}),a&&W>1&&r.jsx("div",{className:S("absolute z-10 flex gap-2",D?"bottom-4 left-1/2 -translate-x-1/2":"right-4 top-1/2 -translate-y-1/2 flex-col"),children:Array.from({length:W}).map((G,P)=>r.jsx(v.motion.button,{whileHover:{scale:1.2},whileTap:{scale:.9},className:S("rounded-full transition-all",P===x?"bg-white w-8 h-2":"bg-white/40 w-2 h-2 hover:bg-white/60"),onClick:()=>U(P)},P))}),t&&i&&r.jsx(le,{variant:"ghost",size:"sm",className:S("absolute top-4 right-4 z-10",d&&"backdrop-blur-md bg-white/10"),onClick:ee,children:R?r.jsx(F.Pause,{className:"w-4 h-4"}):r.jsx(F.Play,{className:"w-4 h-4"})})]})});lr.displayName="LuminarCarousel";const cr=c.forwardRef(({className:e,children:t,...s},n)=>r.jsx("div",{ref:n,className:S("w-full h-full",e),...s,children:t}));cr.displayName="LuminarCarouselItem";const ur=c.forwardRef(({value:e,onIncrement:t,onDecrement:s,min:n=-1/0,max:a=1/0,step:i=1,showControls:o=!1,animated:l=!1,duration:u=1,decimals:d=0,prefix:m="",suffix:p="",separator:y=",",label:w,size:g="md",variant:f="default",glass:h=!1,className:b,...x},N)=>{const[R,I]=c.useState(!1);c.useEffect(()=>{I(!0)},[]);const j=v.useSpring(0,{duration:u*1e3,bounce:.25}),E=v.useTransform(j,O=>k(O,d,y));c.useEffect(()=>{R&&l&&j.set(e)},[j,e,R,l]);function k(O,M,K){const U=O.toFixed(M),[ee,Y]=U.split("."),se=ee.replace(/\B(?=(\d{3})+(?!\d))/g,K);return Y?`${se}.${Y}`:se}const L=()=>{t&&e<a&&t()},$=()=>{s&&e>n&&s()},W=()=>{switch(g){case"sm":return"text-sm p-2 gap-1";case"lg":return"text-lg p-6 gap-3";default:return"text-base p-4 gap-2"}},D=()=>{switch(f){case"primary":return"border-blue-500 bg-blue-50 text-blue-900 dark:bg-blue-900/20 dark:text-blue-100";case"secondary":return"border-gray-500 bg-gray-50 text-gray-900 dark:bg-gray-900/20 dark:text-gray-100";case"success":return"border-green-500 bg-green-50 text-green-900 dark:bg-green-900/20 dark:text-green-100";case"warning":return"border-yellow-500 bg-yellow-50 text-yellow-900 dark:bg-yellow-900/20 dark:text-yellow-100";case"error":return"border-red-500 bg-red-50 text-red-900 dark:bg-red-900/20 dark:text-red-100";default:return"border-gray-200 bg-white text-gray-900 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"}},V=()=>{switch(g){case"sm":return"text-lg";case"lg":return"text-3xl";default:return"text-2xl"}},z=S("flex items-center justify-center rounded-lg border-2 shadow-sm transition-all duration-200","hover:border-blue-300 hover:shadow-md","dark:hover:border-blue-600",W(),D(),h&&"backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border-white/20 dark:border-gray-700/30");return R?r.jsxs(v.motion.div,{ref:N,className:S("flex flex-col items-center",b),initial:{scale:.5,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.5},...x,children:[w&&r.jsx(v.motion.label,{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:w}),r.jsxs("div",{className:z,children:[o&&r.jsx(v.motion.button,{onClick:$,disabled:e<=n,className:"p-1 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors dark:bg-gray-700 dark:hover:bg-gray-600",whileHover:{scale:1.1},whileTap:{scale:.9},children:r.jsx(F.Minus,{className:"w-4 h-4"})}),r.jsxs(v.motion.span,{className:S("font-mono font-bold",V()),children:[r.jsx("span",{children:m}),l?r.jsx(v.motion.span,{children:E}):r.jsx("span",{children:k(e,d,y)}),r.jsx("span",{children:p})]}),o&&r.jsx(v.motion.button,{onClick:L,disabled:e>=a,className:"p-1 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors dark:bg-gray-700 dark:hover:bg-gray-600",whileHover:{scale:1.1},whileTap:{scale:.9},children:r.jsx(F.Plus,{className:"w-4 h-4"})})]})]}):r.jsxs("div",{ref:N,className:S("flex flex-col items-center",b),...x,children:[w&&r.jsx("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:w}),r.jsxs("div",{className:z,children:[o&&r.jsx("button",{onClick:$,disabled:e<=n,className:"p-1 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors dark:bg-gray-700 dark:hover:bg-gray-600",children:r.jsx(F.Minus,{className:"w-4 h-4"})}),r.jsxs("span",{className:S("font-mono font-bold",V()),children:[m,k(e,d,y),p]}),o&&r.jsx("button",{onClick:L,disabled:e>=a,className:"p-1 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors dark:bg-gray-700 dark:hover:bg-gray-600",children:r.jsx(F.Plus,{className:"w-4 h-4"})})]})]})});ur.displayName="LuminarCounter";const Le=c.forwardRef(({className:e,id:t,style:s,"data-testid":n,"aria-label":a,"aria-describedby":i,value:o,defaultValue:l,onChange:u,onBlur:d,onFocus:m,name:p,required:y=defaultFormProps.required,placeholder:w,label:g,helperText:f,error:h,showError:b=defaultFormProps.showError,variant:x="input",size:N=defaultFormProps.size,glass:R=defaultFormProps.glass,glassIntensity:I=defaultFormProps.glassIntensity,glassDepth:j,glassConfig:E,animation:k=defaultFormProps.animation,disableAnimation:L=defaultFormProps.disableAnimation,animationDuration:$,animationDelay:W,motionProps:D,disabled:V=!1,interactive:z=defaultFormProps.interactive,icon:O,iconPosition:M="left",iconSize:K,iconAnimation:U,type:ee="text",autoComplete:Y,pattern:se,minLength:G,maxLength:P,showFocusRing:B=!0,enablePerformanceMonitoring:_=!1,...te},H)=>{const[Z,ue]=c.useState(!1),J=K||sizeToPixels[N]*.6,oe=R||x==="glass"?Ke({element:x==="input"?"input":"card",profile:I==="subtle"?"soft":I==="strong"?"hard":"standard",interactive:z,frost:!0,glow:!1,...E}):"",pe=er[N],q=L||k==="none"?{}:{...He[k]||He.fadeIn,transition:{...ze.default,duration:$||ze.default.duration,delay:W||0}},ce=Ne=>{ue(!0),m?.(Ne)},fe=Ne=>{ue(!1),d?.(Ne)};return r.jsxs("div",{className:"w-full",children:[g&&r.jsxs(v.motion.label,{className:"block text-sm font-medium mb-2",...q,transition:{duration:.15,ease:"easeInOut"},children:[g,y&&r.jsx("span",{className:"text-destructive ml-1",children:"*"})]}),r.jsxs(v.motion.div,{className:S("relative rounded-lg overflow-hidden transition-all duration-300",R&&oe,Z&&B?"border-primary shadow-[0_0_0_2px_rgba(var(--primary),0.2)]":h&&b?"border-destructive":R?"":"border-white/20 dark:border-gray-700/30",Cc(Z),V&&"opacity-50 cursor-not-allowed",e),animate:{scale:Z?1.01:1},transition:{duration:.15,ease:"easeInOut"},style:s,...D,children:[O&&M==="left"&&r.jsx("div",{className:"absolute left-3 top-1/2 -translate-y-1/2",children:r.jsx(Qe,{icon:O,size:J,animation:Z&&U?U:Z?"pulse":"none",trigger:"none",className:"text-muted-foreground"})}),r.jsx("input",{ref:H,id:t,name:p,type:ee,value:o,defaultValue:l,onChange:u,onFocus:ce,onBlur:fe,placeholder:w,disabled:V,required:y,autoComplete:Y,pattern:se,minLength:G,maxLength:P,className:S("w-full bg-transparent outline-none placeholder:text-muted-foreground",pe.padding,pe.text,O&&M==="left"&&"pl-12",O&&M==="right"&&"pr-12"),"data-testid":n,"aria-label":a,"aria-describedby":i,...te}),O&&M==="right"&&r.jsx("div",{className:"absolute right-3 top-1/2 -translate-y-1/2",children:r.jsx(Qe,{icon:O,size:J,animation:Z&&U?U:Z?"pulse":"none",trigger:"none",className:"text-muted-foreground"})})]}),f&&!h&&r.jsx(v.motion.p,{className:"mt-2 text-sm text-muted-foreground",...q,transition:{duration:.15,ease:"easeInOut"},children:f}),h&&b&&r.jsx(v.motion.p,{className:"mt-2 text-sm text-destructive",...q,transition:{duration:.15,ease:"easeInOut"},children:h})]})});Le.displayName="LuminarInput";const St=c.forwardRef(({label:e,options:t,placeholder:s="Select an option",glass:n=!0,error:a,value:i,onChange:o,className:l,...u},d)=>{const[m,p]=c.useState(!1),[y,w]=c.useState(i||""),g=c.useRef(null);if(!t||!Array.isArray(t))return console.error("LuminarSelect: options prop is required and must be an array",{options:t}),null;const f=t.find(b=>b.value===y);c.useEffect(()=>{const b=x=>{g.current&&!g.current.contains(x.target)&&p(!1)};return document.addEventListener("mousedown",b),()=>document.removeEventListener("mousedown",b)},[]);const h=b=>{if(b.disabled)return;w(b.value),p(!1);const x=d;if(x?.current){const N=new Event("change",{bubbles:!0});x.current.value=b.value,x.current.dispatchEvent(N)}if(o){const N={target:{value:b.value},currentTarget:{value:b.value}};o(N)}};return r.jsxs("div",{className:"w-full",children:[e&&r.jsx(v.motion.label,{className:"block text-sm font-medium mb-2",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:e}),r.jsxs("div",{className:"relative",ref:g,children:[r.jsx(v.motion.div,{className:S("relative rounded-lg cursor-pointer",n&&"backdrop-blur-md bg-white/10 dark:bg-gray-900/10","border transition-all duration-300",m?"border-primary shadow-[0_0_0_2px_rgba(var(--primary),0.2)]":a?"border-destructive":"border-white/20 dark:border-gray-700/30",l),onClick:()=>p(!m),whileHover:{scale:1.01},whileTap:{scale:.99},children:r.jsxs("div",{className:"flex items-center justify-between px-4 py-3",children:[r.jsx("span",{className:S(!f&&"text-muted-foreground"),children:f?f.label:s}),r.jsx(v.motion.div,{animate:{rotate:m?180:0},transition:{duration:.2},children:r.jsx(F.ChevronDown,{className:"w-4 h-4 text-muted-foreground"})})]})}),r.jsx(v.AnimatePresence,{children:m&&r.jsx(v.motion.div,{className:S("absolute z-50 w-full mt-2 rounded-lg overflow-hidden",n&&"backdrop-blur-xl bg-white/90 dark:bg-gray-900/90","border border-white/20 dark:border-gray-700/30","shadow-xl"),initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.2},children:r.jsx("div",{className:"max-h-60 overflow-y-auto",children:t.map(b=>r.jsx(v.motion.div,{className:S("px-4 py-3 cursor-pointer transition-colors","hover:bg-white/10 dark:hover:bg-gray-700/20",b.disabled&&"opacity-50 cursor-not-allowed",y===b.value&&"bg-primary/20 text-primary"),onClick:()=>h(b),whileHover:{x:4},transition:{duration:.1},children:b.label},b.value))})})}),r.jsxs("select",{ref:d,value:y,onChange:()=>{},className:"sr-only",...u,children:[r.jsx("option",{value:"",children:s}),t.map(b=>r.jsx("option",{value:b.value,disabled:b.disabled,children:b.label},b.value))]})]}),a&&r.jsx(v.motion.p,{className:"mt-2 text-sm text-destructive",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:a})]})});St.displayName="LuminarSelect";const De=c.forwardRef(({label:e,glass:t=!0,className:s,checked:n,onChange:a,...i},o)=>{const[l,u]=c.useState(!1),d=n!==void 0?n:l,m=p=>{n===void 0&&u(p.target.checked),a?.(p)};return r.jsxs("label",{className:S("flex items-center gap-3 cursor-pointer",s),children:[r.jsxs(v.motion.div,{className:S("relative w-5 h-5 rounded",t&&"backdrop-blur-md bg-white/10 dark:bg-gray-900/10","border transition-all duration-300",d?"border-primary bg-primary/20":"border-white/20 dark:border-gray-700/30"),whileHover:{scale:1.1},whileTap:{scale:.9},children:[r.jsx("input",{ref:o,type:"checkbox",className:"sr-only",checked:d,onChange:m,...i}),r.jsx(v.AnimatePresence,{children:d&&r.jsx(v.motion.div,{className:"absolute inset-0 flex items-center justify-center",initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},transition:{type:"spring",stiffness:500,damping:30},children:r.jsx(F.Check,{className:"w-3 h-3 text-primary",strokeWidth:3})})})]}),e&&r.jsx(v.motion.span,{className:"select-none",animate:{opacity:d?1:.8},children:e})]})});De.displayName="LuminarCheckbox";Edit2,Save,X,Plus,Trash2,Copy,Check,ChevronUp,ChevronDown,Filter,Download;const Qs=c.memo(({value:e,column:t,onChange:s,onSave:n,onCancel:a})=>{const i=c.useRef(null);c.useEffect(()=>{i.current?.focus(),i.current?.select()},[]);const o=l=>{l.key==="Enter"?n():l.key==="Escape"&&a()};if(t.editor)return r.jsx(r.Fragment,{children:t.editor({value:e,row:{},column:t,onChange:s,onCancel:a,onSave:n})});switch(t.type){case"number":return r.jsx(Le,{ref:i,type:"number",value:e,onChange:l=>s(parseFloat(l.target.value)||0),onKeyDown:o,size:"sm",className:"w-full"});case"select":return r.jsx(St,{value:e,onChange:l=>s(l.target.value),options:t.selectOptions||[],className:"w-full"});case"boolean":return r.jsx(De,{checked:e,onChange:s});case"date":return r.jsx(Le,{ref:i,type:"date",value:e,onChange:l=>s(l.target.value),onKeyDown:o,size:"sm",className:"w-full"});default:return r.jsx(Le,{ref:i,value:e,onChange:l=>s(l.target.value),onKeyDown:o,size:"sm",className:"w-full"})}});Qs.displayName="CellEditor";function Zc({data:e,columns:t,onCellEdit:s,onRowAdd:n,onRowDelete:a,onRowsChange:i,rowKey:o,defaultRowData:l={},enableAddRow:u=!0,enableDeleteRow:d=!0,enableCopyRow:m=!0,glass:p=!0,animated:y=!0,striped:w=!1,hoverable:g=!0,height:f="auto",virtualized:h=!1,loading:b=!1,emptyMessage:x="No data available",className:N,...R},I){const[j,E]=c.useState(null),[k,L]=c.useState(null),[$,W]=c.useState(null),[D,V]=c.useState({}),[z,O]=c.useState(new Set);c.useRef(null);const M=c.useRef(null),K=c.useCallback((J,oe)=>typeof o=="function"?o(J):o?J[o]:oe,[o]),U=c.useCallback((J,oe)=>{if(!t.find(ce=>ce.key===oe)?.editable)return;const q=e[J][oe];E({rowIndex:J,columnKey:oe,value:q})},[e,t]),ee=c.useCallback(J=>{j&&E({...j,value:J})},[j]),Y=c.useCallback(()=>{if(!j)return;const J=t.find(oe=>oe.key===j.columnKey);if(!(J?.validator&&J.validator(j.value,e[j.rowIndex])!==!0)){if(s?.(j.rowIndex,j.columnKey,j.value),i){const oe=[...e];oe[j.rowIndex]={...oe[j.rowIndex],[j.columnKey]:j.value},i(oe)}E(null)}},[j,e,t,s,i]),se=c.useCallback(()=>{E(null)},[]),G=c.useCallback(()=>{L({...l})},[l]),P=c.useCallback(()=>{k&&(n?.(k),i&&i([...e,k]),L(null))},[k,e,n,i]),B=c.useCallback(J=>{if(a?.(J),i){const oe=e.filter((pe,q)=>q!==J);i(oe)}},[e,a,i]),_=c.useCallback(J=>{const oe={...e[J]};n&&n(oe),i&&i([...e,oe])},[e,n,i]),te=c.useCallback(J=>{t.find(pe=>pe.key===J)?.sortable&&W(pe=>pe?.key===J?pe.direction==="asc"?{key:J,direction:"desc"}:null:{key:J,direction:"asc"})},[t]),H=c.useMemo(()=>$?[...e].sort((J,oe)=>{const pe=J[$.key],q=oe[$.key];return pe==null?1:q==null?-1:pe<q?$.direction==="asc"?-1:1:pe>q?$.direction==="asc"?1:-1:0}):e,[e,$]),Z=c.useCallback((J,oe)=>{if(J.preventDefault(),!t.find(ke=>ke.key===oe)?.resizable)return;const q=J.clientX,ce=D[oe]||150;M.current={columnKey:oe,startX:q,startWidth:ce};const fe=ke=>{if(!M.current)return;const It=ke.clientX-M.current.startX,Wa=Math.max(50,M.current.startWidth+It);V(Ya=>({...Ya,[M.current.columnKey]:Wa}))},Ne=()=>{M.current=null,document.removeEventListener("mousemove",fe),document.removeEventListener("mouseup",Ne)};document.addEventListener("mousemove",fe),document.addEventListener("mouseup",Ne)},[t,D]),ue=(J,oe,pe)=>{const q=j?.rowIndex===oe&&j?.columnKey===pe.key,ce=J[pe.key];return q?r.jsx(Qs,{value:j.value,row:J,column:pe,onChange:ee,onSave:Y,onCancel:se}):pe.formatter?pe.formatter(ce,J):pe.type==="boolean"?r.jsx(Check,{className:S("w-4 h-4",ce?"text-green-400":"text-gray-600")}):r.jsx("span",{children:ce?.toString()||""})};return r.jsxs("div",{ref:I,className:S("relative rounded-xl overflow-hidden",p&&"backdrop-blur-md bg-white/5 border border-white/20",N),style:{height:f},...R,children:[(u||d)&&r.jsxs("div",{className:"flex items-center justify-between p-3 border-b border-white/10",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[u&&r.jsxs(le,{variant:"ghost",size:"sm",onClick:G,disabled:k!==null,children:[r.jsx(Plus,{className:"w-4 h-4 mr-1"}),"Add Row"]}),d&&z.size>0&&r.jsxs(le,{variant:"ghost",size:"sm",onClick:()=>{z.forEach(J=>B(J)),O(new Set)},children:[r.jsx(Trash2,{className:"w-4 h-4 mr-1"}),"Delete (",z.size,")"]})]}),r.jsx(le,{variant:"ghost",size:"sm",children:r.jsx(Download,{className:"w-4 h-4"})})]}),r.jsx("div",{className:"overflow-auto",style:{maxHeight:f==="auto"?void 0:f},children:r.jsxs("table",{className:"w-full",children:[r.jsx("thead",{className:S("border-b border-white/10",p&&"backdrop-blur-sm bg-white/5"),children:r.jsxs("tr",{children:[(d||m)&&r.jsx("th",{className:"p-3 w-12",children:r.jsx(De,{checked:z.size===H.length&&H.length>0,onChange:J=>{O(J?new Set(H.map((oe,pe)=>pe)):new Set)}})}),t.map(J=>r.jsxs("th",{className:S("p-3 text-left font-medium relative",J.sortable&&"cursor-pointer hover:bg-white/5",J.align==="center"&&"text-center",J.align==="right"&&"text-right"),style:{width:D[J.key]||J.width,minWidth:J.minWidth,maxWidth:J.maxWidth},onClick:()=>J.sortable&&te(J.key),children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("span",{children:J.header}),J.sortable&&r.jsx("div",{className:"ml-2",children:$?.key===J.key&&($.direction==="asc"?r.jsx(ChevronUp,{className:"w-4 h-4"}):r.jsx(ChevronDown,{className:"w-4 h-4"}))})]}),J.resizable&&r.jsx("div",{className:"absolute right-0 top-0 bottom-0 w-1 cursor-col-resize hover:bg-blue-400",onMouseDown:oe=>Z(oe,J.key)})]},J.key)),(d||m)&&r.jsx("th",{className:"p-3 w-24",children:"Actions"})]})}),r.jsx("tbody",{children:b?r.jsx("tr",{children:r.jsx("td",{colSpan:t.length+2,className:"p-8 text-center",children:r.jsx("div",{className:"flex items-center justify-center",children:r.jsx(v.motion.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},className:"w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full"})})})}):H.length===0&&!k?r.jsx("tr",{children:r.jsx("td",{colSpan:t.length+2,className:"p-8 text-center text-gray-500",children:x})}):r.jsxs(r.Fragment,{children:[k&&r.jsxs(v.motion.tr,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"bg-blue-500/10 border-b border-white/10",children:[(d||m)&&r.jsx("td",{}),t.map(J=>r.jsx("td",{className:"p-2",children:r.jsx(Qs,{value:k[J.key]||"",row:k,column:J,onChange:oe=>L({...k,[J.key]:oe}),onSave:P,onCancel:()=>L(null)})},J.key)),r.jsx("td",{className:"p-2",children:r.jsxs("div",{className:"flex items-center gap-1",children:[r.jsx(le,{variant:"ghost",size:"sm",onClick:P,children:r.jsx(Save,{className:"w-4 h-4"})}),r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>L(null),children:r.jsx(X,{className:"w-4 h-4"})})]})})]}),H.map((J,oe)=>{const pe=z.has(oe);return r.jsxs(v.motion.tr,{initial:y?{opacity:0}:{},animate:y?{opacity:1}:{},transition:{delay:oe*.05},className:S("border-b border-white/5 transition-colors",w&&oe%2===1&&"bg-white/[0.02]",g&&"hover:bg-white/5",pe&&"bg-blue-500/10"),children:[(d||m)&&r.jsx("td",{className:"p-3",children:r.jsx(De,{checked:pe,onChange:q=>{const ce=new Set(z);q?ce.add(oe):ce.delete(oe),O(ce)}})}),t.map(q=>r.jsx("td",{className:S("p-3",q.align==="center"&&"text-center",q.align==="right"&&"text-right",q.editable&&"cursor-pointer hover:bg-white/5"),onDoubleClick:()=>U(oe,q.key),children:ue(J,oe,q)},q.key)),(d||m)&&r.jsx("td",{className:"p-3",children:r.jsxs("div",{className:"flex items-center gap-1",children:[m&&r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>_(oe),children:r.jsx(Copy,{className:"w-3 h-3"})}),d&&r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>B(oe),children:r.jsx(Trash2,{className:"w-3 h-3"})})]})})]},K(J,oe))})]})})]})})]})}const Sa=c.forwardRef(Zc);ChevronUp,ChevronDown,ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Search,Filter,Download,Settings,Eye,EyeOff;function eu({data:e,columns:t,pageSize:s=10,pageSizeOptions:n=[10,25,50,100],searchable:a=!0,searchPlaceholder:i="Search...",glass:o=!0,animated:l=!0,striped:u=!1,hoverable:d=!0,selectable:m=!1,onRowClick:p,onSelectionChange:y,loading:w=!1,emptyMessage:g="No data available",exportable:f=!1,onExport:h,showColumnToggle:b=!0,stickyHeader:x=!1,className:N,...R},I){const[j,E]=c.useState(1),[k,L]=c.useState(s),[$,W]=c.useState(""),[D,V]=c.useState({key:"",direction:null}),[z,O]=c.useState({}),[M,K]=c.useState(new Set),[U,ee]=c.useState(t),[Y,se]=c.useState(!1),G=c.useMemo(()=>{let q=[...e];return $&&(q=q.filter(ce=>Object.values(ce).some(fe=>String(fe).toLowerCase().includes($.toLowerCase())))),Object.entries(z).forEach(([ce,fe])=>{fe&&(q=q.filter(Ne=>String(Ne[ce]).toLowerCase().includes(fe.toLowerCase())))}),D.key&&D.direction&&q.sort((ce,fe)=>{const Ne=ce[D.key],ke=fe[D.key];return Ne==null?1:ke==null?-1:Ne<ke?D.direction==="asc"?-1:1:Ne>ke?D.direction==="asc"?1:-1:0}),q},[e,$,D,z]),P=Math.ceil(G.length/k),B=c.useMemo(()=>{const q=(j-1)*k;return G.slice(q,q+k)},[G,j,k]);c.useEffect(()=>{E(1)},[$,z,D]),c.useEffect(()=>{if(y){const q=Array.from(M).map(ce=>G[ce]).filter(Boolean);y(q)}},[M,G,y]);const _=q=>{V(ce=>({key:q,direction:ce.key===q?ce.direction==="asc"?"desc":ce.direction==="desc"?null:"asc":"asc"}))},te=q=>{if(q){const ce=new Set;B.forEach((fe,Ne)=>{const ke=(j-1)*k+Ne;ce.add(ke)}),K(ce)}else K(new Set)},H=(q,ce)=>{const fe=(j-1)*k+q,Ne=new Set(M);ce?Ne.add(fe):Ne.delete(fe),K(Ne)},Z=q=>{ee(ce=>ce.map(fe=>fe.key===q?{...fe,hidden:!fe.hidden}:fe))},ue=U.filter(q=>!q.hidden),J=(q,ce)=>{const fe=String(ce);return fe.includes(".")?fe.split(".").reduce((Ne,ke)=>Ne?.[ke],q):q[ce]},oe=q=>q.sortable?r.jsxs("span",{className:"ml-1 inline-flex flex-col",children:[r.jsx(ChevronUp,{className:S("w-3 h-3 -mb-1",D.key===q.key&&D.direction==="asc"?"text-blue-400":"text-gray-500")}),r.jsx(ChevronDown,{className:S("w-3 h-3",D.key===q.key&&D.direction==="desc"?"text-blue-400":"text-gray-500")})]}):null,pe=B.length>0&&B.every((q,ce)=>{const fe=(j-1)*k+ce;return M.has(fe)});return r.jsxs("div",{ref:I,className:S("space-y-4",N),...R,children:[r.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[r.jsxs("div",{className:"flex items-center gap-2 flex-1",children:[a&&r.jsx(Le,{placeholder:i,value:$,onChange:q=>W(q.target.value),icon:Search,glass:o,className:"max-w-xs"}),U.some(q=>q.filterable)&&r.jsxs(le,{variant:o?"glass":"ghost",size:"sm",onClick:()=>se(!Y),children:[r.jsx(Filter,{className:"w-4 h-4 mr-2"}),"Filters"]})]}),r.jsxs("div",{className:"flex items-center gap-2",children:[b&&r.jsx(kt,{trigger:r.jsxs(le,{variant:o?"glass":"ghost",size:"sm",children:[r.jsx(Settings,{className:"w-4 h-4 mr-2"}),"Columns"]}),items:U.map(q=>({id:q.key,label:q.header,icon:q.hidden?EyeOff:Eye,onClick:()=>Z(q.key)}))}),f&&r.jsxs(le,{variant:o?"glass":"ghost",size:"sm",onClick:h,children:[r.jsx(Download,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),r.jsx(v.AnimatePresence,{children:Y&&U.some(q=>q.filterable)&&r.jsx(v.motion.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},className:"overflow-hidden",children:r.jsx("div",{className:S("p-4 rounded-lg grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",o&&"backdrop-blur-sm bg-white/5 border border-white/20"),children:U.filter(q=>q.filterable).map(q=>r.jsx(Le,{placeholder:`Filter ${q.header}`,value:z[q.key]||"",onChange:ce=>O(fe=>({...fe,[q.key]:ce.target.value})),glass:o},q.key))})})}),r.jsx("div",{className:S("overflow-x-auto rounded-xl",o&&"backdrop-blur-sm bg-white/5 border border-white/20"),children:r.jsxs("table",{className:"w-full",children:[r.jsx("thead",{className:S("border-b border-white/10",x&&"sticky top-0 z-10",o&&x&&"backdrop-blur-sm bg-white/5"),children:r.jsxs("tr",{children:[m&&r.jsx("th",{className:"p-4 text-left",children:r.jsx(De,{checked:pe,onChange:q=>te(q.target.checked),glass:o})}),ue.map(q=>r.jsx("th",{className:S("p-4 text-left font-medium",q.sortable&&"cursor-pointer hover:bg-white/5",q.align==="center"&&"text-center",q.align==="right"&&"text-right"),style:{width:q.width},onClick:()=>q.sortable&&_(q.key),children:r.jsxs("div",{className:"flex items-center",children:[q.header,oe(q)]})},q.key))]})}),r.jsx("tbody",{children:w?r.jsx("tr",{children:r.jsx("td",{colSpan:ue.length+(m?1:0),className:"p-8 text-center",children:r.jsx("div",{className:"flex items-center justify-center",children:r.jsx(v.motion.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},className:"w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full"})})})}):B.length===0?r.jsx("tr",{children:r.jsx("td",{colSpan:ue.length+(m?1:0),className:"p-8 text-center text-gray-500",children:g})}):B.map((q,ce)=>{const fe=(j-1)*k+ce,Ne=M.has(fe);return r.jsxs(v.motion.tr,{initial:l?{opacity:0,y:20}:{},animate:l?{opacity:1,y:0}:{},transition:{delay:ce*.05},className:S("border-b border-white/5 transition-colors",u&&ce%2===1&&"bg-white/[0.02]",d&&"hover:bg-white/5",p&&"cursor-pointer",Ne&&"bg-blue-500/10"),onClick:()=>p?.(q),children:[m&&r.jsx("td",{className:"p-4",children:r.jsx(De,{checked:Ne,onChange:ke=>H(ce,ke.target.checked),onClick:ke=>ke.stopPropagation(),glass:o})}),ue.map(ke=>r.jsx("td",{className:S("p-4",ke.align==="center"&&"text-center",ke.align==="right"&&"text-right"),children:ke.render?ke.render(J(q,ke.key),q):J(q,ke.key)},ke.key))]},ce)})})]})}),G.length>0&&r.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-4",children:[r.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[r.jsx("span",{children:"Showing"}),r.jsx("select",{value:k,onChange:q=>{L(Number(q.target.value)),E(1)},className:S("px-2 py-1 rounded-lg bg-transparent border",o&&"border-white/20",!o&&"border-gray-700"),children:n.map(q=>r.jsx("option",{value:q,className:"bg-gray-900",children:q},q))}),r.jsxs("span",{children:["of ",G.length," results"]})]}),r.jsxs("div",{className:"flex items-center gap-1",children:[r.jsx(le,{variant:o?"glass":"ghost",size:"sm",onClick:()=>E(1),disabled:j===1,children:r.jsx(ChevronsLeft,{className:"w-4 h-4"})}),r.jsx(le,{variant:o?"glass":"ghost",size:"sm",onClick:()=>E(q=>Math.max(1,q-1)),disabled:j===1,children:r.jsx(ChevronLeft,{className:"w-4 h-4"})}),r.jsx("div",{className:"flex items-center gap-1 mx-2",children:Array.from({length:Math.min(5,P)},(q,ce)=>{let fe;return P<=5||j<=3?fe=ce+1:j>=P-2?fe=P-4+ce:fe=j-2+ce,r.jsx(le,{variant:j===fe?"default":o?"glass":"ghost",size:"sm",onClick:()=>E(fe),children:fe},fe)})}),r.jsx(le,{variant:o?"glass":"ghost",size:"sm",onClick:()=>E(q=>Math.min(P,q+1)),disabled:j===P,children:r.jsx(ChevronRight,{className:"w-4 h-4"})}),r.jsx(le,{variant:o?"glass":"ghost",size:"sm",onClick:()=>E(P),disabled:j===P,children:r.jsx(ChevronsRight,{className:"w-4 h-4"})})]})]})]})}const Ta=c.forwardRef(eu);function Ra({title:e,value:t,unit:s,change:n,status:a="neutral",chart:i,className:o=""}){const l=ct("card","md",a),u=p=>{switch(p){case"success":return"text-green-600 dark:text-green-400";case"warning":return"text-yellow-600 dark:text-yellow-400";case"error":return"text-red-600 dark:text-red-400";case"info":return"text-blue-600 dark:text-blue-400";default:return"text-foreground"}},d=p=>{switch(p){case"positive":return"text-green-600 dark:text-green-400";case"negative":return"text-red-600 dark:text-red-400";default:return"text-gray-600 dark:text-gray-400"}},m=p=>{switch(p){case"positive":return"↗";case"negative":return"↘";default:return"→"}};return r.jsxs("div",{className:`${l} p-6 rounded-lg border relative overflow-hidden ${o}`,children:[i&&r.jsx("div",{className:"absolute inset-0 opacity-10",children:i}),r.jsxs("div",{className:"relative z-10",children:[r.jsxs("div",{className:"mb-4",children:[r.jsx("h3",{className:"text-sm font-medium text-muted-foreground mb-1",children:e}),r.jsxs("div",{className:"flex items-baseline gap-1",children:[r.jsx("span",{className:`text-3xl font-bold ${u(a)}`,children:t}),s&&r.jsx("span",{className:"text-sm text-muted-foreground",children:s})]})]}),n&&r.jsxs("div",{className:`flex items-center gap-1 text-sm ${d(n.trend)}`,children:[r.jsx("span",{children:m(n.trend)}),r.jsxs("span",{children:[Math.abs(n.value),"%"]}),r.jsx("span",{className:"text-muted-foreground",children:n.period})]})]})]})}const dr=c.forwardRef(({value:e,max:t=100,size:s=120,strokeWidth:n=8,thickness:a,showValue:i=!0,showLabel:o=!0,label:l,color:u="rgb(var(--primary))",backgroundColor:d="rgba(255, 255, 255, 0.1)",glass:m=!0,animated:p=!0,gradient:y,className:w,...g},f)=>{const b=typeof s=="string"?{sm:60,md:80,lg:120,xl:160}[s]||120:s,x=a||n,N=Math.min(Math.max(e/t*100,0),100),R=(b-x)/2,I=2*Math.PI*R,j=I,E=I-N/100*I;return r.jsxs("div",{ref:f,className:S("relative inline-flex items-center justify-center",m&&"rounded-full backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30 p-4",w),style:{width:b+(m?32:0),height:b+(m?32:0)},...g,children:[r.jsxs("svg",{width:b,height:b,className:"transform -rotate-90",style:{filter:m?"drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))":void 0},children:[r.jsxs("defs",{children:[r.jsxs("linearGradient",{id:"progress-gradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[r.jsx("stop",{offset:"0%",stopColor:u,stopOpacity:.8}),r.jsx("stop",{offset:"100%",stopColor:u,stopOpacity:1})]}),r.jsxs("filter",{id:"glow",children:[r.jsx("feGaussianBlur",{stdDeviation:"3",result:"coloredBlur"}),r.jsxs("feMerge",{children:[r.jsx("feMergeNode",{in:"coloredBlur"}),r.jsx("feMergeNode",{in:"SourceGraphic"})]})]})]}),r.jsx("circle",{cx:b/2,cy:b/2,r:R,fill:"none",stroke:d,strokeWidth:x,strokeLinecap:"round"}),r.jsx(v.motion.circle,{cx:b/2,cy:b/2,r:R,fill:"none",stroke:"url(#progress-gradient)",strokeWidth:x,strokeLinecap:"round",strokeDasharray:j,filter:"url(#glow)",initial:p?{strokeDashoffset:I}:{strokeDashoffset:E},animate:{strokeDashoffset:E},transition:{duration:p?1.5:0,ease:"easeInOut"}}),r.jsx(v.motion.circle,{cx:b/2,cy:b/2,r:R-x/2,fill:"none",stroke:u,strokeWidth:1,strokeOpacity:.3,strokeDasharray:j,initial:p?{strokeDashoffset:I}:{strokeDashoffset:E},animate:{strokeDashoffset:E},transition:{duration:p?1.5:0,ease:"easeInOut",delay:.2}})]}),r.jsx("div",{className:"absolute inset-0 flex flex-col items-center justify-center",children:i&&r.jsxs(v.motion.div,{className:"text-center",initial:p?{opacity:0,scale:.5}:{},animate:{opacity:1,scale:1},transition:{delay:.5,type:"spring",stiffness:400,damping:30},children:[r.jsxs("span",{className:"text-2xl font-bold",children:[Math.round(N),"%"]}),o&&l&&r.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:l})]})}),N>=100&&r.jsx(v.motion.div,{className:"absolute inset-0 rounded-full",style:{background:`radial-gradient(circle, ${u}20 0%, transparent 70%)`},animate:{scale:[1,1.2,1],opacity:[.5,0,.5]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}})]})});dr.displayName="LuminarProgressRing";const we=c.forwardRef(({variant:e=defaultComponentProps.variant,animation:t="pulse",width:s,height:n,count:a=1,rounded:i=!0,glass:o=!0,className:l,...u},d)=>{const m=S("relative overflow-hidden",o?"backdrop-blur-sm bg-white/5 border border-white/10":"bg-gray-800"),p={default:{borderRadius:i?"0.5rem":"0"},text:{height:n||"1rem",width:s||"100%",borderRadius:"0.25rem"},circular:{aspectRatio:"1",borderRadius:"50%",width:s||"2.5rem"},rectangular:{borderRadius:i?"0.5rem":"0"},glass:{borderRadius:i?"0.5rem":"0"},card:{height:n||"200px",width:s||"100%",borderRadius:"0.75rem"},list:{height:n||"60px",width:s||"100%",borderRadius:"0.5rem"},table:{height:n||"40px",width:s||"100%",borderRadius:"0.25rem"},avatar:{width:s||"3rem",height:n||"3rem",borderRadius:"50%"},button:{height:n||"2.5rem",width:s||"6rem",borderRadius:"0.5rem"},input:{height:n||"2.5rem",width:s||"100%",borderRadius:"0.5rem"},chart:{height:n||"300px",width:s||"100%",borderRadius:"0.75rem"}},y=()=>{switch(t){case"wave":return r.jsx(v.motion.div,{className:"absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/10 to-transparent",animate:{x:["0%","200%"]},transition:{duration:1.5,repeat:1/0,ease:"linear"}});case"shimmer":return r.jsx(v.motion.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12",animate:{x:["-200%","200%"]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}});default:return null}},w=t==="pulse"?{animate:{opacity:[.5,1,.5]},transition:{duration:1.5,repeat:1/0,ease:"easeInOut"}}:{},g=()=>{const f=p[e];return r.jsx(v.motion.div,{className:S(m,l),style:{width:"width"in f?f.width:void 0,height:"height"in f?f.height:void 0,borderRadius:f.borderRadius,aspectRatio:"aspectRatio"in f?f.aspectRatio:void 0},...w,children:y()})};return a>1?r.jsx("div",{ref:d,className:"space-y-2",children:Array.from({length:a}).map((f,h)=>r.jsx("div",{children:g()},h))}):g()});we.displayName="LuminarSkeleton";const tu=c.forwardRef(({className:e,...t},s)=>r.jsxs("div",{ref:s,className:S("rounded-xl p-6 space-y-4","backdrop-blur-sm bg-white/5 border border-white/10",e),...t,children:[r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsx(we,{variant:"avatar"}),r.jsxs("div",{className:"flex-1 space-y-2",children:[r.jsx(we,{variant:"text",width:"60%"}),r.jsx(we,{variant:"text",width:"40%"})]})]}),r.jsx(we,{variant:"rectangular",height:120}),r.jsxs("div",{className:"space-y-2",children:[r.jsx(we,{variant:"text"}),r.jsx(we,{variant:"text"}),r.jsx(we,{variant:"text",width:"80%"})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx(we,{variant:"button"}),r.jsx(we,{variant:"button",width:"4rem"})]})]}));tu.displayName="LuminarSkeletonCard";const su=c.forwardRef(({className:e,items:t=5,...s},n)=>r.jsx("div",{ref:n,className:S("space-y-3",e),...s,children:Array.from({length:t}).map((a,i)=>r.jsxs("div",{className:"flex items-center space-x-4 p-4 rounded-lg backdrop-blur-sm bg-white/5 border border-white/10",children:[r.jsx(we,{variant:"circular",width:"2.5rem"}),r.jsxs("div",{className:"flex-1 space-y-2",children:[r.jsx(we,{variant:"text",width:"70%"}),r.jsx(we,{variant:"text",width:"40%",height:"0.75rem"})]}),r.jsx(we,{variant:"button",width:"5rem",height:"2rem"})]},i))}));su.displayName="LuminarSkeletonList";const ru=c.forwardRef(({className:e,rows:t=5,columns:s=4,...n},a)=>r.jsxs("div",{ref:a,className:S("rounded-xl overflow-hidden backdrop-blur-sm bg-white/5 border border-white/10",e),...n,children:[r.jsx("div",{className:"border-b border-white/10 p-4",children:r.jsx("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${s}, 1fr)`},children:Array.from({length:s}).map((i,o)=>r.jsx(we,{variant:"text",height:"1.25rem"},o))})}),r.jsx("div",{className:"divide-y divide-white/5",children:Array.from({length:t}).map((i,o)=>r.jsx("div",{className:"p-4",children:r.jsx("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${s}, 1fr)`},children:Array.from({length:s}).map((l,u)=>r.jsx(we,{variant:"text",height:"1rem",width:u===0?"80%":"60%"},u))})},o))})]}));ru.displayName="LuminarSkeletonTable";const nu=c.forwardRef(({className:e,...t},s)=>r.jsxs("div",{ref:s,className:S("space-y-6",e),...t,children:[r.jsxs("div",{className:"space-y-2",children:[r.jsx(we,{variant:"text",width:"30%",height:"0.875rem"}),r.jsx(we,{variant:"input"})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsx(we,{variant:"text",width:"25%",height:"0.875rem"}),r.jsx(we,{variant:"input"})]}),r.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[r.jsxs("div",{className:"space-y-2",children:[r.jsx(we,{variant:"text",width:"40%",height:"0.875rem"}),r.jsx(we,{variant:"input"})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsx(we,{variant:"text",width:"35%",height:"0.875rem"}),r.jsx(we,{variant:"input"})]})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsx(we,{variant:"text",width:"20%",height:"0.875rem"}),r.jsx(we,{variant:"rectangular",height:"100px"})]}),r.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[r.jsx(we,{variant:"button"}),r.jsx(we,{variant:"button",width:"8rem"})]})]}));nu.displayName="LuminarSkeletonForm";const au=c.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:S("rounded-xl p-6 backdrop-blur-sm bg-white/5 border border-white/10",e),...t,children:r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"flex justify-between items-center",children:[r.jsx(we,{variant:"text",width:"40%",height:"1.5rem"}),r.jsx(we,{variant:"button",width:"7rem",height:"2rem"})]}),r.jsx(we,{variant:"chart",animation:"shimmer"}),r.jsx("div",{className:"grid grid-cols-3 gap-4",children:Array.from({length:3}).map((n,a)=>r.jsxs("div",{className:"space-y-1",children:[r.jsx(we,{variant:"text",width:"60%",height:"0.75rem"}),r.jsx(we,{variant:"text",width:"80%",height:"1.25rem"})]},a))})]})}));au.displayName="LuminarSkeletonChart";function mr({title:e,value:t,description:s,icon:n,trend:a,className:i=""}){const o=ct("card","md","neutral"),l=d=>{switch(d){case"up":return"text-green-600 dark:text-green-400";case"down":return"text-red-600 dark:text-red-400";case"neutral":return"text-gray-600 dark:text-gray-400"}},u=d=>{switch(d){case"up":return"↗";case"down":return"↘";case"neutral":return"→"}};return r.jsxs("div",{className:`${o} p-6 rounded-lg border ${i}`,children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:e}),n&&r.jsx("div",{className:"text-muted-foreground",children:n})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsx("div",{className:"text-2xl font-bold text-foreground",children:t}),s&&r.jsx("p",{className:"text-sm text-muted-foreground",children:s}),a&&r.jsxs("div",{className:`flex items-center gap-1 text-sm ${l(a.direction)}`,children:[r.jsx("span",{children:u(a.direction)}),r.jsxs("span",{children:[a.value,"%"]}),r.jsx("span",{className:"text-muted-foreground",children:a.label})]})]})]})}function pr({children:e,columns:t=3,className:s=""}){const n={1:"grid-cols-1",2:"grid-cols-1 md:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 md:grid-cols-2 lg:grid-cols-4"};return r.jsx("div",{className:`grid ${n[t]} gap-6 ${s}`,children:e})}function os({stats:e,size:t="md",columns:s=4,glass:n=!1,loading:a=!1,className:i=""}){const o={1:"grid-cols-1",2:"grid-cols-1 md:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 md:grid-cols-2 lg:grid-cols-4"},l={sm:"p-4",md:"p-6",lg:"p-8"},u=p=>{switch(p){case"primary":return"text-blue-600 dark:text-blue-400";case"success":return"text-green-600 dark:text-green-400";case"warning":return"text-yellow-600 dark:text-yellow-400";case"error":return"text-red-600 dark:text-red-400";default:return"text-gray-600 dark:text-gray-400"}},d=p=>{switch(p){case"up":return"text-green-600 dark:text-green-400";case"down":return"text-red-600 dark:text-red-400";case"neutral":return"text-gray-600 dark:text-gray-400"}},m=p=>{switch(p){case"up":return"↗";case"down":return"↘";case"neutral":return"→"}};return a?r.jsx("div",{className:`grid ${o[s]} gap-6 ${i}`,children:Array.from({length:e.length}).map((p,y)=>r.jsx("div",{className:"animate-pulse",children:r.jsxs("div",{className:`${n?ct("card","md","neutral"):"bg-card"} ${l[t]} rounded-lg border`,children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("div",{className:"h-4 bg-gray-300 rounded w-1/3"}),r.jsx("div",{className:"h-5 w-5 bg-gray-300 rounded"})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsx("div",{className:"h-8 bg-gray-300 rounded w-2/3"}),r.jsx("div",{className:"h-4 bg-gray-300 rounded w-1/2"})]})]})},y))}):r.jsx("div",{className:`grid ${o[s]} gap-6 ${i}`,children:e.map((p,y)=>{const w=p.icon,g=n?ct("card","md","neutral"):"bg-card";return r.jsxs("div",{className:`${g} ${l[t]} rounded-lg border`,children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:p.label}),w&&r.jsx("div",{className:`${u(p.color)}`,children:c.isValidElement(w)?w:r.jsx(w,{className:"h-5 w-5"})})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsx("div",{className:"text-2xl font-bold text-foreground",children:p.value}),p.description&&r.jsx("p",{className:"text-sm text-muted-foreground",children:p.description}),p.trend&&r.jsxs("div",{className:`flex items-center gap-1 text-sm ${d(p.trend.direction)}`,children:[r.jsx("span",{children:m(p.trend.direction)}),r.jsxs("span",{children:[p.trend.value,"%"]})]})]})]},y)})})}os.Card=mr;os.Grid=pr;const fr=c.forwardRef(({columns:e,data:t,searchable:s=!0,filterable:n=!0,glass:a=!0,animated:i=!0,striped:o=!0,hoverable:l=!0,pagination:u,className:d,...m},p)=>{const[y,w]=c.useState(null),[g,f]=c.useState(null),[h,b]=c.useState(""),[x,N]=c.useState({}),R=c.useMemo(()=>{let L=[...t];return h&&(L=L.filter($=>Object.values($).some(W=>String(W).toLowerCase().includes(h.toLowerCase())))),Object.entries(x).forEach(([$,W])=>{W&&(L=L.filter(D=>String(D[$]).toLowerCase().includes(W.toLowerCase())))}),y&&g&&L.sort(($,W)=>{const D=$[y],V=W[y];if(typeof D=="number"&&typeof V=="number")return g==="asc"?D-V:V-D;const z=String(D).toLowerCase(),O=String(V).toLowerCase();return g==="asc"?z.localeCompare(O):O.localeCompare(z)}),L},[t,h,x,y,g]),I=c.useMemo(()=>{if(!u)return R;const L=(u.currentPage-1)*u.pageSize;return R.slice(L,L+u.pageSize)},[R,u]),j=L=>{L.sortable&&(y===L.key?g==="asc"?f("desc"):g==="desc"?(f(null),w(null)):f("asc"):(w(L.key),f("asc")))},E=(L,$)=>{N(W=>({...W,[L]:$}))},k=L=>L.sortable?y!==L.key?r.jsx("div",{className:"w-4 h-4"}):g==="asc"?r.jsx(F.ChevronUp,{className:"w-4 h-4"}):g==="desc"?r.jsx(F.ChevronDown,{className:"w-4 h-4"}):r.jsx("div",{className:"w-4 h-4"}):null;return r.jsxs("div",{ref:p,className:S("relative rounded-xl overflow-hidden",a&&"backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30",d),...m,children:[(s||n)&&r.jsx(v.motion.div,{className:"p-4 border-b border-white/10 dark:border-gray-700/20",initial:i?{opacity:0,y:-20}:{},animate:{opacity:1,y:0},transition:{duration:.3},children:r.jsxs("div",{className:"flex flex-wrap gap-4 items-center",children:[s&&r.jsx("div",{className:"flex-1 min-w-[200px]",children:r.jsx(Le,{placeholder:"Search...",icon:F.Search,value:h,onChange:L=>b(L.target.value),glass:!1})}),n&&r.jsx("div",{className:"flex gap-2",children:e.filter(L=>L.filterable).map(L=>r.jsx(kt,{trigger:r.jsxs("button",{className:"flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-white/10 dark:hover:bg-gray-700/20 transition-colors",children:[r.jsx(F.Filter,{className:"w-4 h-4"}),r.jsx("span",{className:"text-sm",children:L.label})]}),items:[{id:"clear",label:"Clear Filter",onClick:()=>E(L.key,"")}]},L.key))})]})}),r.jsx("div",{className:"overflow-x-auto",children:r.jsxs("table",{className:"w-full",children:[r.jsx("thead",{children:r.jsx("tr",{className:"border-b border-white/10 dark:border-gray-700/20",children:e.map((L,$)=>r.jsx(v.motion.th,{className:S("px-6 py-4 text-left text-sm font-medium text-muted-foreground",L.sortable&&"cursor-pointer hover:text-foreground transition-colors",L.align==="center"&&"text-center",L.align==="right"&&"text-right"),style:{width:L.width},onClick:()=>j(L),initial:i?{opacity:0,y:-10}:{},animate:{opacity:1,y:0},transition:{delay:$*.05},children:r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("span",{children:L.label}),k(L)]})},L.key))})}),r.jsx("tbody",{children:r.jsx(v.AnimatePresence,{children:I.map((L,$)=>r.jsx(v.motion.tr,{className:S("border-b border-white/5 dark:border-gray-700/10",o&&$%2===1&&"bg-white/5 dark:bg-gray-900/5",l&&"hover:bg-white/10 dark:hover:bg-gray-700/20 transition-colors"),initial:i?{opacity:0,x:-20}:{},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{delay:$*.05},layout:!0,children:e.map(W=>r.jsx("td",{className:S("px-6 py-4 text-sm",W.align==="center"&&"text-center",W.align==="right"&&"text-right"),children:W.render?W.render(L[W.key],L,$):String(L[W.key]||"")},W.key))},$))})})]})}),I.length===0&&r.jsx(v.motion.div,{className:"py-12 text-center text-muted-foreground",initial:i?{opacity:0}:{},animate:{opacity:1},children:r.jsx("p",{children:"No data found"})}),u&&r.jsx(v.motion.div,{className:"px-6 py-4 border-t border-white/10 dark:border-gray-700/20 flex items-center justify-between text-sm text-muted-foreground",initial:i?{opacity:0}:{},animate:{opacity:1},transition:{delay:.3},children:r.jsxs("div",{children:["Showing ",(u.currentPage-1)*u.pageSize+1," to"," ",Math.min(u.currentPage*u.pageSize,R.length)," of"," ",R.length," entries"]})})]})});fr.displayName="LuminarTable";function Ea({children:e,variant:t="neutral",size:s="sm",removable:n=!1,onRemove:a,className:i=""}){const o={xs:"px-2 py-1 text-xs",sm:"px-2.5 py-1.5 text-sm",md:"px-3 py-2 text-base",lg:"px-4 py-2.5 text-lg"},l={primary:"bg-primary/20 text-primary border-primary/30",secondary:"bg-secondary/80 text-secondary-foreground border-secondary",success:"bg-green-50/80 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-200/60 dark:border-green-700/60",warning:"bg-yellow-50/80 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 border-yellow-200/60 dark:border-yellow-700/60",error:"bg-red-50/80 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-red-200/60 dark:border-red-700/60",neutral:"bg-muted text-muted-foreground border-border"},u=ct("button","light",t);return r.jsxs("span",{className:`${u} ${o[s]} ${l[t]} rounded-full border inline-flex items-center gap-1 font-medium ${i}`,children:[e,n&&r.jsx("button",{onClick:a,className:"ml-1 p-0.5 hover:bg-black/10 dark:hover:bg-white/10 rounded-full transition-colors",children:r.jsx("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}const hr=c.forwardRef(({text:e,variant:t="fade",delay:s=0,duration:n=.5,as:a="div",className:i,...o},l)=>{const[u,d]=c.useState(t==="typewriter"?"":e);v.useAnimation();const m=c.useRef(null),p=v.useInView(m,{once:!0});c.useEffect(()=>{if(t==="typewriter"&&p){let h=0;const b=setInterval(()=>{h<=e.length?(d(e.slice(0,h)),h++):clearInterval(b)},n*1e3/e.length);return()=>clearInterval(b)}},[t,e,n,p]);const y={fade:{hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{delay:s,duration:n}}},slide:{hidden:{x:-100,opacity:0},visible:{x:0,opacity:1,transition:{delay:s,duration:n,type:"spring"}}},gradient:{hidden:{opacity:0},visible:{opacity:1,transition:{delay:s,duration:n}}},glitch:{hidden:{opacity:0},visible:{opacity:1,transition:{delay:s,duration:n}}},wave:{hidden:{},visible:{transition:{staggerChildren:.05,delayChildren:s}}},typewriter:{hidden:{opacity:0},visible:{opacity:1}}},w={hidden:{opacity:0,y:50},visible:{opacity:1,y:0,transition:{duration:.5}}},g=()=>{const h=S(i);switch(t){case"gradient":return S(h,"bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 dark:from-purple-400 dark:via-pink-400 dark:to-blue-400","bg-clip-text text-transparent bg-300% animate-gradient");case"glitch":return S(h,"glitch-text");default:return h}};return t==="wave"?r.jsx(v.motion.div,{ref:m,variants:y.wave,initial:"hidden",animate:p?"visible":"hidden",className:g(),children:e.split("").map((h,b)=>r.jsx(v.motion.span,{variants:w,className:"inline-block",style:{display:h===" "?"inline":"inline-block"},children:h===" "?" ":h},b))}):t==="typewriter"?r.jsxs(a,{ref:l,className:g(),...o,children:[u,r.jsx(v.motion.span,{animate:{opacity:[1,0]},transition:{duration:.5,repeat:1/0,repeatType:"reverse"},className:"inline-block w-0.5 h-[1.1em] bg-current ml-1"})]}):r.jsx(v.motion.div,{ref:m,variants:y[t],initial:"hidden",animate:p?"visible":"hidden",children:r.jsx(a,{ref:l,className:g(),...o,children:e})})});hr.displayName="LuminarText";const Es={completed:{icon:F.Check,color:"text-green-400 bg-green-400/20 border-green-400/50",connectorColor:"bg-green-400"},active:{icon:F.Activity,color:"text-blue-400 bg-blue-400/20 border-blue-400/50",connectorColor:"bg-blue-400"},pending:{icon:F.Clock,color:"text-gray-400 bg-gray-400/20 border-gray-400/50",connectorColor:"bg-gray-400"},error:{icon:F.AlertCircle,color:"text-red-400 bg-red-400/20 border-red-400/50",connectorColor:"bg-red-400"},cancelled:{icon:F.X,color:"text-orange-400 bg-orange-400/20 border-orange-400/50",connectorColor:"bg-orange-400"}},gr=c.forwardRef((e,t)=>{const{items:s,orientation:n="vertical",variant:a=defaultComponentProps.variant,glass:i=!0,animated:o=!0,showConnector:l=!0,activeIndex:u,className:d,...m}=e,p=n==="horizontal",y=a==="alternating"&&!p,w=a==="centered"&&!p,g=a==="compact",f=x=>x?(typeof x=="string"?new Date(x):x).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):null,h=S("relative",p?"flex items-center":"space-y-8",d),b=(x,N)=>{const R=x.status||"pending",I=x.icon||Es[R].icon,j=u!==void 0?N===u:R==="active",E=N===s.length-1,k=N%2===0,L=o?{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:N*.1}}:{},$=o&&j?{animate:{scale:[1,1.2,1]},transition:{duration:2,repeat:1/0}}:{};return r.jsxs(v.motion.div,{className:S("relative",p&&"flex flex-col items-center",y&&"flex items-center",w&&"flex items-center justify-center"),...L,children:[l&&!E&&r.jsx("div",{className:S("absolute",Es[R].connectorColor,p?"h-0.5 top-10 left-1/2 right-[-100%] w-full":y||w?"w-0.5 top-10 bottom-0 left-1/2 -translate-x-1/2":"w-0.5 left-5 top-10 bottom-0")}),r.jsxs("div",{className:S("flex items-start gap-4",y&&(k?"flex-row":"flex-row-reverse"),w&&"w-full"),children:[(y||w)&&r.jsx("div",{className:S("flex-1",y&&(k?"text-right pr-8":"text-left pl-8"),w&&"text-right pr-8"),children:(y&&k||w)&&r.jsxs("div",{children:[!g&&x.date&&r.jsx("p",{className:"text-sm text-gray-400 mb-1",children:f(x.date)}),r.jsx("h3",{className:"font-semibold text-lg",children:x.title}),x.description&&r.jsx("p",{className:"text-sm text-gray-400 mt-1",children:x.description}),x.content&&r.jsx("div",{className:"mt-3",children:x.content})]})}),r.jsx(v.motion.div,{className:S("relative z-10 flex items-center justify-center","w-10 h-10 rounded-full border-2",Es[R].color,i&&"backdrop-blur-sm",x.highlight&&"ring-4 ring-white/20"),...$,children:r.jsx(I,{className:"w-5 h-5"})}),r.jsx("div",{className:S("flex-1",!y&&!w&&"flex-1",y&&(k?"text-left pl-8":"text-right pr-8"),w&&"text-left pl-8"),children:(a==="default"||y&&!k||w)&&r.jsxs("div",{className:S(i&&!g&&"backdrop-blur-sm bg-white/5 border border-white/10 rounded-lg p-4"),children:[!g&&x.date&&r.jsx("p",{className:"text-sm text-gray-400 mb-1",children:f(x.date)}),r.jsx("h3",{className:S("font-semibold",g?"text-base":"text-lg"),children:x.title}),x.description&&r.jsx("p",{className:S("text-gray-400 mt-1",g?"text-xs":"text-sm"),children:x.description}),x.content&&!g&&r.jsx("div",{className:"mt-3",children:x.content})]})})]})]},x.id)};return r.jsx("div",{ref:t,className:h,...m,children:s.map((x,N)=>b(x,N))})});gr.displayName="LuminarTimeline";const yr=c.forwardRef(({items:e,value:t=[],onChange:s,leftTitle:n="Available",rightTitle:a="Selected",searchable:i=!0,showSelectAll:o=!0,glass:l=!0,animated:u=!0,height:d=400,emptyMessage:m="No items",disabled:p=!1,showCount:y=!0,className:w,...g},f)=>{const[h,b]=c.useState(new Set),[x,N]=c.useState(new Set),[R,I]=c.useState(""),[j,E]=c.useState(""),{leftItems:k,rightItems:L}=c.useMemo(()=>{const _=new Set(t),te=e.filter(Z=>!_.has(Z.id)),H=e.filter(Z=>_.has(Z.id));return{leftItems:te,rightItems:H}},[e,t]),$=c.useMemo(()=>{if(!R)return k;const _=R.toLowerCase();return k.filter(te=>te.label.toLowerCase().includes(_)||te.description?.toLowerCase().includes(_))},[k,R]),W=c.useMemo(()=>{if(!j)return L;const _=j.toLowerCase();return L.filter(te=>te.label.toLowerCase().includes(_)||te.description?.toLowerCase().includes(_))},[L,j]),D=c.useMemo(()=>Array.from(h).filter(_=>{const te=e.find(H=>H.id===_);return te&&!te.disabled&&!t.includes(_)}),[h,e,t]),V=c.useMemo(()=>Array.from(x).filter(_=>{const te=e.find(H=>H.id===_);return te&&!te.disabled&&t.includes(_)}),[x,e,t]),z=()=>{D.length>0&&(s?.([...t,...D]),b(new Set))},O=()=>{V.length>0&&(s?.(t.filter(_=>!V.includes(_))),N(new Set))},M=()=>{const _=k.filter(te=>!te.disabled).map(te=>te.id);s?.([...t,..._]),b(new Set)},K=()=>{s?.([]),N(new Set)},U=_=>{const te=new Set(h);te.has(_)?te.delete(_):te.add(_),b(te)},ee=_=>{const te=new Set(x);te.has(_)?te.delete(_):te.add(_),N(te)},Y=_=>{if(_){const te=$.filter(H=>!H.disabled).map(H=>H.id);b(new Set(te))}else b(new Set)},se=_=>{if(_){const te=W.filter(H=>!H.disabled).map(H=>H.id);N(new Set(te))}else N(new Set)},G=$.length>0&&$.filter(_=>!_.disabled).every(_=>h.has(_.id)),P=W.length>0&&W.filter(_=>!_.disabled).every(_=>x.has(_.id)),B=(_,te,H,Z,ue,J,oe,pe)=>r.jsxs("div",{className:S("flex-1 flex flex-col rounded-xl overflow-hidden",l&&"backdrop-blur-sm bg-white/5 border border-white/10"),children:[r.jsxs("div",{className:"p-4 border-b border-white/10",children:[r.jsxs("div",{className:"flex items-center justify-between mb-3",children:[r.jsxs("h3",{className:"font-medium",children:[pe,y&&r.jsx(Nt,{variant:"info",className:"ml-2",children:_.length})]}),o&&_.some(q=>!q.disabled)&&r.jsx(De,{checked:ue,onChange:q=>Z(q.target.checked),disabled:p,glass:l})]}),i&&r.jsx(Le,{placeholder:"Search...",value:J,onChange:q=>oe(q.target.value),icon:F.Search,size:"sm",glass:l,disabled:p})]}),r.jsx("div",{className:"flex-1 overflow-y-auto p-2",style:{maxHeight:typeof d=="number"?`${d}px`:d},children:r.jsx(v.AnimatePresence,{mode:"popLayout",children:_.length===0?r.jsx("div",{className:"flex items-center justify-center h-full text-gray-500",children:m}):_.map((q,ce)=>r.jsxs(v.motion.div,{layout:!0,initial:u?{opacity:0,x:-20}:void 0,animate:u?{opacity:1,x:0}:void 0,exit:u?{opacity:0,x:20}:void 0,transition:{delay:ce*.05},className:S("flex items-center p-3 rounded-lg mb-2 cursor-pointer transition-colors","hover:bg-white/5",te.has(q.id)&&"bg-white/10",q.disabled&&"opacity-50 cursor-not-allowed"),onClick:()=>!q.disabled&&!p&&H(q.id),children:[r.jsx(De,{checked:te.has(q.id),onChange:fe=>{fe.stopPropagation(),H(q.id)},disabled:q.disabled||p,onClick:fe=>fe.stopPropagation(),glass:l,className:"mr-3"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"font-medium",children:q.label}),q.description&&r.jsx("div",{className:"text-sm text-gray-400",children:q.description})]})]},q.id))})})]});return r.jsxs("div",{ref:f,className:S("flex gap-4",w),...g,children:[B($,h,U,Y,G,R,I,n),r.jsxs("div",{className:"flex flex-col justify-center gap-2",children:[r.jsx(le,{variant:l?"glass":"ghost",size:"sm",onClick:M,disabled:p||k.filter(_=>!_.disabled).length===0,"aria-label":"Move all to right",children:r.jsx(F.ChevronsRight,{className:"w-4 h-4"})}),r.jsx(le,{variant:l?"glass-primary":"default",size:"sm",onClick:z,disabled:p||D.length===0,"aria-label":"Move selected to right",children:r.jsx(F.ChevronRight,{className:"w-4 h-4"})}),r.jsx(le,{variant:l?"glass-primary":"default",size:"sm",onClick:O,disabled:p||V.length===0,"aria-label":"Move selected to left",children:r.jsx(F.ChevronLeft,{className:"w-4 h-4"})}),r.jsx(le,{variant:l?"glass":"ghost",size:"sm",onClick:K,disabled:p||L.filter(_=>!_.disabled).length===0,"aria-label":"Move all to left",children:r.jsx(F.ChevronsLeft,{className:"w-4 h-4"})})]}),B(W,x,ee,se,P,j,E,a)]})});yr.displayName="LuminarTransferList";const br=c.memo(({node:e,level:t,expanded:s,selected:n,checked:a,onToggle:i,onSelect:o,onCheck:l,selectable:u,multiSelect:d,checkable:m,glass:p,animated:y,showLines:w,indent:g=24,onNodeClick:f})=>{const h=e.children&&e.children.length>0,b=s.has(e.id),x=n.has(e.id),N=a.has(e.id),R=e.icon||(h?b?e.expandedIcon||F.FolderOpen:F.Folder:F.File),I=L=>{L.stopPropagation(),f?.(e),u&&!m&&o(e.id)},j=L=>{L.stopPropagation(),h&&i(e.id)},E=L=>{l(e.id)},k=y?{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2}}:{};return r.jsxs(r.Fragment,{children:[r.jsxs(v.motion.div,{className:S("group flex items-center py-1.5 px-2 rounded-lg cursor-pointer transition-colors",p&&"backdrop-blur-sm",x&&!m&&"bg-blue-500/20",!x&&"hover:bg-white/5",e.disabled&&"opacity-50 cursor-not-allowed"),style:{paddingLeft:`${t*g+8}px`},onClick:I,...k,children:[r.jsx("div",{className:S("mr-1 p-0.5 rounded transition-transform",h?"cursor-pointer hover:bg-white/10":"invisible"),onClick:j,children:r.jsx(v.motion.div,{animate:{rotate:b?90:0},transition:{duration:.2},children:r.jsx(F.ChevronRight,{className:"w-4 h-4"})})}),m&&r.jsx(De,{checked:N,onChange:E,disabled:e.disabled,glass:p,onClick:L=>L.stopPropagation(),className:"mr-2"}),r.jsx(R,{className:S("w-4 h-4 mr-2 flex-shrink-0",x&&!m&&"text-blue-400")}),r.jsxs("div",{className:S("flex-1 text-sm select-none",x&&!m&&"font-medium"),children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("span",{children:e.label}),e.count!==void 0&&r.jsx("span",{className:"ml-2 px-2 py-0.5 text-xs bg-white/10 rounded-full",children:e.count})]}),e.description&&r.jsx("div",{className:"text-xs text-gray-400 mt-0.5",children:e.description})]})]}),r.jsx(v.AnimatePresence,{children:h&&b&&r.jsxs(v.motion.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.2},className:"overflow-hidden",children:[w&&r.jsx("div",{className:"absolute border-l border-white/10",style:{left:`${t*g+20}px`,height:"100%"}}),e.children?.map(L=>r.jsx(br,{node:L,level:t+1,expanded:s,selected:n,checked:a,onToggle:i,onSelect:o,onCheck:l,selectable:u,multiSelect:d,checkable:m,glass:p,animated:y,showLines:w,indent:g,onNodeClick:f},L.id))]})})]})});br.displayName="TreeNodeComponent";const xr=c.forwardRef(({data:e,defaultExpanded:t=[],defaultSelected:s=[],selectable:n=!0,multiSelect:a=!1,checkable:i=!1,onNodeClick:o,onNodeExpand:l,onSelectionChange:u,onCheckedChange:d,glass:m=!0,animated:p=!0,showLines:y=!1,indent:w=24,className:g,...f},h)=>{const[b,x]=c.useState(new Set(t)),[N,R]=c.useState(new Set(s)),[I,j]=c.useState(new Set),E=c.useCallback($=>{x(W=>{const D=new Set(W);return D.has($)?(D.delete($),l?.($,!1)):(D.add($),l?.($,!0)),D})},[l]),k=c.useCallback($=>{R(W=>{const D=new Set(W);return a?D.has($)?D.delete($):D.add($):(D.clear(),D.add($)),u?.(Array.from(D)),D})},[a,u]),L=c.useCallback($=>{j(W=>{const D=new Set(W);D.has($)?D.delete($):D.add($);const V=(M,K)=>{K?D.add(M.id):D.delete(M.id),M.children?.forEach(U=>V(U,K))},z=M=>{for(const K of M){if(K.id===$)return K;const U=z(K.children||[]);if(U)return U}return null},O=z(e);return O?.children&&V(O,!W.has($)),d?.(Array.from(D)),D})},[e,d]);return c.useCallback(()=>{const $=[],W=D=>{D.forEach(V=>{V.children&&V.children.length>0&&($.push(V.id),W(V.children))})};W(e),x(new Set($))},[e]),c.useCallback(()=>{x(new Set)},[]),r.jsx("div",{ref:h,className:S("relative rounded-xl p-2",m&&"backdrop-blur-md bg-white/5 border border-white/20",g),...f,children:e.map($=>r.jsx(br,{node:$,level:0,expanded:b,selected:N,checked:I,onToggle:E,onSelect:k,onCheck:L,selectable:n,multiSelect:a,checkable:i,glass:m,animated:p,showLines:y,indent:w,onNodeClick:o},$.id))})});xr.displayName="LuminarTreeView";const wr=c.forwardRef(({value:e,max:t=100,variant:s=defaultComponentProps.variant,size:n=defaultComponentProps.size,showLabel:a=!1,animated:i=!0,glass:o=!1,className:l,...u},d)=>{const m=v.useAnimation(),p=Math.min(Math.max(e/t*100,0),100);c.useEffect(()=>{i&&m.start({width:`${p}%`})},[p,i,m]);const y={sm:"h-2",md:"h-4",lg:"h-6"},w={default:"bg-primary",gradient:"bg-gradient-to-r from-purple-500 via-pink-500 to-red-500",striped:"bg-primary bg-stripes",glow:"bg-primary shadow-[0_0_15px_rgba(var(--primary),0.5)]"},g=o?"backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30":"bg-secondary";return r.jsxs("div",{className:S("relative",l),...u,children:[r.jsx("div",{ref:d,className:S("w-full rounded-full overflow-hidden",y[n],g),children:r.jsx(v.motion.div,{className:S("h-full rounded-full relative overflow-hidden",w[s]),initial:{width:0},animate:i?m:{width:`${p}%`},transition:{duration:.8,ease:"easeOut"},children:s==="striped"&&r.jsx(v.motion.div,{className:"absolute inset-0 opacity-20",animate:{x:["0%","100%"]},transition:{duration:1,repeat:1/0,ease:"linear"},style:{backgroundImage:"linear-gradient(45deg, transparent 25%, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.3) 50%, transparent 50%, transparent 75%, rgba(255,255,255,0.3) 75%)",backgroundSize:"20px 20px"}})})}),a&&r.jsxs(v.motion.span,{className:S("absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2","text-xs font-medium",p>50?"text-white":"text-foreground"),initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[Math.round(p),"%"]})]})});wr.displayName="ProgressBar";const et=c.forwardRef(({variant:e=defaultComponentProps.variant,animation:t="pulse",width:s,height:n,count:a=1,className:i,...o},l)=>{const u=S("relative overflow-hidden",e==="glass"?"backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30":"bg-muted"),d={default:"rounded-md",text:"rounded-md h-4 w-full",circular:"rounded-full aspect-square",rectangular:"rounded-lg",glass:"rounded-lg"},m=t==="wave"&&r.jsx(v.motion.div,{className:"absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent",animate:{x:["0%","200%"]},transition:{duration:1.5,repeat:1/0,ease:"linear"}}),p=t==="pulse"?{animate:{opacity:[.5,1,.5]},transition:{duration:1.5,repeat:1/0,ease:"easeInOut"}}:{},y=()=>r.jsx(v.motion.div,{className:S(u,d[e],i),style:{width:s,height:n},...p,children:m});return a>1?r.jsx("div",{ref:l,className:"space-y-2",children:Array.from({length:a}).map((w,g)=>r.jsx("div",{children:y()},g))}):y()});et.displayName="Skeleton";const iu=c.forwardRef(({className:e,...t},s)=>r.jsxs("div",{ref:s,className:S("rounded-lg p-4 space-y-3","backdrop-blur-md bg-white/10 dark:bg-gray-900/10","border border-white/20 dark:border-gray-700/30",e),...t,children:[r.jsx(et,{variant:"circular",width:40,height:40}),r.jsxs("div",{className:"space-y-2",children:[r.jsx(et,{variant:"text",className:"w-3/4"}),r.jsx(et,{variant:"text"}),r.jsx(et,{variant:"text",className:"w-1/2"})]})]}));iu.displayName="SkeletonCard";const ou=Object.freeze(Object.defineProperty({__proto__:null,Card:xt,LoadingSpinner:ar,LuminarAccordion:ir,LuminarAvatar:is,LuminarBadge:Nt,LuminarCard:xt,LuminarCarousel:lr,LuminarCarouselItem:cr,LuminarCounter:ur,LuminarDataGrid:Sa,LuminarDataTable:Ta,LuminarMetricCard:Ra,LuminarProgressRing:dr,LuminarSkeleton:we,LuminarStatCard:mr,LuminarStats:os,LuminarStatsGrid:pr,LuminarTable:fr,LuminarTag:Ea,LuminarText:hr,LuminarTimeline:gr,LuminarTransferList:yr,LuminarTreeView:xr,ProgressBar:wr,Skeleton:et},Symbol.toStringTag,{value:"Module"})),ls=c.forwardRef(({label:e,error:t,glass:s=!0,autoResize:n=!1,showCharCount:a=!1,maxLength:i,className:o,onFocus:l,onBlur:u,onChange:d,value:m,...p},y)=>{const[w,g]=c.useState(!1),[f,h]=c.useState(String(m||"").length),b=R=>{g(!0),l?.(R)},x=R=>{g(!1),u?.(R)},N=R=>{h(R.target.value.length),n&&(R.target.style.height="auto",R.target.style.height=R.target.scrollHeight+"px"),d?.(R)};return r.jsxs("div",{className:"w-full",children:[e&&r.jsx(v.motion.label,{className:"block text-sm font-medium mb-2",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:e}),r.jsxs(v.motion.div,{className:S("relative rounded-lg overflow-hidden",s&&"backdrop-blur-md bg-white/10 dark:bg-gray-900/10","border transition-all duration-300",w?"border-primary shadow-[0_0_0_2px_rgba(var(--primary),0.2)]":t?"border-destructive":"border-white/20 dark:border-gray-700/30",o),animate:{scale:w?1.01:1},transition:{duration:.2},children:[r.jsx("textarea",{ref:y,className:S("w-full bg-transparent px-4 py-3 outline-none resize-none","placeholder:text-muted-foreground","min-h-[100px]"),onFocus:b,onBlur:x,onChange:N,value:m,maxLength:i,...p}),r.jsx(v.motion.div,{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-primary",initial:{scaleX:0},animate:{scaleX:w?1:0},transition:{duration:.3}})]}),r.jsxs("div",{className:"flex items-center justify-between mt-2",children:[t?r.jsx(v.motion.p,{className:"text-sm text-destructive",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:t}):r.jsx("div",{}),(a||i)&&r.jsxs(v.motion.span,{className:S("text-xs",i&&f>i*.9?"text-warning":"text-muted-foreground"),initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:[f,i&&` / ${i}`]})]})]})});ls.displayName="LuminarTextarea";const vr=c.forwardRef(({options:e,value:t,defaultValue:s,name:n,label:a,orientation:i="vertical",glass:o=!0,onChange:l,onValueChange:u,className:d,...m},p)=>{const[y,w]=c.useState(t||s||""),g=f=>{y!==f&&(w(f),l?.(f),u?.(f))};return r.jsxs("div",{ref:p,className:S("w-full",d),...m,children:[a&&r.jsx(v.motion.label,{className:"block text-sm font-medium mb-3",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:a}),r.jsx("div",{className:S("flex gap-3",i==="vertical"?"flex-col":"flex-row flex-wrap"),role:"radiogroup","aria-labelledby":a?`${n}-label`:void 0,children:e.map((f,h)=>{const b=y===f.value,x=f.disabled;return r.jsxs(v.motion.label,{className:S("relative flex items-start gap-3 p-4 rounded-lg cursor-pointer transition-all duration-200",o&&"backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30",!o&&"border border-gray-200 dark:border-gray-800",b&&"border-primary bg-primary/5",x&&"opacity-50 cursor-not-allowed"),initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:h*.1},whileHover:x?{}:{scale:1.02},whileTap:x?{}:{scale:.98},children:[r.jsxs("div",{className:"relative flex items-center justify-center",children:[r.jsx("input",{type:"radio",name:n,value:f.value,checked:b,disabled:x,onChange:()=>!x&&g(f.value),className:"sr-only"}),r.jsx(v.motion.div,{className:S("w-5 h-5 rounded-full border-2 transition-colors",b?"border-primary bg-primary/20":"border-gray-300 dark:border-gray-600"),animate:{scale:b?1.1:1,borderColor:b?"rgb(var(--primary))":void 0},transition:{type:"spring",stiffness:300,damping:30},children:r.jsx(v.motion.div,{className:"absolute inset-1 rounded-full bg-primary",initial:{scale:0},animate:{scale:b?1:0},transition:{type:"spring",stiffness:400,damping:30}})}),b&&r.jsx(v.motion.div,{className:"absolute inset-0 rounded-full border-2 border-primary",initial:{scale:1,opacity:.5},animate:{scale:1.5,opacity:0},transition:{duration:.6}})]}),r.jsxs("div",{className:"flex-1 min-w-0",children:[r.jsx("div",{className:S("text-sm font-medium",b&&"text-primary"),children:f.label}),f.description&&r.jsx(v.motion.div,{className:"text-xs text-muted-foreground mt-1",initial:{opacity:0},animate:{opacity:1},transition:{delay:h*.1+.1},children:f.description})]}),b&&r.jsx(v.motion.div,{className:"absolute top-2 right-2 w-2 h-2 rounded-full bg-primary",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:400,damping:30,delay:.1}})]},f.value)})})]})});vr.displayName="LuminarRadioGroup";const cs=c.forwardRef(({label:e,size:t="md",glass:s=!0,className:n,checked:a,onChange:i,onCheckedChange:o,disabled:l,...u},d)=>{const m={sm:{track:"w-8 h-4",thumb:"w-3 h-3",translate:"translate-x-4"},md:{track:"w-11 h-6",thumb:"w-5 h-5",translate:"translate-x-5"},lg:{track:"w-14 h-8",thumb:"w-6 h-6",translate:"translate-x-6"}},{track:p,thumb:y,translate:w}=m[t];return r.jsxs("label",{className:S("inline-flex items-center gap-3 cursor-pointer",l&&"opacity-50 cursor-not-allowed",n),children:[r.jsxs("div",{className:"relative",children:[r.jsx("input",{ref:d,type:"checkbox",className:"sr-only",checked:a,onChange:g=>{i?.(g),o?.(g.target.checked)},disabled:l,...u}),r.jsxs(v.motion.div,{className:S("relative rounded-full transition-colors duration-300",p,s&&"backdrop-blur-md bg-white/20 dark:bg-gray-900/20 border border-white/30 dark:border-gray-700/40",!s&&"bg-gray-300 dark:bg-gray-700",a&&!s&&"bg-primary",a&&s&&"bg-primary/30"),animate:{backgroundColor:a?s?"rgba(var(--primary), 0.3)":"rgb(var(--primary))":s?"rgba(255, 255, 255, 0.2)":void 0},children:[r.jsx(v.motion.div,{className:S("absolute top-0.5 left-0.5 rounded-full bg-white shadow-md",y),animate:{x:a?w.replace("translate-x-",""):"0",scale:a?1.1:1},transition:{type:"spring",stiffness:500,damping:30}}),a&&r.jsx(v.motion.div,{className:"absolute inset-0 rounded-full bg-primary/20",initial:{scale:.8,opacity:0},animate:{scale:1.2,opacity:0},transition:{duration:.6,repeat:1/0,repeatType:"reverse"}})]})]}),e&&r.jsx("span",{className:S("select-none text-sm font-medium",t==="lg"&&"text-base"),children:e})]})});cs.displayName="LuminarSwitch";const jr=c.forwardRef(({value:e=0,min:t=0,max:s=100,step:n=1,label:a,showValue:i=!0,glass:o=!0,disabled:l=!1,onChange:u,className:d,...m},p)=>{const[y,w]=c.useState(!1),g=c.useRef(null),f=c.useRef(null),h=v.useMotionValue(0),b=v.useTransform(h,N=>{if(!g.current)return 0;const R=g.current.getBoundingClientRect(),I=(N+R.width/2)/R.width;return Math.max(0,Math.min(1,I))}),x=v.useTransform(b,N=>{const R=t+(s-t)*N;return Math.round(R/n)*n});return c.useEffect(()=>{const N=(e-t)/(s-t);if(g.current){const R=g.current.getBoundingClientRect();h.set(N*R.width-R.width/2)}},[e,t,s,h]),c.useEffect(()=>x.on("change",N=>{u?.(N)}),[x,u]),r.jsxs("div",{className:"w-full",children:[a&&r.jsxs("div",{className:"flex items-center justify-between mb-2",children:[r.jsx("label",{className:"text-sm font-medium",children:a}),i&&r.jsx(v.motion.span,{className:"text-sm font-mono",animate:{scale:y?1.1:1},children:e})]}),r.jsxs("div",{ref:p,className:S("relative",d),...m,children:[r.jsxs("div",{ref:g,className:S("relative w-full h-2 rounded-full overflow-hidden",o&&"backdrop-blur-md bg-white/20 dark:bg-gray-900/20 border border-white/30 dark:border-gray-700/40",!o&&"bg-secondary"),children:[r.jsx(v.motion.div,{className:"absolute inset-y-0 left-0 bg-primary rounded-full",style:{width:v.useTransform(b,N=>`${N*100}%`)}}),r.jsx(v.motion.div,{className:"absolute inset-y-0 left-0 bg-primary/30 blur-md",style:{width:v.useTransform(b,N=>`${N*100}%`)},animate:{opacity:y?1:.5}})]}),r.jsx("div",{ref:f,className:"absolute inset-y-0 left-0 right-0",style:{margin:"0 -10px"}}),r.jsx(v.motion.div,{className:S("absolute top-1/2 -translate-y-1/2 w-5 h-5 rounded-full cursor-grab active:cursor-grabbing","bg-white shadow-lg border-2 border-primary",o&&"backdrop-blur-md"),style:{x:h,left:"50%"},drag:"x",dragConstraints:f,dragElastic:0,dragMomentum:!1,onDragStart:()=>w(!0),onDragEnd:()=>w(!1),whileHover:{scale:1.2},whileDrag:{scale:1.4},animate:{scale:y?1.4:1},children:r.jsx(v.motion.div,{className:"absolute inset-0 rounded-full bg-primary/20",animate:{scale:y?[1,1.5,1]:1,opacity:y?[.5,0,.5]:0},transition:{duration:1,repeat:y?1/0:0}})}),i&&y&&r.jsx(v.motion.div,{className:S("absolute -top-10 px-2 py-1 rounded text-xs font-mono",o&&"backdrop-blur-md bg-white/90 dark:bg-gray-900/90 border border-white/20 dark:border-gray-700/30",!o&&"bg-popover"),style:{x:h,left:"50%"},initial:{opacity:0,y:10},animate:{opacity:1,y:0},children:e})]})]})});jr.displayName="LuminarSlider";const Aa=c.createContext(null);Aa.displayName="HookFormContext";const rt=()=>c.useContext(Aa),lu=c.createContext(void 0),Tt=()=>{const e=c.useContext(lu);if(!e)throw new Error("useFormField must be used within a FormField");return e},Cr=c.forwardRef(({className:e,variant:t="card",size:s="md",glassIntensity:n="medium",colorTheme:a="neutral",animation:i="fadeIn",disableAnimation:o=!1,spacing:l="normal",children:u,...d},m)=>{const p=tr(t,{intensity:n}),y=er[s],w=o?{}:He[i],g={tight:"space-y-4",normal:"space-y-6",relaxed:"space-y-8"};return r.jsx(v.motion.form,{ref:m,className:S("w-full",p,y.padding,g[l],e),...w,transition:ze.spring,...d,children:u})}),cu=c.forwardRef(({className:e,variant:t="default",size:s="md",glassIntensity:n="light",colorTheme:a="neutral",...i},o)=>{const{name:l,error:u}=Tt(),m=rt()?.register,p=m?m(l):null,y=p?.ref,w=p?{...p,ref:void 0}:{};return r.jsx(Le,{ref:y||o,className:S(u&&"border-destructive focus:border-destructive",e),variant:t==="glass"?"input":"default",size:s,glassIntensity:n,...w,...i})}),uu=c.forwardRef(({className:e,variant:t="default",size:s="md",glassIntensity:n="light",colorTheme:a="neutral",...i},o)=>{const{name:l,error:u}=Tt(),m=rt()?.register,p=m?m(l):null,y=p?.ref,w=p?{...p,ref:void 0}:{};return r.jsx(ls,{ref:y||o,className:S(u&&"border-destructive focus:border-destructive",e),...w,...i})}),du=c.forwardRef(({placeholder:e="Select an option",options:t,variant:s="default",size:n="md",glassIntensity:a="light",colorTheme:i="neutral",...o},l)=>{const{name:u,error:d}=Tt(),m=rt(),{register:p,setValue:y,watch:w}=m||{},g=w?.(u);return r.jsx(St,{ref:l,placeholder:e,value:g,onChange:f=>y?.(u,f.target.value),className:S(d&&"border-destructive focus:border-destructive"),options:t,...o})}),mu=c.forwardRef(({label:e,size:t="md",glassIntensity:s="light",colorTheme:n="neutral",...a},i)=>{const{name:o,error:l}=Tt(),u=rt(),{register:d,watch:m}=u||{},p=m?.(o);return r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(De,{checked:p,className:S(l&&"border-destructive"),...d?{...d(o),ref:i||d(o).ref}:{ref:i},...a}),e&&r.jsx("label",{className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:e})]})}),pu=c.forwardRef(({label:e,size:t="md",glassIntensity:s="light",colorTheme:n="neutral",...a},i)=>{const{name:o,error:l}=Tt(),u=rt(),{register:d,watch:m,setValue:p}=u||{},y=m?.(o);return r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(cs,{ref:i,checked:y,onCheckedChange:w=>p?.(o,w),size:t==="xs"?"sm":t==="xl"?"lg":t,className:S(l&&"border-destructive"),...a}),e&&r.jsx("label",{className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:e})]})}),fu=c.forwardRef(({children:e,variant:t="default",size:s="md",loading:n=!1,disabled:a=!1,className:i,...o},l)=>{const m=rt()?.formState?.isSubmitting||n;return r.jsx(We,{ref:l,type:"submit",variant:t,size:s,loading:m,disabled:a||m,className:i,...o,children:e})}),hu=c.forwardRef(({children:e,variant:t="outline",size:s="md",disabled:n=!1,className:a,...i},o)=>{const u=rt()?.reset;return r.jsx(We,{ref:o,type:"button",variant:t,size:s,disabled:n,className:a,onClick:()=>u?.(),...i,children:e})}),gu=c.forwardRef(({className:e,justify:t="end",spacing:s="normal",children:n,...a},i)=>{const o={start:"justify-start",center:"justify-center",end:"justify-end",between:"justify-between"},l={tight:"gap-2",normal:"gap-4",relaxed:"gap-6"};return r.jsx("div",{ref:i,className:S("flex items-center",o[t],l[s],e),...a,children:n})});Cr.displayName="LuminarForm";cu.displayName="LuminarFormInput";uu.displayName="LuminarFormTextarea";du.displayName="LuminarFormSelect";mu.displayName="LuminarFormCheckbox";pu.displayName="LuminarFormSwitch";fu.displayName="LuminarFormSubmit";hu.displayName="LuminarFormReset";gu.displayName="LuminarFormActions";const Rt=c.forwardRef(({className:e,children:t,variant:s=Ae.variant,size:n=Ae.size,glassIntensity:a="light",colorTheme:i="neutral",required:o=!1,disabled:l=!1,error:u=!1,interactive:d=!1,hoverable:m=!0,animation:p="fadeIn",disableAnimation:y=!1,weight:w="medium",transform:g="none",icon:f,iconPosition:h="left",floatingLabel:b,floatingPlaceholder:x,floatingActive:N=!1,...R},I)=>{const j=componentSizes[n],E=y?{}:animationPresets[p],k={default:S("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",u&&"text-destructive",l&&"text-muted-foreground cursor-not-allowed"),glass:S("text-sm font-medium leading-none backdrop-blur-sm rounded-md px-2 py-1",getGlassClasses("default",{intensity:a,depth:"surface",animated:!y,interactive:d||m}),u&&"border-destructive text-destructive",l&&"opacity-50 cursor-not-allowed"),inline:S("inline-flex items-center text-sm font-medium leading-none",u&&"text-destructive",l&&"text-muted-foreground cursor-not-allowed"),floating:S("absolute left-3 transition-all duration-200 pointer-events-none","text-sm font-medium leading-none",N||t?"top-2 text-xs text-muted-foreground":"top-1/2 -translate-y-1/2 text-muted-foreground",u&&"text-destructive",l&&"text-muted-foreground")},L={normal:"font-normal",medium:"font-medium",semibold:"font-semibold",bold:"font-bold"},$={none:"",uppercase:"uppercase",lowercase:"lowercase",capitalize:"capitalize"},W=(d||m)&&!y&&!l?{...$e.glassHover,whileTap:d?$e.buttonPress.whileTap:void 0}:{};return s==="floating"?r.jsxs(v.motion.label,{ref:I,className:S(k[s],j.text,L[w],$[g],e),...E,...W,transition:transitions.spring,...R,children:[N?b:x,o&&r.jsx("span",{className:"text-destructive ml-1",children:"*"})]}):r.jsxs(v.motion.label,{ref:I,className:S(k[s],j.text,L[w],$[g],(d||m)&&!l&&"cursor-pointer",e),...E,...W,transition:transitions.spring,...R,children:[f&&h==="left"&&r.jsx(v.motion.div,{className:S("flex items-center mr-2",j.icon,u&&"text-destructive",l&&"text-muted-foreground"),...!y&&$e.pulse,children:r.jsx(f,{className:S(j.icon)})}),r.jsxs("span",{className:"select-none",children:[t,o&&r.jsx("span",{className:"text-destructive ml-1",children:"*"})]}),f&&h==="right"&&r.jsx(v.motion.div,{className:S("flex items-center ml-2",j.icon,u&&"text-destructive",l&&"text-muted-foreground"),...!y&&$e.pulse,children:r.jsx(f,{className:S(j.icon)})})]})}),yu=c.forwardRef(({className:e,children:t,name:s,description:n,showOptional:a=!1,optionalText:i="Optional",required:o=!1,...l},u)=>r.jsxs("div",{className:"space-y-1",children:[r.jsxs(Rt,{ref:u,htmlFor:s,required:o,className:S("flex items-center justify-between",e),...l,children:[r.jsx("span",{children:t}),!o&&a&&r.jsx("span",{className:"text-xs text-muted-foreground font-normal",children:i})]}),n&&r.jsx("p",{className:"text-sm text-muted-foreground",children:n})]})),bu=c.forwardRef(({className:e,children:t,field:s,help:n,tooltip:a,...i},o)=>r.jsxs("div",{className:"space-y-1",children:[r.jsxs(Rt,{ref:o,htmlFor:s,className:S("flex items-center gap-2",e),...i,children:[t,a&&r.jsx("span",{className:"text-xs text-muted-foreground cursor-help",title:a,children:"?"})]}),n&&r.jsx("p",{className:"text-xs text-muted-foreground",children:n})]}));Rt.displayName="LuminarLabel";yu.displayName="LuminarFormLabel";bu.displayName="LuminarFieldLabel";const xu=(e,t)=>{const s=t.toLowerCase();return e.label.toLowerCase().includes(s)||(e.description?.toLowerCase().includes(s)??!1)},kr=c.forwardRef(({options:e,value:t,onChange:s,onSearch:n,placeholder:a="Search...",multiple:i=!1,clearable:o=!0,searchable:l=!0,loading:u=!1,loadingText:d="Loading...",emptyText:m="No results found",creatable:p=!1,onCreate:y,createText:w=k=>`Create "${k}"`,maxHeight:g=300,glass:f=!0,animated:h=!0,disabled:b=!1,groupBy:x=!1,filterFunction:N=xu,renderOption:R,className:I,...j},E)=>{const[k,L]=c.useState(!1),[$,W]=c.useState(""),[D,V]=c.useState(-1),[z,O]=c.useState(new Set(Array.isArray(t)?t:t?[t]:[])),M=c.useRef(null),K=c.useRef(null),U=c.useRef([]),ee=c.useMemo(()=>!$||!l?e:e.filter(H=>N(H,$)),[e,$,l,N]),Y=c.useMemo(()=>x?ee.reduce((H,Z)=>{const ue=Z.group||"";return H[ue]||(H[ue]=[]),H[ue].push(Z),H},{}):{"":ee},[ee,x]),se=c.useMemo(()=>z.size===0?"":i?`${z.size} selected`:e.find(Z=>Z.value===Array.from(z)[0])?.label||"",[z,e,i]);c.useEffect(()=>{const H=Z=>{M.current&&!M.current.contains(Z.target)&&K.current&&!K.current.contains(Z.target)&&L(!1)};return k&&document.addEventListener("mousedown",H),()=>{document.removeEventListener("mousedown",H)}},[k]),c.useEffect(()=>{const H=Z=>{if(k)switch(Z.key){case"ArrowDown":Z.preventDefault(),V(ue=>{const J=ue+1;return J>=ee.length?0:J});break;case"ArrowUp":Z.preventDefault(),V(ue=>{const J=ue-1;return J<0?ee.length-1:J});break;case"Enter":Z.preventDefault(),D>=0&&D<ee.length?G(ee[D]):p&&$&&y&&P();break;case"Escape":Z.preventDefault(),L(!1);break}};return document.addEventListener("keydown",H),()=>document.removeEventListener("keydown",H)},[k,D,ee,p,$]),c.useEffect(()=>{D>=0&&U.current[D]&&U.current[D]?.scrollIntoView({block:"nearest",behavior:"smooth"})},[D]);const G=c.useCallback(H=>{if(H.disabled)return;let Z;i?(Z=new Set(z),Z.has(H.value)?Z.delete(H.value):Z.add(H.value)):(Z=new Set([H.value]),L(!1)),O(Z),s?.(i?Array.from(Z):Array.from(Z)[0]||""),W("")},[z,i,s]),P=c.useCallback(()=>{p&&$&&y&&(y($),i||L(!1),W(""))},[p,$,y,i]),B=c.useCallback(()=>{O(new Set),s?.(i?[]:""),W("")},[i,s]),_=c.useCallback(H=>{W(H),n?.(H),V(-1),k||L(!0)},[n]),te=H=>z.has(H.value);return r.jsxs("div",{ref:E,className:S("relative",I),...j,children:[r.jsxs("div",{className:"relative",children:[r.jsx(Le,{ref:M,value:l?$:se,onChange:H=>_(H.target.value),placeholder:a,disabled:b,readOnly:!l,onClick:()=>!b&&L(!k),glass:f,className:"pr-20"}),r.jsxs("div",{className:"absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1",children:[u&&r.jsx(F.Loader2,{className:"w-4 h-4 animate-spin"}),o&&z.size>0&&!b&&r.jsx("button",{onClick:H=>{H.stopPropagation(),B()},className:"p-1 hover:bg-white/10 rounded transition-colors",children:r.jsx(F.X,{className:"w-4 h-4"})}),r.jsx(F.ChevronDown,{className:S("w-4 h-4 transition-transform",k&&"rotate-180")})]})]}),r.jsx(v.AnimatePresence,{children:k&&!b&&r.jsx(v.motion.div,{ref:K,initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.2},className:S("absolute z-50 mt-2 w-full rounded-xl shadow-2xl overflow-hidden",f&&"backdrop-blur-md bg-white/5 border border-white/20",!f&&"bg-gray-900 border border-gray-800"),style:{maxHeight:g},children:r.jsx("div",{className:"overflow-y-auto",children:u?r.jsx("div",{className:"p-4 text-center text-gray-400",children:d}):ee.length===0&&!p?r.jsx("div",{className:"p-4 text-center text-gray-400",children:m}):r.jsxs(r.Fragment,{children:[Object.entries(Y).map(([H,Z])=>r.jsxs("div",{children:[H&&x&&r.jsx("div",{className:"px-3 py-2 text-xs font-medium text-gray-400 uppercase",children:H}),Z.map((ue,J)=>{const oe=ee.indexOf(ue),pe=te(ue);return r.jsx(v.motion.div,{ref:q=>{U.current[oe]=q},whileHover:{backgroundColor:"rgba(255, 255, 255, 0.05)"},onClick:()=>G(ue),className:S("flex items-center px-3 py-2 cursor-pointer transition-colors",D===oe&&"bg-white/10",pe&&"bg-blue-500/20",ue.disabled&&"opacity-50 cursor-not-allowed"),children:R?R(ue,pe):r.jsxs(r.Fragment,{children:[i&&r.jsx("div",{className:S("w-4 h-4 rounded border mr-3 flex items-center justify-center",pe?"bg-blue-500 border-blue-500":"border-white/40"),children:pe&&r.jsx(F.Check,{className:"w-3 h-3"})}),ue.icon&&r.jsx(ue.icon,{className:"w-4 h-4 mr-3 flex-shrink-0"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"font-medium",children:ue.label}),ue.description&&r.jsx("div",{className:"text-xs text-gray-400",children:ue.description})]}),!i&&pe&&r.jsx(F.Check,{className:"w-4 h-4 ml-3 text-blue-400"})]})},ue.value)})]},H)),p&&$&&!ee.some(H=>H.label===$)&&r.jsx(v.motion.div,{whileHover:{backgroundColor:"rgba(255, 255, 255, 0.05)"},onClick:P,className:S("flex items-center px-3 py-2 cursor-pointer transition-colors border-t border-white/10",D===ee.length&&"bg-white/10"),children:r.jsx("div",{className:"text-blue-400",children:w($)})})]})})})}),i&&z.size>0&&r.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:Array.from(z).map(H=>{const Z=e.find(ue=>ue.value===H);return Z?r.jsxs(Nt,{variant:"info",className:"flex items-center gap-1",children:[Z.label,r.jsx("button",{onClick:()=>G(Z),className:"ml-1 hover:bg-white/20 rounded p-0.5",children:r.jsx(F.X,{className:"w-3 h-3"})})]},H):null})})]})});kr.displayName="LuminarAutocomplete";const Pa=c.createContext(void 0),Et=()=>{const e=c.useContext(Pa);if(!e)throw new Error("Combobox components must be used within Combobox");return e},Nr=({value:e="",onValueChange:t,children:s,open:n,onOpenChange:a})=>{const[i,o]=c.useState(!1),[l,u]=c.useState(e),[d,m]=c.useState(""),[p,y]=c.useState([]),w=n!==void 0&&a!==void 0,h={open:w?n:i,setOpen:b=>{w?a(b):o(b)},value:l,setValue:b=>{u(b),t?.(b)},search:d,setSearch:m,options:p};return c.useEffect(()=>{y((x=>{const N=[],R=I=>{if(c.isValidElement(I))if(I.type===La){const j=I.props;N.push({value:j.value,label:j.children||j.value,disabled:j.disabled,keywords:j.keywords})}else I.props?.children&&c.Children.forEach(I.props.children,R)};return c.Children.forEach(x,R),N})(s))},[s]),r.jsx(Pa.Provider,{value:h,children:s})},wu=c.forwardRef(({className:e,variant:t="input",size:s=Ae.size,glassIntensity:n="medium",colorTheme:a="neutral",placeholder:i="Select option...",asChild:o=!1,...l},u)=>{const{open:d,setOpen:m,value:p,options:y}=Et(),w=a!=="neutral"?getGlassClasses(t,{intensity:n}):getGlassClasses(t,{intensity:n,depth:"surface",animated:!0,interactive:!0});componentSizes[s];const g=y.find(f=>f.value===p);return o?r.jsx("div",{onClick:()=>m(!d),children:l.children}):r.jsxs(We,{ref:u,variant:"outline",size:s,role:"combobox","aria-expanded":d,className:S("w-full justify-between",w,!g&&"text-muted-foreground",e),onClick:()=>m(!d),...l,children:[g?g.label:i,r.jsx(F.ChevronsUpDown,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),vu=c.forwardRef(({className:e,variant:t="modal",size:s=Ae.size,glassIntensity:n="medium",colorTheme:a="neutral",animation:i="slideDown",disableAnimation:o=!1,align:l="start",side:u="bottom",children:d,...m},p)=>{const{open:y,setOpen:w}=Et(),g=c.useRef(null),f=a!=="neutral"?getGlassClasses(t,{intensity:n}):getGlassClasses(t,{intensity:n,depth:"floating",animated:!o,interactive:!0}),h=o?{}:animationPresets[i];return c.useEffect(()=>{const b=x=>{g.current&&!g.current.contains(x.target)&&w(!1)};return y&&document.addEventListener("mousedown",b),()=>{document.removeEventListener("mousedown",b)}},[y,w]),r.jsx(v.AnimatePresence,{children:y&&r.jsx(v.motion.div,{ref:g,className:S("absolute z-50 w-full min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",f,e),...h,transition:transitions.spring,...m,children:d})})}),ju=c.forwardRef(({className:e,placeholder:t="Search...",variant:s="input",size:n=Ae.size,glassIntensity:a="light",colorTheme:i="neutral",...o},l)=>{const{search:u,setSearch:d}=Et();return r.jsxs("div",{className:"flex items-center border-b px-3 border-white/10",children:[r.jsx(F.Search,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),r.jsx("input",{ref:l,value:u,onChange:m=>d(m.target.value),placeholder:t,className:S("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",e),...o})]})}),Cu=c.forwardRef(({className:e,...t},s)=>{const{search:n,options:a}=Et();return a.filter(o=>{if(!n)return!0;const l=n.toLowerCase(),u=o.label.toLowerCase().includes(l),d=o.value.toLowerCase().includes(l),m=o.keywords?.some(p=>p.toLowerCase().includes(l));return u||d||m}).length>0?null:r.jsx("div",{ref:s,className:S("py-6 text-center text-sm text-muted-foreground",e),...t})}),ku=c.forwardRef(({className:e,heading:t,children:s,...n},a)=>r.jsxs("div",{ref:a,className:S("overflow-hidden p-1",e),...n,children:[t&&r.jsx("div",{className:"px-2 py-1.5 text-xs font-medium text-muted-foreground",children:t}),s]})),La=c.forwardRef(({className:e,value:t,disabled:s=!1,keywords:n=[],onSelect:a,children:i,...o},l)=>{const{value:u,setValue:d,setOpen:m,search:p}=Et(),y=p.toLowerCase(),w=i?.toString().toLowerCase().includes(y),g=t.toLowerCase().includes(y),f=n.some(N=>N.toLowerCase().includes(y)),h=!p||w||g||f,b=u===t;if(!h)return null;const x=()=>{s||(d(t),m(!1),a?.(t))};return r.jsxs(v.motion.div,{ref:l,role:"option","aria-selected":b,className:S("relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors","hover:bg-white/10 focus:bg-white/10 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",b&&"bg-white/10",s&&"opacity-50 cursor-not-allowed",e),onClick:x,...!s&&$e.buttonPress,...o,children:[r.jsx(F.Check,{className:S("mr-2 h-4 w-4",b?"opacity-100":"opacity-0")}),i]})}),Nu=c.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:S("my-1 h-px bg-white/10",e),...t}));Nr.displayName="LuminarCombobox";wu.displayName="LuminarComboboxTrigger";vu.displayName="LuminarComboboxContent";ju.displayName="LuminarComboboxInput";Cu.displayName="LuminarComboboxEmpty";ku.displayName="LuminarComboboxGroup";La.displayName="LuminarComboboxItem";Nu.displayName="LuminarComboboxSeparator";const Su=(e,t="MM/DD/YYYY")=>{const s=m=>m.toString().padStart(2,"0"),n=s(e.getDate()),a=s(e.getMonth()+1),i=e.getFullYear(),o=e.getHours(),l=s(e.getMinutes()),u=o>=12?"PM":"AM",d=o%12||12;return t.replace("DD",n).replace("MM",a).replace("YYYY",i.toString()).replace("YY",i.toString().slice(-2)).replace("HH",s(o)).replace("hh",s(d)).replace("mm",l).replace("A",u)},Tu=e=>new Date(e.getFullYear(),e.getMonth()+1,0).getDate(),Ru=e=>new Date(e.getFullYear(),e.getMonth(),1).getDay(),qt=(e,t)=>e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear(),Cn=(e,t,s,n)=>!!(t&&e<t||s&&e>s||n?.some(a=>qt(a,e))),Sr=c.forwardRef(({value:e,onChange:t,placeholder:s="Select date",minDate:n,maxDate:a,disabled:i=!1,disabledDates:o=[],highlightDates:l=[],format:u="MM/DD/YYYY",glass:d=!0,animated:m=!0,showTime:p=!1,timeFormat:y="12h",className:w,...g},f)=>{const[h,b]=c.useState(!1),[x,N]=c.useState(e||new Date),[R,I]=c.useState(e||null),[j,E]=c.useState(e?.getHours()||0),[k,L]=c.useState(e?.getMinutes()||0),$=c.useRef(null),W=c.useRef(null),D=["January","February","March","April","May","June","July","August","September","October","November","December"],V=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"];c.useEffect(()=>{const Y=se=>{$.current&&!$.current.contains(se.target)&&b(!1)};return h&&document.addEventListener("mousedown",Y),()=>{document.removeEventListener("mousedown",Y)}},[h]),c.useEffect(()=>{e&&(I(e),N(e),E(e.getHours()),L(e.getMinutes()))},[e]);const z=Y=>{const se=new Date(x.getFullYear(),x.getMonth(),Y,j,k);Cn(se,n,a,o)||(I(se),p||(t?.(se),b(!1)))},O=()=>{if(R){const Y=new Date(R);Y.setHours(j),Y.setMinutes(k),I(Y),t?.(Y)}},M=Y=>{const se=new Date(x);switch(Y){case"prev":se.setMonth(se.getMonth()-1);break;case"next":se.setMonth(se.getMonth()+1);break;case"prevYear":se.setFullYear(se.getFullYear()-1);break;case"nextYear":se.setFullYear(se.getFullYear()+1);break}N(se)},K=()=>{const Y=Tu(x),se=Ru(x),G=[];for(let P=0;P<se;P++)G.push(r.jsx("div",{className:"h-10"},`empty-${P}`));for(let P=1;P<=Y;P++){const B=new Date(x.getFullYear(),x.getMonth(),P),_=Cn(B,n,a,o),te=R&&qt(B,R),H=l.some(ue=>qt(ue,B)),Z=qt(B,new Date);G.push(r.jsx(v.motion.button,{whileHover:_?{}:{scale:1.1},whileTap:_?{}:{scale:.95},disabled:_,onClick:()=>z(P),className:S("h-10 rounded-lg text-sm font-medium transition-all",d&&"backdrop-blur-sm",_&&"opacity-50 cursor-not-allowed",!_&&!te&&"hover:bg-white/10",te&&"bg-blue-500 text-white",H&&!te&&"bg-blue-500/20 text-blue-400",Z&&!te&&"border border-blue-500/50"),children:P},P))}return G},U=()=>p?r.jsx("div",{className:"border-t border-white/10 p-4",children:r.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[r.jsxs("div",{className:"flex flex-col items-center",children:[r.jsx("button",{onClick:()=>{E(Y=>(Y+1)%(y==="12h"?12:24)),O()},className:"p-1 hover:bg-white/10 rounded",children:r.jsx(F.ChevronRight,{className:"w-4 h-4 rotate-[-90deg]"})}),r.jsx("input",{type:"number",value:y==="12h"?j%12||12:j,onChange:Y=>{const se=parseInt(Y.target.value)||0;E(y==="12h"?se%12+(j>=12?12:0):se%24),O()},className:"w-12 text-center bg-transparent border border-white/20 rounded",min:y==="12h"?1:0,max:y==="12h"?12:23}),r.jsx("button",{onClick:()=>{E(Y=>(Y-1+(y==="12h"?12:24))%(y==="12h"?12:24)),O()},className:"p-1 hover:bg-white/10 rounded",children:r.jsx(F.ChevronRight,{className:"w-4 h-4 rotate-90"})})]}),r.jsx("span",{className:"text-lg",children:":"}),r.jsxs("div",{className:"flex flex-col items-center",children:[r.jsx("button",{onClick:()=>{L(Y=>(Y+1)%60),O()},className:"p-1 hover:bg-white/10 rounded",children:r.jsx(F.ChevronRight,{className:"w-4 h-4 rotate-[-90deg]"})}),r.jsx("input",{type:"number",value:k.toString().padStart(2,"0"),onChange:Y=>{L((parseInt(Y.target.value)||0)%60),O()},className:"w-12 text-center bg-transparent border border-white/20 rounded",min:0,max:59}),r.jsx("button",{onClick:()=>{L(Y=>(Y-1+60)%60),O()},className:"p-1 hover:bg-white/10 rounded",children:r.jsx(F.ChevronRight,{className:"w-4 h-4 rotate-90"})})]}),y==="12h"&&r.jsx("button",{onClick:()=>{E(Y=>Y<12?Y+12:Y-12),O()},className:"px-3 py-1 text-sm bg-white/10 rounded hover:bg-white/20",children:j>=12?"PM":"AM"})]})}):null,ee=p?u.includes("hh")||u.includes("HH")?u:u+(y==="12h"?" hh:mm A":" HH:mm"):u;return r.jsxs("div",{ref:$,className:S("relative",w),...g,children:[r.jsxs("div",{className:"relative",children:[r.jsx(Le,{ref:W,value:R?Su(R,ee):"",placeholder:s,readOnly:!0,disabled:i,onClick:()=>!i&&b(!h),glass:d,className:"cursor-pointer pr-10"}),r.jsx(Qe,{icon:F.Calendar,className:"absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none",size:16})]}),r.jsx(v.AnimatePresence,{children:h&&!i&&r.jsxs(v.motion.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.2},className:S("absolute z-50 mt-2 p-4 rounded-xl shadow-2xl",d&&"backdrop-blur-md bg-white/5 border border-white/20",!d&&"bg-gray-900 border border-gray-800"),children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx("button",{onClick:()=>M("prevYear"),className:"p-1 hover:bg-white/10 rounded","aria-label":"Previous year",children:r.jsx(F.ChevronsLeft,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>M("prev"),className:"p-1 hover:bg-white/10 rounded","aria-label":"Previous month",children:r.jsx(F.ChevronLeft,{className:"w-4 h-4"})})]}),r.jsxs("div",{className:"text-sm font-medium",children:[D[x.getMonth()]," ",x.getFullYear()]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx("button",{onClick:()=>M("next"),className:"p-1 hover:bg-white/10 rounded","aria-label":"Next month",children:r.jsx(F.ChevronRight,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>M("nextYear"),className:"p-1 hover:bg-white/10 rounded","aria-label":"Next year",children:r.jsx(F.ChevronsRight,{className:"w-4 h-4"})})]})]}),r.jsx("div",{className:"grid grid-cols-7 gap-1 mb-2",children:V.map(Y=>r.jsx("div",{className:"h-8 flex items-center justify-center text-xs font-medium text-gray-400",children:Y},Y))}),r.jsx("div",{className:"grid grid-cols-7 gap-1 w-[280px]",children:K()}),U(),p&&r.jsxs("div",{className:"flex justify-end space-x-2 mt-4 pt-4 border-t border-white/10",children:[r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>b(!1),children:"Cancel"}),r.jsx(le,{variant:"default",size:"sm",onClick:()=>{R&&t?.(R),b(!1)},children:"Apply"})]})]})})]})});Sr.displayName="LuminarDatePicker";const As=(e,t="MM/DD/YYYY")=>{const s=o=>o.toString().padStart(2,"0"),n=s(e.getDate()),a=s(e.getMonth()+1),i=e.getFullYear();return t.replace("DD",n).replace("MM",a).replace("YYYY",i.toString()).replace("YY",i.toString().slice(-2))},Eu=e=>new Date(e.getFullYear(),e.getMonth()+1,0).getDate(),Au=e=>new Date(e.getFullYear(),e.getMonth(),1).getDay(),at=(e,t)=>e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear(),Pu=(e,t)=>!t.from||!t.to?!1:e>=t.from&&e<=t.to,Lu=(e,t,s,n)=>t&&e<t||s&&e>s?!0:n?typeof n=="function"?n(e):n.some(a=>at(a,e)):!1,Tr=c.forwardRef(({value:e={from:null,to:null},onChange:t,placeholder:s="Select date range",minDate:n,maxDate:a,disabled:i=!1,disabledDates:o,format:l="MM/DD/YYYY",glass:u=!0,animated:d=!0,presets:m=[{label:"Last 7 days",range:{from:new Date(Date.now()-7*24*60*60*1e3),to:new Date}},{label:"Last 30 days",range:{from:new Date(Date.now()-30*24*60*60*1e3),to:new Date}},{label:"This month",range:{from:new Date(new Date().getFullYear(),new Date().getMonth(),1),to:new Date}},{label:"Last month",range:{from:new Date(new Date().getFullYear(),new Date().getMonth()-1,1),to:new Date(new Date().getFullYear(),new Date().getMonth(),0)}}],className:p,...y},w)=>{const[g,f]=c.useState(!1),[h,b]=c.useState(new Date),[x,N]=c.useState(new Date(new Date().getFullYear(),new Date().getMonth()+1)),[R,I]=c.useState(e),[j,E]=c.useState(null),k=c.useRef(null),L=["January","February","March","April","May","June","July","August","September","October","November","December"],$=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"];c.useEffect(()=>{const M=K=>{k.current&&!k.current.contains(K.target)&&f(!1)};return g&&document.addEventListener("mousedown",M),()=>{document.removeEventListener("mousedown",M)}},[g]),c.useEffect(()=>{I(e)},[e]);const W=M=>{const K=new Date(M);!R.from||R.from&&R.to?I({from:K,to:null}):K<R.from?I({from:K,to:R.from}):I({from:R.from,to:K})},D=M=>{I(M.range),t?.(M.range),f(!1)},V=(M,K)=>{const U=M==="first"?b:N,ee=M==="first"?h:x,Y=new Date(ee);K==="prev"?Y.setMonth(Y.getMonth()-1):Y.setMonth(Y.getMonth()+1),U(Y),M==="first"?N(new Date(Y.getFullYear(),Y.getMonth()+1)):b(new Date(Y.getFullYear(),Y.getMonth()-1))},z=(M,K)=>{const U=Eu(M),ee=Au(M),Y=[];for(let se=0;se<ee;se++)Y.push(r.jsx("div",{className:"h-10"},`empty-${se}`));for(let se=1;se<=U;se++){const G=new Date(M.getFullYear(),M.getMonth(),se),P=Lu(G,n,a,o),B=R.from&&at(G,R.from)||R.to&&at(G,R.to),_=Pu(G,R),te=j&&R.from&&!R.to&&(G>=R.from&&G<=j||G<=R.from&&G>=j),H=at(G,new Date);Y.push(r.jsx(v.motion.button,{whileHover:P?{}:{scale:1.1},whileTap:P?{}:{scale:.95},disabled:P,onClick:()=>W(G),onMouseEnter:()=>E(G),onMouseLeave:()=>E(null),className:S("h-10 rounded-lg text-sm font-medium transition-all relative",u&&"backdrop-blur-sm",P&&"opacity-50 cursor-not-allowed",!P&&!B&&!_&&"hover:bg-white/10",B&&"bg-blue-500 text-white z-10",_&&!B&&"bg-blue-500/20",te&&"bg-blue-500/10",H&&!B&&"border border-blue-500/50",_&&R.from&&at(G,R.from)&&"rounded-r-none",_&&R.to&&at(G,R.to)&&"rounded-l-none"),children:se},se))}return Y},O=R.from&&R.to?`${As(R.from,l)} - ${As(R.to,l)}`:R.from?`${As(R.from,l)} - Select end date`:"";return r.jsxs("div",{ref:k,className:S("relative",p),...y,children:[r.jsxs("div",{ref:w,className:"relative",children:[r.jsx(Le,{value:O,placeholder:s,readOnly:!0,disabled:i,onClick:()=>!i&&f(!g),glass:u,className:"cursor-pointer pr-10"}),r.jsx(Qe,{icon:F.Calendar,className:"absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none",size:16})]}),r.jsx(v.AnimatePresence,{children:g&&!i&&r.jsxs(v.motion.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.2},className:S("absolute z-50 mt-2 p-4 rounded-xl shadow-2xl",u&&"backdrop-blur-md bg-white/5 border border-white/20",!u&&"bg-gray-900 border border-gray-800"),children:[r.jsxs("div",{className:"flex gap-4",children:[m.length>0&&r.jsxs("div",{className:"border-r border-white/10 pr-4",children:[r.jsx("h4",{className:"text-sm font-medium mb-3 text-gray-400",children:"Quick Select"}),r.jsx("div",{className:"space-y-1",children:m.map((M,K)=>r.jsx("button",{onClick:()=>D(M),className:S("block w-full text-left px-3 py-2 text-sm rounded-lg transition-colors","hover:bg-white/10"),children:M.label},K))})]}),r.jsxs("div",{className:"flex gap-4",children:[r.jsxs("div",{children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("button",{onClick:()=>V("first","prev"),className:"p-1 hover:bg-white/10 rounded","aria-label":"Previous month",children:r.jsx(F.ChevronLeft,{className:"w-4 h-4"})}),r.jsxs("div",{className:"text-sm font-medium",children:[L[h.getMonth()]," ",h.getFullYear()]}),r.jsx("div",{className:"w-6"})]}),r.jsx("div",{className:"grid grid-cols-7 gap-1 mb-2",children:$.map(M=>r.jsx("div",{className:"h-8 flex items-center justify-center text-xs font-medium text-gray-400",children:M},M))}),r.jsx("div",{className:"grid grid-cols-7 gap-1 w-[280px]",children:z(h)})]}),r.jsxs("div",{children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("div",{className:"w-6"}),r.jsxs("div",{className:"text-sm font-medium",children:[L[x.getMonth()]," ",x.getFullYear()]}),r.jsx("button",{onClick:()=>V("second","next"),className:"p-1 hover:bg-white/10 rounded","aria-label":"Next month",children:r.jsx(F.ChevronRight,{className:"w-4 h-4"})})]}),r.jsx("div",{className:"grid grid-cols-7 gap-1 mb-2",children:$.map(M=>r.jsx("div",{className:"h-8 flex items-center justify-center text-xs font-medium text-gray-400",children:M},M))}),r.jsx("div",{className:"grid grid-cols-7 gap-1 w-[280px]",children:z(x)})]})]})]}),r.jsxs("div",{className:"flex justify-end space-x-2 mt-4 pt-4 border-t border-white/10",children:[r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>{I({from:null,to:null}),t?.({from:null,to:null})},children:"Clear"}),r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>f(!1),children:"Cancel"}),r.jsx(le,{variant:"primary",size:"sm",onClick:()=>{R.from&&R.to&&(t?.(R),f(!1))},disabled:!R.from||!R.to,children:"Apply"})]})]})})]})});Tr.displayName="LuminarDateRangePicker";const Iu=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FECA57","#FF9FF3","#FF6B9D","#C44569","#F8B500","#00D9FF","#7B68EE","#2ECC71","#3498DB","#9B59B6","#1ABC9C","#E74C3C","#F39C12","#D35400","#000000","#2C3E50","#7F8C8D","#BDC3C7","#ECF0F1","#FFFFFF"],Ps=e=>{const t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:null},Ou=(e,t,s)=>"#"+((1<<24)+(e<<16)+(t<<8)+s).toString(16).slice(1),Ls=(e,t,s)=>{e/=255,t/=255,s/=255;const n=Math.max(e,t,s),a=Math.min(e,t,s);let i=0,o=0;const l=(n+a)/2;if(n!==a){const u=n-a;switch(o=l>.5?u/(2-n-a):u/(n+a),n){case e:i=((t-s)/u+(t<s?6:0))/6;break;case t:i=((s-e)/u+2)/6;break;case s:i=((e-t)/u+4)/6;break}}return{h:Math.round(i*360),s:Math.round(o*100),l:Math.round(l*100)}},Du=(e,t,s)=>{e/=360,t/=100,s/=100;let n,a,i;if(t===0)n=a=i=s;else{const o=(d,m,p)=>(p<0&&(p+=1),p>1&&(p-=1),p<.16666666666666666?d+(m-d)*6*p:p<.5?m:p<.6666666666666666?d+(m-d)*(.6666666666666666-p)*6:d),l=s<.5?s*(1+t):s+t-s*t,u=2*s-l;n=o(u,l,e+1/3),a=o(u,l,e),i=o(u,l,e-1/3)}return{r:Math.round(n*255),g:Math.round(a*255),b:Math.round(i*255)}},Rr=c.forwardRef(({value:e="#3B82F6",onChange:t,format:s="hex",showAlpha:n=!1,presetColors:a=Iu,presets:i,glass:o=!0,animated:l=!0,disabled:u=!1,placeholder:d="Select color",className:m,...p},y)=>{const[w,g]=c.useState(!1),[f,h]=c.useState(e),[b,x]=c.useState(0),[N,R]=c.useState(100),[I,j]=c.useState(50),[E,k]=c.useState(100),[L,$]=c.useState(e),W=c.useRef(null),D=c.useRef(null),V=c.useRef(null),z=c.useRef(null),O=c.useRef(null);c.useEffect(()=>{const G=Ps(e);if(G){const P=Ls(G.r,G.g,G.b);x(P.h),R(P.s),j(P.l)}h(e),$(e)},[e]),c.useEffect(()=>{const G=Du(b,N,I),P=Ou(G.r,G.g,G.b);h(P),$(P)},[b,N,I]),c.useEffect(()=>{const G=P=>{W.current&&!W.current.contains(P.target)&&g(!1)};return w&&document.addEventListener("mousedown",G),()=>{document.removeEventListener("mousedown",G)}},[w]);const M=c.useCallback(G=>{if(!D.current)return;const P=D.current.getBoundingClientRect(),B=Math.max(0,Math.min(1,(G.clientX-P.left)/P.width)),_=Math.max(0,Math.min(1,(G.clientY-P.top)/P.height));R(Math.round(B*100)),j(Math.round((1-_)*100))},[]),K=c.useCallback(G=>{if(!V.current)return;const P=V.current.getBoundingClientRect(),B=Math.max(0,Math.min(1,(G.clientX-P.left)/P.width));x(Math.round(B*360))},[]),U=c.useCallback(G=>{if(!z.current)return;const P=z.current.getBoundingClientRect(),B=Math.max(0,Math.min(1,(G.clientX-P.left)/P.width));k(Math.round(B*100))},[]),ee=G=>{const P=Ps(G);if(P){const B=Ls(P.r,P.g,P.b);x(B.h),R(B.s),j(B.l),h(G),t?.(G)}},Y=()=>{t?.(f),g(!1)},se=G=>{if(s==="hex")return G;const P=Ps(G);if(!P)return G;if(s==="rgb")return n?`rgba(${P.r}, ${P.g}, ${P.b}, ${E/100})`:`rgb(${P.r}, ${P.g}, ${P.b})`;if(s==="hsl"){const B=Ls(P.r,P.g,P.b);return n?`hsla(${B.h}, ${B.s}%, ${B.l}%, ${E/100})`:`hsl(${B.h}, ${B.s}%, ${B.l}%)`}return G};return r.jsxs("div",{ref:W,className:S("relative",m),...p,children:[r.jsxs("div",{className:"relative",children:[r.jsx(Le,{ref:O,value:se(L),placeholder:d,disabled:u,onClick:()=>!u&&g(!w),onChange:G=>$(G.target.value),glass:o,className:"cursor-pointer pl-12"}),r.jsx("div",{className:"absolute left-3 top-1/2 -translate-y-1/2 w-6 h-6 rounded border-2 border-white/20",style:{backgroundColor:f}})]}),r.jsx(v.AnimatePresence,{children:w&&!u&&r.jsxs(v.motion.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.2},className:S("absolute z-50 mt-2 p-4 rounded-xl shadow-2xl",o&&"glass-text-safe glass-card-safe border border-white/20",!o&&"bg-gray-900 border border-gray-800"),style:{width:"320px"},children:[r.jsx("div",{className:"mb-4",children:r.jsx("div",{ref:D,className:"relative h-48 rounded-lg cursor-crosshair overflow-hidden",style:{background:`linear-gradient(to bottom, transparent, black),
                                linear-gradient(to right, white, hsl(${b}, 100%, 50%))`},onClick:M,children:r.jsx("div",{className:"absolute w-4 h-4 rounded-full border-2 border-white shadow-lg -translate-x-1/2 -translate-y-1/2",style:{left:`${N}%`,top:`${100-I}%`,backgroundColor:f}})})}),r.jsx("div",{className:"mb-4",children:r.jsx("div",{ref:V,className:"relative h-3 rounded-full cursor-pointer",style:{background:`linear-gradient(to right, 
                      hsl(0, 100%, 50%), 
                      hsl(60, 100%, 50%), 
                      hsl(120, 100%, 50%), 
                      hsl(180, 100%, 50%), 
                      hsl(240, 100%, 50%), 
                      hsl(300, 100%, 50%), 
                      hsl(360, 100%, 50%))`},onClick:K,children:r.jsx("div",{className:"absolute top-1/2 w-4 h-4 rounded-full border-2 border-white shadow-lg -translate-x-1/2 -translate-y-1/2",style:{left:`${b/360*100}%`,backgroundColor:`hsl(${b}, 100%, 50%)`}})})}),n&&r.jsx("div",{className:"mb-4",children:r.jsxs("div",{ref:z,className:"relative h-3 rounded-full cursor-pointer",style:{backgroundImage:`linear-gradient(to right, transparent, ${f})`,backgroundSize:"100% 100%",backgroundPosition:"0 0"},onClick:U,children:[r.jsx("div",{className:"absolute inset-0 rounded-full",style:{background:"repeating-conic-gradient(#808080 0% 25%, transparent 0% 50%) 50% / 10px 10px",opacity:.3}}),r.jsx("div",{className:"absolute top-1/2 w-4 h-4 rounded-full border-2 border-white shadow-lg -translate-x-1/2 -translate-y-1/2",style:{left:`${E}%`,backgroundColor:f,opacity:E/100}})]})}),r.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[r.jsx("div",{className:"flex-1",children:r.jsx(Le,{value:se(f),onChange:G=>$(G.target.value),size:"sm",glass:o})}),r.jsx("button",{className:"p-2 rounded-lg hover:bg-white/10 transition-colors",title:"Pick color from screen",children:r.jsx(F.Pipette,{className:"w-4 h-4"})})]}),r.jsx("div",{className:"grid grid-cols-8 gap-2 mb-4",children:(i||a).map((G,P)=>r.jsx(v.motion.button,{whileHover:{scale:1.1},whileTap:{scale:.95},className:S("w-8 h-8 rounded-lg border-2 relative",f===G?"border-white":"border-transparent"),style:{backgroundColor:G},onClick:()=>ee(G),children:f===G&&r.jsx(F.Check,{className:"w-4 h-4 absolute inset-0 m-auto text-white mix-blend-difference"})},P))}),r.jsxs("div",{className:"flex justify-end space-x-2",children:[r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>g(!1),children:"Cancel"}),r.jsx(le,{variant:"default",size:"sm",onClick:Y,children:"Apply"})]})]})})]})});Rr.displayName="LuminarColorPicker";class Bt{static hexToRgb(t){const s=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return s?{r:parseInt(s[1],16),g:parseInt(s[2],16),b:parseInt(s[3],16)}:null}static rgbToHex(t,s,n){return"#"+((1<<24)+(t<<16)+(s<<8)+n).toString(16).slice(1)}static rgbToHsl(t,s,n){t/=255,s/=255,n/=255;const a=Math.max(t,s,n),i=Math.min(t,s,n);let o=0,l=0,u=(a+i)/2;if(a===i)o=l=0;else{const d=a-i;switch(l=u>.5?d/(2-a-i):d/(a+i),a){case t:o=(s-n)/d+(s<n?6:0);break;case s:o=(n-t)/d+2;break;case n:o=(t-s)/d+4;break}o/=6}return{h:Math.round(o*360),s:Math.round(l*100),l:Math.round(u*100)}}static hslToRgb(t,s,n){t/=360,s/=100,n/=100;const a=(u,d,m)=>(m<0&&(m+=1),m>1&&(m-=1),m<1/6?u+(d-u)*6*m:m<1/2?d:m<2/3?u+(d-u)*(2/3-m)*6:u);let i,o,l;if(s===0)i=o=l=n;else{const u=n<.5?n*(1+s):n+s-n*s,d=2*n-u;i=a(d,u,t+1/3),o=a(d,u,t),l=a(d,u,t-1/3)}return{r:Math.round(i*255),g:Math.round(o*255),b:Math.round(l*255)}}static getContrastRatio(t,s){const n=u=>{const d=this.hexToRgb(u);if(!d)return 0;const m=d.r/255,p=d.g/255,y=d.b/255,w=m<=.03928?m/12.92:Math.pow((m+.055)/1.055,2.4),g=p<=.03928?p/12.92:Math.pow((p+.055)/1.055,2.4),f=y<=.03928?y/12.92:Math.pow((y+.055)/1.055,2.4);return .2126*w+.7152*g+.0722*f},a=n(t),i=n(s),o=Math.max(a,i),l=Math.min(a,i);return(o+.05)/(l+.05)}static formatColor(t,s){const n=this.hexToRgb(t);if(!n)return t;switch(s){case"hex":return t;case"rgb":return`rgb(${n.r}, ${n.g}, ${n.b})`;case"hsl":const a=this.rgbToHsl(n.r,n.g,n.b);return`hsl(${a.h}, ${a.s}%, ${a.l}%)`;case"oklch":const i=this.rgbToOklch(n.r,n.g,n.b);return`oklch(${i.l.toFixed(4)} ${i.c.toFixed(4)} ${i.h.toFixed(1)})`;default:return t}}static rgbToOklch(t,s,n){const a=this.rgbToHsl(t,s,n);return{l:a.l/100,c:a.s/100,h:a.h}}}class Mu{currentTheme;presets=[];constructor(t){this.currentTheme=t||this.getDefaultTheme(),this.loadPresets()}getDefaultTheme(){return{name:"Default",colors:{background:"#ffffff",foreground:"#0a0a0a",card:"#ffffff",cardForeground:"#0a0a0a",popover:"#ffffff",popoverForeground:"#0a0a0a",primary:"#171717",primaryForeground:"#fafafa",secondary:"#f4f4f5",secondaryForeground:"#171717",muted:"#f4f4f5",mutedForeground:"#71717a",accent:"#f4f4f5",accentForeground:"#171717",destructive:"#ef4444",destructiveForeground:"#fafafa",border:"#e4e4e7",input:"#e4e4e7",ring:"#171717",chart1:"#e11d48",chart2:"#f59e0b",chart3:"#10b981",chart4:"#3b82f6",chart5:"#8b5cf6"},radius:.5,fontFamily:"system-ui",letterSpacing:0,spacing:1}}loadPresets(){this.presets=[{id:"default-light",name:"Default Light",description:"Clean and minimal light theme",category:"light",theme:this.getDefaultTheme()},{id:"dark-slate",name:"Dark Slate",description:"Professional dark theme with slate colors",category:"dark",theme:{...this.getDefaultTheme(),name:"Dark Slate",colors:{background:"#020817",foreground:"#f8fafc",card:"#020817",cardForeground:"#f8fafc",popover:"#020817",popoverForeground:"#f8fafc",primary:"#f8fafc",primaryForeground:"#0f172a",secondary:"#1e293b",secondaryForeground:"#f8fafc",muted:"#1e293b",mutedForeground:"#94a3b8",accent:"#1e293b",accentForeground:"#f8fafc",destructive:"#7f1d1d",destructiveForeground:"#f8fafc",border:"#1e293b",input:"#1e293b",ring:"#94a3b8",chart1:"#e11d48",chart2:"#f59e0b",chart3:"#10b981",chart4:"#3b82f6",chart5:"#8b5cf6"}}},{id:"purple-gradient",name:"Purple Gradient",description:"Vibrant purple and pink gradient theme",category:"custom",theme:{...this.getDefaultTheme(),name:"Purple Gradient",colors:{background:"#faf7ff",foreground:"#1a1625",card:"#ffffff",cardForeground:"#1a1625",popover:"#ffffff",popoverForeground:"#1a1625",primary:"#8b5cf6",primaryForeground:"#ffffff",secondary:"#f3f0ff",secondaryForeground:"#1a1625",muted:"#f3f0ff",mutedForeground:"#6b7280",accent:"#f3f0ff",accentForeground:"#1a1625",destructive:"#ef4444",destructiveForeground:"#ffffff",border:"#e5e7eb",input:"#e5e7eb",ring:"#8b5cf6",chart1:"#8b5cf6",chart2:"#ec4899",chart3:"#06b6d4",chart4:"#10b981",chart5:"#f59e0b"}}}]}getCurrentTheme(){return{...this.currentTheme}}setTheme(t){this.currentTheme={...t},this.applyTheme()}updateColor(t,s){this.currentTheme.colors[t]=s,this.applyTheme()}updateProperty(t,s){this.currentTheme[t]=s,this.applyTheme()}applyTheme(){const t=document.documentElement;Object.entries(this.currentTheme.colors).forEach(([s,n])=>{const a=`--${s.replace(/([A-Z])/g,"-$1").toLowerCase()}`;t.style.setProperty(a,n)}),t.style.setProperty("--radius",`${this.currentTheme.radius}rem`),t.style.setProperty("--font-family",this.currentTheme.fontFamily),t.style.setProperty("--letter-spacing",`${this.currentTheme.letterSpacing}em`),t.style.setProperty("--spacing",`${this.currentTheme.spacing}rem`)}getPresets(){return[...this.presets]}applyPreset(t){const s=this.presets.find(n=>n.id===t);s&&this.setTheme(s.theme)}exportTheme(t="css"){switch(t){case"css":return this.exportAsCss();case"json":return JSON.stringify(this.currentTheme,null,2);case"tailwind":return this.exportAsTailwind();default:return this.exportAsCss()}}exportAsCss(){return`:root {
${Object.entries(this.currentTheme.colors).map(([s,n])=>`  ${`--${s.replace(/([A-Z])/g,"-$1").toLowerCase()}`}: ${n};`).join(`
`)}
  --radius: ${this.currentTheme.radius}rem;
  --font-family: ${this.currentTheme.fontFamily};
  --letter-spacing: ${this.currentTheme.letterSpacing}em;
  --spacing: ${this.currentTheme.spacing}rem;
}`}exportAsTailwind(){const t=Object.entries(this.currentTheme.colors).reduce((s,[n,a])=>(s[n]=a,s),{});return`module.exports = {
  theme: {
    extend: {
      colors: ${JSON.stringify(t,null,6)},
      borderRadius: {
        DEFAULT: '${this.currentTheme.radius}rem',
      },
      fontFamily: {
        sans: ['${this.currentTheme.fontFamily}', 'sans-serif'],
      },
      letterSpacing: {
        DEFAULT: '${this.currentTheme.letterSpacing}em',
      },
      spacing: {
        DEFAULT: '${this.currentTheme.spacing}rem',
      },
    },
  },
}`}importTheme(t,s="json"){try{if(s==="json"){const n=JSON.parse(t);if(this.isValidThemeConfig(n))return this.setTheme(n),!0}return!1}catch{return!1}}isValidThemeConfig(t){return t&&typeof t.name=="string"&&t.colors&&typeof t.colors=="object"&&typeof t.radius=="number"&&typeof t.fontFamily=="string"}checkAccessibility(){const t=[],s=this.currentTheme.colors;return[{name:"Primary/Primary Foreground",bg:s.primary,fg:s.primaryForeground},{name:"Secondary/Secondary Foreground",bg:s.secondary,fg:s.secondaryForeground},{name:"Background/Foreground",bg:s.background,fg:s.foreground},{name:"Card/Card Foreground",bg:s.card,fg:s.cardForeground},{name:"Muted/Muted Foreground",bg:s.muted,fg:s.mutedForeground}].forEach(({name:a,bg:i,fg:o})=>{const l=Bt.getContrastRatio(i,o);let u="FAIL";l>=7?u="AAA":l>=4.5&&(u="AA"),t.push({pair:a,ratio:Math.round(l*100)/100,level:u})}),t}}new Mu;const _e={slate:["#f8fafc","#f1f5f9","#e2e8f0","#cbd5e1","#94a3b8","#64748b","#475569","#334155","#1e293b","#0f172a"],gray:["#f9fafb","#f3f4f6","#e5e7eb","#d1d5db","#9ca3af","#6b7280","#4b5563","#374151","#1f2937","#111827"],zinc:["#fafafa","#f4f4f5","#e4e4e7","#d4d4d8","#a1a1aa","#71717a","#52525b","#3f3f46","#27272a","#18181b"],red:["#fef2f2","#fee2e2","#fecaca","#fca5a5","#f87171","#ef4444","#dc2626","#b91c1c","#991b1b","#7f1d1d"],orange:["#fff7ed","#ffedd5","#fed7aa","#fdba74","#fb923c","#f97316","#ea580c","#c2410c","#9a3412","#7c2d12"],amber:["#fffbeb","#fef3c7","#fde68a","#fcd34d","#fbbf24","#f59e0b","#d97706","#b45309","#92400e","#78350f"],yellow:["#fefce8","#fef9c3","#fef08a","#fde047","#facc15","#eab308","#ca8a04","#a16207","#854d0e","#713f12"],lime:["#f7fee7","#ecfccb","#d9f99d","#bef264","#a3e635","#84cc16","#65a30d","#4d7c0f","#365314","#1a2e05"],green:["#f0fdf4","#dcfce7","#bbf7d0","#86efac","#4ade80","#22c55e","#16a34a","#15803d","#166534","#14532d"],emerald:["#ecfdf5","#d1fae5","#a7f3d0","#6ee7b7","#34d399","#10b981","#059669","#047857","#065f46","#064e3b"],teal:["#f0fdfa","#ccfbf1","#99f6e4","#5eead4","#2dd4bf","#14b8a6","#0d9488","#0f766e","#115e59","#134e4a"],cyan:["#ecfeff","#cffafe","#a5f3fc","#67e8f9","#22d3ee","#06b6d4","#0891b2","#0e7490","#155e75","#164e63"],sky:["#f0f9ff","#e0f2fe","#bae6fd","#7dd3fc","#38bdf8","#0ea5e9","#0284c7","#0369a1","#075985","#0c4a6e"],blue:["#eff6ff","#dbeafe","#bfdbfe","#93c5fd","#60a5fa","#3b82f6","#2563eb","#1d4ed8","#1e40af","#1e3a8a"],indigo:["#eef2ff","#e0e7ff","#c7d2fe","#a5b4fc","#818cf8","#6366f1","#4f46e5","#4338ca","#3730a3","#312e81"],violet:["#f5f3ff","#ede9fe","#ddd6fe","#c4b5fd","#a78bfa","#8b5cf6","#7c3aed","#6d28d9","#5b21b6","#4c1d95"],purple:["#faf5ff","#f3e8ff","#e9d5ff","#d8b4fe","#c084fc","#a855f7","#9333ea","#7e22ce","#6b21a8","#581c87"],fuchsia:["#fdf4ff","#fae8ff","#f5d0fe","#f0abfc","#e879f9","#d946ef","#c026d3","#a21caf","#86198f","#701a75"],pink:["#fdf2f8","#fce7f3","#fbcfe8","#f9a8d4","#f472b6","#ec4899","#db2777","#be185d","#9d174d","#831843"],rose:["#fff1f2","#ffe4e6","#fecdd3","#fda4af","#fb7185","#f43f5e","#e11d48","#be123c","#9f1239","#881337"]},Ia=({value:e,onChange:t,label:s,className:n,showFormats:a=!0,showPresets:i=!0,presetColors:o,disabled:l=!1})=>{const[u,d]=c.useState(!1),[m,p]=c.useState("hex"),[y,w]=c.useState(null),g=c.useMemo(()=>e?{hex:e,rgb:Bt.formatColor(e,"rgb"),hsl:Bt.formatColor(e,"hsl"),oklch:Bt.formatColor(e,"oklch")}:{},[e]),f=c.useCallback(x=>{t(x)},[t]),h=c.useCallback(async x=>{const N=g[x];if(N)try{await navigator.clipboard.writeText(N),w(x),setTimeout(()=>w(null),2e3)}catch(R){console.error("Failed to copy color:",R)}},[g]),b=c.useMemo(()=>o||[..._e.slate.slice(3,8),..._e.red.slice(3,8),..._e.orange.slice(3,8),..._e.yellow.slice(3,8),..._e.green.slice(3,8),..._e.blue.slice(3,8),..._e.purple.slice(3,8),..._e.pink.slice(3,8)],[o]);return c.useMemo(()=>Object.entries(_e).flatMap(([x,N])=>N.map((R,I)=>({name:`${x}-${(I+1)*100}`,value:R,category:x}))),[]),r.jsxs("div",{className:S("relative",n),children:[s&&r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:s}),r.jsxs("button",{type:"button",onClick:()=>!l&&d(!u),disabled:l,className:S("relative flex items-center gap-3 w-full p-3 rounded-lg border transition-all","border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800","hover:border-gray-300 dark:hover:border-gray-600","focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",l&&"opacity-50 cursor-not-allowed"),children:[r.jsx("div",{className:"w-8 h-8 rounded-md border border-gray-200 dark:border-gray-600 shadow-sm",style:{backgroundColor:e}}),r.jsxs("div",{className:"flex-1 text-left",children:[r.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.toUpperCase()}),a&&r.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:g[m]})]}),r.jsx(F.Palette,{className:"w-4 h-4 text-gray-500 dark:text-gray-400"})]}),u&&r.jsxs(v.motion.div,{initial:{opacity:0,y:8,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:8,scale:.95},className:"absolute top-full left-0 mt-2 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl z-50 min-w-[320px]",children:[r.jsxs("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2",children:"Color Picker"}),r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("input",{type:"color",value:e,onChange:x=>f(x.target.value),className:"w-12 h-12 rounded border border-gray-200 dark:border-gray-600 cursor-pointer"}),r.jsx("input",{type:"text",value:e,onChange:x=>f(x.target.value),className:"flex-1 px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"#000000"})]})]}),a&&r.jsxs("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2",children:"Color Formats"}),r.jsx("div",{className:"space-y-2",children:Object.entries(g).map(([x,N])=>r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("button",{onClick:()=>p(x),className:S("px-2 py-1 text-xs rounded border transition-colors",m===x?"bg-purple-100 dark:bg-purple-900 border-purple-300 dark:border-purple-700 text-purple-700 dark:text-purple-300":"bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-400"),children:x.toUpperCase()}),r.jsx("code",{className:"flex-1 px-2 py-1 text-xs bg-gray-50 dark:bg-gray-700 rounded font-mono text-gray-800 dark:text-gray-200",children:N}),r.jsx("button",{onClick:()=>h(x),className:"p-1 hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors",children:y===x?r.jsx(F.Check,{className:"w-3 h-3 text-green-500"}):r.jsx(F.Copy,{className:"w-3 h-3 text-gray-500 dark:text-gray-400"})})]},x))})]}),i&&r.jsxs("div",{className:"mb-4",children:[r.jsx("label",{className:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2",children:"Quick Colors"}),r.jsx("div",{className:"grid grid-cols-8 gap-1 mb-3",children:b.map((x,N)=>r.jsx("button",{onClick:()=>f(x),className:S("w-8 h-8 rounded border-2 transition-all hover:scale-110",e===x?"border-purple-500 shadow-lg":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"),style:{backgroundColor:x},title:x},`preset-${N}`))})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2",children:"Tailwind Colors"}),r.jsx("div",{className:"max-h-32 overflow-y-auto",children:Object.entries(_e).map(([x,N])=>r.jsxs("div",{className:"mb-2",children:[r.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mb-1 capitalize",children:x}),r.jsx("div",{className:"grid grid-cols-10 gap-1",children:N.map((R,I)=>r.jsx("button",{onClick:()=>f(R),className:S("w-6 h-6 rounded border transition-all hover:scale-110",e===R?"border-purple-500 shadow-lg border-2":"border-gray-200 dark:border-gray-600"),style:{backgroundColor:R},title:`${x}-${(I+1)*100}: ${R}`},`${x}-${I}`))})]},x))})]}),r.jsx("div",{className:"mt-4 pt-3 border-t border-gray-200 dark:border-gray-700",children:r.jsx("button",{onClick:()=>d(!1),className:"w-full px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors",children:"Done"})})]}),u&&r.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>d(!1)})]})},Er=c.forwardRef(({accept:e,multiple:t=!1,maxSize:s=10*1024*1024,maxFiles:n=5,files:a,onFilesChange:i,onFileRemove:o,disabled:l=!1,glass:u=!0,animated:d=!0,showProgress:m=!0,className:p,...y},w)=>{const[g,f]=c.useState([]),h=a||g,[b,x]=c.useState(!1),N=c.useRef(null),R=O=>{const M=O.split(".").pop()?.toLowerCase();return["jpg","jpeg","png","gif","webp"].includes(M||"")?F.Image:["mp4","avi","mov","wmv"].includes(M||"")?F.Video:["mp3","wav","ogg","flac"].includes(M||"")?F.Music:["txt","doc","docx","pdf"].includes(M||"")?F.FileText:F.File},I=O=>{if(O===0)return"0 Bytes";const M=1024,K=["Bytes","KB","MB","GB"],U=Math.floor(Math.log(O)/Math.log(M));return parseFloat((O/Math.pow(M,U)).toFixed(2))+" "+K[U]},j=O=>{if(O.size>s)return`File size exceeds ${I(s)}`;if(e){const M=e.split(",").map(Y=>Y.trim()),K=O.type,U=O.name;if(!M.some(Y=>{if(Y.startsWith("."))return U.toLowerCase().endsWith(Y.toLowerCase());if(Y.includes("*")){const se=Y.split("/")[0];return K.startsWith(se)}return K===Y}))return`File type not accepted. Accepted types: ${e}`}},E=c.useCallback(O=>{const M=[],K=Array.from(O);for(const ee of K){if(h.length+M.length>=n)break;const Y=j(ee),se={file:ee,id:Math.random().toString(36).substr(2,9),error:Y,progress:Y?void 0:0};M.push(se)}const U=[...h,...M];a||f(U),i?.(U),M.forEach(ee=>{ee.error||k(ee.id)})},[h,n,i,a]),k=O=>{let M=0;const K=setInterval(()=>{if(M+=Math.random()*30,M>=100&&(M=100,clearInterval(K)),a||f(U=>U.map(ee=>ee.id===O?{...ee,progress:M}:ee)),a){const U=h.map(ee=>ee.id===O?{...ee,progress:M}:ee);i?.(U)}},200)},L=O=>{const M=h.filter(K=>K.id!==O);a||f(M),i?.(M),o?.(O)},$=O=>{O.preventDefault(),l||x(!0)},W=O=>{O.preventDefault(),x(!1)},D=O=>{if(O.preventDefault(),x(!1),l)return;const M=O.dataTransfer.files;M.length>0&&E(M)},V=O=>{const M=O.target.files;M&&M.length>0&&E(M),O.target.value=""},z=()=>{l||N.current?.click()};return r.jsxs("div",{ref:w,className:S("w-full",p),...y,children:[r.jsxs(v.motion.div,{className:S("relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300",u&&"backdrop-blur-md bg-white/10 dark:bg-gray-900/10",b&&!l?"border-primary bg-primary/5":"border-white/20 dark:border-gray-700/30",l?"opacity-50 cursor-not-allowed":"cursor-pointer hover:border-primary/50"),onDragOver:$,onDragLeave:W,onDrop:D,onClick:z,animate:{scale:b?1.02:1,borderColor:b?"rgb(var(--primary))":void 0},transition:{type:"spring",stiffness:300,damping:30},children:[r.jsx("input",{ref:N,type:"file",accept:e,multiple:t,onChange:V,disabled:l,className:"sr-only"}),r.jsxs(v.motion.div,{initial:d?{opacity:0,y:20}:{},animate:{opacity:1,y:0},children:[r.jsx(Qe,{icon:F.Upload,size:48,animation:b?"bounce":"none",className:"mx-auto mb-4 text-muted-foreground"}),r.jsx("h3",{className:"text-lg font-medium mb-2",children:b?"Drop files here":"Upload Files"}),r.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:"Drag and drop files here, or click to select files"}),r.jsxs("div",{className:"text-xs text-muted-foreground",children:[e&&r.jsxs("div",{children:["Accepted types: ",e]}),r.jsxs("div",{children:["Max size: ",I(s)]}),t&&r.jsxs("div",{children:["Max files: ",n]})]})]})]}),r.jsx(v.AnimatePresence,{children:h.length>0&&r.jsx(v.motion.div,{className:"mt-4 space-y-2",initial:d?{opacity:0,height:0}:{},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},children:h.map((O,M)=>{const K=R(O.file.name),U=O.progress===100,ee=!!O.error;return r.jsxs(v.motion.div,{className:S("flex items-center gap-3 p-3 rounded-lg",u&&"backdrop-blur-md bg-white/10 dark:bg-gray-900/10","border border-white/20 dark:border-gray-700/30",ee&&"border-red-500/50 bg-red-500/10"),initial:d?{opacity:0,x:-20}:{},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{delay:M*.1},layout:!0,children:[r.jsx(K,{className:S("w-8 h-8 flex-shrink-0",ee?"text-red-500":"text-muted-foreground")}),r.jsxs("div",{className:"flex-1 min-w-0",children:[r.jsx("div",{className:"font-medium truncate",children:O.file.name}),r.jsx("div",{className:"text-sm text-muted-foreground",children:I(O.file.size)}),ee?r.jsx("div",{className:"text-xs text-red-500 mt-1",children:O.error}):m&&O.progress!==void 0&&r.jsxs("div",{className:"mt-2",children:[r.jsx("div",{className:"flex items-center justify-between text-xs",children:r.jsx("span",{children:U?"Complete":`${Math.round(O.progress)}%`})}),r.jsx("div",{className:"w-full bg-white/20 rounded-full h-1.5 mt-1",children:r.jsx(v.motion.div,{className:"bg-primary h-1.5 rounded-full",initial:{width:0},animate:{width:`${O.progress}%`},transition:{duration:.3}})})]})]}),r.jsx(v.motion.button,{className:"p-1 rounded hover:bg-white/10 dark:hover:bg-gray-700/20",onClick:Y=>{Y.stopPropagation(),L(O.id)},whileHover:{scale:1.1},whileTap:{scale:.9},children:r.jsx(F.X,{className:"w-4 h-4"})})]},O.id)})})})]})});Er.displayName="LuminarFileUpload";const qs={xs:"text-xs py-0.5 px-1.5",sm:"text-xs py-1 px-2",md:"text-sm py-1.5 px-3",lg:"text-base py-2 px-4",xl:"text-lg py-2.5 px-5"},kn=["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6","#EC4899","#14B8A6","#F97316","#6366F1","#84CC16"],Ar=c.forwardRef(({value:e=[],onChange:t,placeholder:s="Type and press Enter",maxChips:n,allowDuplicates:a=!1,validateChip:i,suggestions:o=[],delimiter:l=/[,\n]/,glass:u=!0,animated:d=!0,disabled:m=!1,readOnly:p=!1,variant:y=Ae.variant,size:w=Ae.size,colorScheme:g="auto",onChipClick:f,renderChip:h,className:b,...x},N)=>{const[R,I]=c.useState(""),[j,E]=c.useState(!1),[k,L]=c.useState(-1),[$,W]=c.useState(null),D=c.useRef(null),V=c.useRef(null),z=c.useCallback(P=>g==="auto"?kn[P%kn.length]:typeof g=="string"?g:Array.isArray(g)?g[P%g.length]:"#3B82F6",[g]),O=o.filter(P=>P.toLowerCase().includes(R.toLowerCase())&&!e.some(B=>B.label===P)),M=c.useCallback(P=>{const B=P.trim();if(!B)return null;if(!a&&e.some(_=>_.label===B))return W("Duplicate chip"),null;if(i){const _=i(B);if(_!==!0)return W(typeof _=="string"?_:"Invalid chip"),null}return n&&e.length>=n?(W(`Maximum ${n} chips allowed`),null):{id:`chip-${Date.now()}-${Math.random()}`,label:B,removable:!0}},[e,a,i,n]),K=c.useCallback(P=>{const B=M(P);B&&(t?.([...e,B]),I(""),W(null),E(!1))},[e,t,M]),U=c.useCallback(P=>{const B=e[P];if(!B.removable&&B.removable!==void 0)return;const _=e.filter((te,H)=>H!==P);t?.(_)},[e,t]),ee=c.useCallback(P=>{const B=P.target.value;I(B),W(null);const _=typeof l=="string"?new RegExp(l.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")):l;_.test(B)?B.split(_).filter(Boolean).forEach(H=>K(H)):E(o.length>0&&B.length>0)},[l,o,K]),Y=c.useCallback(P=>{P.key==="Enter"?(P.preventDefault(),k>=0&&k<O.length?(K(O[k]),L(-1)):R&&K(R)):P.key==="Backspace"&&!R&&e.length>0?U(e.length-1):P.key==="ArrowDown"?(P.preventDefault(),L(B=>B<O.length-1?B+1:0)):P.key==="ArrowUp"?(P.preventDefault(),L(B=>B>0?B-1:O.length-1)):P.key==="Escape"&&(E(!1),L(-1))},[R,e,O,k,K,U]),se=c.useCallback(P=>{P.preventDefault();const B=P.clipboardData.getData("text"),_=typeof l=="string"?new RegExp(l.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"g"):l;B.split(_).filter(Boolean).forEach(H=>K(H))},[l,K]),G=S("relative flex flex-wrap items-center gap-2 p-2 rounded-lg transition-all min-h-[42px]",y==="default"&&u&&"backdrop-blur-sm bg-white/5 border border-white/20",y==="outlined"&&"border-2 border-gray-300 dark:border-gray-700",y==="filled"&&"bg-gray-100 dark:bg-gray-800",m&&"opacity-50 cursor-not-allowed","focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2",b);return r.jsxs("div",{ref:N,...x,children:[r.jsxs("div",{ref:V,className:G,onClick:()=>!m&&!p&&D.current?.focus(),children:[r.jsx(v.AnimatePresence,{children:e.map((P,B)=>r.jsx(v.motion.div,{initial:d?{scale:0,opacity:0}:{},animate:d?{scale:1,opacity:1}:{},exit:d?{scale:0,opacity:0}:{},transition:{duration:.2},children:h?h(P,B):r.jsxs("div",{className:S("inline-flex items-center gap-1 rounded-full font-medium transition-all",qs[w],P.removable!==!1&&"pr-1",f&&"cursor-pointer hover:opacity-80"),style:{backgroundColor:P.color||z(B),color:"white"},onClick:()=>f?.(P,B),children:[P.icon&&c.createElement(P.icon,{className:"w-4 h-4"}),r.jsx("span",{children:P.label}),P.removable!==!1&&!m&&!p&&r.jsx("button",{onClick:_=>{_.stopPropagation(),U(B)},className:"ml-1 rounded-full p-0.5 hover:bg-white/20 transition-colors",children:r.jsx(F.X,{className:"w-3 h-3"})})]})},P.id))}),!m&&!p&&(!n||e.length<n)&&r.jsx("input",{ref:D,type:"text",value:R,onChange:ee,onKeyDown:Y,onPaste:se,onFocus:()=>E(o.length>0&&R.length>0),onBlur:()=>setTimeout(()=>E(!1),200),placeholder:e.length===0?s:"",className:S("flex-1 min-w-[120px] bg-transparent outline-none",qs[w])})]}),r.jsx(v.AnimatePresence,{children:$&&r.jsx(v.motion.div,{initial:{opacity:0,y:-5},animate:{opacity:1,y:0},exit:{opacity:0,y:-5},className:"text-red-500 text-sm mt-1",children:$})}),r.jsx(v.AnimatePresence,{children:j&&O.length>0&&r.jsx(v.motion.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:S("absolute z-50 mt-2 w-full rounded-lg shadow-lg overflow-hidden",u&&"backdrop-blur-md bg-white/5 border border-white/20",!u&&"bg-gray-900 border border-gray-800"),children:O.map((P,B)=>r.jsx("div",{className:S("px-3 py-2 cursor-pointer transition-colors",B===k&&"bg-white/10","hover:bg-white/5"),onClick:()=>{K(P),L(-1)},children:P},P))})})]})});Ar.displayName="LuminarChipInput";const Fu=c.forwardRef(({label:e,onRemove:t,icon:s,color:n="#3B82F6",size:a=Ae.size||"md",variant:i="filled",className:o,...l},u)=>r.jsxs(v.motion.div,{ref:u,initial:{scale:0},animate:{scale:1},exit:{scale:0},className:S("inline-flex items-center gap-1 rounded-full font-medium transition-all",qs[a],i==="filled"&&"text-white",i==="outlined"&&"border-2",t&&"pr-1",o),style:{backgroundColor:i==="filled"?n:"transparent",borderColor:i==="outlined"?n:void 0,color:i==="outlined"?n:void 0},...l,children:[s&&c.createElement(s,{className:"w-4 h-4"}),r.jsx("span",{children:e}),t&&r.jsx("button",{onClick:t,className:"ml-1 rounded-full p-0.5 hover:bg-white/20 transition-colors",children:r.jsx(F.X,{className:"w-3 h-3"})})]}));Fu.displayName="LuminarChip";const Pr=c.forwardRef(({value:e=0,max:t=5,size:s="md",readonly:n=!1,allowHalf:a=!1,allowHalfStar:i=!1,glass:o=!0,onValueChange:l,className:u,...d},m)=>{const[p,y]=c.useState(null),w=p!==null?p:e,g={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"},f=x=>{n||l?.(x)},h=(x,N)=>{if(n)return;const R=x.currentTarget.getBoundingClientRect(),I=x.clientX-R.left,j=R.width,E=(a||i)&&I<j/2;y(N+(E?.5:1))},b=()=>{y(null)};return r.jsxs("div",{ref:m,className:S("inline-flex items-center gap-1",o&&"p-2 rounded-lg backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30",!n&&"cursor-pointer",u),onMouseLeave:b,...d,children:[Array.from({length:t},(x,N)=>{const R=N+1,I=w>=R,j=w>N&&w<R;return r.jsxs(v.motion.div,{className:"relative",onClick:()=>f(R),onMouseMove:E=>h(E,N),whileHover:n?{}:{scale:1.2},whileTap:n?{}:{scale:.9},initial:{rotate:-180,opacity:0},animate:{rotate:0,opacity:1},transition:{delay:N*.1},children:[r.jsx(F.Star,{className:S(g[s],"text-gray-300 dark:text-gray-600 fill-gray-300 dark:fill-gray-600")}),r.jsx(v.motion.div,{className:"absolute inset-0 overflow-hidden",animate:{width:j?"50%":I?"100%":"0%"},transition:{duration:.2},children:r.jsx(F.Star,{className:S(g[s],"text-yellow-500 fill-yellow-500")})}),!n&&p!==null&&r.jsx(v.motion.div,{className:"absolute inset-0",initial:{scale:0},animate:{scale:[1,1.5,1]},transition:{duration:.3},children:r.jsx(F.Star,{className:S(g[s],"text-yellow-400 fill-transparent opacity-50")})})]},N)}),w>0&&r.jsx(v.motion.span,{className:"ml-2 text-sm font-medium",initial:{opacity:0,x:-10},animate:{opacity:1,x:0},children:w.toFixed((a||i)&&w%1!==0?1:0)})]})});Pr.displayName="LuminarRating";const _u=Object.freeze(Object.defineProperty({__proto__:null,AdvancedColorPicker:Ia,LuminarAutocomplete:kr,LuminarCheckbox:De,LuminarChipInput:Ar,LuminarColorPicker:Rr,LuminarCombobox:Nr,LuminarDatePicker:Sr,LuminarDateRangePicker:Tr,LuminarFileUpload:Er,LuminarForm:Cr,LuminarInput:Le,LuminarLabel:Rt,LuminarRadioGroup:vr,LuminarRating:Pr,LuminarSelect:St,LuminarSlider:jr,LuminarSwitch:cs,LuminarTextarea:ls},Symbol.toStringTag,{value:"Module"})),Oa=c.createContext(void 0),us=()=>{const e=c.useContext(Oa);if(!e)throw new Error("Alert dialog components must be used within AlertDialog");return e},Lr=({open:e=!1,onOpenChange:t,children:s})=>r.jsx(Oa.Provider,{value:{open:e,onOpenChange:t||(()=>{})},children:s}),Uu=c.forwardRef(({className:e,children:t,asChild:s=!1,...n},a)=>{const{onOpenChange:i}=us();return s?r.jsx("div",{onClick:()=>i(!0),children:t}):r.jsx("button",{ref:a,className:S("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium","ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2","focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50","bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2",e),onClick:()=>i(!0),...n,children:t})}),$u=c.forwardRef(({className:e,variant:t="modal",size:s=Ae.size,glassIntensity:n="medium",colorTheme:a="neutral",animation:i="glassScale",disableAnimation:o=!1,closeOnOverlayClick:l=!1,showCloseButton:u=!1,children:d},m)=>{const{open:p,onOpenChange:y}=us(),w=tr(t,{intensity:n}),g=o?{}:He[i],f={sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl"};return c.useEffect(()=>{const h=b=>{b.key==="Escape"&&y(!1)};return p&&(document.addEventListener("keydown",h),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",h),document.body.style.overflow="unset"}},[p,y]),r.jsx(v.AnimatePresence,{children:p&&r.jsxs(r.Fragment,{children:[r.jsx(v.motion.div,{className:"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:l?()=>y(!1):void 0}),r.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:r.jsxs(v.motion.div,{ref:m,className:S("relative w-full rounded-2xl p-6",w,f[s],e),...g,transition:ze.spring,onClick:h=>h.stopPropagation(),children:[u&&r.jsx(v.motion.button,{className:"absolute top-4 right-4 p-1 rounded-lg hover:bg-white/10 dark:hover:bg-gray-700/20 transition-colors",onClick:()=>y(!1),whileTap:{scale:.95},whileHover:{scale:1.05},children:r.jsx(F.X,{className:"w-5 h-5 text-gray-700 dark:text-gray-300"})}),d]})})]})})}),zu=c.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:S("flex flex-col space-y-2 text-center sm:text-left",e),...t})),Qu=c.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:S("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 gap-2",e),...t})),qu=c.forwardRef(({className:e,...t},s)=>r.jsx("h2",{ref:s,className:S("text-lg font-semibold text-foreground",e),...t})),Bu=c.forwardRef(({className:e,...t},s)=>r.jsx("p",{ref:s,className:S("text-sm text-muted-foreground",e),...t})),Vu=c.forwardRef(({className:e,variant:t=Ae.variant,size:s=Ae.size,asChild:n=!1,...a},i)=>{const{onOpenChange:o}=us();return n?r.jsx("div",{onClick:()=>o(!1),children:a.children}):r.jsx(We,{ref:i,variant:t,size:s,className:e,onClick:()=>o(!1),...a})}),Hu=c.forwardRef(({className:e,size:t=Ae.size,asChild:s=!1,...n},a)=>{const{onOpenChange:i}=us();return s?r.jsx("div",{onClick:()=>i(!1),children:n.children}):r.jsx(We,{ref:a,variant:"outline",size:t,className:e,onClick:()=>i(!1),...n})});Lr.displayName="LuminarAlertDialog";Uu.displayName="LuminarAlertDialogTrigger";$u.displayName="LuminarAlertDialogContent";zu.displayName="LuminarAlertDialogHeader";Qu.displayName="LuminarAlertDialogFooter";qu.displayName="LuminarAlertDialogTitle";Bu.displayName="LuminarAlertDialogDescription";Vu.displayName="LuminarAlertDialogAction";Hu.displayName="LuminarAlertDialogCancel";const Gu={success:{icon:F.CheckCircle,colors:"text-green-600 dark:text-green-400 border-green-500/30 bg-green-500/10",iconColor:"text-green-500"},error:{icon:F.AlertCircle,colors:"text-red-600 dark:text-red-400 border-red-500/30 bg-red-500/10",iconColor:"text-red-500"},warning:{icon:F.AlertTriangle,colors:"text-yellow-600 dark:text-yellow-400 border-yellow-500/30 bg-yellow-500/10",iconColor:"text-yellow-500"},info:{icon:F.Info,colors:"text-blue-600 dark:text-blue-400 border-blue-500/30 bg-blue-500/10",iconColor:"text-blue-500"}},Ir=c.forwardRef(({type:e,title:t,description:s,dismissible:n=!1,onDismiss:a,glass:i=!0,animated:o=!0,actions:l,className:u,children:d},m)=>{const[p,y]=c.useState(!0),w=Gu[e],g=w.icon,f=()=>{y(!1),setTimeout(()=>{a?.()},300)};return r.jsx(v.AnimatePresence,{children:p&&r.jsxs(v.motion.div,{ref:m,className:S("relative p-4 rounded-lg border",i&&"backdrop-blur-md",w.colors,u),initial:o?{opacity:0,y:-20,scale:.95}:{},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-20,scale:.95},transition:{type:"spring",stiffness:400,damping:30},children:[r.jsxs("div",{className:"flex items-start gap-3",children:[r.jsx(Qe,{icon:g,size:20,animation:"pulse",trigger:"auto",className:w.iconColor}),r.jsxs("div",{className:"flex-1 min-w-0",children:[t&&r.jsx(v.motion.h4,{className:"font-medium mb-1",initial:o?{opacity:0,x:-10}:{},animate:{opacity:1,x:0},transition:{delay:.1},children:t}),s&&r.jsx(v.motion.p,{className:"text-sm opacity-90",initial:o?{opacity:0,x:-10}:{},animate:{opacity:1,x:0},transition:{delay:.15},children:s}),d&&r.jsx(v.motion.div,{className:"mt-2",initial:o?{opacity:0}:{},animate:{opacity:1},transition:{delay:.2},children:d}),l&&r.jsx(v.motion.div,{className:"mt-3",initial:o?{opacity:0,y:10}:{},animate:{opacity:1,y:0},transition:{delay:.25},children:l})]}),n&&r.jsx(v.motion.button,{className:"flex-shrink-0 p-1 rounded hover:bg-black/10 dark:hover:bg-white/10 transition-colors",onClick:f,initial:o?{opacity:0,scale:0}:{},animate:{opacity:1,scale:1},transition:{delay:.3},whileHover:{scale:1.1},whileTap:{scale:.9},children:r.jsx(F.X,{className:"w-4 h-4"})})]}),r.jsx(v.motion.div,{className:S("absolute bottom-0 left-0 h-1 rounded-b-lg",w.iconColor.replace("text-","bg-")),initial:o?{width:"0%"}:{width:"100%"},animate:{width:"100%"},transition:{duration:.8,delay:.1}})]})})});Ir.displayName="LuminarAlert";function Da({children:e,variant:t="info",size:s="md",closable:n=!1,onClose:a,className:i=""}){const o={sm:"px-3 py-2 text-sm",md:"px-4 py-3 text-base",lg:"px-6 py-4 text-lg"},l={info:"bg-blue-50/80 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100 border-blue-200/60 dark:border-blue-700/60",success:"bg-green-50/80 dark:bg-green-900/20 text-green-900 dark:text-green-100 border-green-200/60 dark:border-green-700/60",warning:"bg-yellow-50/80 dark:bg-yellow-900/20 text-yellow-900 dark:text-yellow-100 border-yellow-200/60 dark:border-yellow-700/60",error:"bg-red-50/80 dark:bg-red-900/20 text-red-900 dark:text-red-100 border-red-200/60 dark:border-red-700/60",neutral:"bg-gray-50/80 dark:bg-gray-900/20 text-foreground border-border"},u=ct("card","md",t==="neutral"?void 0:t);return r.jsxs("div",{className:`${u} ${o[s]} ${l[t]} rounded-lg border flex items-center justify-between ${i}`,children:[r.jsx("div",{className:"flex-1",children:e}),n&&r.jsx("button",{onClick:a,className:"ml-4 p-1 hover:bg-black/5 dark:hover:bg-white/5 rounded-full transition-colors",children:r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}const Ku={sm:{left:"w-64",right:"w-64",top:"h-64",bottom:"h-64"},md:{left:"w-80",right:"w-80",top:"h-80",bottom:"h-80"},lg:{left:"w-96",right:"w-96",top:"h-96",bottom:"h-96"},xl:{left:"w-[448px]",right:"w-[448px]",top:"h-[448px]",bottom:"h-[448px]"},full:{left:"w-full",right:"w-full",top:"h-full",bottom:"h-full"}},Wu={left:"inset-y-0 left-0",right:"inset-y-0 right-0",top:"inset-x-0 top-0",bottom:"inset-x-0 bottom-0"},Yu={left:{initial:{x:"-100%"},animate:{x:0},exit:{x:"-100%"}},right:{initial:{x:"100%"},animate:{x:0},exit:{x:"100%"}},top:{initial:{y:"-100%"},animate:{y:0},exit:{y:"-100%"}},bottom:{initial:{y:"100%"},animate:{y:0},exit:{y:"100%"}}},Or=c.forwardRef(({open:e,onClose:t,position:s="right",size:n=defaultComponentProps.size,overlay:a=!0,overlayClosable:i=!0,closeButton:o=!0,title:l,footer:u,glass:d=!0,animated:m=!0,persistent:p=!1,className:y,children:w},g)=>{const f=c.useRef(null);c.useEffect(()=>{const b=x=>{x.key==="Escape"&&e&&!p&&t()};return e&&(document.addEventListener("keydown",b),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",b),document.body.style.overflow=""}},[e,t,p]),c.useEffect(()=>{e&&f.current&&f.current.focus()},[e]);const h=r.jsx(v.AnimatePresence,{children:e&&r.jsxs(r.Fragment,{children:[a&&r.jsx(v.motion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},className:S("fixed inset-0 z-40",d&&"backdrop-blur-sm bg-black/50",!d&&"bg-black/50"),onClick:i&&!p?t:void 0,"aria-hidden":"true"}),r.jsxs(v.motion.div,{ref:f,variants:m?Yu[s]:{},initial:m?"initial":void 0,animate:m?"animate":void 0,exit:m?"exit":void 0,transition:{type:"spring",stiffness:300,damping:30,mass:.8},className:S("fixed z-50 flex flex-col",Wu[s],Ku[n][s],d&&"backdrop-blur-md bg-white/10 border",!d&&"bg-gray-900 border",s==="left"&&"border-r border-white/20",s==="right"&&"border-l border-white/20",s==="top"&&"border-b border-white/20",s==="bottom"&&"border-t border-white/20","shadow-2xl",y),tabIndex:-1,role:"dialog","aria-modal":"true","aria-labelledby":l?"drawer-title":void 0,children:[(l||o)&&r.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-white/10",children:[l&&r.jsx("h2",{id:"drawer-title",className:"text-lg font-semibold",children:l}),o&&!p&&r.jsx(le,{variant:"ghost",size:"sm",onClick:t,className:"ml-auto","aria-label":"Close drawer",children:r.jsx(F.X,{className:"w-4 h-4"})})]}),r.jsx("div",{className:"flex-1 overflow-y-auto p-4",children:w}),u&&r.jsx("div",{className:"p-4 border-t border-white/10",children:u})]})]})});return typeof document<"u"?Nn.createPortal(h,document.body):h});Or.displayName="LuminarDrawer";const Dr=c.forwardRef(({className:e,open:t,onClose:s,variant:n="default",size:a=Ae.size||"md",closeOnOverlayClick:i=!0,showCloseButton:o=!0,animation:l="scale",disableAnimation:u=!1,children:d},m)=>{const p="bg-white/10 backdrop-blur-md border border-white/20",y=u?{}:{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95}},w={xs:"max-w-xs",sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl"};return c.useEffect(()=>{const g=f=>{f.key==="Escape"&&s()};return t&&(document.addEventListener("keydown",g),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",g),document.body.style.overflow="unset"}},[t,s]),r.jsx(v.AnimatePresence,{children:t&&r.jsxs(r.Fragment,{children:[r.jsx(v.motion.div,{className:"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:i?s:void 0}),r.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:r.jsxs(v.motion.div,{ref:m,className:S("relative w-full rounded-2xl p-6",p,w[a],e),...y,transition:{type:"spring",damping:25,stiffness:500},onClick:g=>g.stopPropagation(),children:[o&&r.jsx(v.motion.button,{className:"absolute top-4 right-4 p-1 rounded-lg hover:bg-white/10 dark:hover:bg-gray-700/20 transition-colors",onClick:s,whileHover:{scale:1.1},whileTap:{scale:.9},children:r.jsx(F.X,{className:"w-5 h-5 text-gray-700 dark:text-gray-300"})}),d]})})]})})});Dr.displayName="LuminarModal";const Mr=c.forwardRef(({trigger:e,children:t,placement:s="bottom",open:n,onOpenChange:a,offset:i=8,glass:o=!0,animated:l=!0,closeOnClickOutside:u=!0,closeOnEscape:d=!0,arrow:m=!0,portal:p=!0,className:y,...w},g)=>{const[f,h]=c.useState(!1),[b,x]=c.useState({x:0,y:0}),[N,R]=c.useState(s),I=c.useRef(null),j=c.useRef(null),E=n??f,k=V=>{a?a(V):h(V)},L=()=>{if(!I.current||!E)return;const V=I.current.getBoundingClientRect(),z=j.current;if(!z)return;const O=z.getBoundingClientRect(),M={width:window.innerWidth,height:window.innerHeight};let K=s,U=0,ee=0;switch(s.split("-")[0]){case"top":U=V.left+V.width/2-O.width/2,ee=V.top-O.height-i;break;case"bottom":U=V.left+V.width/2-O.width/2,ee=V.bottom+i;break;case"left":U=V.left-O.width-i,ee=V.top+V.height/2-O.height/2;break;case"right":U=V.right+i,ee=V.top+V.height/2-O.height/2;break}s.includes("-start")?s.startsWith("top")||s.startsWith("bottom")?U=V.left:ee=V.top:s.includes("-end")&&(s.startsWith("top")||s.startsWith("bottom")?U=V.right-O.width:ee=V.bottom-O.height),U<0?U=8:U+O.width>M.width&&(U=M.width-O.width-8),ee<0?s.startsWith("top")?(ee=V.bottom+i,K=s.replace("top","bottom")):ee=8:ee+O.height>M.height&&(s.startsWith("bottom")?(ee=V.top-O.height-i,K=s.replace("bottom","top")):ee=M.height-O.height-8),x({x:U,y:ee}),R(K)};c.useEffect(()=>{if(!E||!u)return;const V=z=>{I.current&&j.current&&!I.current.contains(z.target)&&!j.current.contains(z.target)&&k(!1)};return document.addEventListener("mousedown",V),()=>document.removeEventListener("mousedown",V)},[E,u]),c.useEffect(()=>{if(!E||!d)return;const V=z=>{z.key==="Escape"&&k(!1)};return document.addEventListener("keydown",V),()=>document.removeEventListener("keydown",V)},[E,d]),c.useEffect(()=>{if(E){L();const V=()=>L(),z=()=>L();return window.addEventListener("resize",V),window.addEventListener("scroll",z,!0),()=>{window.removeEventListener("resize",V),window.removeEventListener("scroll",z,!0)}}},[E,s]);const $=()=>{switch(N.split("-")[0]){case"top":return{bottom:-8,left:N.includes("-start")?12:N.includes("-end")?"calc(100% - 20px)":"50%",transform:N.includes("-start")||N.includes("-end")?"none":"translateX(-50%)",borderLeft:"8px solid transparent",borderRight:"8px solid transparent",borderTop:"8px solid rgb(var(--popover))"};case"bottom":return{top:-8,left:N.includes("-start")?12:N.includes("-end")?"calc(100% - 20px)":"50%",transform:N.includes("-start")||N.includes("-end")?"none":"translateX(-50%)",borderLeft:"8px solid transparent",borderRight:"8px solid transparent",borderBottom:"8px solid rgb(var(--popover))"};case"left":return{right:-8,top:N.includes("-start")?12:N.includes("-end")?"calc(100% - 20px)":"50%",transform:N.includes("-start")||N.includes("-end")?"none":"translateY(-50%)",borderTop:"8px solid transparent",borderBottom:"8px solid transparent",borderLeft:"8px solid rgb(var(--popover))"};case"right":return{left:-8,top:N.includes("-start")?12:N.includes("-end")?"calc(100% - 20px)":"50%",transform:N.includes("-start")||N.includes("-end")?"none":"translateY(-50%)",borderTop:"8px solid transparent",borderBottom:"8px solid transparent",borderRight:"8px solid rgb(var(--popover))"};default:return{}}},W=()=>{const V={initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95}};if(!l)return V;switch(N.split("-")[0]){case"top":return{initial:{opacity:0,scale:.95,y:10},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:10}};case"bottom":return{initial:{opacity:0,scale:.95,y:-10},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:-10}};case"left":return{initial:{opacity:0,scale:.95,x:10},animate:{opacity:1,scale:1,x:0},exit:{opacity:0,scale:.95,x:10}};case"right":return{initial:{opacity:0,scale:.95,x:-10},animate:{opacity:1,scale:1,x:0},exit:{opacity:0,scale:.95,x:-10}};default:return V}},D=r.jsx(v.AnimatePresence,{children:E&&r.jsxs(v.motion.div,{ref:j,className:S("absolute z-50 shadow-lg",o?Ke({element:"control",profile:"hard"}):"bg-popover border-border rounded-lg",y),style:{left:b.x,top:b.y},variants:W(),initial:"initial",animate:"animate",exit:"exit",transition:{type:"spring",stiffness:400,damping:30},...w,children:[t,m&&r.jsx("div",{className:"absolute w-0 h-0 pointer-events-none",style:$()})]})});return r.jsxs(r.Fragment,{children:[r.jsx("div",{ref:I,onClick:()=>k(!E),className:"inline-block cursor-pointer",children:e}),p?Nn.createPortal(D,document.body):D]})});Mr.displayName="LuminarPopover";const Ju={success:F.CheckCircle,error:F.AlertCircle,info:F.Info,warning:F.AlertTriangle},Xu={success:"text-green-500",error:"text-red-500",info:"text-blue-500",warning:"text-yellow-500"},Zu={success:"bg-green-500",error:"bg-red-500",info:"bg-blue-500",warning:"bg-yellow-500"},Fr=c.forwardRef((e,t)=>{const{toast:s,onClose:n,position:a="top-right",className:i,...o}=e,l=Ju[s.type];c.useEffect(()=>{if(s.duration){const m=setTimeout(()=>{n(s.id)},s.duration);return()=>clearTimeout(m)}},[s,n]);const d=(()=>{switch(a){case"top-right":case"bottom-right":return{initial:{x:100,opacity:0},animate:{x:0,opacity:1},exit:{x:100,opacity:0}};case"top-left":case"bottom-left":return{initial:{x:-100,opacity:0},animate:{x:0,opacity:1},exit:{x:-100,opacity:0}};case"top-center":return{initial:{y:-100,opacity:0},animate:{y:0,opacity:1},exit:{y:-100,opacity:0}};case"bottom-center":return{initial:{y:100,opacity:0},animate:{y:0,opacity:1},exit:{y:100,opacity:0}}}})();return r.jsxs(v.motion.div,{ref:t,layout:!0,...d,transition:{type:"spring",stiffness:400,damping:30},className:S("relative min-w-[300px] max-w-md p-4 mb-3",Ke({element:"modal",profile:"intense"}),i),...o,children:[r.jsxs("div",{className:"flex items-start gap-3",children:[r.jsx(Qe,{icon:l,size:20,animation:"pulse",trigger:"auto",className:Xu[s.type]}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h4",{className:"font-medium",children:s.title}),s.description&&r.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:s.description})]}),r.jsx(v.motion.button,{className:"p-1 rounded hover:bg-white/10 dark:hover:bg-gray-700/20",onClick:()=>n(s.id),whileHover:{scale:1.1},whileTap:{scale:.9},children:r.jsx(F.X,{className:"w-4 h-4"})})]}),r.jsx(v.motion.div,{className:S("absolute bottom-0 left-0 h-1 rounded-b-lg",Zu[s.type]),initial:{width:"100%"},animate:{width:"0%"},transition:{duration:(s.duration||5e3)/1e3,ease:"linear"}})]})});Fr.displayName="LuminarToast";c.createContext(void 0);const _r=c.forwardRef(({content:e,placement:t="top",trigger:s="hover",delay:n=200,glass:a=!0,children:i,className:o,...l},u)=>{const[d,m]=c.useState(!1),[p,y]=c.useState({x:0,y:0}),w=c.useRef(null),g=c.useRef(null),f=c.useRef(null);c.useEffect(()=>{if(d&&w.current&&g.current){const I=w.current.getBoundingClientRect(),j=g.current.getBoundingClientRect();let E=0,k=0;switch(t){case"top":E=I.left+I.width/2-j.width/2,k=I.top-j.height-8;break;case"right":E=I.right+8,k=I.top+I.height/2-j.height/2;break;case"bottom":E=I.left+I.width/2-j.width/2,k=I.bottom+8;break;case"left":E=I.left-j.width-8,k=I.top+I.height/2-j.height/2;break}const L=8;E=Math.max(L,Math.min(E,window.innerWidth-j.width-L)),k=Math.max(L,Math.min(k,window.innerHeight-j.height-L)),y({x:E,y:k})}},[d,t]);const h=()=>{n?f.current=setTimeout(()=>m(!0),n):m(!0)},b=()=>{f.current&&clearTimeout(f.current),m(!1)},x=()=>{d?b():h()},R=(()=>{switch(t){case"top":return{initial:{y:10},animate:{y:0}};case"right":return{initial:{x:-10},animate:{x:0}};case"bottom":return{initial:{y:-10},animate:{y:0}};case"left":return{initial:{x:10},animate:{x:0}}}})();return r.jsxs(r.Fragment,{children:[r.jsx("div",{ref:w,className:S("inline-block",o),onMouseEnter:s==="hover"?h:void 0,onMouseLeave:s==="hover"?b:void 0,onClick:s==="click"?x:void 0,...l,children:i}),r.jsx(v.AnimatePresence,{children:d&&r.jsxs(v.motion.div,{ref:g,className:S("fixed z-50 px-3 py-2 text-sm rounded-lg pointer-events-none",a&&"backdrop-blur-xl bg-white/90 dark:bg-gray-900/90",!a&&"bg-popover text-popover-foreground","border border-white/20 dark:border-gray-700/30","shadow-xl"),style:{left:p.x,top:p.y},initial:{opacity:0,...R.initial},animate:{opacity:1,...R.animate},exit:{opacity:0},transition:{duration:.2},children:[e,r.jsx("div",{className:S("absolute w-2 h-2 rotate-45",a?"bg-white/90 dark:bg-gray-900/90":"bg-popover","border-l border-b border-white/20 dark:border-gray-700/30",t==="top"&&"bottom-[-5px] left-1/2 -translate-x-1/2 border-r-0 border-t-0",t==="right"&&"left-[-5px] top-1/2 -translate-y-1/2 border-l-0 border-b-0",t==="bottom"&&"top-[-5px] left-1/2 -translate-x-1/2 border-l-0 border-b-0",t==="left"&&"right-[-5px] top-1/2 -translate-y-1/2 border-r-0 border-t-0")})]})})]})});_r.displayName="LuminarTooltip";const ed=Object.freeze(Object.defineProperty({__proto__:null,LuminarAlert:Ir,LuminarAlertDialog:Lr,LuminarBanner:Da,LuminarDrawer:Or,LuminarModal:Dr,LuminarPopover:Mr,LuminarToast:Fr,LuminarTooltip:_r},Symbol.toStringTag,{value:"Module"})),td=jt("inline-flex items-center justify-center rounded-md transition-all duration-300 relative overflow-hidden",{variants:{variant:{default:"bg-background text-foreground hover:bg-accent hover:text-accent-foreground",glass:"backdrop-blur-md bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 text-gray-900 dark:text-white hover:bg-white/20 dark:hover:bg-white/10",primary:"bg-primary text-primary-foreground hover:bg-primary/90",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",success:"bg-green-500/20 text-green-700 dark:text-green-400 hover:bg-green-500/30",warning:"bg-yellow-500/20 text-yellow-700 dark:text-yellow-400 hover:bg-yellow-500/30",error:"bg-red-500/20 text-red-700 dark:text-red-400 hover:bg-red-500/30",info:"bg-blue-500/20 text-blue-700 dark:text-blue-400 hover:bg-blue-500/30"},size:{sm:"h-6 w-6 text-xs",md:"h-8 w-8 text-sm",lg:"h-10 w-10 text-base",xl:"h-12 w-12 text-lg"},state:{idle:"",active:"scale-110 shadow-lg",disabled:"opacity-50 cursor-not-allowed"}},defaultVariants:{variant:"default",size:"md",state:"idle"}}),sd={mail:F.Mail,send:F.Send,inbox:F.Inbox,star:F.Star,archive:F.Archive,trash:F.Trash2,reply:F.Reply,forward:F.Forward,attachment:F.Paperclip,secure:F.Shield,flag:F.Flag,read:F.Eye,unread:F.EyeOff,more:F.MoreHorizontal,sent:F.CheckCircle,draft:F.AlertCircle,spam:F.Flag},Ee=me.forwardRef(({className:e,variant:t,size:s,state:n,icon:a,animated:i=!0,pulse:o=!1,glow:l=!1,bounce:u=!1,loading:d=!1,count:m,showTooltip:p=!1,tooltipText:y,children:w,...g},f)=>{const h=sd[a]||F.Mail,[b,x]=me.useState(!1),[N,R]=me.useState(!1),I=()=>i?{initial:{scale:1,rotate:0},hover:{scale:1.1,rotate:a==="star"?15:0,transition:{type:"spring",stiffness:400,damping:17}},tap:{scale:.95,transition:{type:"spring",stiffness:400,damping:17}}}:{},j=()=>o?{scale:[1,1.2,1],opacity:[.7,1,.7],transition:{duration:2,repeat:1/0,ease:"easeInOut"}}:{},E=()=>u?{y:[0,-10,0],transition:{duration:.6,repeat:1/0,ease:"easeInOut"}}:{},k=()=>l?r.jsx(v.motion.div,{className:"absolute inset-0 rounded-md bg-gradient-to-r from-blue-400/0 via-blue-400/40 to-blue-400/0",animate:{opacity:[0,.8,0],scale:[.8,1.1,.8]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}):null,L=()=>{switch(a){case"send":return b?{x:[0,3,0],transition:{duration:.3,ease:"easeInOut"}}:{};case"trash":return b?{rotate:[-2,2,-2,2,0],transition:{duration:.4,ease:"easeInOut"}}:{};case"star":return b?{rotate:[0,360],transition:{duration:.5,ease:"easeInOut"}}:{};case"mail":return b?{y:[0,-2,0],transition:{duration:.3,ease:"easeInOut"}}:{};default:return{}}};return r.jsx("div",{className:"relative inline-block",children:r.jsxs(v.motion.button,{ref:f,className:S(td({variant:t,size:s,state:n}),d&&"cursor-not-allowed",e),onMouseEnter:()=>x(!0),onMouseLeave:()=>x(!1),onMouseDown:()=>R(!0),onMouseUp:()=>R(!1),disabled:d||n==="disabled",variants:I(),initial:"initial",whileHover:"hover",whileTap:"tap",animate:{...j(),...E(),...L()},children:[k(),d&&r.jsx(v.motion.div,{className:"absolute inset-0 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.2},children:r.jsx(v.motion.div,{className:"h-3 w-3 rounded-full border-2 border-current border-t-transparent",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}})}),r.jsx(v.motion.div,{className:"relative z-10",initial:{opacity:1},animate:{opacity:d?0:1},transition:{duration:.2},children:r.jsx(h,{className:"h-full w-full"})}),m&&m>0&&r.jsx(v.motion.div,{className:"absolute -top-1 -right-1 min-w-[1.25rem] h-5 px-1 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:400,damping:10},children:m>99?"99+":m}),p&&y&&b&&r.jsxs(v.motion.div,{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap z-20",initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{duration:.2},children:[y,r.jsx("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"})]})]})})});Ee.displayName="EmailIcon";const gt={Send:me.forwardRef((e,t)=>r.jsx(Ee,{ref:t,icon:"send",...e})),Reply:me.forwardRef((e,t)=>r.jsx(Ee,{ref:t,icon:"reply",...e})),Forward:me.forwardRef((e,t)=>r.jsx(Ee,{ref:t,icon:"forward",...e})),Archive:me.forwardRef((e,t)=>r.jsx(Ee,{ref:t,icon:"archive",...e})),Delete:me.forwardRef((e,t)=>r.jsx(Ee,{ref:t,icon:"trash",variant:"error",...e})),Star:me.forwardRef((e,t)=>r.jsx(Ee,{ref:t,icon:"star",variant:"warning",...e})),MarkRead:me.forwardRef((e,t)=>r.jsx(Ee,{ref:t,icon:"read",variant:"success",...e})),MarkUnread:me.forwardRef((e,t)=>r.jsx(Ee,{ref:t,icon:"unread",variant:"info",...e})),Flag:me.forwardRef((e,t)=>r.jsx(Ee,{ref:t,icon:"flag",variant:"error",...e})),Secure:me.forwardRef((e,t)=>r.jsx(Ee,{ref:t,icon:"secure",variant:"success",...e})),Attachment:me.forwardRef((e,t)=>r.jsx(Ee,{ref:t,icon:"attachment",variant:"info",...e}))},ds=me.forwardRef(({status:e,size:t=defaultComponentProps.size,animated:s=!0,className:n},a)=>{const o=(()=>{switch(e){case"sent":return{icon:F.CheckCircle,color:"text-green-500",bg:"bg-green-100 dark:bg-green-900/20"};case"draft":return{icon:F.AlertCircle,color:"text-yellow-500",bg:"bg-yellow-100 dark:bg-yellow-900/20"};case"failed":return{icon:F.AlertCircle,color:"text-red-500",bg:"bg-red-100 dark:bg-red-900/20"};case"sending":return{icon:F.Send,color:"text-blue-500",bg:"bg-blue-100 dark:bg-blue-900/20"};case"delivered":return{icon:F.CheckCircle,color:"text-blue-500",bg:"bg-blue-100 dark:bg-blue-900/20"};case"read":return{icon:F.Eye,color:"text-purple-500",bg:"bg-purple-100 dark:bg-purple-900/20"};default:return{icon:F.Mail,color:"text-gray-500",bg:"bg-gray-100 dark:bg-gray-900/20"}}})(),l=o.icon,u=t==="sm"?"h-4 w-4":t==="lg"?"h-6 w-6":"h-5 w-5";return r.jsxs(v.motion.div,{ref:a,className:S("inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",o.bg,n),initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.2},children:[r.jsx(v.motion.div,{animate:s&&e==="sending"?{rotate:360}:{},transition:s&&e==="sending"?{duration:1,repeat:1/0,ease:"linear"}:{},children:r.jsx(l,{className:S(u,o.color)})}),r.jsx("span",{className:o.color,children:e.charAt(0).toUpperCase()+e.slice(1)})]})});ds.displayName="EmailStatusIndicator";const rd=jt("relative flex flex-col bg-background border rounded-lg shadow-xl overflow-hidden",{variants:{variant:{default:"bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700",glass:"backdrop-blur-md bg-white/80 dark:bg-gray-900/80 border-white/20 dark:border-gray-700/30",minimal:"bg-gray-50 dark:bg-gray-800 border-gray-100 dark:border-gray-800"},size:{sm:"max-w-md max-h-96",md:"max-w-2xl max-h-[70vh]",lg:"max-w-4xl max-h-[80vh]",fullscreen:"w-full h-full max-w-none max-h-none"},state:{open:"opacity-100 scale-100",closed:"opacity-0 scale-95 pointer-events-none",minimized:"opacity-100 scale-100 h-12"}},defaultVariants:{variant:"default",size:"md",state:"open"}}),Ur=me.forwardRef(({className:e,variant:t,size:s,state:n,open:a=!0,onClose:i,onSend:o,onMinimize:l,onMaximize:u,isMinimized:d=!1,isMaximized:m=!1,animated:p=!0,autoFocus:y=!0,defaultTo:w="",defaultSubject:g="",defaultBody:f="",showFormatting:h=!0,showAttachments:b=!0,placeholder:x="Compose your message...",loading:N=!1,...R},I)=>{const[j,E]=me.useState({to:w,cc:"",bcc:"",subject:g,body:f,priority:"normal",isHtml:void 0}),[k,L]=me.useState([]),[$,W]=me.useState(!1),[D,V]=me.useState(!1),[z,O]=me.useState(!1),[M,K]=me.useState(!1),[U,ee]=me.useState(!1),Y=me.useRef(null),se=me.useRef(null),G=(Z,ue)=>{E(J=>({...J,[Z]:ue}))},P=async()=>{if(!(!j.to||!j.subject||!j.body)){ee(!0);try{await o?.(j),i?.()}finally{ee(!1)}}},B=Z=>{if(!Z)return;const ue=Array.from(Z).map(J=>({id:Math.random().toString(36).substr(2,9),file:J,name:J.name,size:J.size,type:J.type,progress:0}));L(J=>[...J,...ue])},_=Z=>{L(ue=>ue.filter(J=>J.id!==Z))},te=Z=>{if(Z===0)return"0 Bytes";const ue=1024,J=["Bytes","KB","MB","GB"],oe=Math.floor(Math.log(Z)/Math.log(ue));return parseFloat((Z/Math.pow(ue,oe)).toFixed(2))+" "+J[oe]},H=()=>p?{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:-20},transition:{type:"spring",stiffness:300,damping:30}}:{};return d?r.jsxs(v.motion.div,{ref:I,className:S("fixed bottom-4 right-4 w-80 h-12 bg-white dark:bg-gray-900 border rounded-lg shadow-lg cursor-pointer","flex items-center justify-between px-4 py-2"),onClick:u,initial:p?{opacity:0,y:20}:void 0,animate:p?{opacity:1,y:0}:void 0,transition:p?{type:"spring",stiffness:300,damping:30}:void 0,children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx(Ee,{icon:"mail",size:"sm"}),r.jsx("span",{className:"text-sm font-medium truncate",children:j.subject||"New Message"})]}),r.jsx(le,{variant:"ghost",size:"sm",onClick:Z=>{Z.stopPropagation(),i?.()},children:r.jsx(F.X,{className:"h-4 w-4"})})]}):r.jsx(v.AnimatePresence,{children:a&&r.jsxs(v.motion.div,{ref:I,className:S(rd({variant:t,size:s,state:n}),m&&"fixed inset-4 z-50",!m&&"fixed bottom-4 right-4 z-50",e),...H(),children:[r.jsxs(v.motion.div,{className:"flex items-center justify-between p-4 border-b bg-gray-50 dark:bg-gray-800",initial:p?{opacity:0,y:-20}:void 0,animate:p?{opacity:1,y:0}:void 0,transition:p?{delay:.1}:void 0,children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx(Ee,{icon:"mail",size:"sm"}),r.jsx("h3",{className:"text-lg font-semibold",children:"New Message"})]}),r.jsxs("div",{className:"flex items-center gap-1",children:[r.jsx(le,{variant:"ghost",size:"sm",onClick:l,children:r.jsx(F.Minimize2,{className:"h-4 w-4"})}),r.jsx(le,{variant:"ghost",size:"sm",onClick:u,children:r.jsx(F.Maximize2,{className:"h-4 w-4"})}),r.jsx(le,{variant:"ghost",size:"sm",onClick:i,children:r.jsx(F.X,{className:"h-4 w-4"})})]})]}),r.jsxs(v.motion.div,{className:"flex-1 flex flex-col overflow-hidden",initial:p?{opacity:0}:void 0,animate:p?{opacity:1}:void 0,transition:p?{delay:.2}:void 0,children:[r.jsxs("div",{className:"p-4 space-y-3 border-b",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("label",{className:"text-sm font-medium w-12",children:"To:"}),r.jsx("input",{type:"email",value:j.to,onChange:Z=>G("to",Z.target.value),className:"flex-1 px-3 py-2 border rounded-md bg-background text-sm",placeholder:"<EMAIL>"}),r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>W(!$),children:"Cc"}),r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>V(!D),children:"Bcc"})]}),r.jsx(v.AnimatePresence,{children:$&&r.jsxs(v.motion.div,{className:"flex items-center gap-2",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},children:[r.jsx("label",{className:"text-sm font-medium w-12",children:"Cc:"}),r.jsx("input",{type:"email",value:j.cc,onChange:Z=>G("cc",Z.target.value),className:"flex-1 px-3 py-2 border rounded-md bg-background text-sm",placeholder:"<EMAIL>"})]})}),r.jsx(v.AnimatePresence,{children:D&&r.jsxs(v.motion.div,{className:"flex items-center gap-2",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},children:[r.jsx("label",{className:"text-sm font-medium w-12",children:"Bcc:"}),r.jsx("input",{type:"email",value:j.bcc,onChange:Z=>G("bcc",Z.target.value),className:"flex-1 px-3 py-2 border rounded-md bg-background text-sm",placeholder:"<EMAIL>"})]})}),r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("label",{className:"text-sm font-medium w-12",children:"Subject:"}),r.jsx("input",{type:"text",value:j.subject,onChange:Z=>G("subject",Z.target.value),className:"flex-1 px-3 py-2 border rounded-md bg-background text-sm",placeholder:"Email subject"})]})]}),h&&r.jsxs(v.motion.div,{className:"flex items-center gap-1 p-2 border-b bg-gray-50 dark:bg-gray-800",initial:p?{opacity:0,y:-10}:void 0,animate:p?{opacity:1,y:0}:void 0,transition:p?{delay:.3}:void 0,children:[r.jsx(le,{variant:"ghost",size:"sm",children:r.jsx(F.Bold,{className:"h-4 w-4"})}),r.jsx(le,{variant:"ghost",size:"sm",children:r.jsx(F.Italic,{className:"h-4 w-4"})}),r.jsx(le,{variant:"ghost",size:"sm",children:r.jsx(F.Underline,{className:"h-4 w-4"})}),r.jsx("div",{className:"w-px h-4 bg-border mx-1"}),r.jsx(le,{variant:"ghost",size:"sm",children:r.jsx(F.AlignLeft,{className:"h-4 w-4"})}),r.jsx(le,{variant:"ghost",size:"sm",children:r.jsx(F.AlignCenter,{className:"h-4 w-4"})}),r.jsx(le,{variant:"ghost",size:"sm",children:r.jsx(F.AlignRight,{className:"h-4 w-4"})}),r.jsx("div",{className:"w-px h-4 bg-border mx-1"}),r.jsx(le,{variant:"ghost",size:"sm",children:r.jsx(F.List,{className:"h-4 w-4"})}),r.jsx(le,{variant:"ghost",size:"sm",children:r.jsx(F.Link,{className:"h-4 w-4"})})]}),r.jsx("div",{className:"flex-1 p-4",children:r.jsx("textarea",{ref:Y,value:j.body,onChange:Z=>G("body",Z.target.value),className:"w-full h-full resize-none border-0 outline-0 bg-transparent text-sm",placeholder:x,autoFocus:y,onDragOver:Z=>{Z.preventDefault(),K(!0)},onDragLeave:()=>K(!1),onDrop:Z=>{Z.preventDefault(),K(!1),B(Z.dataTransfer.files)}})}),r.jsx(v.AnimatePresence,{children:k.length>0&&r.jsx(v.motion.div,{className:"p-4 border-t bg-gray-50 dark:bg-gray-800",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},children:r.jsx("div",{className:"flex flex-wrap gap-2",children:k.map(Z=>r.jsxs(v.motion.div,{className:"flex items-center gap-2 px-3 py-2 bg-white dark:bg-gray-900 rounded-md border",initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},children:[r.jsx(F.Paperclip,{className:"h-4 w-4 text-gray-500"}),r.jsx("span",{className:"text-sm",children:Z.name}),r.jsx("span",{className:"text-xs text-gray-500",children:te(Z.size)}),r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>_(Z.id),children:r.jsx(F.X,{className:"h-3 w-3"})})]},Z.id))})})}),r.jsx(v.AnimatePresence,{children:M&&r.jsx(v.motion.div,{className:"absolute inset-0 bg-blue-500/20 border-2 border-dashed border-blue-500 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:r.jsxs("div",{className:"text-center",children:[r.jsx(F.Paperclip,{className:"h-8 w-8 text-blue-500 mx-auto mb-2"}),r.jsx("p",{className:"text-blue-600 font-medium",children:"Drop files to attach"})]})})})]}),r.jsxs(v.motion.div,{className:"flex items-center justify-between p-4 border-t bg-gray-50 dark:bg-gray-800",initial:p?{opacity:0,y:20}:void 0,animate:p?{opacity:1,y:0}:void 0,transition:p?{delay:.4}:void 0,children:[r.jsxs("div",{className:"flex items-center gap-2",children:[b&&r.jsxs(r.Fragment,{children:[r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>se.current?.click(),children:r.jsx(F.Paperclip,{className:"h-4 w-4"})}),r.jsx(le,{variant:"ghost",size:"sm",children:r.jsx(F.Image,{className:"h-4 w-4"})}),r.jsx(le,{variant:"ghost",size:"sm",children:r.jsx(F.Smile,{className:"h-4 w-4"})})]}),r.jsx("input",{ref:se,type:"file",multiple:!0,className:"hidden",onChange:Z=>B(Z.target.files)})]}),r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx(le,{variant:"ghost",onClick:i,disabled:U,children:"Cancel"}),r.jsxs(le,{onClick:P,disabled:!j.to||!j.subject||!j.body||U,loading:U,className:"gap-2",children:[r.jsx(F.Send,{className:"h-4 w-4"}),"Send"]})]})]})]})})});Ur.displayName="EmailCompose";const nd=jt("flex flex-col bg-background border rounded-lg overflow-hidden",{variants:{variant:{default:"bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700",glass:"backdrop-blur-md bg-white/80 dark:bg-gray-900/80 border-white/20 dark:border-gray-700/30",minimal:"bg-transparent border-0"},size:{sm:"max-h-96",md:"max-h-[60vh]",lg:"max-h-[80vh]",full:"h-full"}},defaultVariants:{variant:"default",size:"md"}}),ad=jt("flex items-center gap-3 p-4 border-b last:border-b-0 cursor-pointer transition-all duration-200 relative overflow-hidden",{variants:{variant:{default:"hover:bg-gray-50 dark:hover:bg-gray-800 border-gray-200 dark:border-gray-700",glass:"hover:bg-white/60 dark:hover:bg-gray-800/60 border-white/10 dark:border-gray-700/20",minimal:"hover:bg-gray-50 dark:hover:bg-gray-800 border-gray-100 dark:border-gray-800"},state:{unread:"bg-blue-50/50 dark:bg-blue-900/10 font-medium",read:"opacity-80",selected:"bg-blue-100 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700",archived:"opacity-50",deleted:"opacity-30"},priority:{low:"",normal:"",high:"border-l-4 border-l-red-500"}},defaultVariants:{variant:"default",state:"read",priority:"normal"}}),$r=me.forwardRef(({className:e,variant:t,size:s,emails:n,selectedEmails:a=[],onEmailSelect:i,onEmailToggle:o,onEmailAction:l,onBulkAction:u,loading:d=!1,animated:m=!0,showPreview:p=!0,showActions:y=!0,showSearch:w=!0,showFilters:g=!0,groupBy:f="none",sortBy:h="date",sortOrder:b="desc",searchQuery:x="",onSearchChange:N,emptyState:R,virtualScrolling:I=!1,itemHeight:j=80,...E},k)=>{const[L,$]=me.useState(null),[W,D]=me.useState(new Set),V=P=>{const B=new Date,_=P.toDateString()===B.toDateString(),te=P.toDateString()===new Date(B.getTime()-24*60*60*1e3).toDateString();return _?P.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):te?"Yesterday":P.toLocaleDateString([],{month:"short",day:"numeric"})},z=(P,B=100)=>{const _=P.replace(/<[^>]*>/g,"").replace(/\s+/g," ").trim();return _.length>B?_.substring(0,B)+"...":_},O=P=>{if(f==="none")return{"All Emails":P};const B={};return P.forEach(_=>{let te="";switch(f){case"date":const H=_.date,Z=new Date;H.toDateString()===Z.toDateString()?te="Today":H.toDateString()===new Date(Z.getTime()-24*60*60*1e3).toDateString()?te="Yesterday":te=H.toLocaleDateString([],{month:"long",day:"numeric"});break;case"sender":te=_.from;break;default:te="All Emails"}B[te]||(B[te]=[]),B[te].push(_)}),B},M=P=>[...P].sort((B,_)=>{let te=0;switch(h){case"date":te=B.date.getTime()-_.date.getTime();break;case"sender":te=B.from.localeCompare(_.from);break;case"subject":te=B.subject.localeCompare(_.subject);break;case"priority":const H={high:3,normal:2,low:1};te=H[B.priority]-H[_.priority];break}return b==="asc"?te:-te}),K=me.useMemo(()=>{let P=n;return x&&(P=P.filter(B=>B.subject.toLowerCase().includes(x.toLowerCase())||B.from.toLowerCase().includes(x.toLowerCase())||B.body.toLowerCase().includes(x.toLowerCase()))),M(P)},[n,x,h,b]),U=me.useMemo(()=>O(K),[K,f]),ee=P=>{D(B=>{const _=new Set(B);return _.has(P)?_.delete(P):_.add(P),_})},Y=(P,B,_)=>{_.stopPropagation(),l?.(P,B)},se=P=>{u?.(P,a)},G=P=>m?{initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{delay:P*.05,duration:.3}},exit:{opacity:0,y:-20},whileHover:{scale:1.01,transition:{duration:.2}}}:{};return r.jsxs("div",{ref:k,className:S(nd({variant:t,size:s}),e),...E,children:[r.jsxs("div",{className:"flex items-center justify-between p-4 border-b bg-gray-50 dark:bg-gray-800",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsxs("h2",{className:"text-lg font-semibold",children:["Inbox ",K.length>0&&`(${K.length})`]}),a.length>0&&r.jsxs(v.motion.div,{className:"flex items-center gap-2",initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},children:[r.jsxs("span",{className:"text-sm text-gray-500",children:[a.length," selected"]}),r.jsxs("div",{className:"flex items-center gap-1",children:[r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>se("archive"),children:r.jsx(F.Archive,{className:"h-4 w-4"})}),r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>se("delete"),children:r.jsx(F.Trash2,{className:"h-4 w-4"})}),r.jsx(le,{variant:"ghost",size:"sm",onClick:()=>se("star"),children:r.jsx(F.Star,{className:"h-4 w-4"})})]})]})]}),r.jsxs("div",{className:"flex items-center gap-2",children:[w&&r.jsxs("div",{className:"relative",children:[r.jsx(F.Search,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),r.jsx("input",{type:"text",placeholder:"Search emails...",value:x,onChange:P=>N?.(P.target.value),className:"pl-10 pr-4 py-2 border rounded-md bg-background text-sm w-64"})]}),g&&r.jsx(le,{variant:"ghost",size:"sm",children:r.jsx(F.Filter,{className:"h-4 w-4"})})]})]}),r.jsx("div",{className:"flex-1 overflow-hidden",children:d?r.jsx("div",{className:"flex items-center justify-center h-32",children:r.jsx(v.motion.div,{className:"h-8 w-8 rounded-full border-2 border-primary border-t-transparent",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}})}):K.length===0?r.jsx("div",{className:"flex items-center justify-center h-32 text-gray-500",children:R||r.jsxs("div",{className:"text-center",children:[r.jsx(F.Mail,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),r.jsx("p",{children:"No emails found"})]})}):r.jsx("div",{className:"overflow-y-auto h-full",children:Object.entries(U).map(([P,B])=>r.jsxs("div",{children:[f!=="none"&&r.jsx(v.motion.div,{className:"flex items-center justify-between p-3 bg-gray-100 dark:bg-gray-800 cursor-pointer",onClick:()=>ee(P),initial:{opacity:0},animate:{opacity:1},children:r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx(F.ChevronDown,{className:S("h-4 w-4 transition-transform",W.has(P)?"rotate-0":"-rotate-90")}),r.jsx("span",{className:"font-medium",children:P}),r.jsxs("span",{className:"text-sm text-gray-500",children:["(",B.length,")"]})]})}),r.jsx(v.AnimatePresence,{children:(f==="none"||W.has(P))&&r.jsx(v.motion.div,{initial:{height:0},animate:{height:"auto"},exit:{height:0},transition:{duration:.2},children:B.map((_,te)=>r.jsxs(v.motion.div,{className:S(ad({variant:t,state:a.includes(_.id)?"selected":_.isRead?"read":"unread",priority:_.priority})),onClick:()=>i?.(_.id),onMouseEnter:()=>$(_.id),onMouseLeave:()=>$(null),...G(te),children:[r.jsx(v.motion.div,{className:"flex items-center",initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},children:r.jsx("input",{type:"checkbox",checked:a.includes(_.id),onChange:()=>o?.(_.id),className:"rounded border-gray-300",onClick:H=>H.stopPropagation()})}),r.jsx(v.motion.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:r.jsx(Ee,{icon:"star",size:"sm",variant:_.isStarred?"warning":"default",onClick:H=>Y("star",_.id,H)})}),r.jsx("div",{className:"flex-shrink-0",children:_.avatar?r.jsx("img",{src:_.avatar,alt:_.from,className:"h-8 w-8 rounded-full"}):r.jsx("div",{className:"h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center",children:r.jsx(F.User,{className:"h-4 w-4 text-gray-500"})})}),r.jsxs("div",{className:"flex-1 min-w-0",children:[r.jsxs("div",{className:"flex items-center justify-between mb-1",children:[r.jsx("span",{className:"font-medium truncate",children:_.from}),r.jsxs("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[_.hasAttachments&&r.jsx(F.Paperclip,{className:"h-3 w-3"}),_.isFlagged&&r.jsx(F.Flag,{className:"h-3 w-3 text-red-500"}),r.jsx("span",{children:V(_.date)})]})]}),r.jsx("div",{className:"text-sm font-medium mb-1 truncate",children:_.subject}),p&&r.jsx("div",{className:"text-xs text-gray-500 truncate",children:z(_.body)}),_.labels.length>0&&r.jsx("div",{className:"flex gap-1 mt-1",children:_.labels.map(H=>r.jsx("span",{className:"px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full",children:H},H))})]}),r.jsx(v.AnimatePresence,{children:y&&L===_.id&&r.jsxs(v.motion.div,{className:"flex items-center gap-1",initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.1},children:[r.jsx(gt.Archive,{size:"sm",onClick:H=>Y("archive",_.id,H)}),r.jsx(gt.Delete,{size:"sm",onClick:H=>Y("delete",_.id,H)}),r.jsx(gt.Reply,{size:"sm",onClick:H=>Y("reply",_.id,H)}),r.jsx(le,{variant:"ghost",size:"sm",onClick:H=>Y("more",_.id,H),children:r.jsx(F.MoreHorizontal,{className:"h-4 w-4"})})]})}),_.status&&r.jsx(ds,{status:_.status,size:"sm",className:"ml-2"}),!_.isRead&&r.jsx(v.motion.div,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-2 w-2 bg-blue-500 rounded-full",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:400,damping:10}})]},_.id))})})]},P))})})]})});$r.displayName="EmailList";const id=Object.freeze(Object.defineProperty({__proto__:null,EmailActions:gt,EmailCompose:Ur,EmailIcon:Ee,EmailList:$r,EmailStatusIndicator:ds},Symbol.toStringTag,{value:"Module"})),od=c.createContext(void 0),ld=()=>{const e=c.useContext(od);if(!e)throw new Error("useIntegrationContext must be used within IntegrationProvider");return e};function cd({initialState:e="dormant",autoTransition:t=!1,transitionDelay:s=300,onStateChange:n}={}){const[a,i]=c.useState(e),[o,l]=c.useState(!1),u=c.useRef(null),d=c.useRef(null);c.useEffect(()=>()=>{u.current&&clearTimeout(u.current),d.current&&clearTimeout(d.current)},[]);const m=c.useCallback(f=>{a===f||o||(l(!0),i(f),n?.(f),u.current&&clearTimeout(u.current),u.current=setTimeout(()=>{l(!1)},s))},[a,o,s,n]),p=c.useCallback(async()=>new Promise(f=>{m("processing");const h=setTimeout(()=>{m("dormant"),f()},2e3);u.current=h}),[m]),y=c.useCallback(async(f=1500)=>new Promise(h=>{m("communicating");const b=setTimeout(()=>{m("dormant"),h()},f);u.current=b}),[m]),w=c.useCallback(async(f=2e3)=>new Promise(h=>{m("voice");const b=setTimeout(()=>{m("dormant"),h()},f);u.current=b}),[m]),g=c.useCallback(()=>{u.current&&clearTimeout(u.current),d.current&&clearTimeout(d.current),l(!1),i(e)},[e]);return c.useEffect(()=>{if(!t)return;const f=async()=>{a==="dormant"&&(await new Promise(h=>{d.current=setTimeout(h,3e3)}),m("processing"),await new Promise(h=>{d.current=setTimeout(h,2e3)}),m("communicating"),await new Promise(h=>{d.current=setTimeout(h,1500)}),m("voice"),await new Promise(h=>{d.current=setTimeout(h,2e3)}),m("dormant"))};if(a==="dormant"&&!o){const h=setTimeout(f,1e3);d.current=h}return()=>{d.current&&clearTimeout(d.current)}},[t,a,o,m]),{currentState:a,isTransitioning:o,setState:m,triggerProcessing:p,triggerCommunication:y,triggerVoice:w,reset:g}}const ud=({position:e="bottom-right",initialOpen:t=!1,showBadge:s=!0,context:n})=>{const[a,i]=c.useState(t),[o,l]=c.useState(""),[u,d]=c.useState([]),m=c.useRef(null),p=c.useRef(null),{connected:y,sendMessage:w}=ld(),{state:g,sendMessage:f}=cd(),h=()=>{p.current?.scrollIntoView({behavior:"smooth"})};c.useEffect(()=>{h()},[u]);const b=async()=>{if(!o.trim()||!y)return;const N={id:Date.now().toString(),content:o,role:"user",timestamp:new Date};d(R=>[...R,N]),l("");try{const R=await f(o,{context:n}),I={id:(Date.now()+1).toString(),content:R.content,role:"assistant",timestamp:new Date};d(j=>[...j,I])}catch(R){console.error("Failed to send message:",R)}},x=()=>{const N={position:"fixed",zIndex:1e3};switch(e){case"bottom-right":return{...N,bottom:20,right:20};case"bottom-left":return{...N,bottom:20,left:20};case"top-right":return{...N,top:20,right:20};case"top-left":return{...N,top:20,left:20}}};return r.jsxs(r.Fragment,{children:[r.jsx(Te.IconButton,{ref:m,onClick:()=>i(!a),sx:{...x(),bgcolor:"primary.main",color:"white","&:hover":{bgcolor:"primary.dark"},width:56,height:56},children:r.jsx(Te.Badge,{badgeContent:g.unreadCount,color:"error",invisible:!s||g.unreadCount===0,children:r.jsx(Ot.SmartToy,{})})}),r.jsxs(Te.Popover,{open:a,anchorEl:m.current,onClose:()=>i(!1),anchorOrigin:{vertical:e.startsWith("bottom")?"top":"bottom",horizontal:e.endsWith("right")?"left":"right"},transformOrigin:{vertical:e.startsWith("bottom")?"bottom":"top",horizontal:e.endsWith("right")?"right":"left"},PaperProps:{sx:{width:350,height:500,display:"flex",flexDirection:"column"}},children:[r.jsxs(Te.Paper,{sx:{p:2,borderBottom:1,borderColor:"divider"},children:[r.jsxs(Te.Box,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[r.jsxs(Te.Box,{sx:{display:"flex",alignItems:"center",gap:1},children:[r.jsx(Ot.SmartToy,{color:"primary"}),r.jsx(Te.Typography,{variant:"h6",children:"AMNA Assistant"})]}),r.jsx(Te.IconButton,{size:"small",onClick:()=>i(!1),children:r.jsx(Ot.Close,{})})]}),y?r.jsx(Te.Chip,{label:"Connected",color:"success",size:"small",sx:{mt:1}}):r.jsx(Te.Chip,{label:"Offline",color:"error",size:"small",sx:{mt:1}})]}),r.jsx(Te.Box,{sx:{flex:1,overflow:"auto",p:2},children:u.length===0?r.jsx(Te.Typography,{variant:"body2",color:"text.secondary",align:"center",sx:{mt:4},children:"Hi! I'm AMNA, your AI assistant. How can I help you today?"}):r.jsxs(Te.List,{children:[u.map((N,R)=>r.jsx(Te.ListItem,{sx:{flexDirection:"column",alignItems:N.role==="user"?"flex-end":"flex-start"},children:r.jsxs(Te.Paper,{sx:{p:1.5,maxWidth:"80%",bgcolor:N.role==="user"?"primary.main":"grey.100",color:N.role==="user"?"white":"text.primary"},children:[r.jsx(Te.Typography,{variant:"body2",children:N.content}),r.jsx(Te.Typography,{variant:"caption",sx:{display:"block",mt:.5,opacity:.7},children:N.timestamp.toLocaleTimeString()})]})},N.id)),r.jsx("div",{ref:p})]})}),r.jsx(Te.Divider,{}),r.jsx(Te.Box,{sx:{p:2},children:r.jsx(Te.TextField,{fullWidth:!0,size:"small",placeholder:"Type your message...",value:o,onChange:N=>l(N.target.value),onKeyPress:N=>{N.key==="Enter"&&!N.shiftKey&&(N.preventDefault(),b())},disabled:!y,InputProps:{endAdornment:r.jsx(Te.IconButton,{size:"small",onClick:b,disabled:!y||!o.trim(),children:r.jsx(Ot.Send,{})})}})})]})]})},dd=["React","Vue","Angular","Next.js","Svelte","TailwindCSS","TypeScript","JavaScript","Node.js"],md=()=>r.jsx("svg",{style:{position:"absolute",width:0,height:0},"aria-hidden":"true",children:r.jsx("defs",{children:r.jsxs("filter",{id:"gooey-effect",children:[r.jsx("feGaussianBlur",{in:"SourceGraphic",stdDeviation:"7",result:"blur"}),r.jsx("feColorMatrix",{in:"blur",type:"matrix",values:"1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 19 -8",result:"goo"}),r.jsx("feComposite",{in:"SourceGraphic",in2:"goo",operator:"atop"})]})})}),pd=({placeholder:e="Search...",onSearch:t})=>{const s=c.useRef(null),[n,a]=c.useState(!1),[i,o]=c.useState(""),[l,u]=c.useState(!1),[d,m]=c.useState([]),[p,y]=c.useState(!1),[w,g]=c.useState({x:0,y:0}),f=c.useMemo(()=>{if(typeof window>"u")return!1;const k=navigator.userAgent.toLowerCase(),L=k.includes("safari")&&!k.includes("chrome")&&!k.includes("chromium"),$=k.includes("crios");return L||$},[]),h=k=>{const L=k.target.value;if(o(L),L.trim()){const $=dd.filter(W=>W.toLowerCase().includes(L.toLowerCase()));m($)}else m([])},b=k=>{k.preventDefault(),t&&i.trim()&&(t(i),u(!0),setTimeout(()=>u(!1),1e3))},x=k=>{if(n){const L=k.currentTarget.getBoundingClientRect();g({x:k.clientX-L.left,y:k.clientY-L.top})}},N=k=>{const L=k.currentTarget.getBoundingClientRect();g({x:k.clientX-L.left,y:k.clientY-L.top}),y(!0),setTimeout(()=>y(!1),800)};c.useEffect(()=>{n&&s.current&&s.current.focus()},[n]);const R={initial:{scale:1},animate:{rotate:l?[0,-15,15,-10,10,0]:0,scale:l?[1,1.3,1]:1,transition:{duration:.6,ease:"easeInOut"}}},I={hidden:k=>({opacity:0,y:-10,scale:.95,transition:{duration:.15,delay:k*.05}}),visible:k=>({opacity:1,y:0,scale:1,transition:{type:"spring",stiffness:300,damping:15,delay:k*.07}}),exit:k=>({opacity:0,y:-5,scale:.9,transition:{duration:.1,delay:k*.03}})},j=Array.from({length:n?18:0},(k,L)=>r.jsx(v.motion.div,{initial:{scale:0},animate:{x:[0,(Math.random()-.5)*40],y:[0,(Math.random()-.5)*40],scale:[0,Math.random()*.8+.4],opacity:[0,.8,0]},transition:{duration:Math.random()*1.5+1.5,ease:"easeInOut",repeat:Number.POSITIVE_INFINITY,repeatType:"reverse"},className:"absolute w-3 h-3 rounded-full bg-gradient-to-r from-purple-400 to-pink-400",style:{left:`${Math.random()*100}%`,top:`${Math.random()*100}%`,filter:"blur(2px)"}},L)),E=p?Array.from({length:14},(k,L)=>r.jsx(v.motion.div,{initial:{x:w.x,y:w.y,scale:0,opacity:1},animate:{x:w.x+(Math.random()-.5)*160,y:w.y+(Math.random()-.5)*160,scale:Math.random()*.8+.2,opacity:[1,0]},transition:{duration:Math.random()*.8+.5,ease:"easeOut"},className:"absolute w-3 h-3 rounded-full",style:{background:`rgba(${Math.floor(Math.random()*255)}, ${Math.floor(Math.random()*200)+55}, ${Math.floor(Math.random()*255)}, 0.8)`,boxShadow:"0 0 8px rgba(255, 255, 255, 0.8)"}},`click-${L}`)):null;return r.jsxs("div",{className:"relative w-full",children:[r.jsx(md,{}),r.jsx(v.motion.form,{onSubmit:b,className:"relative flex items-center justify-center w-full mx-auto",initial:{width:"240px"},animate:{width:n?"340px":"240px",scale:n?1.05:1},transition:{type:"spring",stiffness:400,damping:25},onMouseMove:x,children:r.jsxs(v.motion.div,{className:S("flex items-center w-full rounded-full border relative overflow-hidden backdrop-blur-md",n?"border-transparent shadow-xl":"border-gray-200 dark:border-gray-700 bg-white/30 dark:bg-gray-800/50"),animate:{boxShadow:p?"0 0 40px rgba(139, 92, 246, 0.5), 0 0 15px rgba(236, 72, 153, 0.7) inset":n?"0 15px 35px rgba(0, 0, 0, 0.2)":"0 0 0 rgba(0, 0, 0, 0)"},onClick:N,children:[n&&r.jsx(v.motion.div,{className:"absolute inset-0 -z-10",initial:{opacity:0},animate:{opacity:.15,background:["linear-gradient(90deg, #f6d365 0%, #fda085 100%)","linear-gradient(90deg, #a1c4fd 0%, #c2e9fb 100%)","linear-gradient(90deg, #d4fc79 0%, #96e6a1 100%)","linear-gradient(90deg, #f6d365 0%, #fda085 100%)"]},transition:{duration:15,repeat:Number.POSITIVE_INFINITY,ease:"linear"}}),r.jsx("div",{className:"absolute inset-0 overflow-hidden rounded-full -z-5",style:{filter:f?"none":"url(#gooey-effect)"},children:j}),p&&r.jsxs(r.Fragment,{children:[r.jsx(v.motion.div,{className:"absolute inset-0 -z-5 rounded-full bg-purple-400/10",initial:{scale:0,opacity:.7},animate:{scale:2,opacity:0},transition:{duration:.8,ease:"easeOut"}}),r.jsx(v.motion.div,{className:"absolute inset-0 -z-5 rounded-full bg-white dark:bg-white/20",initial:{opacity:.5},animate:{opacity:0},transition:{duration:.3,ease:"easeOut"}})]}),E,r.jsx(v.motion.div,{className:"pl-4 py-3",variants:R,initial:"initial",animate:"animate",children:r.jsx(F.Search,{size:20,strokeWidth:n?2.5:2,className:S("transition-all duration-300",l?"text-purple-500":n?"text-purple-600":"text-gray-500 dark:text-gray-300")})}),r.jsx("input",{ref:s,type:"text",placeholder:e,value:i,onChange:h,onFocus:()=>a(!0),onBlur:()=>setTimeout(()=>a(!1),200),className:S("w-full py-3 bg-transparent outline-none placeholder:text-gray-400 dark:placeholder:text-gray-500 font-medium text-base relative z-10",n?"text-gray-800 dark:text-white tracking-wide":"text-gray-600 dark:text-gray-300")}),r.jsx(v.AnimatePresence,{children:i&&r.jsx(v.motion.button,{type:"submit",initial:{opacity:0,scale:.8,x:-20},animate:{opacity:1,scale:1,x:0},exit:{opacity:0,scale:.8,x:-20},whileHover:{scale:1.05,background:"linear-gradient(45deg, #8B5CF6 0%, #EC4899 100%)",boxShadow:"0 10px 25px -5px rgba(139, 92, 246, 0.5)"},whileTap:{scale:.95},className:"px-5 py-2 mr-2 text-sm font-medium rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white backdrop-blur-sm transition-all shadow-lg",children:"Search"})}),n&&r.jsx(v.motion.div,{className:"absolute inset-0 rounded-full",initial:{opacity:0},animate:{opacity:[0,.1,.2,.1,0],background:"radial-gradient(circle at 50% 0%, rgba(255,255,255,0.8) 0%, transparent 70%)"},transition:{duration:2,repeat:Number.POSITIVE_INFINITY,repeatType:"loop"}})]})}),r.jsx(v.AnimatePresence,{children:n&&d.length>0&&r.jsx(v.motion.div,{initial:{opacity:0,y:10,height:0},animate:{opacity:1,y:0,height:"auto"},exit:{opacity:0,y:10,height:0},transition:{duration:.2},className:"absolute z-10 w-full mt-2 overflow-hidden bg-white/90 dark:bg-gray-900/90 backdrop-blur-md rounded-lg shadow-xl border border-gray-100 dark:border-gray-700",style:{maxHeight:"300px",overflowY:"auto",filter:f?"none":"drop-shadow(0 15px 15px rgba(0,0,0,0.1))"},children:r.jsx("div",{className:"p-2",children:d.map((k,L)=>r.jsxs(v.motion.div,{custom:L,variants:I,initial:"hidden",animate:"visible",exit:"exit",onClick:()=>{o(k),t&&t(k),a(!1)},className:"flex items-center gap-2 px-4 py-2 cursor-pointer rounded-md hover:bg-purple-50 dark:hover:bg-purple-900/20 group",children:[r.jsx(v.motion.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:L*.06},children:r.jsx(F.CircleDot,{size:16,className:"text-purple-400 group-hover:text-purple-600"})}),r.jsx(v.motion.span,{className:"text-gray-700 dark:text-gray-100 group-hover:text-purple-700 dark:group-hover:text-purple-400",initial:{x:-5,opacity:0},animate:{x:0,opacity:1},transition:{delay:L*.08},children:k})]},k))})})})]})};function fd(e){return e&&typeof e.role=="string"&&typeof e.content=="string"}function hd(e){return e.metadata?.generationId!==void 0}function gd(e){return e&&typeof e.code=="string"&&typeof e.message=="string"}function yd(e){return e&&typeof e.name=="string"&&typeof e.parameters=="object"}const bd={GPT_4O:"gpt-4o",GPT_4O_MINI:"gpt-4o-mini",GPT_4_TURBO:"gpt-4-turbo",GPT_4:"gpt-4",GPT_3_5_TURBO:"gpt-3.5-turbo"},xd={OPENAI:"openai",ANTHROPIC:"anthropic",GOOGLE:"google",COHERE:"cohere",HUGGINGFACE:"huggingface"},wd={TEXT_GENERATION:"text-generation",IMAGE_GENERATION:"image-generation",VOICE_SYNTHESIS:"voice-synthesis",FUNCTION_CALLING:"function-calling",VISION:"vision",CODE_GENERATION:"code-generation",STREAMING:"streaming",EMBEDDINGS:"embeddings"},vd={SLOW:"slow",NORMAL:"normal",FAST:"fast"},jd={APA:"APA",MLA:"MLA",CHICAGO:"Chicago",HARVARD:"Harvard",IEEE:"IEEE"},Cd=()=>{const e=Ge(),t=c.useMemo(()=>e.user?{id:e.user.id,email:e.user.email,name:e.user.name,departmentId:e.user.departmentId,roleId:e.user.roleId,roles:e.getUserRoles(),permissions:e.getUserPermissions(),isAdmin:e.hasRole("admin")||e.hasRole("super_admin"),isSuperAdmin:e.hasRole("super_admin"),department:e.user.department}:null,[e.user,e.getUserRoles,e.getUserPermissions,e.hasRole]);return{...e,userProfile:t}},kd=()=>{const{hasPermission:e,hasRole:t,hasAnyRole:s,hasAllRoles:n,getUserPermissions:a,getUserRoles:i}=Ge(),o=c.useMemo(()=>a(),[a]),l=c.useMemo(()=>i(),[i]),u=c.useCallback((f,h)=>e(f,h),[e]),d=c.useCallback(f=>t(f),[t]),m=c.useCallback(f=>s(f),[s]),p=c.useCallback(f=>n(f),[n]),y=c.useCallback((f,h)=>e(h,f),[e]),w=c.useCallback(f=>o.filter(h=>h.resource===f),[o]),g=c.useCallback(f=>o.some(h=>h.resource===f),[o]);return{permissions:o,roles:l,checkPermission:u,checkRole:d,checkAnyRole:m,checkAllRoles:p,canAccessResource:y,getResourcePermissions:w,hasResourceAccess:g}},Nd=(e,t,s=!1)=>{const{isAuthenticated:n,hasPermission:a,hasRole:i,hasAnyRole:o,hasAllRoles:l,isLoading:u}=Ge(),d=c.useMemo(()=>!(u||!n||t&&t.length>0&&!(s?l(t):o(t))||e&&e.length>0&&!(s?e.every(y=>a(y)):e.some(y=>a(y)))),[u,n,e,t,s,a,i,o,l]),m=c.useMemo(()=>u?"loading":n?t&&t.length>0&&!(s?l(t):o(t))?"insufficient_role":e&&e.length>0&&!(s?e.every(y=>a(y)):e.some(y=>a(y)))?"insufficient_permission":"authorized":"not_authenticated",[u,n,e,t,s,a,i,o,l]);return{canAccess:d,reason:m,isLoading:u,isAuthenticated:n}},Sd=()=>{const{user:e,refreshUser:t,logout:s}=Ge(),n=c.useCallback(async o=>{try{return await t(),!0}catch(l){return console.error("Failed to update profile:",l),!1}},[t]),a=c.useCallback(async(o,l)=>{try{return console.log("Password change requested"),!0}catch(u){return console.error("Failed to change password:",u),!1}},[]),i=c.useCallback(async()=>{try{return await s(),!0}catch(o){return console.error("Failed to terminate session:",o),!1}},[s]);return{user:e,updateProfile:n,changePassword:a,terminateSession:i,refreshUser:t}},Td=()=>{const{isAuthenticated:e,isLoading:t,user:s,refreshUser:n}=Ge(),a=c.useMemo(()=>!e||!s?null:{userId:s.id,email:s.email,name:s.name,roles:s.roles?.map(o=>o.name)||[],department:s.department?.name,isActive:e,lastRefresh:new Date().toISOString()},[e,s]),i=c.useCallback(async()=>{if(!e)return!1;try{return await n(),!0}catch(o){return console.error("Failed to extend session:",o),!1}},[e,n]);return{sessionInfo:a,isLoading:t,isAuthenticated:e,extendSession:i}},Rd=()=>{const{hasRole:e,hasPermission:t,isAuthenticated:s}=Ge(),n=c.useCallback((i,o,l)=>!(!s||o&&o.length>0&&!o.some(d=>e(d))||l&&l.length>0&&!l.some(d=>t(d))),[s,e,t]),a=c.useCallback(i=>{const o=[];return Object.entries(i).forEach(([l,u])=>{n(l,u.roles,u.permissions)&&o.push(l)}),o},[n]);return{canNavigateTo:n,getAvailableRoutes:a}},Ed=()=>{const{hasRole:e,hasPermission:t,isAuthenticated:s}=Ge(),n=c.useMemo(()=>s&&(e("admin")||e("super_admin")),[s,e]),a=c.useMemo(()=>s&&e("super_admin"),[s,e]),i=c.useMemo(()=>s&&t("manage","users"),[s,t]),o=c.useMemo(()=>s&&t("manage","roles"),[s,t]),l=c.useMemo(()=>s&&t("view","analytics"),[s,t]);return{isAdmin:n,isSuperAdmin:a,canManageUsers:i,canManageRoles:o,canViewAnalytics:l}};function Ad(e){return T.useQuery({queryKey:C.users.list(e),queryFn:()=>be.getUsers(e),staleTime:2*60*1e3})}function Pd(e){return T.useInfiniteQuery({queryKey:C.users.list(e),queryFn:({pageParam:t=1})=>be.getUsers({...e,page:t}),initialPageParam:1,getNextPageParam:(t,s)=>{const n=s.length,a=t.pagination?.totalPages||1;return n<a?n+1:void 0},staleTime:2*60*1e3})}function Ld(e,t=!0){return T.useQuery({queryKey:C.users.detail(e),queryFn:()=>be.getUser(e),enabled:t&&!!e,staleTime:5*60*1e3})}function Id(){return T.useQuery({queryKey:C.users.current(),queryFn:()=>be.getCurrentUser(),staleTime:5*60*1e3})}function Od(e,t){return T.useQuery({queryKey:C.users.activity(e),queryFn:()=>be.getUserActivity(e,t),staleTime:1*60*1e3})}function Dd(){return T.useQuery({queryKey:C.users.sessions(),queryFn:()=>be.getUserSessions(),staleTime:1*60*1e3})}function Md(){return T.useQuery({queryKey:C.users.notifications(),queryFn:()=>be.getUserNotifications(),staleTime:30*1e3})}function Fd(e){return T.useQuery({queryKey:C.users.stats(e),queryFn:()=>be.getUserStats(e),staleTime:10*60*1e3})}function _d(e){return T.useQuery({queryKey:C.users.growth(e),queryFn:()=>be.getUserGrowth(e),staleTime:10*60*1e3})}function Ud(e,t){return T.useQuery({queryKey:C.users.search(e,t),queryFn:()=>be.searchUsers(e,t),enabled:e.length>0,staleTime:2*60*1e3})}function $d(e){return T.useQuery({queryKey:C.users.suggestions(e),queryFn:()=>be.getUserSuggestions(e),staleTime:5*60*1e3})}function zd(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>be.createUser(t),onSuccess:t=>{ie.invalidateQueries(C.users.lists()),e.setQueryData(C.users.detail(t.data.id),t),re.success("User created successfully")},onError:t=>{re.error(t.userMessage||"Failed to create user")}})}function Qd(){const e=T.useQueryClient();return T.useMutation({mutationFn:({id:t,data:s})=>be.updateUser(t,s),onSuccess:(t,s)=>{e.setQueryData(C.users.detail(s.id),t),ie.invalidateQueries(C.users.lists()),re.success("User updated successfully")}})}function qd(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>be.updateCurrentUser(t),onSuccess:t=>{e.setQueryData(C.users.current(),t),t.data.id&&e.setQueryData(C.users.detail(t.data.id),t),re.success("Profile updated successfully")}})}function Bd(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>be.updateUserPreferences(t),onSuccess:t=>{e.setQueryData(C.users.current(),t),re.success("Preferences updated successfully")}})}function Vd(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>be.deleteUser(t),onSuccess:(t,s)=>{e.removeQueries({queryKey:C.users.detail(s)}),ie.invalidateQueries(C.users.lists()),re.success("User deleted successfully")}})}function Hd(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>be.activateUser(t),onSuccess:(t,s)=>{e.setQueryData(C.users.detail(s),t),ie.invalidateQueries(C.users.lists()),re.success("User activated successfully")}})}function Gd(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>be.deactivateUser(t),onSuccess:(t,s)=>{e.setQueryData(C.users.detail(s),t),ie.invalidateQueries(C.users.lists()),re.success("User deactivated successfully")}})}function Kd(){return T.useQueryClient(),T.useMutation({mutationFn:e=>be.bulkUpdateUsers(e),onSuccess:e=>{ie.invalidateQueries(C.users.lists()),re.success(`${e.data.updated} users updated successfully`),e.data.failed>0&&re.error(`${e.data.failed} users failed to update`)}})}function Wd(){return T.useQueryClient(),T.useMutation({mutationFn:e=>be.uploadAvatar(e),onSuccess:e=>{ie.invalidateQueries(C.users.current()),re.success("Avatar uploaded successfully")}})}function Yd(){return T.useMutation({mutationFn:e=>be.changePassword(e),onSuccess:()=>{re.success("Password changed successfully")}})}function Jd(){return T.useQueryClient(),T.useMutation({mutationFn:e=>be.revokeSession(e),onSuccess:()=>{ie.invalidateQueries(C.users.sessions()),re.success("Session revoked successfully")}})}function Xd(){return T.useQueryClient(),T.useMutation({mutationFn:()=>be.revokeAllSessions(),onSuccess:()=>{ie.invalidateQueries(C.users.sessions()),re.success("All sessions revoked successfully")}})}function Zd(){return T.useQueryClient(),T.useMutation({mutationFn:e=>be.markNotificationAsRead(e),onSuccess:()=>{ie.invalidateQueries(C.users.notifications())}})}function em(){return T.useQueryClient(),T.useMutation({mutationFn:()=>be.markAllNotificationsAsRead(),onSuccess:()=>{ie.invalidateQueries(C.users.notifications()),re.success("All notifications marked as read")}})}function tm(){return T.useQuery({queryKey:C.roles.list(),queryFn:()=>be.getRoles(),staleTime:10*60*1e3})}function sm(e,t=!0){return T.useQuery({queryKey:C.roles.detail(e),queryFn:()=>be.getRole(e),enabled:t&&!!e,staleTime:10*60*1e3})}function rm(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>be.createRole(t),onSuccess:t=>{ie.invalidateQueries(C.roles.lists()),e.setQueryData(C.roles.detail(t.data.id),t),re.success("Role created successfully")}})}function nm(){const e=T.useQueryClient();return T.useMutation({mutationFn:({id:t,data:s})=>be.updateRole(t,s),onSuccess:(t,s)=>{e.setQueryData(C.roles.detail(s.id),t),ie.invalidateQueries(C.roles.lists()),re.success("Role updated successfully")}})}function am(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>be.deleteRole(t),onSuccess:(t,s)=>{e.removeQueries({queryKey:C.roles.detail(s)}),ie.invalidateQueries(C.roles.lists()),re.success("Role deleted successfully")}})}function im(){return T.useQuery({queryKey:C.departments.list(),queryFn:()=>be.getDepartments(),staleTime:10*60*1e3})}function om(e,t=!0){return T.useQuery({queryKey:C.departments.detail(e),queryFn:()=>be.getDepartment(e),enabled:t&&!!e,staleTime:10*60*1e3})}function lm(e,t=!0){return T.useQuery({queryKey:C.departments.members(e),queryFn:()=>be.getDepartmentMembers(e),enabled:t&&!!e,staleTime:5*60*1e3})}function cm(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>be.createDepartment(t),onSuccess:t=>{ie.invalidateQueries(C.departments.lists()),e.setQueryData(C.departments.detail(t.data.id),t),re.success("Department created successfully")}})}function um(){const e=T.useQueryClient();return T.useMutation({mutationFn:({id:t,data:s})=>be.updateDepartment(t,s),onSuccess:(t,s)=>{e.setQueryData(C.departments.detail(s.id),t),ie.invalidateQueries(C.departments.lists()),re.success("Department updated successfully")}})}function dm(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>be.deleteDepartment(t),onSuccess:(t,s)=>{e.removeQueries({queryKey:C.departments.detail(s)}),ie.invalidateQueries(C.departments.lists()),re.success("Department deleted successfully")}})}function mm(){const e=T.useQueryClient();return(t,s)=>{const n=e.getQueryData(C.users.detail(t));return n&&e.setQueryData(C.users.detail(t),{...n,data:s(n.data)}),n}}function pm(){const e=T.useQueryClient();return t=>{const s=e.getQueryData(C.users.notifications());return s&&e.setQueryData(C.users.notifications(),{...s,data:s.data.map(n=>n.id===t?{...n,isRead:!0}:n)}),s}}function fm(e){return T.useQuery({queryKey:C.email.templates.list(e),queryFn:()=>xe.getTemplates(e),staleTime:5*60*1e3})}function hm(e,t=!0){return T.useQuery({queryKey:C.email.templates.detail(e),queryFn:()=>xe.getTemplate(e),enabled:t&&!!e,staleTime:5*60*1e3})}function gm(e,t,s=!0){return T.useQuery({queryKey:C.email.templates.preview(e,t),queryFn:()=>xe.previewTemplate(e,t),enabled:s&&!!e,staleTime:1*60*1e3})}function ym(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>xe.createTemplate(t),onSuccess:t=>{ie.invalidateQueries(C.email.templates.lists()),e.setQueryData(C.email.templates.detail(t.data.id),t),re.success("Email template created successfully")}})}function bm(){const e=T.useQueryClient();return T.useMutation({mutationFn:({id:t,data:s})=>xe.updateTemplate(t,s),onSuccess:(t,s)=>{e.setQueryData(C.email.templates.detail(s.id),t),ie.invalidateQueries(C.email.templates.lists()),re.success("Email template updated successfully")}})}function xm(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>xe.deleteTemplate(t),onSuccess:(t,s)=>{e.removeQueries({queryKey:C.email.templates.detail(s)}),ie.invalidateQueries(C.email.templates.lists()),re.success("Email template deleted successfully")}})}function wm(){return T.useMutation({mutationFn:({id:e,email:t,variables:s})=>xe.testTemplate(e,t,s),onSuccess:()=>{re.success("Test email sent successfully")}})}function vm(e){return T.useQuery({queryKey:C.email.campaigns.list(e),queryFn:()=>xe.getCampaigns(e),staleTime:2*60*1e3})}function jm(e,t=!0){return T.useQuery({queryKey:C.email.campaigns.detail(e),queryFn:()=>xe.getCampaign(e),enabled:t&&!!e,staleTime:2*60*1e3})}function Cm(e,t=!0){return T.useQuery({queryKey:C.email.campaigns.metrics(e),queryFn:()=>xe.getCampaignMetrics(e),enabled:t&&!!e,staleTime:1*60*1e3,refetchInterval:30*1e3})}function km(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>xe.createCampaign(t),onSuccess:t=>{ie.invalidateQueries(C.email.campaigns.lists()),e.setQueryData(C.email.campaigns.detail(t.data.id),t),re.success("Email campaign created successfully")}})}function Nm(){const e=T.useQueryClient();return T.useMutation({mutationFn:({id:t,data:s})=>xe.updateCampaign(t,s),onSuccess:(t,s)=>{e.setQueryData(C.email.campaigns.detail(s.id),t),ie.invalidateQueries(C.email.campaigns.lists()),re.success("Email campaign updated successfully")}})}function Sm(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>xe.deleteCampaign(t),onSuccess:(t,s)=>{e.removeQueries({queryKey:C.email.campaigns.detail(s)}),ie.invalidateQueries(C.email.campaigns.lists()),re.success("Email campaign deleted successfully")}})}function Tm(){return T.useQueryClient(),T.useMutation({mutationFn:e=>xe.sendCampaign(e),onSuccess:(e,t)=>{ie.invalidateQueries(C.email.campaigns.detail(t)),ie.invalidateQueries(C.email.campaigns.lists()),re.success("Email campaign sent successfully")}})}function Rm(){const e=T.useQueryClient();return T.useMutation({mutationFn:({id:t,scheduledAt:s})=>xe.scheduleCampaign(t,s),onSuccess:(t,s)=>{e.setQueryData(C.email.campaigns.detail(s.id),t),ie.invalidateQueries(C.email.campaigns.lists()),re.success("Email campaign scheduled successfully")}})}function Em(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>xe.pauseCampaign(t),onSuccess:(t,s)=>{e.setQueryData(C.email.campaigns.detail(s),t),ie.invalidateQueries(C.email.campaigns.lists()),re.success("Email campaign paused successfully")}})}function Am(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>xe.resumeCampaign(t),onSuccess:(t,s)=>{e.setQueryData(C.email.campaigns.detail(s),t),ie.invalidateQueries(C.email.campaigns.lists()),re.success("Email campaign resumed successfully")}})}function Pm(){return T.useQueryClient(),T.useMutation({mutationFn:e=>xe.sendEmail(e),onSuccess:()=>{ie.invalidateQueries(C.email.logs.lists()),re.success("Email sent successfully")}})}function Lm(){return T.useQueryClient(),T.useMutation({mutationFn:e=>xe.sendBulkEmail(e),onSuccess:e=>{ie.invalidateQueries(C.email.logs.lists()),ie.invalidateQueries(C.email.queue()),re.success(`${e.data.queued} emails queued for sending`)}})}function Im(){return T.useMutation({mutationFn:e=>xe.sendTransactionalEmail(e),onSuccess:()=>{re.success("Email sent successfully")}})}function Om(e){return T.useQuery({queryKey:C.email.logs.list(e),queryFn:()=>xe.getEmailLogs(e),staleTime:30*1e3})}function Dm(e,t=!0){return T.useQuery({queryKey:C.email.logs.detail(e),queryFn:()=>xe.getEmailLog(e),enabled:t&&!!e,staleTime:30*1e3})}function Mm(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>xe.retryFailedEmail(t),onSuccess:(t,s)=>{e.setQueryData(C.email.logs.detail(s),t),ie.invalidateQueries(C.email.logs.lists()),re.success("Email retry initiated")}})}function Fm(e){return T.useQuery({queryKey:C.email.recipients.list(e),queryFn:()=>xe.getRecipients(e),staleTime:5*60*1e3})}function _m(){return T.useQueryClient(),T.useMutation({mutationFn:e=>xe.addRecipient(e),onSuccess:()=>{ie.invalidateQueries(C.email.recipients.lists()),re.success("Email recipient added successfully")}})}function Um(){return T.useQueryClient(),T.useMutation({mutationFn:({id:e,data:t})=>xe.updateRecipient(e,t),onSuccess:()=>{ie.invalidateQueries(C.email.recipients.lists()),re.success("Email recipient updated successfully")}})}function $m(){return T.useQueryClient(),T.useMutation({mutationFn:e=>xe.deleteRecipient(e),onSuccess:()=>{ie.invalidateQueries(C.email.recipients.lists()),re.success("Email recipient deleted successfully")}})}function zm(){return T.useQueryClient(),T.useMutation({mutationFn:e=>xe.importRecipients(e),onSuccess:e=>{ie.invalidateQueries(C.email.recipients.lists()),re.success(`${e.data.imported} recipients imported successfully`),e.data.failed>0&&re.error(`${e.data.failed} recipients failed to import`)}})}function Qm(){return T.useQuery({queryKey:C.email.settings(),queryFn:()=>xe.getSettings(),staleTime:10*60*1e3})}function qm(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>xe.updateSettings(t),onSuccess:t=>{e.setQueryData(C.email.settings(),t),re.success("Email settings updated successfully")}})}function Bm(){return T.useMutation({mutationFn:()=>xe.testSettings(),onSuccess:e=>{e.data.success?re.success("Email settings test successful"):re.error(`Email settings test failed: ${e.data.message}`)}})}function Vm(e){return T.useQuery({queryKey:C.email.analytics(e),queryFn:()=>xe.getAnalytics(e),staleTime:5*60*1e3})}function Hm(){return T.useQuery({queryKey:C.email.queue(),queryFn:()=>xe.getQueueStatus(),staleTime:30*1e3,refetchInterval:30*1e3})}function Gm(){return T.useQueryClient(),T.useMutation({mutationFn:()=>xe.clearQueue(),onSuccess:()=>{ie.invalidateQueries(C.email.queue()),re.success("Email queue cleared successfully")}})}function Km(){return T.useQueryClient(),T.useMutation({mutationFn:()=>xe.retryFailedJobs(),onSuccess:e=>{ie.invalidateQueries(C.email.queue()),re.success(`${e.data.retriedCount} jobs retried successfully`)}})}function Wm(){const e=T.useQueryClient();return(t,s)=>{const n=e.getQueryData(C.email.templates.detail(t));return n&&e.setQueryData(C.email.templates.detail(t),{...n,data:s(n.data)}),n}}function Ym(){const e=T.useQueryClient();return(t,s)=>{const n=e.getQueryData(C.email.campaigns.detail(t));return n&&e.setQueryData(C.email.campaigns.detail(t),{...n,data:s(n.data)}),n}}function Jm(e,t=!0){return T.useQuery({queryKey:C.email.campaigns.detail(e),queryFn:()=>xe.getCampaign(e),enabled:t&&!!e,refetchInterval:s=>{const n=s?.data?.status;return n==="sending"||n==="scheduled"?5*1e3:30*1e3},staleTime:0})}function Xm(e){return T.useQuery({queryKey:C.training.courses.list(e),queryFn:()=>ve.getCourses(e),staleTime:5*60*1e3})}function Zm(e){return T.useInfiniteQuery({queryKey:C.training.courses.list(e),queryFn:({pageParam:t=1})=>ve.getCourses({...e,page:t}),initialPageParam:1,getNextPageParam:(t,s)=>{const n=s.length,a=t.pagination?.totalPages||1;return n<a?n+1:void 0},staleTime:5*60*1e3})}function ep(e,t=!0){return T.useQuery({queryKey:C.training.courses.detail(e),queryFn:()=>ve.getCourse(e),enabled:t&&!!e,staleTime:10*60*1e3})}function tp(e,t=!0){return T.useQuery({queryKey:C.training.courses.analytics(e),queryFn:()=>ve.getCourseAnalytics(e),enabled:t&&!!e,staleTime:2*60*1e3})}function sp(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>ve.createCourse(t),onSuccess:t=>{ie.invalidateQueries(C.training.courses.lists()),e.setQueryData(C.training.courses.detail(t.data.id),t),re.success("Training course created successfully")}})}function rp(){const e=T.useQueryClient();return T.useMutation({mutationFn:({id:t,data:s})=>ve.updateCourse(t,s),onSuccess:(t,s)=>{e.setQueryData(C.training.courses.detail(s.id),t),ie.invalidateQueries(C.training.courses.lists()),re.success("Training course updated successfully")}})}function np(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>ve.deleteCourse(t),onSuccess:(t,s)=>{e.removeQueries({queryKey:C.training.courses.detail(s)}),ie.invalidateQueries(C.training.courses.lists()),re.success("Training course deleted successfully")}})}function ap(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>ve.publishCourse(t),onSuccess:(t,s)=>{e.setQueryData(C.training.courses.detail(s),t),ie.invalidateQueries(C.training.courses.lists()),re.success("Training course published successfully")}})}function ip(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>ve.unpublishCourse(t),onSuccess:(t,s)=>{e.setQueryData(C.training.courses.detail(s),t),ie.invalidateQueries(C.training.courses.lists()),re.success("Training course unpublished successfully")}})}function op(e){return T.useQuery({queryKey:C.training.enrollments.list(e),queryFn:()=>ve.getEnrollments(e),staleTime:2*60*1e3})}function lp(e,t=!0){return T.useQuery({queryKey:C.training.enrollments.detail(e),queryFn:()=>ve.getEnrollment(e),enabled:t&&!!e,staleTime:2*60*1e3})}function cp(e){return T.useQuery({queryKey:C.training.enrollments.progress(e||"current"),queryFn:()=>ve.getUserProgress(e),staleTime:1*60*1e3})}function up(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>ve.createEnrollment(t),onSuccess:t=>{ie.invalidateQueries(C.training.enrollments.lists()),e.setQueryData(C.training.enrollments.detail(t.data.id),t),ie.invalidateQueries(C.training.enrollments.progress("current")),re.success("Enrolled in training course successfully")}})}function dp(){const e=T.useQueryClient();return T.useMutation({mutationFn:({id:t,data:s})=>ve.updateEnrollment(t,s),onSuccess:(t,s)=>{e.setQueryData(C.training.enrollments.detail(s.id),t),ie.invalidateQueries(C.training.enrollments.lists()),ie.invalidateQueries(C.training.enrollments.progress("current")),re.success("Training enrollment updated successfully")}})}function mp(){const e=T.useQueryClient();return T.useMutation({mutationFn:t=>ve.unenroll(t),onSuccess:(t,s)=>{e.removeQueries({queryKey:C.training.enrollments.detail(s)}),ie.invalidateQueries(C.training.enrollments.lists()),ie.invalidateQueries(C.training.enrollments.progress("current")),re.success("Unenrolled from training course successfully")}})}function pp(){const e=T.useQueryClient();return T.useMutation({mutationFn:({enrollmentId:t,moduleId:s})=>ve.completeModule(t,s),onSuccess:(t,s)=>{e.setQueryData(C.training.enrollments.detail(s.enrollmentId),t),ie.invalidateQueries(C.training.enrollments.progress("current")),re.success("Module completed successfully")}})}function fp(e){return T.useQuery({queryKey:C.training.skills.list(e),queryFn:()=>ve.getSkills(e),staleTime:10*60*1e3})}function hp(e){return T.useQuery({queryKey:C.training.skills.gaps(e),queryFn:()=>ve.getSkillGaps(e),staleTime:5*60*1e3})}function gp(e){return T.useQuery({queryKey:C.training.skills.ratings(e),queryFn:()=>ve.getSkillRatings(e),staleTime:5*60*1e3})}function yp(e){return T.useQuery({queryKey:C.training.skills.recommendations(e),queryFn:()=>ve.getTrainingRecommendations(e),staleTime:5*60*1e3})}function bp(){return T.useQueryClient(),T.useMutation({mutationFn:e=>ve.rateSkill(e.skillId,e.rating,e.userId),onSuccess:(e,t)=>{ie.invalidateQueries(C.training.skills.ratings(t.userId)),ie.invalidateQueries(C.training.skills.gaps(t.userId)),ie.invalidateQueries(C.training.skills.recommendations(t.userId)),re.success("Skill rated successfully")}})}function xp(){return T.useQueryClient(),T.useMutation({mutationFn:e=>ve.updateSkillLevel(e.skillId,e.level,e.userId),onSuccess:(e,t)=>{ie.invalidateQueries(C.training.skills.ratings(t.userId)),ie.invalidateQueries(C.training.skills.gaps(t.userId)),re.success("Skill level updated successfully")}})}function wp(e){return T.useQuery({queryKey:C.training.assessments.list(e),queryFn:()=>ve.getAssessments(e),staleTime:5*60*1e3})}function vp(e,t=!0){return T.useQuery({queryKey:C.training.assessments.detail(e),queryFn:()=>ve.getAssessment(e),enabled:t&&!!e,staleTime:5*60*1e3})}function jp(e,t=!0){return T.useQuery({queryKey:C.training.assessments.responses(e),queryFn:()=>ve.getAssessmentResponses(e),enabled:t&&!!e,staleTime:2*60*1e3})}function Cp(){return T.useQueryClient(),T.useMutation({mutationFn:e=>ve.submitAssessment(e.assessmentId,e.responses),onSuccess:(e,t)=>{ie.invalidateQueries(C.training.assessments.responses(t.assessmentId)),ie.invalidateQueries(C.training.enrollments.progress("current")),re.success("Assessment submitted successfully")}})}function kp(){return T.useQueryClient(),T.useMutation({mutationFn:e=>ve.gradeAssessment(e.assessmentId,e.userId,e.score,e.feedback),onSuccess:(e,t)=>{ie.invalidateQueries(C.training.assessments.responses(t.assessmentId)),re.success("Assessment graded successfully")}})}function Np(e){return T.useQuery({queryKey:C.training.analytics.dashboard(e),queryFn:()=>ve.getAnalytics(e),staleTime:5*60*1e3})}function Sp(e){return T.useQuery({queryKey:C.training.analytics.metrics(e),queryFn:()=>ve.getMetrics(e),staleTime:5*60*1e3})}function Tp(e){return T.useQuery({queryKey:C.training.analytics.reports(e),queryFn:()=>ve.getReports(e),staleTime:10*60*1e3})}function Rp(e){return T.useQuery({queryKey:C.training.all().concat(["learning-paths"]),queryFn:()=>ve.getLearningPaths(e),staleTime:10*60*1e3})}function Ep(){return T.useQueryClient(),T.useMutation({mutationFn:e=>ve.createLearningPath(e),onSuccess:()=>{ie.invalidateQueries(C.training.all().concat(["learning-paths"])),re.success("Learning path created successfully")}})}function Ap(){return T.useQueryClient(),T.useMutation({mutationFn:e=>ve.followLearningPath(e),onSuccess:()=>{ie.invalidateQueries(C.training.enrollments.progress("current")),ie.invalidateQueries(C.training.enrollments.lists()),re.success("Learning path followed successfully")}})}function Pp(){const e=T.useQueryClient();return(t,s)=>{const n=e.getQueryData(C.training.enrollments.detail(t));return n&&e.setQueryData(C.training.enrollments.detail(t),{...n,data:s(n.data)}),n}}function Lp(){const e=T.useQueryClient();return(t,s)=>{const n=e.getQueryData(C.training.enrollments.progress("current"));return n&&e.setQueryData(C.training.enrollments.progress("current"),{...n,data:{...n.data,completedModules:s?[...n.data.completedModules,t]:n.data.completedModules.filter(a=>a!==t)}}),n}}function Ip(e,t=!0){return T.useQuery({queryKey:C.training.enrollments.detail(e),queryFn:()=>ve.getEnrollment(e),enabled:t&&!!e,refetchInterval:s=>s?.data?.status==="in_progress"?30*1e3:2*60*1e3,staleTime:0})}const je={all:["users"],lists:()=>[...je.all,"list"],list:e=>[...je.lists(),e],details:()=>[...je.all,"detail"],detail:e=>[...je.details(),e],roles:e=>[...je.detail(e),"roles"],permissions:e=>[...je.detail(e),"permissions"]};function Op(e){return{useUsers:g=>T.useQuery({queryKey:je.list(g),queryFn:()=>e.getUsers(g),staleTime:3e5}),useUser:(g,f=!0)=>T.useQuery({queryKey:je.detail(g),queryFn:()=>e.getUser(g),enabled:f&&!!g,staleTime:3e5}),useUserRoles:(g,f=!0)=>T.useQuery({queryKey:je.roles(g),queryFn:()=>e.getUserRoles(g),enabled:f&&!!g,staleTime:6e5}),useUserPermissions:(g,f=!0)=>T.useQuery({queryKey:je.permissions(g),queryFn:()=>e.getUserPermissions(g),enabled:f&&!!g,staleTime:6e5}),useActiveUsers:()=>T.useQuery({queryKey:je.list({isActive:!0}),queryFn:()=>e.getActiveUsers(),staleTime:3e5}),useSearchUsers:(g,f=10)=>T.useQuery({queryKey:[...je.lists(),"search",g,f],queryFn:()=>e.searchUsers(g,f),enabled:!!g&&g.trim().length>0,staleTime:12e4}),useCreateUser:()=>{const g=T.useQueryClient();return T.useMutation({mutationFn:f=>e.createUser(f),onSuccess:f=>{g.invalidateQueries({queryKey:je.lists()}),g.setQueryData(je.detail(f.data.id),f)},onError:f=>{console.error("Failed to create user:",f)}})},useUpdateUser:()=>{const g=T.useQueryClient();return T.useMutation({mutationFn:({id:f,userData:h})=>e.updateUser(f,h),onSuccess:(f,{id:h})=>{g.setQueryData(je.detail(h),f),g.invalidateQueries({queryKey:je.lists()})},onError:f=>{console.error("Failed to update user:",f)}})},useDeleteUser:()=>{const g=T.useQueryClient();return T.useMutation({mutationFn:f=>e.deleteUser(f),onSuccess:(f,h)=>{g.removeQueries({queryKey:je.detail(h)}),g.invalidateQueries({queryKey:je.lists()})},onError:f=>{console.error("Failed to delete user:",f)}})},useUpdateUserRoles:()=>{const g=T.useQueryClient();return T.useMutation({mutationFn:({id:f,roleIds:h})=>e.updateUserRoles(f,h),onSuccess:(f,{id:h})=>{g.setQueryData(je.detail(h),f),g.invalidateQueries({queryKey:je.roles(h)}),g.invalidateQueries({queryKey:je.permissions(h)}),g.invalidateQueries({queryKey:je.lists()})},onError:f=>{console.error("Failed to update user roles:",f)}})},useActivateUser:()=>{const g=T.useQueryClient();return T.useMutation({mutationFn:f=>e.activateUser(f),onSuccess:(f,h)=>{g.setQueryData(je.detail(h),f),g.invalidateQueries({queryKey:je.lists()})}})},useDeactivateUser:()=>{const g=T.useQueryClient();return T.useMutation({mutationFn:f=>e.deactivateUser(f),onSuccess:(f,h)=>{g.setQueryData(je.detail(h),f),g.invalidateQueries({queryKey:je.lists()})}})},useOptimisticUpdateUser:()=>{const g=T.useQueryClient();return T.useMutation({mutationFn:({id:f,userData:h})=>e.updateUser(f,h),onMutate:async({id:f,userData:h})=>{await g.cancelQueries({queryKey:je.detail(f)});const b=g.getQueryData(je.detail(f));return g.setQueryData(je.detail(f),x=>({...x,data:{...x?.data,...h}})),{previousUser:b}},onError:(f,{id:h},b)=>{b?.previousUser&&g.setQueryData(je.detail(h),b.previousUser)},onSettled:(f,h,{id:b})=>{g.invalidateQueries({queryKey:je.detail(b)})}})},userKeys:je}}const he={all:["trainings"],lists:()=>[...he.all,"list"],list:e=>[...he.lists(),e],details:()=>[...he.all,"detail"],detail:e=>[...he.details(),e],participants:e=>[...he.detail(e),"participants"],categories:()=>[...he.all,"categories"]};function Dp(e){return{useTrainings:j=>T.useQuery({queryKey:he.list(j),queryFn:()=>e.getTrainings(j),staleTime:3e5}),useTraining:(j,E=!0)=>T.useQuery({queryKey:he.detail(j),queryFn:()=>e.getTraining(j),enabled:E&&!!j,staleTime:3e5}),useTrainingParticipants:(j,E=!0)=>T.useQuery({queryKey:he.participants(j),queryFn:()=>e.getTrainingParticipants(j),enabled:E&&!!j,staleTime:12e4}),useTrainingCategories:()=>T.useQuery({queryKey:he.categories(),queryFn:()=>e.getTrainingCategories(),staleTime:18e5}),usePublicTrainings:()=>T.useQuery({queryKey:he.list({isPublic:!0,status:Xa.TrainingStatus.PUBLISHED}),queryFn:()=>e.getPublicTrainings(),staleTime:3e5}),useUpcomingTrainings:()=>T.useQuery({queryKey:[...he.lists(),"upcoming"],queryFn:()=>e.getUpcomingTrainings(),staleTime:12e4,refetchInterval:3e5}),useTrainingsByInstructor:(j,E=!0)=>T.useQuery({queryKey:he.list({instructorId:j}),queryFn:()=>e.getTrainingsByInstructor(j),enabled:E&&!!j,staleTime:3e5}),useTrainingsByCategory:(j,E=!0)=>T.useQuery({queryKey:he.list({categoryId:j}),queryFn:()=>e.getTrainingsByCategory(j),enabled:E&&!!j,staleTime:3e5}),useTrainingsByDifficulty:j=>T.useQuery({queryKey:he.list({difficulty:j}),queryFn:()=>e.getTrainingsByDifficulty(j),staleTime:3e5}),useSearchTrainings:(j,E=10)=>T.useQuery({queryKey:[...he.lists(),"search",j,E],queryFn:()=>e.searchTrainings(j,E),enabled:!!j&&j.trim().length>0,staleTime:12e4}),useCreateTraining:()=>{const j=T.useQueryClient();return T.useMutation({mutationFn:E=>e.createTraining(E),onSuccess:E=>{j.invalidateQueries({queryKey:he.lists()}),j.setQueryData(he.detail(E.data.id),E)},onError:E=>{console.error("Failed to create training:",E)}})},useUpdateTraining:()=>{const j=T.useQueryClient();return T.useMutation({mutationFn:({id:E,trainingData:k})=>e.updateTraining(E,k),onSuccess:(E,{id:k})=>{j.setQueryData(he.detail(k),E),j.invalidateQueries({queryKey:he.lists()})},onError:E=>{console.error("Failed to update training:",E)}})},useDeleteTraining:()=>{const j=T.useQueryClient();return T.useMutation({mutationFn:E=>e.deleteTraining(E),onSuccess:(E,k)=>{j.removeQueries({queryKey:he.detail(k)}),j.invalidateQueries({queryKey:he.lists()})},onError:E=>{console.error("Failed to delete training:",E)}})},useEnrollInTraining:()=>{const j=T.useQueryClient();return T.useMutation({mutationFn:({trainingId:E,userId:k})=>e.enrollInTraining(E,k),onSuccess:(E,{trainingId:k})=>{j.invalidateQueries({queryKey:he.participants(k)}),j.invalidateQueries({queryKey:he.detail(k)})},onError:E=>{console.error("Failed to enroll in training:",E)}})},useUnenrollFromTraining:()=>{const j=T.useQueryClient();return T.useMutation({mutationFn:({trainingId:E,userId:k})=>e.unenrollFromTraining(E,k),onSuccess:(E,{trainingId:k})=>{j.invalidateQueries({queryKey:he.participants(k)}),j.invalidateQueries({queryKey:he.detail(k)})},onError:E=>{console.error("Failed to unenroll from training:",E)}})},usePublishTraining:()=>{const j=T.useQueryClient();return T.useMutation({mutationFn:E=>e.publishTraining(E),onSuccess:(E,k)=>{j.setQueryData(he.detail(k),E),j.invalidateQueries({queryKey:he.lists()})}})},useScheduleTraining:()=>{const j=T.useQueryClient();return T.useMutation({mutationFn:({id:E,startDate:k,endDate:L})=>e.scheduleTraining(E,k,L),onSuccess:(E,{id:k})=>{j.setQueryData(he.detail(k),E),j.invalidateQueries({queryKey:he.lists()}),j.invalidateQueries({queryKey:[...he.lists(),"upcoming"]})}})},useStartTraining:()=>{const j=T.useQueryClient();return T.useMutation({mutationFn:E=>e.startTraining(E),onSuccess:(E,k)=>{j.setQueryData(he.detail(k),E),j.invalidateQueries({queryKey:he.lists()})}})},useCompleteTraining:()=>{const j=T.useQueryClient();return T.useMutation({mutationFn:E=>e.completeTraining(E),onSuccess:(E,k)=>{j.setQueryData(he.detail(k),E),j.invalidateQueries({queryKey:he.lists()})}})},useCancelTraining:()=>{const j=T.useQueryClient();return T.useMutation({mutationFn:E=>e.cancelTraining(E),onSuccess:(E,k)=>{j.setQueryData(he.detail(k),E),j.invalidateQueries({queryKey:he.lists()})}})},useOptimisticEnrollment:()=>{const j=T.useQueryClient();return T.useMutation({mutationFn:({trainingId:E,userId:k})=>e.enrollInTraining(E,k),onMutate:async({trainingId:E,userId:k})=>{await j.cancelQueries({queryKey:he.participants(E)});const L=j.getQueryData(he.participants(E));return j.setQueryData(he.participants(E),$=>{if(!$?.data)return $;const W={id:k};return{...$,data:[...$.data,W]}}),{previousParticipants:L}},onError:(E,{trainingId:k},L)=>{L?.previousParticipants&&j.setQueryData(he.participants(k),L.previousParticipants)},onSettled:(E,k,{trainingId:L})=>{j.invalidateQueries({queryKey:he.participants(L)})}})},trainingKeys:he}}function Ma(e){const t=createUsersHooks(e.users),s=createTrainingHooks(e.training);return{users:t,training:s,queryKeys:{users:t.userKeys,training:s.trainingKeys}}}const Fa=c.createContext(null);function Mp({children:e,services:t}){const s=Ma(t);return r.jsx(Fa.Provider,{value:s,children:e})}function ms(){const e=c.useContext(Fa);if(!e)throw new Error("useApiHooks must be used within an ApiHooksProvider");return e}function Fp(){return ms().users}function _p(){return ms().training}function Up(){const{queryKeys:e}=ms();return{invalidateUsers:()=>{},invalidateTrainings:()=>{},invalidateAll:()=>{}}}const $p={HOME:"/",LOGIN:"/login",SIGNUP:"/signup",PROFILE:"/profile",SETTINGS:"/settings",DASHBOARD:"/dashboard",NOT_FOUND:"/404",UNAUTHORIZED:"/unauthorized",SERVER_ERROR:"/500"};function At(e,t,s,n){let a=e;if(t&&Object.entries(t).forEach(([i,o])=>{a=a.replace(`:${i}`,o).replace(`$${i}`,o)}),s){const i=new URLSearchParams;Object.entries(s).forEach(([l,u])=>{u!=null&&i.append(l,String(u))});const o=i.toString();o&&(a+=`?${o}`)}return n&&(a+=`#${n}`),a}function zp(e){return{path:e,params:{},search:{},hash:""}}function Qp(e,t){const s={},n=[],a=e.replace(/[.:]/g,"\\$&").replace(/\$([^/]+)/g,(l,u)=>(n.push(u),"([^/]+)")).replace(/:([^/]+)/g,(l,u)=>(n.push(u),"([^/]+)")),i=new RegExp(`^${a}$`),o=t.match(i);return o&&n.forEach((l,u)=>{s[l]=o[u+1]}),s}function qp(e){const t={},s=new URLSearchParams(e);for(const[n,a]of s.entries())try{t[n]=JSON.parse(a)}catch{a==="true"?t[n]=!0:a==="false"?t[n]=!1:isNaN(Number(a))?t[n]=a:t[n]=Number(a)}return t}function Bp(e){return e.startsWith("/")&&!e.includes("//")&&!e.endsWith("/")||e==="/"}function _a(e,t){const s=e.split("/").filter(Boolean),n=[];n.push({title:"Home",path:"/",isActive:e==="/"});let a="";return s.forEach((i,o)=>{a+=`/${i}`;const l=o===s.length-1,u=t?.[a],d=u?.breadcrumb||u?.title||i;n.push({title:d,path:a,isActive:l,metadata:u})}),n}function Vp(e,t){const s=e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&").replace(/\\\*/g,".*").replace(/\\\$([^/]+)/g,"[^/]+").replace(/\\:([^/]+)/g,"[^/]+");return new RegExp(`^${s}$`).test(t)}function Hp(e){const t={};return Object.entries(e).forEach(([s,n])=>{t[s]={...n,build:(a,i)=>At(n.path,a,i)}}),t}function Gp(e){const{sourceApp:t,targetApp:s,targetPath:n,context:a,preserveState:i}=e,l=`${Ua(s)}${n}`;if(a||i){const u={sourceApp:t,context:a,preserveState:i,timestamp:Date.now()};sessionStorage.setItem("crossAppNavigation",JSON.stringify(u))}window.location.href=l}function Ua(e){return{amna:"/amna","e-connect":"/e-connect","training-analysis":"/training-analysis",lighthouse:"/lighthouse",vendors:"/vendors","wins-of-week":"/wins-of-week"}[e]||"/"}function ps(e,t){for(const s of t){const n=e.replace(s.basePath,"");if(s.routes[n])return s.routes[n]}}function Kp(e,t){return ps(e,t)?.requireAuth||!1}function Wp(e,t,s){const n=ps(e,s);return n?.permissions?n.permissions.some(a=>t.includes(a)):!0}function Yp(e,t,s){const n=ps(e,s);return n?.roles?n.roles.some(a=>t.includes(a)):!0}function Jp(e){return e.replace(/\/+/g,"/").replace(/\/$/g,"").replace(/^(?!\/)/,"/")}function Pt(e,t,s=!1){return s?e===t:e.startsWith(t)}function Kt(e){const t=e.split("/").filter(Boolean);return t.length<=1?"/":"/"+t.slice(0,-1).join("/")}function Xp(e){return e.split("/").filter(Boolean).length}function Zp(e){const t={};return Object.entries(e).forEach(([s,n])=>{t[s]={...n,children:[]}}),Object.keys(e).forEach(s=>{const n=Kt(s);n!=="/"&&t[n]&&t[n].children.push(s)}),t}function ef(e,t){const s=Kt(e),n=[];return Object.keys(t).forEach(a=>{a!==e&&Kt(a)===s&&n.push(a)}),n}function tf(e){return e.split("/").filter(Boolean).map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" > ")}function sf(e,t,s){const n=[e];return t&&n.push(JSON.stringify(t)),s&&n.push(JSON.stringify(s)),n.join("|")}function rf(e,t=300){let s;return(...n)=>{clearTimeout(s),s=setTimeout(()=>e(...n),t)}}function nf(e,t=300){let s=0;return(...n)=>{const a=Date.now();a-s>=t&&(s=a,e(...n))}}function zr(e){return t=>{const{user:s,roles:n=[],permissions:a=[]}=t;return e.requireAuth&&!s?{redirect:e.redirectTo||"/login"}:e.requireRole&&s&&!(Array.isArray(e.requireRole)?e.requireRole:[e.requireRole]).some(l=>n.includes(l))?{redirect:e.redirectTo||"/unauthorized"}:e.requirePermission&&s&&!(Array.isArray(e.requirePermission)?e.requirePermission:[e.requirePermission]).some(l=>a.includes(l))?{redirect:e.redirectTo||"/unauthorized"}:{allowed:!0}}}function af(e){return t=>{const{user:s}=t;return s?{redirect:e.redirectTo||"/"}:{allowed:!0}}}function of(e){return t=>{const{user:s,roles:n=[]}=t;return s?!n.includes("admin")&&!n.includes("super_admin")?{redirect:e.redirectTo||"/unauthorized"}:{allowed:!0}:{redirect:"/login"}}}function lf(e){return t=>process.env.NODE_ENV==="development"?{allowed:!0}:{redirect:e.redirectTo||"/"}}function cf(e,t){return s=>{const{user:n}=s;return{allowed:!0}}}function uf(e,t,s){return n=>{const a=new Date().getHours();return a<e||a>t?{redirect:s.redirectTo||"/"}:{allowed:!0}}}function df(e,t){return s=>{const n=hf();return e.includes(n)?{allowed:!0}:{redirect:t.redirectTo||"/unauthorized"}}}function mf(e,t){return s=>{const n=gf();return e.includes(n)?{allowed:!0}:{redirect:t.redirectTo||"/"}}}function pf(e,t){return s=>{const{user:n}=s;if(!n)return{redirect:"/login"};const a=n.subscription?.plan,i=["free","basic","premium","enterprise"],o=i.indexOf(a),l=i.indexOf(e);return o<l?{redirect:t.redirectTo||"/upgrade"}:{allowed:!0}}}function ff(...e){return t=>{for(const s of e){const n=s(t);if(!n.allowed)return n}return{allowed:!0}}}function $a(e,t){return s=>{const a=zr(e)(t);if(!a.allowed){if(a.redirect)throw new Error(`Redirect to ${a.redirect}`);if(a.fallback)return a.fallback}return s}}function hf(){return"127.0.0.1"}function gf(){const e=navigator.userAgent;return/Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(e)?"mobile":/iPad|Android(?!.*Mobile)/.test(e)?"tablet":"desktop"}function yf(e,t){return s=>{const{context:n,...a}=s,o=zr(t)(n);return o.allowed?r.jsx(e,{...a}):o.fallback?o.fallback:null}}function it({error:e,reset:t}){const s=process.env.NODE_ENV==="development";return r.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50 dark:bg-gray-900",children:r.jsxs("div",{className:"max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 border border-gray-200 dark:border-gray-700",children:[r.jsx("div",{className:"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/20 rounded-full mb-4",children:r.jsx("svg",{className:"w-6 h-6 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white text-center mb-4",children:"Something went wrong"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-center mb-6",children:s&&e.message?e.message:"An unexpected error occurred. Please try again."}),s&&e.stack&&r.jsxs("details",{className:"mb-6",children:[r.jsx("summary",{className:"text-sm text-gray-500 dark:text-gray-400 cursor-pointer hover:text-gray-700 dark:hover:text-gray-200",children:"Technical Details"}),r.jsx("pre",{className:"mt-2 text-xs text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 p-3 rounded overflow-auto max-h-40",children:e.stack})]}),r.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[r.jsxs("button",{onClick:t,className:"flex-1 flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors",children:[r.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Try Again"]}),r.jsx("button",{onClick:()=>window.location.href="/",className:"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md transition-colors",children:"Go Home"})]}),r.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 text-center mt-4",children:"If this problem persists, please contact support."})]})})}function ot(){return r.jsx("div",{className:"flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900",children:r.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400"}),r.jsx("div",{className:"animate-ping absolute top-0 left-0 h-12 w-12 border border-blue-300 dark:border-blue-600 rounded-full opacity-20"})]}),r.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 animate-pulse",children:"Loading..."})]})})}function lt(){return r.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50 dark:bg-gray-900",children:r.jsx("div",{className:"max-w-md w-full text-center",children:r.jsxs("div",{className:"animate-in zoom-in duration-500",children:[r.jsx("h1",{className:"text-6xl font-bold mb-4 text-primary animate-pulse",children:"404"}),r.jsx("p",{className:"text-xl text-muted-foreground mb-8",children:"Page not found"}),r.jsx("button",{className:"inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",onClick:()=>window.location.href="/",children:"Go Home"})]})})})}function za(){return{trackPageView:(e,t)=>{console.log(`Page view: ${e}`,{title:t}),typeof gtag<"u"&&gtag("config","GA_MEASUREMENT_ID",{page_path:e,page_title:t}),typeof window<"u"&&window.__analyticsStore&&window.__analyticsStore.trackPageView(e,t)},trackNavigation:(e,t,s)=>{console.log(`Navigation: ${e} -> ${t}`,{method:s}),typeof window<"u"&&window.__analyticsStore&&window.__analyticsStore.trackNavigation(e,t,s)},trackError:(e,t)=>{console.error(`Route error in ${t}:`,e),typeof window<"u"&&window.__errorTracking&&window.__errorTracking.captureException(e,{route:t,timestamp:new Date().toISOString()})}}}function bf(e,t={}){const s=za(),n=Ce.createRouter({routeTree:e,defaultPreload:t.defaultPreload||"intent",defaultPreloadStaleTime:t.defaultPreloadStaleTime||1e3*60*5,defaultGcTime:t.defaultGcTime||1e3*60*10,defaultErrorComponent:t.defaultErrorComponent||it,defaultPendingComponent:t.defaultPendingComponent||ot,defaultNotFoundComponent:t.defaultNotFoundComponent||lt,onRouteChange:()=>{try{const a=window.location.pathname,i=document.title;console.log("Route changed to:",a),s.trackPageView(a,i),t.onRouteChange&&t.onRouteChange(a),xf(a),wf(a,t.context)}catch(a){console.error("Route change tracking failed:",a),s.trackError(a,window.location.pathname)}},beforeLoad:t.routeGuards?$a(t.routeGuards,t.context||{}):void 0,context:t.context||{}});return process.env.NODE_ENV==="development"&&(window.__router=n),n}function xf(e){const t=document.getElementById("route-announcer");t&&(t.textContent=`Navigated to ${e}`)}function wf(e,t){const s=e.split("/").filter(Boolean),n=s.length>0?s[s.length-1].replace(/-/g," ").replace(/\b\w/g,a=>a.toUpperCase()):"Home";document.title=`${n} - Luminar`}function vf(e){return function({children:s}){return r.jsxs(r.Fragment,{children:[s,r.jsx("div",{id:"route-announcer","aria-live":"polite","aria-atomic":"true",className:"sr-only"}),process.env.NODE_ENV==="development"&&r.jsx("div",{className:"fixed bottom-4 right-4 z-50",children:r.jsxs("details",{className:"bg-gray-800 text-white p-2 rounded text-xs",children:[r.jsx("summary",{children:"Route Info"}),r.jsx("pre",{className:"mt-2 max-w-xs overflow-auto",children:JSON.stringify({pathname:window.location.pathname,search:window.location.search,hash:window.location.hash},null,2)})]})})]})}}const Vt={default:{defaultPreload:"intent",defaultPreloadStaleTime:1e3*60*5,defaultGcTime:1e3*60*10,defaultErrorComponent:it,defaultPendingComponent:ot,defaultNotFoundComponent:lt},performance:{defaultPreload:!1,defaultPreloadStaleTime:1e3*60*2,defaultGcTime:1e3*60*5,defaultErrorComponent:it,defaultPendingComponent:ot,defaultNotFoundComponent:lt},development:{defaultPreload:"intent",defaultPreloadStaleTime:1e3*30,defaultGcTime:1e3*60*2,defaultErrorComponent:it,defaultPendingComponent:ot,defaultNotFoundComponent:lt},production:{defaultPreload:!1,defaultPreloadStaleTime:1e3*60*10,defaultGcTime:1e3*60*30,defaultErrorComponent:it,defaultPendingComponent:ot,defaultNotFoundComponent:lt}};function jf(e="default"){const t=Vt[e],s=process.env.NODE_ENV;return s==="development"?{...t,...Vt.development}:s==="production"?{...t,...Vt.production}:t}function Qr(){const e=Ce.useNavigate(),t=Ce.useLocation(),s=Ce.useRouter(),n=c.useCallback((l,u={})=>{const{replace:d=!1,resetScroll:m=!0,state:p,search:y,params:w,hash:g,from:f,mask:h}=u;console.log(`Navigating from ${t.pathname} to ${l}`);const b=At(l,w,y,g);e({to:b,replace:d,resetScroll:m,state:p,from:f,mask:h})},[e,t]),a=c.useCallback(()=>{window.history.back()},[]),i=c.useCallback(()=>{window.history.forward()},[]),o=c.useCallback(()=>{window.location.reload()},[]);return{navigate:n,goBack:a,goForward:i,refresh:o,location:t,router:s}}function Cf(){const e=Ce.useParams(),t=Ce.useLocation(),s=Ce.useNavigate(),n=c.useCallback(a=>{const i={...e};Object.assign(i,a);const o=At(t.pathname,i);s({to:o,replace:!0})},[e,t,s]);return{params:e,updateParams:n}}function kf(e){const t=Ce.useLocation();return c.useMemo(()=>_a(t.pathname,e),[t.pathname,e])}function Nf(e,t=!1){const s=Ce.useLocation();return c.useMemo(()=>Pt(s.pathname,e,t),[s.pathname,e,t])}function Sf(){const e=Ce.useRouterState();return{initial:e.isLoading,navigation:e.isTransitioning,preload:e.isPending,error:e.error}}function Qa(){const e=Ce.useLocation(),[t,s]=c.useState(null);return c.useEffect(()=>{(()=>{const a={from:t?.to||"/",to:e.pathname,type:"push",timestamp:Date.now()};s(a)})()},[e.pathname]),t}function Tf(){const e=Ce.useLocation(),t=Ce.useRouterState();return c.useMemo(()=>({title:document.title,path:e.pathname,params:t.location.params,search:t.location.search}),[e,t])}function Rf(){const e=Ce.useLocation(),t=Ce.useNavigate();return{validateAndNavigate:c.useCallback((n,a={})=>(t({to:n,...a}),!0),[t]),currentPath:e.pathname,isValid:!0}}function Ef(){const e=Ce.useLocation(),[t,s]=c.useState(new Map),n=c.useCallback(l=>t.get(l),[t]),a=c.useCallback((l,u)=>{s(d=>new Map(d).set(l,u))},[]),i=c.useCallback(()=>{s(new Map)},[]),o=c.useCallback(l=>{s(u=>{const d=new Map(u);return d.delete(l),d})},[]);return{getCached:n,setCached:a,clearCache:i,clearCached:o,currentPath:e.pathname}}function Af(){const e=Ce.useLocation(),t=Qa(),s=c.useCallback((a,i)=>{const o=a||e.pathname,l=i||document.title;console.log(`Page view: ${o}`,{title:l}),typeof window<"u"&&window.__analyticsStore&&window.__analyticsStore.trackPageView(o,l)},[e.pathname]),n=c.useCallback((a,i)=>{console.log(`Event: ${a}`,i),typeof window<"u"&&window.__analyticsStore&&window.__analyticsStore.trackEvent(a,{...i,currentPath:e.pathname})},[e.pathname]);return{trackPageView:s,trackEvent:n,currentPath:e.pathname,transition:t}}function qa(){const e=Ce.useRouter(),t=c.useCallback(n=>{e.preloadRoute({to:n})},[e]),s=c.useCallback(n=>({onMouseEnter:()=>t(n),onFocus:()=>t(n)}),[t]);return{prefetch:t,prefetchOnHover:s}}function Pf(){const[e,t]=c.useState([]),s=Ce.useLocation();c.useEffect(()=>{t(i=>[...i,s.pathname].slice(-10))},[s.pathname]);const n=e.length>1,a=e[e.length-2];return{history:e,canGoBack:n,previousRoute:a,currentRoute:s.pathname}}function Lf(){const e=Ce.useLocation(),t=["read","write"],s=c.useCallback(i=>t.includes(i),[t]),n=c.useCallback(i=>i.some(o=>t.includes(o)),[t]),a=c.useCallback(i=>i.every(o=>t.includes(o)),[t]);return{hasPermission:s,hasAnyPermission:n,hasAllPermissions:a,userPermissions:t,currentPath:e.pathname}}function If(e){const t=Ce.useLocation();return c.useEffect(()=>{if(e)document.title=`${e} - Luminar`;else{const s=t.pathname.split("/").filter(Boolean),n=s.length>0?s[s.length-1].replace(/-/g," ").replace(/\b\w/g,a=>a.toUpperCase()):"Home";document.title=`${n} - Luminar`}},[e,t.pathname]),{setTitle:s=>{document.title=`${s} - Luminar`},currentTitle:document.title}}function Of(){const{navigate:e,goBack:t}=Qr();return c.useEffect(()=>{const s=n=>{n.altKey&&n.key==="ArrowLeft"&&(n.preventDefault(),t()),n.altKey&&n.key==="ArrowRight"&&(n.preventDefault(),window.history.forward()),(n.ctrlKey||n.metaKey)&&n.key==="k"&&(n.preventDefault(),console.log("Command palette shortcut pressed"))};return window.addEventListener("keydown",s),()=>window.removeEventListener("keydown",s)},[e,t]),{registerShortcut:(s,n)=>{console.log(`Registered shortcut: ${s}`)}}}const Lt=c.forwardRef(({to:e,params:t,search:s,hash:n,replace:a=!1,activeClass:i="active",inactiveClass:o="",exactMatch:l=!1,prefetch:u=!1,children:d,className:m,onClick:p,onMouseEnter:y,onFocus:w,disabled:g=!1,external:f=!1,download:h,target:b,rel:x,"aria-label":N,"aria-describedby":R,role:I,tabIndex:j,...E},k)=>{const L=Ce.useLocation(),{prefetch:$,prefetchOnHover:W}=qa(),D=c.useMemo(()=>At(e,t,s,n),[e,t,s,n]),V=c.useMemo(()=>Pt(L.pathname,D,l),[L.pathname,D,l]),z=c.useMemo(()=>{const U=[m];return V?U.push(i):U.push(o),g&&U.push("disabled"),U.filter(Boolean).join(" ")},[m,V,i,o,g]),O=c.useCallback(U=>{if(g){U.preventDefault();return}p&&p(U),console.log(`Link clicked: ${D}`)},[g,p,D]),M=c.useCallback(U=>{u&&!g&&$(D),y&&y(U)},[u,g,$,D,y]),K=c.useCallback(U=>{u&&!g&&$(D),w&&w(U)},[u,g,$,D,w]);return f?r.jsx("a",{ref:k,href:D,className:z,onClick:O,onMouseEnter:M,onFocus:K,target:b||"_blank",rel:x||"noopener noreferrer",download:h,"aria-label":N,"aria-describedby":R,role:I,tabIndex:g?-1:j,...E,children:d}):r.jsx(Ce.Link,{ref:k,to:D,replace:a,className:z,onClick:O,onMouseEnter:M,onFocus:K,"aria-label":N,"aria-describedby":R,role:I,tabIndex:g?-1:j,...E,children:d})});Lt.displayName="EnhancedLink";function Ba({items:e,config:t={},className:s="",itemClassName:n="",activeClassName:a="active",separatorClassName:i="",onItemClick:o}){const l=kf(),u=e||l,{separator:d="/",maxItems:m=5,showHome:p=!0,homeTitle:y="Home",homePath:w="/"}=t,g=c.useMemo(()=>{let h=u;if(p||(h=h.filter(b=>b.path!=="/")),h.length>m){const b=h.slice(0,1),x=h.slice(-m+2);return[...b,{title:"...",path:"",isActive:!1},...x]}return h},[u,p,m]),f=c.useCallback(h=>{o&&o(h)},[o]);return r.jsx("nav",{"aria-label":"Breadcrumb",className:`breadcrumb ${s}`,children:r.jsx("ol",{className:"breadcrumb-list",children:g.map((h,b)=>r.jsxs("li",{className:`breadcrumb-item ${n}`,children:[h.path&&!h.isActive?r.jsx(Lt,{to:h.path,className:"breadcrumb-link",onClick:()=>f(h),"aria-current":h.isActive?"page":void 0,children:h.title}):r.jsx("span",{className:`breadcrumb-text ${h.isActive?a:""}`,"aria-current":h.isActive?"page":void 0,children:h.title}),b<g.length-1&&r.jsx("span",{className:`breadcrumb-separator ${i}`,"aria-hidden":"true",children:d})]},b))})})}function Df({items:e,orientation:t="horizontal",className:s="",itemClassName:n="",activeClassName:a="active",disabledClassName:i="disabled",onItemClick:o,collapsible:l=!1,defaultCollapsed:u=!1,renderIcon:d,renderBadge:m}){const p=Ce.useLocation(),[y,w]=c.useState(u),g=c.useCallback((h,b=0)=>{if(h.hidden)return null;const x=Pt(p.pathname,h.path,!1),N=h.children&&h.children.length>0,R=["nav-item",n,x?a:"",h.disabled?i:"",N?"has-children":""].filter(Boolean).join(" "),I=c.useCallback(()=>{h.disabled||o&&o(h)},[h]);return r.jsxs("li",{className:R,style:{paddingLeft:`${b*16}px`},children:[h.external?r.jsxs("a",{href:h.path,className:"nav-link",onClick:I,target:"_blank",rel:"noopener noreferrer","aria-disabled":h.disabled,children:[d&&h.icon&&d(h.icon),r.jsx("span",{children:h.title}),m&&h.badge&&m(h.badge)]}):r.jsxs(Lt,{to:h.path,className:"nav-link",activeClass:a,onClick:I,disabled:h.disabled,"aria-disabled":h.disabled,children:[d&&h.icon&&d(h.icon),r.jsx("span",{children:h.title}),m&&h.badge&&m(h.badge)]}),N&&r.jsx("ul",{className:"nav-submenu",children:h.children.map(j=>g(j,b+1))})]},h.path)},[p.pathname,n,a,i,o,d,m]),f=c.useCallback(()=>{w(!y)},[y]);return r.jsxs("nav",{className:`navigation ${t} ${s} ${y?"collapsed":""}`,children:[l&&r.jsx("button",{type:"button",className:"nav-toggle",onClick:f,"aria-expanded":!y,"aria-label":y?"Expand navigation":"Collapse navigation",children:r.jsx("span",{className:"nav-toggle-icon"})}),r.jsx("ul",{className:"nav-list",children:e.map(h=>g(h))})]})}function Mf({fallback:e,error:t,className:s="",children:n}){const i=Ce.useRouter().state;return i.error?r.jsx("div",{className:`route-error ${s}`,children:t||r.jsxs("div",{className:"error-message",children:[r.jsx("h2",{children:"Something went wrong"}),r.jsx("p",{children:i.error.message})]})}):i.isLoading||i.isTransitioning?r.jsx("div",{className:`route-loading ${s}`,children:e||r.jsxs("div",{className:"loading-spinner",children:[r.jsx("div",{className:"spinner"}),r.jsx("p",{children:"Loading..."})]})}):r.jsx(r.Fragment,{children:n})}function Ff({requireAuth:e=!1,requireRole:t,requirePermission:s,fallback:n,redirectTo:a="/login",loading:i,children:o}){const{navigate:l}=Qr(),[u,d]=c.useState(!0),[m,p]=c.useState(!1),y={id:1,name:"User",roles:["user"],permissions:["read"]};return c.useEffect(()=>{(async()=>{d(!0);try{if(e&&!y){if(a){l(a);return}p(!1);return}if(t&&y&&!(Array.isArray(t)?t:[t]).some(h=>y.roles.includes(h))){p(!1);return}if(s&&y&&!(Array.isArray(s)?s:[s]).some(h=>y.permissions.includes(h))){p(!1);return}p(!0)}catch(g){console.error("Route guard check failed:",g),p(!1)}finally{d(!1)}})()},[e,t,s,y,l,a]),u?i||r.jsx("div",{className:"route-guard-loading",children:"Loading..."}):m?r.jsx(r.Fragment,{children:o}):n||r.jsx("div",{className:"route-guard-denied",children:"Access denied"})}function _f({children:e,className:t="",duration:s=300,appear:n=!0,enter:a=!0,exit:i=!0}){const o=Ce.useLocation(),[l,u]=c.useState(!1);return c.useEffect(()=>{if(a||i){u(!0);const d=setTimeout(()=>{u(!1)},s);return()=>clearTimeout(d)}},[o.pathname,s,a,i]),r.jsx("div",{className:`route-transition ${t} ${l?"transitioning":""}`,style:{"--transition-duration":`${s}ms`},children:e})}function Uf({trackPageViews:e=!0,trackClicks:t=!1,trackErrors:s=!0,customEvents:n=[],children:a}){const i=Ce.useLocation(),o=Ce.useRouter();return c.useEffect(()=>{e&&console.log(`Page view tracked: ${i.pathname}`)},[i.pathname,e]),c.useEffect(()=>{if(s){const l=u=>{console.error("Route error tracked:",u)};return o.subscribe("onError",l),()=>{o.unsubscribe("onError",l)}}},[o,s]),c.useEffect(()=>{if(t){const l=u=>{const d=u.target;d.tagName==="A"&&console.log(`Link click tracked: ${d.getAttribute("href")}`)};return document.addEventListener("click",l),()=>document.removeEventListener("click",l)}},[t]),r.jsx(r.Fragment,{children:a})}function $f({routeMetadata:e={},showDescription:t=!1,showBadges:s=!1,maxDisplayItems:n=5,showIcons:a=!1,...i}){const l=Ce.useLocation().pathname.split("/").filter(Boolean),u=c.useMemo(()=>l.map((p,y)=>{const w="/"+l.slice(0,y+1).join("/"),g=e[w]||{};return{title:g.title||p.replace(/-/g," ").replace(/\b\w/g,f=>f.toUpperCase()),path:w,isActive:y===l.length-1,description:g.description,badge:g.badge,icon:g.icon,metadata:g}}),[l,e]),d=c.useMemo(()=>u.length<=n?u:[u[0],{title:"...",path:"",isActive:!1},...u.slice(-(n-2))],[u,n]),m=u[u.length-1];return r.jsxs("div",{className:"smart-breadcrumb-container",children:[r.jsx(Ba,{items:d,...i}),t&&m?.description&&r.jsx("p",{className:"text-sm text-gray-600 mt-2 ml-4",children:m.description}),s&&r.jsx("div",{className:"flex items-center space-x-2 mt-2 ml-4",children:u.filter(p=>p.badge).map((p,y)=>r.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:p.badge},y))}),a&&r.jsx("div",{className:"flex items-center space-x-2 mt-2 ml-4",children:u.filter(p=>p.icon).map((p,y)=>r.jsxs("span",{className:"flex items-center space-x-1",children:[p.icon,r.jsx("span",{className:"text-xs text-gray-500",children:p.title})]},y))})]})}function zf({loader:e,fallback:t,errorBoundary:s,preload:n=!1,timeout:a=1e4,retryAttempts:i=3,onLoadStart:o,onLoadEnd:l,onError:u,...d}){const[m,p]=c.useState(null),[y,w]=c.useState(!1),[g,f]=c.useState(null),[h,b]=c.useState(0),x=c.useCallback(async()=>{if(!m){w(!0),f(null),o&&o();try{const N=new Promise((j,E)=>{setTimeout(()=>E(new Error("Component load timeout")),a)}),R=e(),I=await Promise.race([R,N]);if(I&&typeof I=="object"&&"default"in I)p(()=>I.default);else throw new Error("Invalid component module")}catch(N){const R=N;console.error("Code splitting error:",R),f(R),u&&u(R),h<i&&setTimeout(()=>{b(I=>I+1),x()},1e3*(h+1))}finally{w(!1),l&&l()}}},[e,m,a,h,i,o,l,u]);return c.useEffect(()=>{n&&x()},[n,x]),c.useEffect(()=>{!m&&!y&&!g&&x()},[m,y,g,x]),g?s?r.jsx(s,{error:g,reset:()=>{f(null),b(0),x()}}):r.jsx("div",{className:"flex flex-col items-center justify-center min-h-64 p-4",children:r.jsxs("div",{className:"text-center",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Failed to load component"}),r.jsx("p",{className:"text-sm text-gray-600 mb-4",children:g.message}),r.jsxs("button",{onClick:()=>{f(null),b(0),x()},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:["Retry (",i-h," attempts left)"]})]})}):y||!m?r.jsx("div",{className:"route-loading",children:t||r.jsxs("div",{className:"flex items-center justify-center min-h-64",children:[r.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),r.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"Loading component..."})]})}):r.jsx(m,{...d})}function Qf({items:e,showDescriptions:t=!1,showShortcuts:s=!1,groupItems:n=!1,searchable:a=!1,onSearch:i,filterFn:o,...l}){const[u,d]=c.useState(""),[m,p]=c.useState(new Set),y=c.useMemo(()=>{if(!u)return e;const x=o||((N,R)=>N.title.toLowerCase().includes(R.toLowerCase())||N.description&&N.description.toLowerCase().includes(R.toLowerCase()));return e.filter(N=>x(N,u))},[e,u,o]),w=c.useMemo(()=>n?y.reduce((b,x)=>{const N=x.group||"ungrouped";return b[N]||(b[N]=[]),b[N].push(x),b},{}):{ungrouped:y},[y,n]),g=c.useCallback(b=>{d(b),i&&i(b)},[i]),f=c.useCallback(b=>{p(x=>{const N=new Set(x);return N.has(b)?N.delete(b):N.add(b),N})},[]),h=c.useCallback((b,x=0)=>{const N=Pt(Ce.useLocation().pathname,b.path,!1);return r.jsxs("div",{className:"nav-item-container",children:[r.jsxs("div",{className:"flex items-center space-x-2 p-2 hover:bg-gray-100 rounded-md transition-colors",children:[r.jsxs(Lt,{to:b.path,className:`flex-1 flex items-center space-x-2 ${N?"text-blue-600 font-medium":"text-gray-700"}`,onClick:()=>b.onClick?.(b),disabled:b.disabled,children:[b.icon&&r.jsx("span",{className:"nav-icon",children:b.icon}),r.jsx("span",{className:"nav-title",children:b.title}),b.badge&&r.jsx("span",{className:"ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full",children:b.badge})]}),s&&b.shortcut&&r.jsx("span",{className:"text-xs text-gray-400 bg-gray-100 px-1 py-0.5 rounded",children:b.shortcut})]}),t&&b.description&&r.jsx("p",{className:"text-xs text-gray-500 mt-1 pl-8",children:b.description}),b.children&&r.jsx("div",{className:"ml-4 mt-1 border-l border-gray-200 pl-4",children:b.children.map(R=>h(R,x+1))})]},b.path)},[t,s]);return r.jsxs("div",{className:"enhanced-navigation",children:[a&&r.jsx("div",{className:"nav-search mb-4",children:r.jsx("input",{type:"text",placeholder:"Search navigation...",value:u,onChange:b=>g(b.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})}),r.jsx("div",{className:"nav-content",children:Object.entries(w).map(([b,x])=>r.jsxs("div",{className:"nav-group",children:[n&&b!=="ungrouped"&&r.jsx("div",{className:"nav-group-header",children:r.jsxs("button",{onClick:()=>f(b),className:"flex items-center space-x-2 w-full text-left p-2 text-sm font-medium text-gray-900 hover:bg-gray-100 rounded-md",children:[r.jsx("span",{className:`transform transition-transform ${m.has(b)?"rotate-90":""}`,children:"▶"}),r.jsx("span",{children:b})]})}),r.jsx("div",{className:`nav-group-items ${n&&!m.has(b)&&b!=="ungrouped"?"hidden":""}`,children:x.map(N=>h(N))})]},b))})]})}const fs={name:"auth",check:e=>e.auth?.isAuthenticated??!1,redirectTo:"/login",onFailure:e=>{console.warn("[Route Guard] Authentication required for route")}},Va=e=>({name:"permission",check:t=>{const s=t.auth?.permissions??[];return e.every(n=>s.includes(n))},redirectTo:"/unauthorized",onFailure:t=>{console.warn("[Route Guard] Insufficient permissions for route",{required:e,user:t.auth?.permissions})}}),qf=e=>({name:"role",check:t=>{const s=t.auth?.roles??[];return e.some(n=>s.includes(n))},redirectTo:"/unauthorized",onFailure:t=>{console.warn("[Route Guard] Insufficient roles for route",{required:e,user:t.auth?.roles})}}),Ha={name:"admin",check:e=>{const t=e.auth?.roles??[];return t.includes("admin")||t.includes("super_admin")},redirectTo:"/unauthorized",onFailure:e=>{console.warn("[Route Guard] Admin access required for route")}};async function Ga(e,t){for(const s of e)try{if(!await s.check(t))throw s.onFailure?.(t),s.redirectTo?Ce.redirect({to:s.redirectTo,search:{redirect:window.location.pathname}}):new Error(`Route guard '${s.name}' failed`)}catch(n){throw n instanceof Error&&n.message.includes("redirect")||console.error(`[Route Guard] Error executing guard '${s.name}':`,n),n}}function hs(...e){return e}function Bf(e){return async t=>{await Ga(e,t)}}const Vf=hs(fs),Hf=hs(fs,Ha),Gf=e=>hs(fs,Va(e));class Ka{navigate;router;constructor(t,s){this.navigate=t,this.router=s}async navigateTo(t,s={}){try{await this.navigate({to:t,replace:s.replace,resetScroll:s.resetScroll,search:s.search,hash:s.hash,state:s.state})}catch(n){throw console.error("[Navigation] Failed to navigate to:",t,n),n}}goBack(){window.history.length>1?window.history.back():this.navigateTo("/")}goForward(){window.history.forward()}async replace(t,s={}){return this.navigateTo(t,{...s,replace:!0})}async navigateWithSearch(t,s,n={}){return this.navigateTo(t,{...n,search:s})}navigateExternal(t,s="_blank"){window.open(t,s)}isCurrentRoute(t){return this.router.state.location.pathname===t}isActiveRoute(t){return this.router.state.location.pathname.startsWith(t)}getCurrentRoute(){return{pathname:this.router.state.location.pathname,search:this.router.state.location.search,hash:this.router.state.location.hash,state:this.router.state.location.state}}}function Kf(){const e=Ce.useNavigate(),t=Ce.useRouter();return new Ka(e,t)}const Wf={matchesPattern:(e,t)=>new RegExp("^"+t.replace(/\*/g,".*").replace(/:\w+/g,"[^/]+")+"$").test(e),extractParams:(e,t)=>{const s={},n=t.split("/"),a=e.split("/");return n.forEach((i,o)=>{if(i.startsWith(":")){const l=i.slice(1);s[l]=a[o]||""}}),s},buildRoute:(e,t)=>{let s=e;return Object.entries(t).forEach(([n,a])=>{s=s.replace(`:${n}`,a)}),s},getDepth:e=>e.split("/").filter(Boolean).length,isNestedUnder:(e,t)=>e.startsWith(t)&&e!==t},Yf={withLoading:async(e,t)=>{t?.(!0);try{await e()}finally{t?.(!1)}},withConfirmation:async(e,t="Are you sure you want to leave this page?")=>{window.confirm(t)&&await e()},withAnalytics:async(e,t,s)=>{try{typeof window<"u"&&window.analytics&&window.analytics.track(t,s),await e()}catch(n){throw console.error("[Navigation Analytics] Failed to track navigation:",n),n}}},Jf={navigateToApp:(e,t="/")=>{const n={amna:"/amna","e-connect":"/e-connect",training:"/training",vendors:"/vendors",wins:"/wins",lighthouse:"/lighthouse",shell:"/shell"}[e];n?window.location.href=`${n}${t}`:console.error(`[Cross-App Navigation] Unknown app: ${e}`)},canNavigateToApp:e=>["amna","e-connect","training","vendors","wins","lighthouse","shell"].includes(e),getCurrentApp:()=>{const t=window.location.pathname.match(/^\/([^\/]+)/);return t?t[1]:null}},de={LOGIN:"/login",LOGOUT:"/logout",SIGNUP:"/signup",FORGOT_PASSWORD:"/forgot-password",RESET_PASSWORD:"/reset-password",HOME:"/",DASHBOARD:"/dashboard",PROFILE:"/profile",SETTINGS:"/settings",NOT_FOUND:"/404",UNAUTHORIZED:"/401",FORBIDDEN:"/403",SERVER_ERROR:"/500",CHAT:"/chat",NOTIFICATIONS:"/notifications",HELP:"/help",SUPPORT:"/support",DOCUMENTATION:"/docs",ADMIN:"/admin",ADMIN_USERS:"/admin/users",ADMIN_SETTINGS:"/admin/settings",ADMIN_ANALYTICS:"/admin/analytics"},Ue={AMNA:{base:"/amna",routes:{HOME:"/amna",CHAT:"/amna/chat",SETTINGS:"/amna/settings",HISTORY:"/amna/history",TEMPLATES:"/amna/templates",PERSONALIZATION:"/amna/personalization",INTEGRATIONS:"/amna/integrations"}},E_CONNECT:{base:"/e-connect",routes:{HOME:"/e-connect",MAIL:"/e-connect/mail",COMPOSE:"/e-connect/mail/compose",THREAD:"/e-connect/mail/thread",AUTOMATION:"/e-connect/automation",RULES:"/e-connect/rules",SETTINGS:"/e-connect/settings",STATS:"/e-connect/stats",ASSISTANT:"/e-connect/assistant",COLD_EMAIL_BLOCKER:"/e-connect/cold-email-blocker",BULK_UNSUBSCRIBE:"/e-connect/bulk-unsubscribe",CLEAN:"/e-connect/clean"}},TRAINING_ANALYSIS:{base:"/training-analysis",routes:{HOME:"/training-analysis",DASHBOARD:"/training-analysis/dashboard",ASSESSMENTS:"/training-analysis/assessments",TRAINING:"/training-analysis/training",REPORTS:"/training-analysis/reports",USERS:"/training-analysis/users",POSTS:"/training-analysis/posts"}},LIGHTHOUSE:{base:"/lighthouse",routes:{HOME:"/lighthouse",PROJECTS:"/lighthouse/projects",ANALYTICS:"/lighthouse/analytics",INSIGHTS:"/lighthouse/insights",PERFORMANCE:"/lighthouse/performance",SECURITY:"/lighthouse/security",ACCESSIBILITY:"/lighthouse/accessibility"}},VENDORS:{base:"/vendors",routes:{HOME:"/vendors",DIRECTORY:"/vendors/directory",PROFILES:"/vendors/profiles",CONTRACTS:"/vendors/contracts",EVALUATIONS:"/vendors/evaluations",ANALYTICS:"/vendors/analytics"}},WINS_OF_WEEK:{base:"/wins-of-week",routes:{HOME:"/wins-of-week",SUBMISSIONS:"/wins-of-week/submissions",VOTING:"/wins-of-week/voting",LEADERBOARD:"/wins-of-week/leaderboard",ARCHIVE:"/wins-of-week/archive",ANALYTICS:"/wins-of-week/analytics"}}},Xf={[de.HOME]:{title:"Home",description:"Luminar L&D Platform",breadcrumb:"Home",icon:"home",requireAuth:!1},[de.DASHBOARD]:{title:"Dashboard",description:"Your personal dashboard",breadcrumb:"Dashboard",icon:"dashboard",requireAuth:!0},[de.PROFILE]:{title:"Profile",description:"Manage your profile",breadcrumb:"Profile",icon:"user",requireAuth:!0},[de.SETTINGS]:{title:"Settings",description:"Application settings",breadcrumb:"Settings",icon:"settings",requireAuth:!0},[de.LOGIN]:{title:"Login",description:"Sign in to your account",breadcrumb:"Login",icon:"login",requireAuth:!1},[de.CHAT]:{title:"Chat",description:"AI-powered chat interface",breadcrumb:"Chat",icon:"chat",requireAuth:!0},[de.NOT_FOUND]:{title:"Page Not Found",description:"The page you are looking for does not exist",breadcrumb:"404",icon:"error",requireAuth:!1},[de.UNAUTHORIZED]:{title:"Unauthorized",description:"You are not authorized to access this page",breadcrumb:"Unauthorized",icon:"lock",requireAuth:!1},[de.SERVER_ERROR]:{title:"Server Error",description:"An internal server error occurred",breadcrumb:"Error",icon:"error",requireAuth:!1}},Zf={ID:/^[a-zA-Z0-9-_]+$/,UUID:/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,SLUG:/^[a-z0-9-]+$/,API_ROUTE:/^\/api\//,ADMIN_ROUTE:/^\/admin\//,USER_ROUTE:/^\/user\//,ASSET_ROUTE:/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/,STATIC_ROUTE:/^\/static\//},eh={PUBLIC_ROUTES:[de.HOME,de.LOGIN,de.SIGNUP,de.FORGOT_PASSWORD,de.RESET_PASSWORD,de.NOT_FOUND,de.UNAUTHORIZED,de.SERVER_ERROR],PROTECTED_ROUTES:[de.DASHBOARD,de.PROFILE,de.SETTINGS,de.CHAT,de.NOTIFICATIONS],ADMIN_ROUTES:[de.ADMIN,de.ADMIN_USERS,de.ADMIN_SETTINGS,de.ADMIN_ANALYTICS],GUEST_ROUTES:[de.LOGIN,de.SIGNUP,de.FORGOT_PASSWORD,de.RESET_PASSWORD]},th={toAMNAChat:e=>({sourceApp:"current",targetApp:"amna",targetPath:Ue.AMNA.routes.CHAT,context:e,preserveState:!0}),toEmailApp:e=>({sourceApp:"current",targetApp:"e-connect",targetPath:Ue.E_CONNECT.routes.MAIL,context:e,preserveState:!0}),toTrainingAnalysis:e=>({sourceApp:"current",targetApp:"training-analysis",targetPath:Ue.TRAINING_ANALYSIS.routes.DASHBOARD,context:e,preserveState:!0}),toLighthouse:e=>({sourceApp:"current",targetApp:"lighthouse",targetPath:Ue.LIGHTHOUSE.routes.HOME,context:e,preserveState:!0}),toVendors:e=>({sourceApp:"current",targetApp:"vendors",targetPath:Ue.VENDORS.routes.HOME,context:e,preserveState:!0}),toWinsOfWeek:e=>({sourceApp:"current",targetApp:"wins-of-week",targetPath:Ue.WINS_OF_WEEK.routes.HOME,context:e,preserveState:!0})},sh={CRITICAL_ROUTES:[de.HOME,de.DASHBOARD,de.LOGIN,Ue.AMNA.routes.CHAT,Ue.E_CONNECT.routes.MAIL],PRELOAD_ROUTES:[de.DASHBOARD,de.PROFILE,de.SETTINGS],CACHE_ROUTES:[de.HOME,de.HELP,de.DOCUMENTATION]},rh={ANNOUNCE_ROUTES:[de.DASHBOARD,de.CHAT,de.NOTIFICATIONS],FOCUS_ROUTES:[de.LOGIN,de.SIGNUP,de.SETTINGS],SKIP_LINK_ROUTES:[de.DASHBOARD,de.ADMIN,Ue.E_CONNECT.routes.MAIL]},nh="1.0.0",ah={CROSS_APP_NAVIGATION:!0,ROUTE_GUARDS:!0,ROUTE_ANALYTICS:!0,ROUTE_PREFETCHING:!0,ROUTE_CACHING:!0,BREADCRUMB_NAVIGATION:!0,KEYBOARD_NAVIGATION:!0,ACCESSIBILITY_FEATURES:!0};Object.defineProperty(exports,"useInfiniteQuery",{enumerable:!0,get:()=>T.useInfiniteQuery});Object.defineProperty(exports,"useIsFetching",{enumerable:!0,get:()=>T.useIsFetching});Object.defineProperty(exports,"useIsMutating",{enumerable:!0,get:()=>T.useIsMutating});Object.defineProperty(exports,"useMutation",{enumerable:!0,get:()=>T.useMutation});Object.defineProperty(exports,"useMutationState",{enumerable:!0,get:()=>T.useMutationState});Object.defineProperty(exports,"useQueries",{enumerable:!0,get:()=>T.useQueries});Object.defineProperty(exports,"useQuery",{enumerable:!0,get:()=>T.useQuery});Object.defineProperty(exports,"useQueryClient",{enumerable:!0,get:()=>T.useQueryClient});Object.defineProperty(exports,"useSuspenseInfiniteQuery",{enumerable:!0,get:()=>T.useSuspenseInfiniteQuery});Object.defineProperty(exports,"useSuspenseQuery",{enumerable:!0,get:()=>T.useSuspenseQuery});exports.AI_ANIMATION_SPEEDS=vd;exports.AI_CITATION_STYLES=jd;exports.AI_FEATURES=wd;exports.AI_MODELS=bd;exports.AI_PROVIDERS=xd;exports.AMNAWidget=ud;exports.APP_ROUTES=Ue;exports.Actions=Gc;exports.AdvancedColorPicker=Ia;exports.ApiHooksProvider=Mp;exports.AuthProvider=ic;exports.Breadcrumb=Ba;exports.Button=le;exports.COMMON_ROUTES=$p;exports.CROSS_APP_NAVIGATION=th;exports.Card=xt;exports.CodeSplitRoute=zf;exports.DefaultErrorComponent=it;exports.DefaultLoadingComponent=ot;exports.DefaultNotFoundComponent=lt;exports.Display=ou;exports.Email=id;exports.EmailActions=gt;exports.EmailCompose=Ur;exports.EmailIcon=Ee;exports.EmailList=$r;exports.EmailStatusIndicator=ds;exports.EnhancedLink=Lt;exports.EnhancedNavigation=Qf;exports.Feedback=ed;exports.Forms=_u;exports.LoadingSpinner=ar;exports.LuminarAccordion=ir;exports.LuminarAlert=Ir;exports.LuminarAlertDialog=Lr;exports.LuminarAutocomplete=kr;exports.LuminarAvatar=is;exports.LuminarBadge=Nt;exports.LuminarBanner=Da;exports.LuminarButton=We;exports.LuminarCard=xt;exports.LuminarCarousel=lr;exports.LuminarCarouselItem=cr;exports.LuminarCheckbox=De;exports.LuminarChipInput=Ar;exports.LuminarColorPicker=Rr;exports.LuminarCombobox=Nr;exports.LuminarCommand=nr;exports.LuminarCommandPalette=rr;exports.LuminarCounter=ur;exports.LuminarDataGrid=Sa;exports.LuminarDataTable=Ta;exports.LuminarDatePicker=Sr;exports.LuminarDateRangePicker=Tr;exports.LuminarDrawer=Or;exports.LuminarDropdown=kt;exports.LuminarFab=Ct;exports.LuminarFileUpload=Er;exports.LuminarForm=Cr;exports.LuminarIconButton=sr;exports.LuminarInput=Le;exports.LuminarLabel=Rt;exports.LuminarMetricCard=Ra;exports.LuminarModal=Dr;exports.LuminarPopover=Mr;exports.LuminarProgressRing=dr;exports.LuminarRadioGroup=vr;exports.LuminarRating=Pr;exports.LuminarSelect=St;exports.LuminarSkeleton=we;exports.LuminarSlider=jr;exports.LuminarStatCard=mr;exports.LuminarStats=os;exports.LuminarStatsGrid=pr;exports.LuminarSwitch=cs;exports.LuminarTable=fr;exports.LuminarTag=Ea;exports.LuminarText=hr;exports.LuminarTextarea=ls;exports.LuminarTimeline=gr;exports.LuminarToast=Fr;exports.LuminarTooltip=_r;exports.LuminarTransferList=yr;exports.LuminarTreeView=xr;exports.Navigation=Df;exports.NavigationUtils=Ka;exports.ProgressBar=wr;exports.QueryProvider=cc;exports.ROUTE_ACCESSIBILITY=rh;exports.ROUTE_CONFIGS=eh;exports.ROUTE_METADATA=Xf;exports.ROUTE_PATTERNS=Zf;exports.ROUTE_PERFORMANCE=sh;exports.ROUTING_FEATURES=ah;exports.ROUTING_VERSION=nh;exports.RouteAnalytics=Uf;exports.RouteGuard=Ff;exports.RouteLoader=Mf;exports.RouteTransition=_f;exports.RouterPresets=Vt;exports.SHARED_ROUTES=de;exports.SearchBar=pd;exports.Skeleton=et;exports.SmartBreadcrumb=$f;exports.StatefulButton=as;exports.ThemeProvider=Zi;exports.adminGuard=Ha;exports.adminRoute=Hf;exports.api=Be;exports.apiClient=A;exports.authApi=sc;exports.authGuard=fs;exports.buildRoute=At;exports.cacheUtils=ie;exports.cn=S;exports.combineGuards=hs;exports.commonMigrationMaps=$i;exports.composeGuards=ff;exports.createAdminGuard=of;exports.createApiHooks=Ma;exports.createCombinedGuard=zr;exports.createComponentFactory=Yi;exports.createDevelopmentGuard=lf;exports.createDeviceGuard=mf;exports.createFeatureFlagGuard=cf;exports.createGuestGuard=af;exports.createIPGuard=df;exports.createLuminarRouter=bf;exports.createPropValidator=Ji;exports.createProtectedRoute=Gf;exports.createRouteAnalytics=za;exports.createRouteBuilder=zp;exports.createRouteCacheKey=sf;exports.createRouteGuardMiddleware=$a;exports.createRouteHierarchy=Zp;exports.createRouterProvider=vf;exports.createSubscriptionGuard=pf;exports.createTimeBasedGuard=uf;exports.createTrainingHooks=Dp;exports.createTypeSafeRoutes=Hp;exports.createUsersHooks=Op;exports.crossAppNavigation=Jf;exports.debounceRouteChange=rf;exports.defaultComponentProps=Ae;exports.defaultFormProps=Oc;exports.devToolsConfig=ka;exports.emailApi=xe;exports.enablePropValidation=Wt;exports.executeRouteGuards=Ga;exports.filterUndefinedProps=Ys;exports.formatRouteForDisplay=tf;exports.generateBreadcrumbs=_a;exports.getAnimationClasses=Qi;exports.getAppBaseUrl=Ua;exports.getAriaAttributes=Vi;exports.getFocusRingClasses=Hi;exports.getGlassClasses=Hn;exports.getParentRoutePath=Kt;exports.getResponsiveClasses=Kn;exports.getResponsiveSizeClasses=Bi;exports.getRouteDepth=Xp;exports.getRouteMetadata=ps;exports.getRouteSiblings=ef;exports.getRouterConfig=jf;exports.getSizeClasses=Je;exports.getVariantClasses=Vn;exports.hasAnimationProps=Ic;exports.hasGlassProps=Lc;exports.hasRoutePermission=Wp;exports.hasRouteRole=Yp;exports.hasVariantProps=Pc;exports.isAIError=gd;exports.isAIMessage=fd;exports.isAIStreamingMessage=hd;exports.isAIToolCall=yd;exports.isRouteActive=Pt;exports.isValidAnimationPreset=Un;exports.isValidColorTheme=Di;exports.isValidComponentSize=Hs;exports.isValidComponentVariant=Gs;exports.isValidGlassDepth=Ii;exports.isValidGlassIntensity=_n;exports.isValidLoadingState=Oi;exports.matchRoute=Vp;exports.memoizeClasses=Yt;exports.memoizedGetGlassClasses=Wi;exports.memoizedGetSizeClasses=Ki;exports.memoizedGetVariantClasses=Gi;exports.mergeComponentProps=Gn;exports.mergeProps=zi;exports.migrateProps=Ui;exports.navigateToApp=Gp;exports.parseRouteParams=Qp;exports.parseSearchParams=qp;exports.permissionGuard=Va;exports.processComponentProps=qi;exports.protectedRoute=Vf;exports.queryClient=Ve;exports.queryKeys=C;exports.roleGuard=qf;exports.routeRequiresAuth=Kp;exports.routeUtils=Wf;exports.sanitizeComponentProps=qn;exports.sanitizeRoutePath=Jp;exports.sizeToPixels=Dc;exports.sizeToSpacing=Mc;exports.throttleRouteChange=nf;exports.trainingApi=ve;exports.trainingKeys=he;exports.transitionUtils=Yf;exports.useActivateUser=Hd;exports.useAddEmailRecipient=_m;exports.useAdminOperations=Ed;exports.useApiHooks=ms;exports.useAssessment=vp;exports.useAssessmentResponses=jp;exports.useAssessments=wp;exports.useAuth=Ge;exports.useAuthEnhanced=Cd;exports.useAuthGuard=Nd;exports.useBulkUpdateUsers=Kd;exports.useChangePassword=Yd;exports.useClearEmailQueue=Gm;exports.useCompleteTrainingModule=pp;exports.useCreateDepartment=cm;exports.useCreateEmailCampaign=km;exports.useCreateEmailTemplate=ym;exports.useCreateLearningPath=Ep;exports.useCreateRole=rm;exports.useCreateTrainingCourse=sp;exports.useCreateTrainingEnrollment=up;exports.useCreateUser=zd;exports.useCurrentUser=Id;exports.useDeactivateUser=Gd;exports.useDeleteDepartment=dm;exports.useDeleteEmailCampaign=Sm;exports.useDeleteEmailRecipient=$m;exports.useDeleteEmailTemplate=xm;exports.useDeleteRole=am;exports.useDeleteTrainingCourse=np;exports.useDeleteUser=Vd;exports.useDepartment=om;exports.useDepartmentMembers=lm;exports.useDepartments=im;exports.useEmailAnalytics=Vm;exports.useEmailCampaign=jm;exports.useEmailCampaignMetrics=Cm;exports.useEmailCampaignStatusPolling=Jm;exports.useEmailCampaigns=vm;exports.useEmailLog=Dm;exports.useEmailLogs=Om;exports.useEmailQueueStatus=Hm;exports.useEmailRecipients=Fm;exports.useEmailSettings=Qm;exports.useEmailTemplate=hm;exports.useEmailTemplatePreview=gm;exports.useEmailTemplates=fm;exports.useFollowLearningPath=Ap;exports.useGradeAssessment=kp;exports.useImportEmailRecipients=zm;exports.useInfiniteTrainingCourses=Zm;exports.useInfiniteUsers=Pd;exports.useInvalidateAllQueries=Up;exports.useLearningPaths=Rp;exports.useMarkAllNotificationsAsRead=em;exports.useMarkNotificationAsRead=Zd;exports.useNavigation=Qr;exports.useNavigationUtils=Kf;exports.useOptimisticEmailCampaignUpdate=Ym;exports.useOptimisticEmailTemplateUpdate=Wm;exports.useOptimisticEnrollmentUpdate=Pp;exports.useOptimisticNotificationUpdate=pm;exports.useOptimisticProgressUpdate=Lp;exports.useOptimisticUserUpdate=mm;exports.usePauseEmailCampaign=Em;exports.usePermissions=kd;exports.usePublishTrainingCourse=ap;exports.useRateSkill=bp;exports.useResumeEmailCampaign=Am;exports.useRetryFailedEmail=Mm;exports.useRetryFailedEmailJobs=Km;exports.useRevokeAllSessions=Xd;exports.useRevokeSession=Jd;exports.useRole=sm;exports.useRoleNavigation=Rd;exports.useRoles=tm;exports.useRouteAnalytics=Af;exports.useRouteCache=Ef;exports.useRouteHistory=Pf;exports.useRouteKeyboard=Of;exports.useRouteLoading=Sf;exports.useRouteMatch=Nf;exports.useRouteMetadata=Tf;exports.useRouteParams=Cf;exports.useRoutePermissions=Lf;exports.useRoutePrefetch=qa;exports.useRouteTitle=If;exports.useRouteTransition=Qa;exports.useRouteValidation=Rf;exports.useScheduleEmailCampaign=Rm;exports.useSearchUsers=Ud;exports.useSendBulkEmail=Lm;exports.useSendEmail=Pm;exports.useSendEmailCampaign=Tm;exports.useSendTransactionalEmail=Im;exports.useSession=Td;exports.useSkillGaps=hp;exports.useSkillRatings=gp;exports.useSkills=fp;exports.useSubmitAssessment=Cp;exports.useTestEmailSettings=Bm;exports.useTestEmailTemplate=wm;exports.useTrainingAnalytics=Np;exports.useTrainingCourse=ep;exports.useTrainingCourseAnalytics=tp;exports.useTrainingCourses=Xm;exports.useTrainingEnrollment=lp;exports.useTrainingEnrollments=op;exports.useTrainingHooks=_p;exports.useTrainingMetrics=Sp;exports.useTrainingProgressPolling=Ip;exports.useTrainingRecommendations=yp;exports.useTrainingReports=Tp;exports.useUnenrollFromTraining=mp;exports.useUnpublishTrainingCourse=ip;exports.useUpdateCurrentUser=qd;exports.useUpdateDepartment=um;exports.useUpdateEmailCampaign=Nm;exports.useUpdateEmailRecipient=Um;exports.useUpdateEmailSettings=qm;exports.useUpdateEmailTemplate=bm;exports.useUpdateRole=nm;exports.useUpdateSkillLevel=xp;exports.useUpdateTrainingCourse=rp;exports.useUpdateTrainingEnrollment=dp;exports.useUpdateUser=Qd;exports.useUpdateUserPreferences=Bd;exports.useUploadAvatar=Wd;exports.useUser=Ld;exports.useUserActivity=Od;exports.useUserGrowth=_d;exports.useUserManagement=Sd;exports.useUserNotifications=Md;exports.useUserSessions=Dd;exports.useUserStats=Fd;exports.useUserSuggestions=$d;exports.useUserTrainingProgress=cp;exports.useUsers=Ad;exports.useUsersHooks=Fp;exports.userKeys=je;exports.usersApi=be;exports.validateBaseComponentProps=$n;exports.validateFormComponentProps=Qn;exports.validatePropCombinations=Bn;exports.validatePropsInDev=Fi;exports.validateRoutePath=Bp;exports.validateStandardComponentProps=Ks;exports.validateStandardFormComponentProps=Mi;exports.validateVariantProps=zn;exports.vendorApi=rc;exports.winsApi=nc;exports.withAuth=oc;exports.withPropValidation=_i;exports.withRouteGuard=yf;exports.withRouteGuards=Bf;
//# sourceMappingURL=luminar-ui.cjs.js.map
