{"name": "@luminar/vite-config", "version": "1.0.0", "private": true, "description": "Shared Vite configuration for Luminar monorepo", "main": "index.js", "type": "module", "exports": {".": "./index.js", "./base": "./base.js", "./react": "./react.js", "./lib": "./lib.js"}, "files": ["index.js", "base.js", "react.js", "lib.js"], "scripts": {"typecheck": "tsc --noEmit"}, "dependencies": {"vite": "^6.0.0", "@vitejs/plugin-react": "^4.3.4", "vite-plugin-dts": "^4.3.0", "vite-plugin-checker": "^0.8.0", "vite-plugin-windicss": "^1.9.3", "vite-tsconfig-paths": "^5.1.3", "rollup-plugin-visualizer": "^5.12.0", "vite-plugin-eslint": "^1.8.1", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-commonjs": "^28.0.1", "esbuild": "^0.24.0", "terser": "^5.36.0"}, "devDependencies": {"@types/node": "^22.5.4", "typescript": "^5.7.2"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}