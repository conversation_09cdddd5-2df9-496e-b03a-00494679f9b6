import * as vitest from 'vitest';
import React$1, { ReactElement, ReactNode } from 'react';
import { RenderOptions, RenderResult, RenderHookOptions, RenderHookResult } from '@testing-library/react';
import userEvent__default from '@testing-library/user-event';
import * as react_jsx_runtime from 'react/jsx-runtime';

declare const mockLocalStorage: () => {
    getItem: vitest.Mock<(key: string) => string | null>;
    setItem: vitest.Mock<(key: string, value: string) => void>;
    removeItem: vitest.Mock<(key: string) => void>;
    clear: vitest.Mock<() => void>;
    readonly length: number;
    key: vitest.Mock<(index: number) => string | null>;
};
declare const setupBrowserMocks: () => void;

interface MemoryRouterPropsType {
    initialEntries?: string[];
    initialIndex?: number;
    [key: string]: any;
}
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
    routerProps?: MemoryRouterPropsType;
    route?: string;
    providers?: Array<React$1.ComponentType<{
        children: ReactNode;
    }>>;
    providerProps?: Record<string, any>;
    mockData?: Record<string, any>;
}
declare function customRender(ui: ReactElement, options?: CustomRenderOptions): RenderResult & {
    user: ReturnType<typeof userEvent__default.setup>;
};

declare const renderWithRouter: (ui: ReactElement, routerProps?: MemoryRouterPropsType, renderOptions?: Omit<CustomRenderOptions, "routerProps">) => RenderResult & {
    user: ReturnType<typeof userEvent__default.setup>;
};
declare const renderWithProviders: (ui: ReactElement, providers?: CustomRenderOptions["providers"], renderOptions?: Omit<CustomRenderOptions, "providers">) => RenderResult & {
    user: ReturnType<typeof userEvent__default.setup>;
};
declare const renderHookWithProviders: <TProps, TResult>(hook: (props: TProps) => TResult, options?: CustomRenderOptions) => (RenderResult & {
    user: ReturnType<typeof userEvent__default.setup>;
}) & {
    result: {
        current: TResult;
    };
};
declare const renderAndGetElements: (ui: ReactElement, options?: CustomRenderOptions) => RenderResult & {
    user: ReturnType<typeof userEvent__default.setup>;
};

interface CustomRenderHookOptions<TProps> extends Omit<RenderHookOptions<TProps>, 'wrapper'> {
    providers?: Array<React.ComponentType<{
        children: ReactNode;
    }>>;
    providerProps?: Record<string, any>;
}
declare function renderHookWithContext<TProps, TResult>(hook: (props: TProps) => TResult, options?: CustomRenderHookOptions<TProps>): RenderHookResult<TResult, TProps>;
declare const waitForHookUpdate: (_result: {
    current: any;
}, condition: () => boolean, options?: {
    timeout: number;
    interval: number;
}) => Promise<void>;
declare const testHookWithAsyncState: <T>(hook: () => {
    data: T | null;
    loading: boolean;
    error: Error | null;
}, expectedData: T) => Promise<{
    result: {
        current: {
            data: T | null;
            loading: boolean;
            error: Error | null;
        };
    };
    rerender: (props?: unknown) => void;
}>;
declare const testHookErrorState: <T>(hook: () => {
    data: T | null;
    loading: boolean;
    error: Error | null;
}, expectedError: string) => Promise<void>;
declare const trackHookRenders: <T>(hook: () => T) => {
    result: {
        current: T;
    };
    rerender: (props?: unknown) => void;
    getRenderCount: () => number;
    getValues: () => T[];
    getLastValue: () => T;
};

interface ProviderOptions {
    providers?: Array<React$1.ComponentType<{
        children: ReactNode;
    }>>;
    providerProps?: Record<string, any>;
}
declare const createWrapper: (options?: ProviderOptions) => ({ children }: {
    children: ReactNode;
}) => react_jsx_runtime.JSX.Element;
declare const TestProviders: React$1.FC<{
    children: ReactNode;
}>;
declare const createContextWrapper: <T>(Context: React$1.Context<T>, value: T) => ({ children }: {
    children: ReactNode;
}) => react_jsx_runtime.JSX.Element;
declare const createMockProvider: <T extends Record<string, any>>(name: string, defaultValue: T) => {
    Context: React$1.Context<T>;
    Provider: React$1.FC<{
        children: ReactNode;
        value?: Partial<T>;
    }>;
};
declare const CombinedProviders: React$1.FC<{
    children: ReactNode;
    providers?: ProviderOptions['providers'];
    providerProps?: ProviderOptions['providerProps'];
}>;

declare const queries: {
    getElement: (testId: string, container?: HTMLElement) => HTMLElement;
    findElement: (testId: string, options?: {
        timeout?: number;
    }) => Promise<HTMLElement>;
    queryElement: (testId: string, container?: HTMLElement) => HTMLElement | null;
    getAllElements: (testId: string, container?: HTMLElement) => HTMLElement[];
    getByTextContent: (text: string | RegExp, container?: HTMLElement) => HTMLElement;
    getByRoleWithName: (role: string, name: string | RegExp) => HTMLElement;
    getButtonByText: (text: string | RegExp) => HTMLElement;
    getInputByLabel: (label: string | RegExp) => HTMLElement;
    getLinkByText: (text: string | RegExp) => HTMLElement;
    getFormField: (name: string) => HTMLElement;
    getTableCell: (rowIndex: number, colIndex: number) => HTMLElement;
    getListItems: (listTestId: string) => HTMLElement[];
};
declare const waitForElement: (testId: string, options?: {
    timeout: number;
}) => Promise<HTMLElement>;
declare const waitForElementToBeRemoved: (testId: string, options?: {
    timeout: number;
}) => Promise<void>;
declare const waitForText: (text: string | RegExp, options?: {
    timeout: number;
}) => Promise<HTMLElement>;
declare const assertElementExists: (testId: string) => HTMLElement;
declare const assertElementNotExists: (testId: string) => void;
declare const assertElementVisible: (testId: string) => HTMLElement;
declare const assertElementHidden: (testId: string) => HTMLElement;
declare const getInteractiveElement: (testId: string, options?: {
    disabled?: boolean;
}) => HTMLElement;

export { CombinedProviders, TestProviders, assertElementExists, assertElementHidden, assertElementNotExists, assertElementVisible, createContextWrapper, createMockProvider, createWrapper, getInteractiveElement, mockLocalStorage, queries, customRender as render, renderAndGetElements, renderHookWithContext, renderHookWithProviders, renderWithProviders, renderWithRouter, setupBrowserMocks, testHookErrorState, testHookWithAsyncState, trackHookRenders, waitForElement, waitForElementToBeRemoved, waitForHookUpdate, waitForText };
