import React from 'react';
import { useModuleFederation } from './ModuleFederationProvider';

interface FeatureFlagProps {
  flag: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  variant?: string;
}

/**
 * Component for feature flag based rendering
 */
export function FeatureFlagComponent({ 
  flag, 
  children, 
  fallback = null,
  variant 
}: FeatureFlagProps) {
  const { featureFlags } = useModuleFederation();
  const flagConfig = featureFlags[flag];

  if (!flagConfig || !flagConfig.enabled) {
    return <>{fallback}</>;
  }

  if (variant && flagConfig.variants) {
    const variantValue = flagConfig.variants[variant];
    if (!variantValue) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
}

/**
 * Hook to check feature flags
 */
export function useFeatureFlag(flag: string): boolean {
  const { featureFlags } = useModuleFederation();
  return featureFlags[flag]?.enabled || false;
}

/**
 * Hook to get feature flag variant
 */
export function useFeatureFlagVariant(flag: string, variant: string): any {
  const { featureFlags } = useModuleFederation();
  return featureFlags[flag]?.variants?.[variant];
}

/**
 * HOC for feature flag gating
 */
export function withFeatureFlag<P extends object>(
  flag: string,
  fallback?: React.ComponentType<P>
) {
  return function (Component: React.ComponentType<P>) {
    const WrappedComponent = (props: P) => {
      const isEnabled = useFeatureFlag(flag);

      if (!isEnabled) {
        if (fallback) {
          const FallbackComponent = fallback;
          return <FallbackComponent {...props} />;
        }
        return null;
      }

      return <Component {...props} />;
    };

    WrappedComponent.displayName = `withFeatureFlag(${Component.displayName || Component.name})`;

    return WrappedComponent;
  };
}