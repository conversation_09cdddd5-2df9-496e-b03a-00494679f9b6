import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { io, Socket } from 'socket.io-client';
import { IntegrationModule } from '../../integration.module';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';

describe('Integration E2E Tests', () => {
  let app: INestApplication;
  let socket: Socket;
  let authToken: string;
  const testUserId = 'test-user-123';

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({ isGlobal: true }),
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_PORT || '5432'),
          username: process.env.DB_USER || 'test',
          password: process.env.DB_PASSWORD || 'test',
          database: process.env.DB_NAME || 'luminar_test',
          autoLoadEntities: true,
          synchronize: true,
        }),
        JwtModule.register({
          secret: process.env.JWT_SECRET || 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
        IntegrationModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get auth token
    const authResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ username: 'test', password: 'test' });

    authToken = authResponse.body.access_token;
  });

  afterAll(async () => {
    if (socket) {
      socket.disconnect();
    }
    await app.close();
  });

  describe('Cross-Application Integration Flow', () => {
    it('should handle complete E-Connect → AMNA → Wins flow', async () => {
      // Step 1: E-Connect sends email activity
      const emailActivity = {
        userId: testUserId,
        source: 'e-connect',
        type: 'email.sent',
        data: {
          to: '<EMAIL>',
          subject: 'Project Update',
          sentiment: 'positive',
          timestamp: new Date(),
        },
      };

      const activityResponse = await request(app.getHttpServer())
        .post('/api/integration/activity')
        .set('Authorization', `Bearer ${authToken}`)
        .send(emailActivity)
        .expect(201);

      expect(activityResponse.body).toHaveProperty('id');
      expect(activityResponse.body.processed).toBe(false);

      // Step 2: AMNA processes the activity
      const amnaResponse = await request(app.getHttpServer())
        .post('/api/amna/process-activity')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ activityId: activityResponse.body.id })
        .expect(200);

      expect(amnaResponse.body).toHaveProperty('insights');
      expect(amnaResponse.body.insights).toContain('positive engagement');

      // Step 3: Verify Wins-of-Week receives the activity
      const winsResponse = await request(app.getHttpServer())
        .get(`/api/integration/wins/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(winsResponse.body.activities).toHaveLength(1);
      expect(winsResponse.body.activities[0].source).toBe('e-connect');
    });

    it('should handle bidirectional Training ↔ Vendors integration', async () => {
      // Step 1: Training identifies a skill gap
      const skillGap = {
        userId: testUserId,
        source: 'training',
        type: 'skill.gap.identified',
        data: {
          skill: 'Cloud Architecture',
          currentLevel: 2,
          requiredLevel: 4,
          priority: 'high',
        },
      };

      const trainingResponse = await request(app.getHttpServer())
        .post('/api/integration/activity')
        .set('Authorization', `Bearer ${authToken}`)
        .send(skillGap)
        .expect(201);

      // Step 2: AMNA finds matching vendors
      const vendorMatchResponse = await request(app.getHttpServer())
        .post('/api/amna/match-vendors')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          activityId: trainingResponse.body.id,
          skill: 'Cloud Architecture',
        })
        .expect(200);

      expect(vendorMatchResponse.body.vendors).toHaveLength(3);
      expect(vendorMatchResponse.body.vendors[0]).toHaveProperty('matchScore');

      // Step 3: Vendor responds with availability
      const vendorResponse = {
        userId: testUserId,
        source: 'vendors',
        type: 'vendor.availability.updated',
        data: {
          vendorId: vendorMatchResponse.body.vendors[0].id,
          availability: 'immediate',
          proposedStartDate: new Date(),
        },
      };

      await request(app.getHttpServer())
        .post('/api/integration/activity')
        .set('Authorization', `Bearer ${authToken}`)
        .send(vendorResponse)
        .expect(201);

      // Step 4: Verify Training receives the update
      const trainingUpdateResponse = await request(app.getHttpServer())
        .get(`/api/integration/training/vendor-updates/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(trainingUpdateResponse.body.updates).toHaveLength(1);
      expect(trainingUpdateResponse.body.updates[0].vendorId).toBe(
        vendorMatchResponse.body.vendors[0].id,
      );
    });
  });

  describe('WebSocket Real-time Integration', () => {
    beforeEach((done) => {
      socket = io('http://localhost:3000', {
        auth: { token: authToken },
      });
      socket.on('connect', done);
    });

    afterEach(() => {
      socket.disconnect();
    });

    it('should broadcast real-time updates across applications', (done) => {
      const testActivity = {
        userId: testUserId,
        source: 'lighthouse',
        type: 'research.insight.generated',
        data: {
          topic: 'Market Analysis',
          insight: 'Emerging trend detected',
          confidence: 0.85,
        },
      };

      // Listen for broadcast
      socket.on('integration.update', (data) => {
        expect(data.userId).toBe(testUserId);
        expect(data.source).toBe('lighthouse');
        expect(data.type).toBe('research.insight.generated');
        done();
      });

      // Send activity
      request(app.getHttpServer())
        .post('/api/integration/activity')
        .set('Authorization', `Bearer ${authToken}`)
        .send(testActivity)
        .expect(201)
        .end((err) => {
          if (err) done(err);
        });
    });

    it('should handle concurrent updates without data loss', async () => {
      const activities = Array.from({ length: 100 }, (_, i) => ({
        userId: testUserId,
        source: 'e-connect',
        type: 'email.sent',
        data: { index: i, timestamp: new Date() },
      }));

      // Send all activities concurrently
      const promises = activities.map((activity) =>
        request(app.getHttpServer())
          .post('/api/integration/activity')
          .set('Authorization', `Bearer ${authToken}`)
          .send(activity),
      );

      const responses = await Promise.all(promises);
      expect(responses.every((r) => r.status === 201)).toBe(true);

      // Verify all activities were recorded
      const allActivities = await request(app.getHttpServer())
        .get(`/api/integration/activities/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .query({ limit: 100 })
        .expect(200);

      expect(allActivities.body.activities).toHaveLength(100);
    });
  });

  describe('Failure Scenarios and Recovery', () => {
    it('should handle circuit breaker activation', async () => {
      // Simulate service failure
      await request(app.getHttpServer())
        .post('/api/integration/test/simulate-failure')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ service: 'e-connect', duration: 5000 })
        .expect(200);

      // Attempt to send activity (should fail fast)
      const failureResponse = await request(app.getHttpServer())
        .post('/api/integration/activity')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          userId: testUserId,
          source: 'e-connect',
          type: 'email.sent',
          data: { test: true },
        })
        .expect(503);

      expect(failureResponse.body.error).toContain('Circuit breaker OPEN');

      // Wait for circuit to half-open
      await new Promise((resolve) => setTimeout(resolve, 6000));

      // Should work again
      await request(app.getHttpServer())
        .post('/api/integration/activity')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          userId: testUserId,
          source: 'e-connect',
          type: 'email.sent',
          data: { test: true },
        })
        .expect(201);
    });

    it('should handle offline queue when services are down', async () => {
      // Disable real-time sync
      await request(app.getHttpServer())
        .put(`/api/integration/preferences/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ real_time_sync: false })
        .expect(200);

      // Send activities while offline
      const offlineActivities = Array.from({ length: 10 }, (_, i) => ({
        userId: testUserId,
        source: 'lighthouse',
        type: 'research.completed',
        data: { index: i },
      }));

      for (const activity of offlineActivities) {
        await request(app.getHttpServer())
          .post('/api/integration/activity')
          .set('Authorization', `Bearer ${authToken}`)
          .send(activity)
          .expect(201);
      }

      // Re-enable sync
      await request(app.getHttpServer())
        .put(`/api/integration/preferences/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ real_time_sync: true })
        .expect(200);

      // Trigger sync
      const syncResponse = await request(app.getHttpServer())
        .post(`/api/integration/sync/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(syncResponse.body.synced).toBe(10);
    });
  });

  describe('Data Consistency and Federation', () => {
    it('should maintain data consistency across applications', async () => {
      const testData = {
        userId: testUserId,
        projectId: 'proj-123',
        vendorId: 'vendor-456',
      };

      // Create data in multiple applications
      await request(app.getHttpServer())
        .post('/api/integration/training/project')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          ...testData,
          skills: ['AWS', 'Kubernetes'],
          duration: '3 months',
        })
        .expect(201);

      await request(app.getHttpServer())
        .post('/api/integration/vendors/contract')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          ...testData,
          rate: 150,
          currency: 'USD',
        })
        .expect(201);

      // Query federated data
      const federatedResponse = await request(app.getHttpServer())
        .get(`/api/integration/federated/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .query({ includeAll: true })
        .expect(200);

      expect(federatedResponse.body.training).toBeDefined();
      expect(federatedResponse.body.vendors).toBeDefined();
      expect(federatedResponse.body.training.projectId).toBe(
        testData.projectId,
      );
      expect(federatedResponse.body.vendors.vendorId).toBe(testData.vendorId);
    });

    it('should handle eventual consistency with proper ordering', async () => {
      const events = [];

      // Subscribe to events
      socket.on('integration.event', (event) => {
        events.push(event);
      });

      // Send ordered events
      for (let i = 0; i < 5; i++) {
        await request(app.getHttpServer())
          .post('/api/integration/activity')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            userId: testUserId,
            source: 'training',
            type: 'progress.update',
            data: { step: i, timestamp: new Date() },
          })
          .expect(201);

        // Small delay to ensure ordering
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      // Wait for all events
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Verify ordering
      expect(events).toHaveLength(5);
      for (let i = 0; i < 4; i++) {
        expect(events[i].data.step).toBeLessThan(events[i + 1].data.step);
      }
    });
  });

  describe('Performance and Load Testing', () => {
    it('should handle high-frequency updates efficiently', async () => {
      const startTime = Date.now();
      const activities = Array.from({ length: 1000 }, (_, i) => ({
        userId: `user-${i % 10}`,
        source: ['e-connect', 'lighthouse', 'training'][i % 3],
        type: 'test.performance',
        data: { index: i },
      }));

      // Send in batches
      const batchSize = 100;
      for (let i = 0; i < activities.length; i += batchSize) {
        const batch = activities.slice(i, i + batchSize);
        await Promise.all(
          batch.map((activity) =>
            request(app.getHttpServer())
              .post('/api/integration/activity')
              .set('Authorization', `Bearer ${authToken}`)
              .send(activity),
          ),
        );
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within 10 seconds
      expect(duration).toBeLessThan(10000);

      // Verify all activities were processed
      const stats = await request(app.getHttpServer())
        .get('/api/integration/stats')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(stats.body.totalProcessed).toBeGreaterThanOrEqual(1000);
    });
  });
});
