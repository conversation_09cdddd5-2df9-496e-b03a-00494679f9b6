'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

// src/luminar-apps.config.ts
var LUMINAR_APPS = [
  {
    id: "amna",
    name: "AMNA - AI Assistant",
    shortName: "AMNA",
    description: "AI-powered assistant for modern needs",
    url: process.env.AMNA_URL || "http://localhost:5173",
    icon: "\u{1F916}",
    color: "#3b82f6",
    roles: ["user"],
    permissions: [],
    category: "core",
    status: "active"
  },
  {
    id: "training-dashboard",
    name: "Training Dashboard",
    shortName: "Training",
    description: "Manage training programs and enrollments",
    url: process.env.TRAINING_URL || "http://localhost:5174",
    icon: "\u{1F4DA}",
    color: "#10b981",
    roles: ["user"],
    permissions: ["training:view"],
    category: "training",
    status: "active"
  },
  {
    id: "vendor-management",
    name: "Vendor Management",
    shortName: "Vendors",
    description: "Manage training vendors and contracts",
    url: process.env.VENDOR_URL || "http://localhost:5175",
    icon: "\u{1F3E2}",
    color: "#8b5cf6",
    roles: ["manager", "admin"],
    permissions: ["vendors:view"],
    category: "management",
    status: "active"
  },
  {
    id: "wins-of-week",
    name: "Wins of the Week",
    shortName: "Wins",
    description: "Submit and review weekly achievements",
    url: process.env.WINS_URL || "http://localhost:5176",
    icon: "\u{1F3C6}",
    color: "#f59e0b",
    roles: ["user"],
    permissions: ["wins:submit"],
    category: "core",
    status: "active"
  },
  {
    id: "training-needs",
    name: "Training Need Analysis",
    shortName: "TNA",
    description: "Analyze skill gaps and training needs",
    url: process.env.TNA_URL || "http://localhost:5177",
    icon: "\u{1F4CA}",
    color: "#ef4444",
    roles: ["user"],
    permissions: ["training:view"],
    category: "training",
    status: "active"
  },
  {
    id: "lighthouse",
    name: "Lighthouse Reports",
    shortName: "Lighthouse",
    description: "Performance metrics and analytics",
    url: process.env.LIGHTHOUSE_URL || "http://localhost:5178",
    icon: "\u{1F3E0}",
    color: "#06b6d4",
    roles: ["manager", "admin"],
    permissions: ["analytics:view"],
    category: "analytics",
    status: "active"
  },
  {
    id: "e-connect",
    name: "e-Connect Integration",
    shortName: "e-Connect",
    description: "Connect with external training systems",
    url: process.env.ECONNECT_URL || "http://localhost:5179",
    icon: "\u{1F50C}",
    color: "#64748b",
    roles: ["admin"],
    permissions: ["system:config"],
    category: "management",
    status: "beta"
  },
  {
    id: "admin-panel",
    name: "Admin Panel",
    shortName: "Admin",
    description: "System administration and configuration",
    url: process.env.ADMIN_URL || "http://localhost:5180",
    icon: "\u2699\uFE0F",
    color: "#1f2937",
    roles: ["admin", "super_admin"],
    permissions: ["system:config"],
    category: "management",
    status: "active"
  }
];
function getAppById(id) {
  return LUMINAR_APPS.find((app) => app.id === id);
}
function getAppsByCategory(category) {
  return LUMINAR_APPS.filter((app) => app.category === category);
}
function getActiveApps() {
  return LUMINAR_APPS.filter((app) => app.status === "active");
}
function getAppsForUser(roles, permissions) {
  return LUMINAR_APPS.filter((app) => {
    const hasRole = app.roles.length === 0 || app.roles.some((role) => roles.includes(role));
    const hasPermission = app.permissions.length === 0 || app.permissions.some((permission) => permissions.includes(permission));
    return hasRole && hasPermission && app.status !== "coming-soon";
  });
}
var APP_ENVIRONMENTS = {
  development: {
    apiUrl: "http://localhost:3000/api/v1",
    wsUrl: "ws://localhost:3000",
    domain: "localhost"
  },
  staging: {
    apiUrl: "https://api-staging.luminar.com",
    wsUrl: "wss://api-staging.luminar.com",
    domain: ".luminar-staging.com"
  },
  production: {
    apiUrl: "https://api.luminar.com",
    wsUrl: "wss://api.luminar.com",
    domain: ".luminar.com"
  }
};
function getEnvironmentConfig(env = "development") {
  return APP_ENVIRONMENTS[env];
}
var SHARED_CONFIG = {
  companyName: "Luminar",
  supportEmail: "<EMAIL>",
  helpUrl: "https://help.luminar.com",
  privacyUrl: "https://luminar.com/privacy",
  termsUrl: "https://luminar.com/terms",
  logo: "/assets/luminar-logo.svg",
  favicon: "/favicon.ico",
  theme: {
    primaryColor: "#3b82f6",
    secondaryColor: "#10b981",
    accentColor: "#8b5cf6",
    backgroundColor: "#f3f4f6",
    textColor: "#1f2937"
  },
  auth: {
    sessionTimeout: 30 * 60 * 1e3,
    // 30 minutes
    refreshInterval: 10 * 60 * 1e3,
    // 10 minutes
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true
    }
  },
  features: {
    enableSSO: true,
    enableMFA: true,
    enableOfflineMode: true,
    enablePushNotifications: true,
    enableAnalytics: true
  }
};

exports.APP_ENVIRONMENTS = APP_ENVIRONMENTS;
exports.LUMINAR_APPS = LUMINAR_APPS;
exports.SHARED_CONFIG = SHARED_CONFIG;
exports.default = LUMINAR_APPS;
exports.getActiveApps = getActiveApps;
exports.getAppById = getAppById;
exports.getAppsByCategory = getAppsByCategory;
exports.getAppsForUser = getAppsForUser;
exports.getEnvironmentConfig = getEnvironmentConfig;
//# sourceMappingURL=index.js.map
//# sourceMappingURL=index.js.map