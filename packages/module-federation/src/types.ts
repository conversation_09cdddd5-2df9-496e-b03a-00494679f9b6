// Define our own interface based on the vite-plugin-federation expected structure
export interface LuminarModuleFederationConfig {
  name: string;
  filename?: string;
  exposes?: Record<string, string>;
  remotes?: Record<string, string | { external: string; format?: string; from?: string }>;
  shared?: string[] | Record<string, any>;
  /** Luminar-specific configuration */
  luminar?: {
    /** Enable runtime configuration */
    runtimeConfig?: boolean;
    /** Enable feature flags integration */
    featureFlags?: boolean;
    /** Enable error tracking */
    errorTracking?: boolean;
    /** Health check endpoint */
    healthCheck?: string;
  };
}

export interface RemoteModule {
  name: string;
  url: string;
  format?: 'esm' | 'systemjs' | 'var';
  from?: 'vite' | 'webpack';
}

export interface SharedDependency {
  name: string;
  version: string;
  singleton?: boolean;
  requiredVersion?: string;
  eager?: boolean;
}

export interface ModuleFederationRuntime {
  loadRemote: (remoteName: string, modulePath?: string) => Promise<any>;
  registerRemote: (name: string, url: string) => void;
  unregisterRemote: (name: string) => void;
  getRemoteUrl: (name: string) => string | undefined;
  setRemoteUrl: (name: string, url: string) => void;
  reloadRemote: (name: string) => Promise<void>;
}

export interface ErrorBoundaryProps {
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  children: React.ReactNode;
}

export interface RemoteComponentProps<T = any> {
  remote: string;
  module?: string;
  fallback?: React.ReactNode;
  onError?: (error: Error) => void;
  props?: T;
}

export interface FeatureFlag {
  key: string;
  enabled: boolean;
  variants?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface RuntimeConfig {
  remotes: Record<string, string>;
  shared?: Record<string, SharedDependency>;
  featureFlags?: Record<string, FeatureFlag>;
  environment?: 'development' | 'staging' | 'production';
}