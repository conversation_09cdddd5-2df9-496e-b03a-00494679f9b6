import { screen, prettyDOM } from '@testing-library/react'

// Debug utilities for tests
export const debug = {
  // Log the current DOM
  dom: (element?: HTMLElement) => {
    const target = element || document.body
    console.log(prettyDOM(target, 99999))
  },

  // Log all elements with a specific role
  role: (role: string) => {
    try {
      const elements = screen.getAllByRole(role)
      console.log(`Found ${elements.length} elements with role "${role}":`)
      elements.forEach((el, index) => {
        console.log(`[${index}]:`, prettyDOM(el))
      })
    } catch (error) {
      console.log(`No elements found with role "${role}"`)
    }
  },

  // Log all elements with a specific testId
  testId: (testId: string) => {
    try {
      const elements = screen.getAllByTestId(testId)
      console.log(`Found ${elements.length} elements with testId "${testId}":`)
      elements.forEach((el, index) => {
        console.log(`[${index}]:`, prettyDOM(el))
      })
    } catch (error) {
      console.log(`No elements found with testId "${testId}"`)
    }
  },

  // Log all elements containing specific text
  text: (text: string | RegExp) => {
    try {
      const elements = screen.getAllByText(text)
      console.log(`Found ${elements.length} elements with text "${text}":`)
      elements.forEach((el, index) => {
        console.log(`[${index}]:`, prettyDOM(el))
      })
    } catch (error) {
      console.log(`No elements found with text "${text}"`)
    }
  },

  // Log element tree
  tree: (element: HTMLElement, depth = 3) => {
    const printTree = (el: Element, level = 0, maxDepth = depth) => {
      if (level > maxDepth) return
      
      const indent = '  '.repeat(level)
      const tag = el.tagName.toLowerCase()
      const classes = el.className ? `.${el.className.split(' ').join('.')}` : ''
      const id = el.id ? `#${el.id}` : ''
      const testId = el.getAttribute('data-testid') ? `[data-testid="${el.getAttribute('data-testid')}"]` : ''
      
      console.log(`${indent}${tag}${id}${classes}${testId}`)
      
      Array.from(el.children).forEach(child => {
        printTree(child, level + 1, maxDepth)
      })
    }
    
    printTree(element)
  },

  // Log all inputs and their values
  inputs: () => {
    const inputs = document.querySelectorAll('input, textarea, select')
    console.log(`Found ${inputs.length} form inputs:`)
    inputs.forEach((input: any, index) => {
      console.log(`[${index}] ${input.tagName} name="${input.name}" value="${input.value}"`)
    })
  },

  // Log component props (for debugging React components)
  props: (element: HTMLElement) => {
    const reactFiber = (element as any)._reactInternalFiber || 
                      (element as any)._reactInternalInstance
    
    if (reactFiber) {
      console.log('React component props:', reactFiber.memoizedProps)
    } else {
      console.log('No React component found for element')
    }
  },

  // Performance debugging
  performance: (label: string, fn: () => void) => {
    const start = performance.now()
    fn()
    const end = performance.now()
    console.log(`[Performance] ${label}: ${(end - start).toFixed(2)}ms`)
  },

  // Memory debugging
  memory: () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      console.log('Memory usage:', {
        usedJSHeapSize: `${(memory.usedJSHeapSize / 1048576).toFixed(2)} MB`,
        totalJSHeapSize: `${(memory.totalJSHeapSize / 1048576).toFixed(2)} MB`,
        jsHeapSizeLimit: `${(memory.jsHeapSizeLimit / 1048576).toFixed(2)} MB`,
      })
    }
  },

  // Event listeners debugging
  listeners: (element: HTMLElement) => {
    const events = (element as any)._events || {}
    console.log('Event listeners:', events)
  },

  // Style debugging
  styles: (element: HTMLElement) => {
    const computed = window.getComputedStyle(element)
    const styles: Record<string, string> = {}
    
    Array.from(computed).forEach(prop => {
      const value = computed.getPropertyValue(prop)
      if (value && value !== 'initial' && value !== 'none') {
        styles[prop] = value
      }
    })
    
    console.log('Computed styles:', styles)
  },
}

// Test snapshot utilities
export const snapshot = {
  // Create a snapshot of the current DOM
  dom: (element?: HTMLElement) => {
    const target = element || document.body
    return prettyDOM(target, 99999)
  },

  // Create a snapshot of component state
  state: (component: any) => {
    return JSON.stringify(component, null, 2)
  },

  // Create a snapshot with metadata
  withMetadata: (data: any, metadata: Record<string, any>) => {
    return {
      timestamp: new Date().toISOString(),
      metadata,
      data,
    }
  },
}

// Assertion debugging helpers
export const assertionHelpers = {
  // Log why an assertion might fail
  whyNot: {
    visible: (element: HTMLElement) => {
      const rect = element.getBoundingClientRect()
      const computed = window.getComputedStyle(element)
      
      console.log('Visibility debug:', {
        display: computed.display,
        visibility: computed.visibility,
        opacity: computed.opacity,
        position: computed.position,
        zIndex: computed.zIndex,
        dimensions: {
          width: rect.width,
          height: rect.height,
        },
        visibilityPosition: {
          top: rect.top,
          left: rect.left,
        },
        inViewport: rect.top >= 0 && rect.left >= 0 &&
                   rect.bottom <= window.innerHeight &&
                   rect.right <= window.innerWidth,
      })
    },

    enabled: (element: HTMLElement) => {
      console.log('Disabled state debug:', {
        disabled: element.hasAttribute('disabled'),
        ariaDisabled: element.getAttribute('aria-disabled'),
        readOnly: element.hasAttribute('readonly'),
        ariaReadOnly: element.getAttribute('aria-readonly'),
        pointerEvents: window.getComputedStyle(element).pointerEvents,
      })
    },
  },
}

// Test environment info
export const environmentInfo = () => {
  console.log('Test environment:', {
    userAgent: navigator.userAgent,
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight,
    },
    screen: {
      width: window.screen.width,
      height: window.screen.height,
    },
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    locale: navigator.language,
  })
}