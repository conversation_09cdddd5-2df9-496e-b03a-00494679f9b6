import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';

import { AuditLogService } from '../services/audit-log.service';

export interface AuditLogJobData {
  eventType: string;
  userId?: string;
  action: string;
  resource: string;
  metadata?: Record<string, any>;
  severity?: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp?: Date;
}

@Processor('audit-processing')
export class AuditLogProcessor {
  private readonly logger = new Logger(AuditLogProcessor.name);

  constructor(private readonly auditLogService: AuditLogService) {}

  @Process('create-audit-log')
  async processAuditLog(job: Job<AuditLogJobData>): Promise<void> {
    this.logger.log(`Processing audit log job ${job.id}`);

    try {
      const { eventType, userId, action, resource, metadata, severity, ipAddress, userAgent, timestamp } = job.data;

      await this.auditLogService.createAuditLog({
        eventType,
        userId,
        action,
        resource,
        metadata: metadata || {},
        severity: severity || 'info',
        ipAddress,
        userAgent,
        timestamp: timestamp || new Date(),
      });

      this.logger.log(`Audit log processed successfully for job ${job.id}`);
    } catch (error) {
      this.logger.error(`Failed to process audit log job ${job.id}:`, error);
      throw error;
    }
  }

  @Process('batch-audit-logs')
  async processBatchAuditLogs(job: Job<{ logs: AuditLogJobData[] }>): Promise<void> {
    this.logger.log(`Processing batch audit logs job ${job.id} with ${job.data.logs.length} logs`);

    try {
      const { logs } = job.data;

      for (const logData of logs) {
        await this.auditLogService.createAuditLog({
          eventType: logData.eventType,
          userId: logData.userId,
          action: logData.action,
          resource: logData.resource,
          metadata: logData.metadata || {},
          severity: logData.severity || 'info',
          ipAddress: logData.ipAddress,
          userAgent: logData.userAgent,
          timestamp: logData.timestamp || new Date(),
        });
      }

      this.logger.log(`Batch audit logs processed successfully for job ${job.id}`);
    } catch (error) {
      this.logger.error(`Failed to process batch audit logs job ${job.id}:`, error);
      throw error;
    }
  }

  @Process('cleanup-old-logs')
  async cleanupOldLogs(job: Job<{ retentionDays: number }>): Promise<void> {
    this.logger.log(`Processing cleanup old logs job ${job.id}`);

    try {
      const { retentionDays } = job.data;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const deletedCount = await this.auditLogService.cleanupOldLogs(cutoffDate);

      this.logger.log(`Cleanup completed for job ${job.id}, deleted ${deletedCount} old logs`);
    } catch (error) {
      this.logger.error(`Failed to cleanup old logs for job ${job.id}:`, error);
      throw error;
    }
  }

  @Process('archive-logs')
  async archiveLogs(job: Job<{ startDate: Date; endDate: Date; archivePath: string }>): Promise<void> {
    this.logger.log(`Processing archive logs job ${job.id}`);

    try {
      const { startDate, endDate, archivePath } = job.data;

      // Get logs to archive
      const logs = await this.auditLogService.getAuditLogs({
        dateRange: { start: startDate, end: endDate },
        limit: 10000, // Large limit for archiving
      });

      // Simulate archiving process
      const archiveData = {
        archiveDate: new Date(),
        totalLogs: logs.length,
        dateRange: { start: startDate, end: endDate },
        logs: logs,
      };

      // In a real implementation, you would:
      // 1. Compress the data
      // 2. Store to file system or cloud storage
      // 3. Update database records as archived
      
      this.logger.log(`Archive completed for job ${job.id}, archived ${logs.length} logs to ${archivePath}`);
    } catch (error) {
      this.logger.error(`Failed to archive logs for job ${job.id}:`, error);
      throw error;
    }
  }

  @Process('generate-audit-report')
  async generateAuditReport(job: Job<{ 
    reportType: string; 
    startDate: Date; 
    endDate: Date; 
    userId?: string;
    format: 'json' | 'csv' | 'pdf';
  }>): Promise<void> {
    this.logger.log(`Processing generate audit report job ${job.id}`);

    try {
      const { reportType, startDate, endDate, userId, format } = job.data;

      // Get audit logs for the report
      const logs = await this.auditLogService.getAuditLogs({
        filters: userId ? { userId } : {},
        dateRange: { start: startDate, end: endDate },
        limit: 50000, // Large limit for reports
      });

      // Generate report data
      const reportData = {
        reportType,
        generatedAt: new Date(),
        period: { start: startDate, end: endDate },
        totalLogs: logs.length,
        summary: {
          byAction: logs.reduce((acc, log) => {
            acc[log.action] = (acc[log.action] || 0) + 1;
            return acc;
          }, {} as Record<string, number>),
          bySeverity: logs.reduce((acc, log) => {
            acc[log.severity] = (acc[log.severity] || 0) + 1;
            return acc;
          }, {} as Record<string, number>),
          byUser: logs.reduce((acc, log) => {
            if (log.userId) {
              acc[log.userId] = (acc[log.userId] || 0) + 1;
            }
            return acc;
          }, {} as Record<string, number>),
        },
        logs: logs,
      };

      // In a real implementation, you would format the report according to the requested format
      // and store it somewhere accessible

      this.logger.log(`Audit report generated successfully for job ${job.id}`);
    } catch (error) {
      this.logger.error(`Failed to generate audit report for job ${job.id}:`, error);
      throw error;
    }
  }
}