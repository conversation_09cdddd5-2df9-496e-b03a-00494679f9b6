import { screen, within, waitFor } from '@testing-library/react'

// Enhanced query utilities
export const queries = {
  // Get element by data-testid with better error messages
  getElement: (testId: string, container?: HTMLElement) => {
    const withinContext = container ? within(container) : screen
    try {
      return withinContext.getByTestId(testId)
    } catch (error) {
      throw new Error(`Element with testId="${testId}" not found`)
    }
  },

  // Find element with retry
  findElement: async (testId: string, options: { timeout?: number } = { timeout: 3000 }) => {
    return await screen.findByTestId(testId, {}, { timeout: options.timeout || 3000 })
  },

  // Query element (returns null if not found)
  queryElement: (testId: string, container?: HTMLElement) => {
    const withinContext = container ? within(container) : screen
    return withinContext.queryByTestId(testId)
  },

  // Get all elements by testId
  getAllElements: (testId: string, container?: HTMLElement) => {
    const withinContext = container ? within(container) : screen
    return withinContext.getAllByTestId(testId)
  },

  // Text queries with normalization
  getByTextContent: (text: string | RegExp, container?: HTMLElement) => {
    const withinContext = container ? within(container) : screen
    return withinContext.getByText(text, {
      normalizer: (str) => str.trim().replace(/\s+/g, ' '),
    })
  },

  // Role queries with accessible name
  getByRoleWithName: (role: string, name: string | RegExp) => {
    return screen.getByRole(role, { name })
  },

  // Complex queries
  getButtonByText: (text: string | RegExp) => {
    return screen.getByRole('button', { name: text })
  },

  getInputByLabel: (label: string | RegExp) => {
    return screen.getByLabelText(label)
  },

  getLinkByText: (text: string | RegExp) => {
    return screen.getByRole('link', { name: text })
  },

  // Form-specific queries
  getFormField: (name: string) => {
    return screen.getByRole('textbox', { name }) ||
           screen.getByRole('combobox', { name }) ||
           screen.getByRole('spinbutton', { name })
  },

  // Table queries
  getTableCell: (rowIndex: number, colIndex: number) => {
    const rows = screen.getAllByRole('row')
    const cells = within(rows[rowIndex]).getAllByRole('cell')
    return cells[colIndex]
  },

  // List queries
  getListItems: (listTestId: string) => {
    const list = screen.getByTestId(listTestId)
    return within(list).getAllByRole('listitem')
  },
}

// Wait for element utilities
export const waitForElement = async (
  testId: string,
  options = { timeout: 3000 }
) => {
  return await waitFor(
    () => {
      const element = screen.getByTestId(testId)
      expect(element).toBeInTheDocument()
      return element
    },
    options
  )
}

export const waitForElementToBeRemoved = async (
  testId: string,
  options = { timeout: 3000 }
) => {
  return await waitFor(
    () => {
      expect(screen.queryByTestId(testId)).not.toBeInTheDocument()
    },
    options
  )
}

export const waitForText = async (
  text: string | RegExp,
  options = { timeout: 3000 }
) => {
  return await waitFor(
    () => {
      const element = screen.getByText(text)
      expect(element).toBeInTheDocument()
      return element
    },
    options
  )
}

// Assertion helpers
export const assertElementExists = (testId: string) => {
  const element = screen.getByTestId(testId)
  expect(element).toBeInTheDocument()
  return element
}

export const assertElementNotExists = (testId: string) => {
  const element = screen.queryByTestId(testId)
  expect(element).not.toBeInTheDocument()
}

export const assertElementVisible = (testId: string) => {
  const element = screen.getByTestId(testId)
  expect(element).toBeVisible()
  return element
}

export const assertElementHidden = (testId: string) => {
  const element = screen.getByTestId(testId)
  expect(element).not.toBeVisible()
  return element
}

// Interaction helpers
export const getInteractiveElement = (
  testId: string,
  options: { disabled?: boolean } = {}
) => {
  const element = screen.getByTestId(testId)
  
  if (options.disabled !== undefined) {
    if (options.disabled) {
      expect(element).toBeDisabled()
    } else {
      expect(element).toBeEnabled()
    }
  }
  
  return element
}