'use strict';

require('@testing-library/jest-dom/vitest');
var react = require('@testing-library/react');
var vitest = require('vitest');
var userEvent = require('@testing-library/user-event');
var jsxRuntime = require('react/jsx-runtime');
var React = require('react');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var userEvent__default = /*#__PURE__*/_interopDefault(userEvent);
var React__default = /*#__PURE__*/_interopDefault(React);

var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
  get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
}) : x)(function(x) {
  if (typeof require !== "undefined") return require.apply(this, arguments);
  throw Error('Dynamic require of "' + x + '" is not supported');
});
vitest.afterEach(() => {
  react.cleanup();
});
vitest.beforeAll(() => {
  Object.defineProperty(window, "matchMedia", {
    writable: true,
    value: vitest.vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vitest.vi.fn(),
      // deprecated
      removeListener: vitest.vi.fn(),
      // deprecated
      addEventListener: vitest.vi.fn(),
      removeEventListener: vitest.vi.fn(),
      dispatchEvent: vitest.vi.fn()
    }))
  });
  global.IntersectionObserver = vitest.vi.fn().mockImplementation(() => ({
    observe: vitest.vi.fn(),
    unobserve: vitest.vi.fn(),
    disconnect: vitest.vi.fn()
  }));
  global.ResizeObserver = vitest.vi.fn().mockImplementation(() => ({
    observe: vitest.vi.fn(),
    unobserve: vitest.vi.fn(),
    disconnect: vitest.vi.fn()
  }));
  window.scrollTo = vitest.vi.fn();
  if (!global.fetch) {
    global.fetch = vitest.vi.fn();
  }
  const originalError = console.error;
  console.error = (...args) => {
    if (typeof args[0] === "string" && (args[0].includes("Warning: ReactDOM.render") || args[0].includes("Warning: An invalid form control") || args[0].includes("Not implemented: HTMLFormElement.prototype.submit"))) {
      return;
    }
    originalError.call(console, ...args);
  };
});
var mockLocalStorage = () => {
  const storage = {};
  return {
    getItem: vitest.vi.fn((key) => storage[key] || null),
    setItem: vitest.vi.fn((key, value) => {
      storage[key] = value;
    }),
    removeItem: vitest.vi.fn((key) => {
      delete storage[key];
    }),
    clear: vitest.vi.fn(() => {
      Object.keys(storage).forEach((key) => delete storage[key]);
    }),
    get length() {
      return Object.keys(storage).length;
    },
    key: vitest.vi.fn((index) => {
      const keys = Object.keys(storage);
      return keys[index] || null;
    })
  };
};
var setupBrowserMocks = () => {
  const localStorage = mockLocalStorage();
  const sessionStorage = mockLocalStorage();
  Object.defineProperty(window, "localStorage", {
    value: localStorage,
    writable: true
  });
  Object.defineProperty(window, "sessionStorage", {
    value: sessionStorage,
    writable: true
  });
  global.URL = URL;
  global.URLSearchParams = URLSearchParams;
  Object.defineProperty(window, "crypto", {
    value: {
      randomUUID: () => "12345678-1234-1234-1234-123456789012",
      getRandomValues: (arr) => {
        for (let i = 0; i < arr.length; i++) {
          arr[i] = Math.floor(Math.random() * 256);
        }
        return arr;
      }
    }
  });
};
var MemoryRouter;
try {
  const routerModule = __require("react-router-dom");
  MemoryRouter = routerModule.MemoryRouter;
} catch (error) {
  MemoryRouter = ({ children }) => /* @__PURE__ */ jsxRuntime.jsx(jsxRuntime.Fragment, { children });
}
var createWrapper = (options = {}) => {
  const { routerProps = {}, route = "/", providers = [], providerProps = {} } = options;
  return ({ children }) => {
    if (route && routerProps.initialEntries === void 0) {
      routerProps.initialEntries = [route];
    }
    let wrapped = children;
    wrapped = /* @__PURE__ */ jsxRuntime.jsx(MemoryRouter, { ...routerProps, children: wrapped });
    providers.reverse().forEach((Provider) => {
      const props = providerProps[Provider.displayName || Provider.name] || {};
      wrapped = /* @__PURE__ */ jsxRuntime.jsx(Provider, { ...props, children: wrapped });
    });
    return wrapped;
  };
};
function customRender(ui, options = {}) {
  const { mockData, ...renderOptions } = options;
  const user = userEvent__default.default.setup();
  if (mockData) {
    Object.entries(mockData).forEach(([key, value]) => {
      window[key] = value;
    });
  }
  const wrapper = createWrapper(options);
  const renderResult = react.render(ui, { wrapper, ...renderOptions });
  return {
    ...renderResult,
    user
  };
}
var renderWithRouter = (ui, routerProps, renderOptions) => {
  const options = { ...renderOptions };
  if (routerProps) {
    options.routerProps = routerProps;
  }
  return customRender(ui, options);
};
var renderWithProviders = (ui, providers = [], renderOptions) => {
  return customRender(ui, { ...renderOptions, providers });
};
var renderHookWithProviders = (hook, options) => {
  const TestComponent = ({ hookProps }) => {
    const result = hook(hookProps);
    return /* @__PURE__ */ jsxRuntime.jsx("div", { "data-testid": "hook-result", children: JSON.stringify(result) });
  };
  return {
    ...customRender(/* @__PURE__ */ jsxRuntime.jsx(TestComponent, { hookProps: {} }), options),
    result: {
      current: hook({})
    }
  };
};
var renderAndGetElements = (ui, options) => {
  const renderResult = customRender(ui, options);
  return {
    ...renderResult
  };
};
var createWrapper2 = (options = {}) => {
  const { providers = [], providerProps = {} } = options;
  return ({ children }) => {
    let wrapped = children;
    providers.reverse().forEach((Provider) => {
      const props = providerProps[Provider.displayName || Provider.name] || {};
      wrapped = /* @__PURE__ */ jsxRuntime.jsx(Provider, { ...props, children: wrapped });
    });
    return /* @__PURE__ */ jsxRuntime.jsx(jsxRuntime.Fragment, { children: wrapped });
  };
};
var TestProviders = ({ children }) => {
  return /* @__PURE__ */ jsxRuntime.jsx(jsxRuntime.Fragment, { children });
};
var createContextWrapper = (Context, value) => {
  return ({ children }) => /* @__PURE__ */ jsxRuntime.jsx(Context.Provider, { value, children });
};
var createMockProvider = (name, defaultValue) => {
  const Context = React__default.default.createContext(defaultValue);
  const Provider = ({
    children,
    value = {}
  }) => {
    const mergedValue = { ...defaultValue, ...value };
    return /* @__PURE__ */ jsxRuntime.jsx(Context.Provider, { value: mergedValue, children });
  };
  Provider.displayName = name;
  return { Context, Provider };
};
var CombinedProviders = ({ children, providers = [], providerProps = {} }) => {
  const Wrapper = createWrapper2({ providers, providerProps });
  return /* @__PURE__ */ jsxRuntime.jsx(Wrapper, { children });
};

// src/react/hooks.ts
function renderHookWithContext(hook, options = {}) {
  const { providers = [], providerProps = {}, ...renderOptions } = options;
  const wrapper = createWrapper2({ providers, providerProps });
  return react.renderHook(hook, {
    wrapper,
    ...renderOptions
  });
}
var waitForHookUpdate = async (_result, condition, options = { timeout: 1e3, interval: 50 }) => {
  const { timeout, interval } = options;
  const startTime = Date.now();
  while (!condition() && Date.now() - startTime < timeout) {
    await new Promise((resolve) => setTimeout(resolve, interval));
  }
  if (!condition()) {
    throw new Error(`Hook update condition not met within ${timeout}ms`);
  }
};
var testHookWithAsyncState = async (hook, expectedData) => {
  const { result, rerender } = react.renderHook(hook);
  expect(result.current.loading).toBe(true);
  expect(result.current.data).toBe(null);
  expect(result.current.error).toBe(null);
  await waitForHookUpdate(result, () => !result.current.loading);
  expect(result.current.loading).toBe(false);
  expect(result.current.data).toEqual(expectedData);
  expect(result.current.error).toBe(null);
  return { result, rerender };
};
var testHookErrorState = async (hook, expectedError) => {
  const { result } = react.renderHook(hook);
  await waitForHookUpdate(result, () => result.current.error !== null);
  expect(result.current.loading).toBe(false);
  expect(result.current.data).toBe(null);
  expect(result.current.error?.message).toBe(expectedError);
};
var trackHookRenders = (hook) => {
  let renderCount = 0;
  const values = [];
  const TrackedHook = () => {
    const result2 = hook();
    renderCount++;
    values.push(result2);
    return result2;
  };
  const { result, rerender } = react.renderHook(TrackedHook);
  return {
    result,
    rerender,
    getRenderCount: () => renderCount,
    getValues: () => values,
    getLastValue: () => values[values.length - 1]
  };
};
var queries = {
  // Get element by data-testid with better error messages
  getElement: (testId, container) => {
    const withinContext = container ? react.within(container) : react.screen;
    try {
      return withinContext.getByTestId(testId);
    } catch (error) {
      throw new Error(`Element with testId="${testId}" not found`);
    }
  },
  // Find element with retry
  findElement: async (testId, options = { timeout: 3e3 }) => {
    return await react.screen.findByTestId(testId, {}, { timeout: options.timeout || 3e3 });
  },
  // Query element (returns null if not found)
  queryElement: (testId, container) => {
    const withinContext = container ? react.within(container) : react.screen;
    return withinContext.queryByTestId(testId);
  },
  // Get all elements by testId
  getAllElements: (testId, container) => {
    const withinContext = container ? react.within(container) : react.screen;
    return withinContext.getAllByTestId(testId);
  },
  // Text queries with normalization
  getByTextContent: (text, container) => {
    const withinContext = container ? react.within(container) : react.screen;
    return withinContext.getByText(text, {
      normalizer: (str) => str.trim().replace(/\s+/g, " ")
    });
  },
  // Role queries with accessible name
  getByRoleWithName: (role, name) => {
    return react.screen.getByRole(role, { name });
  },
  // Complex queries
  getButtonByText: (text) => {
    return react.screen.getByRole("button", { name: text });
  },
  getInputByLabel: (label) => {
    return react.screen.getByLabelText(label);
  },
  getLinkByText: (text) => {
    return react.screen.getByRole("link", { name: text });
  },
  // Form-specific queries
  getFormField: (name) => {
    return react.screen.getByRole("textbox", { name }) || react.screen.getByRole("combobox", { name }) || react.screen.getByRole("spinbutton", { name });
  },
  // Table queries
  getTableCell: (rowIndex, colIndex) => {
    const rows = react.screen.getAllByRole("row");
    const cells = react.within(rows[rowIndex]).getAllByRole("cell");
    return cells[colIndex];
  },
  // List queries
  getListItems: (listTestId) => {
    const list = react.screen.getByTestId(listTestId);
    return react.within(list).getAllByRole("listitem");
  }
};
var waitForElement = async (testId, options = { timeout: 3e3 }) => {
  return await react.waitFor(
    () => {
      const element = react.screen.getByTestId(testId);
      expect(element).toBeInTheDocument();
      return element;
    },
    options
  );
};
var waitForElementToBeRemoved = async (testId, options = { timeout: 3e3 }) => {
  return await react.waitFor(
    () => {
      expect(react.screen.queryByTestId(testId)).not.toBeInTheDocument();
    },
    options
  );
};
var waitForText = async (text, options = { timeout: 3e3 }) => {
  return await react.waitFor(
    () => {
      const element = react.screen.getByText(text);
      expect(element).toBeInTheDocument();
      return element;
    },
    options
  );
};
var assertElementExists = (testId) => {
  const element = react.screen.getByTestId(testId);
  expect(element).toBeInTheDocument();
  return element;
};
var assertElementNotExists = (testId) => {
  const element = react.screen.queryByTestId(testId);
  expect(element).not.toBeInTheDocument();
};
var assertElementVisible = (testId) => {
  const element = react.screen.getByTestId(testId);
  expect(element).toBeVisible();
  return element;
};
var assertElementHidden = (testId) => {
  const element = react.screen.getByTestId(testId);
  expect(element).not.toBeVisible();
  return element;
};
var getInteractiveElement = (testId, options = {}) => {
  const element = react.screen.getByTestId(testId);
  if (options.disabled !== void 0) {
    if (options.disabled) {
      expect(element).toBeDisabled();
    } else {
      expect(element).toBeEnabled();
    }
  }
  return element;
};

exports.CombinedProviders = CombinedProviders;
exports.TestProviders = TestProviders;
exports.assertElementExists = assertElementExists;
exports.assertElementHidden = assertElementHidden;
exports.assertElementNotExists = assertElementNotExists;
exports.assertElementVisible = assertElementVisible;
exports.createContextWrapper = createContextWrapper;
exports.createMockProvider = createMockProvider;
exports.createWrapper = createWrapper2;
exports.getInteractiveElement = getInteractiveElement;
exports.mockLocalStorage = mockLocalStorage;
exports.queries = queries;
exports.render = customRender;
exports.renderAndGetElements = renderAndGetElements;
exports.renderHookWithContext = renderHookWithContext;
exports.renderHookWithProviders = renderHookWithProviders;
exports.renderWithProviders = renderWithProviders;
exports.renderWithRouter = renderWithRouter;
exports.setupBrowserMocks = setupBrowserMocks;
exports.testHookErrorState = testHookErrorState;
exports.testHookWithAsyncState = testHookWithAsyncState;
exports.trackHookRenders = trackHookRenders;
exports.waitForElement = waitForElement;
exports.waitForElementToBeRemoved = waitForElementToBeRemoved;
exports.waitForHookUpdate = waitForHookUpdate;
exports.waitForText = waitForText;
//# sourceMappingURL=index.cjs.map
//# sourceMappingURL=index.cjs.map