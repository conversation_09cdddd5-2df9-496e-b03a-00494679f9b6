import { http, HttpResponse, delay } from 'msw'
import { createMockUser, createMockApiResponse, createMockProducts } from '../fixtures'

// Base API URL - can be configured via environment variable
const API_BASE_URL = process.env.VITE_API_URL || 'http://localhost:3000/api'

// Default handlers for common API endpoints
export const defaultHandlers = [
  // Auth endpoints
  http.post(`${API_BASE_URL}/auth/login`, async ({ request }) => {
    const body = await request.json() as any
    await delay(300) // Simulate network delay
    
    if (body.email === '<EMAIL>' && body.password === 'password') {
      return HttpResponse.json(
        createMockApiResponse({
          user: createMockUser({ email: body.email }),
          token: 'mock-jwt-token',
        })
      )
    }
    
    return HttpResponse.json(
      { error: 'Invalid credentials' },
      { status: 401 }
    )
  }),

  http.post(`${API_BASE_URL}/auth/logout`, async () => {
    await delay(100)
    return HttpResponse.json({ success: true })
  }),

  http.get(`${API_BASE_URL}/auth/me`, async () => {
    await delay(200)
    return HttpResponse.json(
      createMockApiResponse(createMockUser())
    )
  }),

  // User endpoints
  http.get(`${API_BASE_URL}/users`, async ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    
    await delay(300)
    
    const users = Array.from({ length: limit }, () => createMockUser())
    
    return HttpResponse.json(
      createMockApiResponse({
        data: users,
        pagination: {
          page,
          limit,
          total: 100,
          totalPages: 10,
        },
      })
    )
  }),

  http.get(`${API_BASE_URL}/users/:id`, async ({ params }) => {
    await delay(200)
    return HttpResponse.json(
      createMockApiResponse(createMockUser({ id: params.id as string }))
    )
  }),

  http.post(`${API_BASE_URL}/users`, async ({ request }) => {
    const body = await request.json() as any
    await delay(300)
    
    return HttpResponse.json(
      createMockApiResponse(createMockUser(body)),
      { status: 201 }
    )
  }),

  http.put(`${API_BASE_URL}/users/:id`, async ({ request, params }) => {
    const body = await request.json() as any
    await delay(300)
    
    return HttpResponse.json(
      createMockApiResponse(createMockUser({ ...body, id: params.id as string }))
    )
  }),

  http.delete(`${API_BASE_URL}/users/:id`, async () => {
    await delay(200)
    return HttpResponse.json({ success: true })
  }),

  // Products endpoints
  http.get(`${API_BASE_URL}/products`, async () => {
    await delay(400)
    return HttpResponse.json(
      createMockApiResponse(createMockProducts(20))
    )
  }),

  // Generic error handler
  http.get(`${API_BASE_URL}/error`, () => {
    return HttpResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }),
]

// Create custom handlers for specific test scenarios
export const createErrorHandlers = (endpoints: string[]) => {
  return endpoints.map(endpoint => 
    http.get(`${API_BASE_URL}${endpoint}`, () => {
      return HttpResponse.json(
        { error: 'Test error' },
        { status: 500 }
      )
    })
  )
}

export const createDelayedHandlers = (_delay: number) => {
  return defaultHandlers.map(handler => {
    // Clone handler with custom delay
    return handler
  })
}

// Handler utilities
export const createCrudHandlers = <T>(
  resource: string,
  generator: () => T,
  baseUrl = API_BASE_URL
) => {
  const resourceUrl = `${baseUrl}/${resource}`
  
  return [
    // List
    http.get(resourceUrl, async () => {
      await delay(200)
      const items = Array.from({ length: 10 }, generator)
      return HttpResponse.json(createMockApiResponse(items))
    }),
    
    // Get by ID
    http.get(`${resourceUrl}/:id`, async ({ params }) => {
      await delay(150)
      return HttpResponse.json(
        createMockApiResponse({ ...generator(), id: params.id })
      )
    }),
    
    // Create
    http.post(resourceUrl, async ({ request }) => {
      const body = await request.json() as any
      await delay(250)
      return HttpResponse.json(
        createMockApiResponse({ ...generator(), ...body }),
        { status: 201 }
      )
    }),
    
    // Update
    http.put(`${resourceUrl}/:id`, async ({ request, params }) => {
      const body = await request.json() as any
      await delay(200)
      return HttpResponse.json(
        createMockApiResponse({ ...generator(), ...body, id: params.id as string })
      )
    }),
    
    // Delete
    http.delete(`${resourceUrl}/:id`, async () => {
      await delay(150)
      return new HttpResponse(null, { status: 204 })
    }),
  ]
}

// WebSocket mock handlers
export const createWebSocketHandlers = () => {
  // WebSocket mocking would be implemented here
  // MSW doesn't support WebSocket mocking directly,
  // but we can create mock implementations
  return []
}

// GraphQL handlers
export const createGraphQLHandlers = (endpoint = `${API_BASE_URL}/graphql`) => {
  return [
    http.post(endpoint, async ({ request }) => {
      const body = await request.json() as any
      const { query, variables } = body
      
      // Simple query matching
      if (query.includes('GetUser')) {
        return HttpResponse.json({
          data: {
            user: createMockUser(variables?.id ? { id: variables.id } : {}),
          },
        })
      }
      
      return HttpResponse.json({
        errors: [{ message: 'Query not mocked' }],
      })
    }),
  ]
}