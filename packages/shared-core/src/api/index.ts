/**
 * API Module - Unified API client and utilities
 */

import { 
  LuminarAP<PERSON>lient, 
  LuminarAPIClientConfig 
} from './types';
import { createLuminarAPIClient } from './client';
import { 
  AuthTokenManager, 
  createAuthInterceptor 
} from './auth-interceptor';

// Core API client
export {
  LuminarAPIClientImpl,
  createLuminarAPIClient,
  getDefaultAPIClient,
  initializeDefaultAPIClient,
  setDefaultAPIClient
} from './client';

// Types and interfaces
export type {
  LuminarAPIClient,
  LuminarAPIClientConfig,
  ApiResponse,
  ApiError,
  ApiMetadata,
  RequestConfig,
  RequestInterceptor,
  ResponseInterceptor,
  ErrorContext,
  RetryConfig
} from './types';

export { ErrorType } from './types';

// Error handling
export {
  ApiErrorHandler,
  isApiError,
  createApiError
} from './error-handler';

// Authentication
export {
  AuthenticationInterceptor,
  LocalStorageTokenManager,
  createAuthInterceptor
} from './auth-interceptor';

export type {
  AuthTokenManager,
  AuthInterceptorConfig
} from './auth-interceptor';

// Utility function to create a configured API client with auth
export function createConfiguredAPIClient(config: {
  baseURL: string;
  tokenManager?: AuthTokenManager;
  enableAuth?: boolean;
  enableLogging?: boolean;
  timeout?: number;
  retries?: number;
}): LuminarAPIClient {
  const client = createLuminarAPIClient({
    baseURL: config.baseURL,
    timeout: config.timeout || 10000,
    retries: config.retries || 3,
    enableLogging: config.enableLogging || false
  });

  // Add authentication interceptor if enabled
  if (config.enableAuth !== false) {
    const authInterceptor = createAuthInterceptor({
      tokenManager: config.tokenManager,
      onAuthError: (error: any) => {
        console.error('[API Auth Error]', error);
        // You might want to redirect to login or show a notification
      },
      onTokenRefresh: (tokens: { accessToken: string; refreshToken?: string }) => {
        console.log('[API] Tokens refreshed successfully');
      }
    });

    client.addRequestInterceptor(authInterceptor.getRequestInterceptor());
    client.addResponseInterceptor(authInterceptor.getResponseInterceptor());
  }

  return client;
}