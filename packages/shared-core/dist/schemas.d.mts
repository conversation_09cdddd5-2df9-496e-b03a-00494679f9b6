import { z } from 'zod';

/**
 * Common validation schemas for Luminar applications
 */
declare const emailSchema: z.ZodString;
declare const passwordSchema: z.ZodString;
declare const nameSchema: z.ZodString;
declare const phoneSchema: z.ZodOptional<z.ZodString>;
declare const urlSchema: z.ZodOptional<z.ZodString>;
declare const idSchema: z.ZodString;
declare const slugSchema: z.ZodString;
declare const paginationSchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
    offset: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodType<PERSON>ny, {
    page: number;
    limit: number;
    offset?: number | undefined;
}, {
    page?: number | undefined;
    limit?: number | undefined;
    offset?: number | undefined;
}>;
declare const sortSchema: z.ZodObject<{
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.<PERSON>efault<z.ZodEnum<["asc", "desc"]>>;
}, "strip", z.Z<PERSON>ype<PERSON>ny, {
    sortOrder: "asc" | "desc";
    sortBy?: string | undefined;
}, {
    sortBy?: string | undefined;
    sortOrder?: "asc" | "desc" | undefined;
}>;
declare const searchSchema: z.ZodObject<{
    query: z.ZodOptional<z.ZodString>;
    filters: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    query?: string | undefined;
    filters?: Record<string, any> | undefined;
}, {
    query?: string | undefined;
    filters?: Record<string, any> | undefined;
}>;
declare const baseQuerySchema: z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
    offset: z.ZodOptional<z.ZodNumber>;
} & {
    sortBy: z.ZodOptional<z.ZodString>;
    sortOrder: z.ZodDefault<z.ZodEnum<["asc", "desc"]>>;
} & {
    query: z.ZodOptional<z.ZodString>;
    filters: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    page: number;
    limit: number;
    sortOrder: "asc" | "desc";
    offset?: number | undefined;
    sortBy?: string | undefined;
    query?: string | undefined;
    filters?: Record<string, any> | undefined;
}, {
    page?: number | undefined;
    limit?: number | undefined;
    offset?: number | undefined;
    sortBy?: string | undefined;
    sortOrder?: "asc" | "desc" | undefined;
    query?: string | undefined;
    filters?: Record<string, any> | undefined;
}>;
declare const loginSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
    rememberMe: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    email: string;
    password: string;
    rememberMe: boolean;
}, {
    email: string;
    password: string;
    rememberMe?: boolean | undefined;
}>;
declare const registerSchema: z.ZodEffects<z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
    confirmPassword: z.ZodString;
    name: z.ZodString;
    acceptTerms: z.ZodEffects<z.ZodBoolean, boolean, boolean>;
}, "strip", z.ZodTypeAny, {
    email: string;
    password: string;
    confirmPassword: string;
    name: string;
    acceptTerms: boolean;
}, {
    email: string;
    password: string;
    confirmPassword: string;
    name: string;
    acceptTerms: boolean;
}>, {
    email: string;
    password: string;
    confirmPassword: string;
    name: string;
    acceptTerms: boolean;
}, {
    email: string;
    password: string;
    confirmPassword: string;
    name: string;
    acceptTerms: boolean;
}>;
declare const forgotPasswordSchema: z.ZodObject<{
    email: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
}, {
    email: string;
}>;
declare const resetPasswordSchema: z.ZodEffects<z.ZodObject<{
    token: z.ZodString;
    password: z.ZodString;
    confirmPassword: z.ZodString;
}, "strip", z.ZodTypeAny, {
    password: string;
    confirmPassword: string;
    token: string;
}, {
    password: string;
    confirmPassword: string;
    token: string;
}>, {
    password: string;
    confirmPassword: string;
    token: string;
}, {
    password: string;
    confirmPassword: string;
    token: string;
}>;
declare const changePasswordSchema: z.ZodEffects<z.ZodObject<{
    currentPassword: z.ZodString;
    newPassword: z.ZodString;
    confirmPassword: z.ZodString;
}, "strip", z.ZodTypeAny, {
    confirmPassword: string;
    currentPassword: string;
    newPassword: string;
}, {
    confirmPassword: string;
    currentPassword: string;
    newPassword: string;
}>, {
    confirmPassword: string;
    currentPassword: string;
    newPassword: string;
}, {
    confirmPassword: string;
    currentPassword: string;
    newPassword: string;
}>;
declare const userProfileSchema: z.ZodObject<{
    name: z.ZodString;
    email: z.ZodString;
    phone: z.ZodOptional<z.ZodString>;
    avatar: z.ZodOptional<z.ZodString>;
    bio: z.ZodOptional<z.ZodString>;
    timezone: z.ZodOptional<z.ZodString>;
    language: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    email: string;
    name: string;
    phone?: string | undefined;
    avatar?: string | undefined;
    bio?: string | undefined;
    timezone?: string | undefined;
    language?: string | undefined;
}, {
    email: string;
    name: string;
    phone?: string | undefined;
    avatar?: string | undefined;
    bio?: string | undefined;
    timezone?: string | undefined;
    language?: string | undefined;
}>;
declare const userPreferencesSchema: z.ZodObject<{
    theme: z.ZodDefault<z.ZodEnum<["light", "dark", "system"]>>;
    notifications: z.ZodDefault<z.ZodObject<{
        email: z.ZodDefault<z.ZodBoolean>;
        push: z.ZodDefault<z.ZodBoolean>;
        sms: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        email: boolean;
        push: boolean;
        sms: boolean;
    }, {
        email?: boolean | undefined;
        push?: boolean | undefined;
        sms?: boolean | undefined;
    }>>;
    privacy: z.ZodDefault<z.ZodObject<{
        profileVisible: z.ZodDefault<z.ZodBoolean>;
        activityVisible: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        profileVisible: boolean;
        activityVisible: boolean;
    }, {
        profileVisible?: boolean | undefined;
        activityVisible?: boolean | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    theme: "light" | "dark" | "system";
    notifications: {
        email: boolean;
        push: boolean;
        sms: boolean;
    };
    privacy: {
        profileVisible: boolean;
        activityVisible: boolean;
    };
}, {
    theme?: "light" | "dark" | "system" | undefined;
    notifications?: {
        email?: boolean | undefined;
        push?: boolean | undefined;
        sms?: boolean | undefined;
    } | undefined;
    privacy?: {
        profileVisible?: boolean | undefined;
        activityVisible?: boolean | undefined;
    } | undefined;
}>;
declare const fileUploadSchema: z.ZodObject<{
    name: z.ZodString;
    size: z.ZodNumber;
    type: z.ZodString;
    data: z.ZodUnion<[z.ZodType<File, z.ZodTypeDef, File>, z.ZodString]>;
}, "strip", z.ZodTypeAny, {
    type: string;
    name: string;
    size: number;
    data: string | File;
}, {
    type: string;
    name: string;
    size: number;
    data: string | File;
}>;
declare const apiResponseSchema: z.ZodObject<{
    data: z.ZodAny;
    message: z.ZodOptional<z.ZodString>;
    success: z.ZodBoolean;
    timestamp: z.ZodString;
    requestId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    timestamp: string;
    message?: string | undefined;
    data?: any;
    requestId?: string | undefined;
}, {
    success: boolean;
    timestamp: string;
    message?: string | undefined;
    data?: any;
    requestId?: string | undefined;
}>;
declare const paginatedResponseSchema: z.ZodObject<{
    message: z.ZodOptional<z.ZodString>;
    success: z.ZodBoolean;
    timestamp: z.ZodString;
    requestId: z.ZodOptional<z.ZodString>;
} & {
    data: z.ZodArray<z.ZodAny, "many">;
    pagination: z.ZodObject<{
        page: z.ZodNumber;
        limit: z.ZodNumber;
        total: z.ZodNumber;
        totalPages: z.ZodNumber;
        hasNext: z.ZodBoolean;
        hasPrev: z.ZodBoolean;
    }, "strip", z.ZodTypeAny, {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    }, {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    }>;
}, "strip", z.ZodTypeAny, {
    success: boolean;
    data: any[];
    timestamp: string;
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
    message?: string | undefined;
    requestId?: string | undefined;
}, {
    success: boolean;
    data: any[];
    timestamp: string;
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
    message?: string | undefined;
    requestId?: string | undefined;
}>;
declare const apiErrorSchema: z.ZodObject<{
    code: z.ZodString;
    message: z.ZodString;
    details: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    timestamp: z.ZodString;
    path: z.ZodOptional<z.ZodString>;
    requestId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    code: string;
    message: string;
    timestamp: string;
    path?: string | undefined;
    requestId?: string | undefined;
    details?: Record<string, any> | undefined;
}, {
    code: string;
    message: string;
    timestamp: string;
    path?: string | undefined;
    requestId?: string | undefined;
    details?: Record<string, any> | undefined;
}>;
type LoginFormData = z.infer<typeof loginSchema>;
type RegisterFormData = z.infer<typeof registerSchema>;
type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;
type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;
type UserProfileFormData = z.infer<typeof userProfileSchema>;
type UserPreferencesFormData = z.infer<typeof userPreferencesSchema>;
type FileUploadFormData = z.infer<typeof fileUploadSchema>;
type PaginationParams = z.infer<typeof paginationSchema>;
type SortParams = z.infer<typeof sortSchema>;
type SearchParams = z.infer<typeof searchSchema>;
type BaseQueryParams = z.infer<typeof baseQuerySchema>;

export { type BaseQueryParams, type ChangePasswordFormData, type FileUploadFormData, type ForgotPasswordFormData, type LoginFormData, type PaginationParams, type RegisterFormData, type ResetPasswordFormData, type SearchParams, type SortParams, type UserPreferencesFormData, type UserProfileFormData, apiErrorSchema, apiResponseSchema, baseQuerySchema, changePasswordSchema, emailSchema, fileUploadSchema, forgotPasswordSchema, idSchema, loginSchema, nameSchema, paginatedResponseSchema, paginationSchema, passwordSchema, phoneSchema, registerSchema, resetPasswordSchema, searchSchema, slugSchema, sortSchema, urlSchema, userPreferencesSchema, userProfileSchema };
