import React from 'react';
import { useFeatureFlag, useFeatureVariant, useABTest } from './hooks';

interface FeatureProps {
  flag: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Component for conditional rendering based on feature flag
 */
export function Feature({ flag, children, fallback = null }: FeatureProps) {
  const isEnabled = useFeatureFlag(flag);
  return <>{isEnabled ? children : fallback}</>;
}

interface VariantProps {
  flag: string;
  variants: Record<string, React.ReactNode>;
  default?: React.ReactNode;
}

/**
 * Component for rendering different variants
 */
export function VariantRenderer({ flag, variants, default: defaultContent = null }: VariantProps) {
  const variant = useFeatureVariant(flag);

  if (!variant) return <>{defaultContent}</>;

  return <>{variants[variant] || defaultContent}</>;
}

interface ABTestProps {
  testId: string;
  variants: Record<string, React.ReactNode>;
  loading?: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Component for A/B testing
 */
export function ABTest({ testId, variants, loading, fallback = null }: ABTestProps) {
  const { variant, isLoading } = useABTest(testId);
  
  if (isLoading && loading) return <>{loading}</>;
  if (!variant) return <>{fallback}</>;
  
  return <>{variants[variant.key] || fallback}</>;
}

interface FeatureGateProps {
  flags: string[];
  requireAll?: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * Component for gating content behind multiple feature flags
 */
export function FeatureGate({ 
  flags, 
  requireAll = true, 
  children, 
  fallback = null 
}: FeatureGateProps) {
  const flagStates = flags.map(flag => useFeatureFlag(flag));
  
  const isEnabled = requireAll 
    ? flagStates.every(state => state)
    : flagStates.some(state => state);
  
  return <>{isEnabled ? children : fallback}</>;
}

/**
 * HOC for feature flag wrapping
 */
export function withFeature<P extends object>(
  flagKey: string,
  FallbackComponent?: React.ComponentType<P>
) {
  return function FeatureWrapped(Component: React.ComponentType<P>) {
    return function WrappedComponent(props: P) {
      return (
        <Feature flag={flagKey} fallback={FallbackComponent && <FallbackComponent {...props} />}>
          <Component {...props} />
        </Feature>
      );
    };
  };
}

/**
 * HOC for A/B test wrapping
 */
export function withABTest<P extends object>(
  testId: string,
  variants: Record<string, React.ComponentType<P>>
) {
  return function ABTestWrapped(props: P) {
    const { variant } = useABTest(testId);
    
    if (!variant || !variants[variant.key]) {
      return null;
    }
    
    const VariantComponent = variants[variant.key];
    return <VariantComponent {...props} />;
  };
}