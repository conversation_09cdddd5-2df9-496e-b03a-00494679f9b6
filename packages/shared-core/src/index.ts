// Core utilities and functions
export * from './utils'

// Constants and configuration
export * from './constants'

// Validation schemas (excluding types that conflict with ./types)
export {
  // Schema validators
  emailSchema,
  passwordSchema,
  nameSchema,
  phoneSchema,
  urlSchema,
  idSchema,
  slugSchema,
  paginationSchema,
  sortSchema,
  searchSchema,
  baseQuerySchema,
  loginSchema,
  registerSchema,
  forgotPasswordSchema,
  resetPasswordSchema,
  changePasswordSchema,
  userProfileSchema,
  userPreferencesSchema,
  fileUploadSchema,
  apiResponseSchema,
  paginatedResponseSchema,
  apiErrorSchema,
  
  // Form data types (prefixed to avoid conflicts)
  type LoginFormData,
  type RegisterFormData,
  type ForgotPasswordFormData,
  type ResetPasswordFormData,
  type ChangePasswordFormData,
  type UserProfileFormData,
  type UserPreferencesFormData,
  type FileUploadFormData
} from './schemas'

// Error handling
export * from './errors'

// API client and services
export * from './api'
export * from './services'

// Type definitions (selective exports to avoid conflicts)
export type {
  // Base types
  BaseUser,
  AuthState,
  LoginCredentials,
  RegisterData,
  
  // File and media types
  FileUpload,
  
  // Notification types
  Notification,
  NotificationAction,
  
  // Configuration types
  ThemeConfig,
  AppConfig,
  
  // Form types
  FormField,
  TableColumn,
  
  // Chart types
  ChartDataPoint,
  TimeSeriesDataPoint,
  AnalyticsData,
  
  // Feature flags
  FeatureFlag,
  
  // Audit types
  AuditLogEntry,
  
  // Base entity
  BaseEntity,
  
  // Utility types
  Status,
  Priority,
  KeyValuePair,
  EnvironmentVariables,
  
  // Pagination types (for general use, API-specific ones come from ./api)
  PaginatedResponse
} from './types'
